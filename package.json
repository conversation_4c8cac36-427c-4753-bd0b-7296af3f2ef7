{"name": "ubuntu-connect", "version": "1.0.0", "description": "A culturally sensitive platform designed to unite South Africa through cross-cultural collaboration", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "build:force": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "firebase": "^10.7.1", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "immer": "^10.0.3", "qrcode": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.1", "tailwind-merge": "^2.2.0", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-pwa": "^0.17.4", "vitest": "^1.0.4"}, "keywords": ["south-africa", "cultural-diversity", "cross-cultural", "ubuntu", "pwa", "react", "firebase", "community"], "author": "Ubuntu Connect Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}