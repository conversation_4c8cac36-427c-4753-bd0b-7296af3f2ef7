{"hash": "80f5db48", "configHash": "5199e722", "lockfileHash": "f598aa5c", "browserHash": "408b497e", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "222f83dc", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "9bb2c054", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "b2f178b9", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "06a7824b", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "82622ca2", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "5dc7aff0", "needsInterop": false}, "firebase/analytics": {"src": "../../firebase/analytics/dist/esm/index.esm.js", "file": "firebase_analytics.js", "fileHash": "521c7347", "needsInterop": false}, "firebase/app": {"src": "../../firebase/app/dist/esm/index.esm.js", "file": "firebase_app.js", "fileHash": "5fd09648", "needsInterop": false}, "firebase/auth": {"src": "../../firebase/auth/dist/esm/index.esm.js", "file": "firebase_auth.js", "fileHash": "f427e9a2", "needsInterop": false}, "firebase/firestore": {"src": "../../firebase/firestore/dist/esm/index.esm.js", "file": "firebase_firestore.js", "fileHash": "ed1c02b9", "needsInterop": false}, "firebase/storage": {"src": "../../firebase/storage/dist/esm/index.esm.js", "file": "firebase_storage.js", "fileHash": "437951e8", "needsInterop": false}, "i18next": {"src": "../../i18next/dist/esm/i18next.js", "file": "i18next.js", "fileHash": "60ee29d6", "needsInterop": false}, "i18next-browser-languagedetector": {"src": "../../i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js", "file": "i18next-browser-languagedetector.js", "fileHash": "7ddae7ef", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b3f31d7e", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "f7ffdc1d", "needsInterop": false}, "react-i18next": {"src": "../../react-i18next/dist/es/index.js", "file": "react-i18next.js", "fileHash": "5ff95c5d", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "7cdd998c", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "ad2ae3f6", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "ccfebfe8", "needsInterop": false}, "zustand/middleware/immer": {"src": "../../zustand/esm/middleware/immer.mjs", "file": "zustand_middleware_immer.js", "fileHash": "30e2cfe7", "needsInterop": false}}, "chunks": {"chunk-TYILIMWK": {"file": "chunk-TYILIMWK.js"}, "chunk-CANBAPAS": {"file": "chunk-CANBAPAS.js"}, "chunk-NZACC4LT": {"file": "chunk-NZACC4LT.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}