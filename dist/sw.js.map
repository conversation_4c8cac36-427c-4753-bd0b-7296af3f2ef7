{"version": 3, "file": "sw.js", "sources": ["../../../../private/var/folders/q8/9mydxg3d7kjc_gk2b_c2701c0000gn/T/167784a56d2c56593a5d6e72184903a1/sw.js"], "sourcesContent": ["import {registerRoute as workbox_routing_registerRoute} from '/Users/<USER>/Projects/ubuntu-connect/node_modules/workbox-routing/registerRoute.mjs';\nimport {ExpirationPlugin as workbox_expiration_ExpirationPlugin} from '/Users/<USER>/Projects/ubuntu-connect/node_modules/workbox-expiration/ExpirationPlugin.mjs';\nimport {CacheFirst as workbox_strategies_CacheFirst} from '/Users/<USER>/Projects/ubuntu-connect/node_modules/workbox-strategies/CacheFirst.mjs';\nimport {NetworkFirst as workbox_strategies_NetworkFirst} from '/Users/<USER>/Projects/ubuntu-connect/node_modules/workbox-strategies/NetworkFirst.mjs';\nimport {clientsClaim as workbox_core_clientsClaim} from '/Users/<USER>/Projects/ubuntu-connect/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from '/Users/<USER>/Projects/ubuntu-connect/node_modules/workbox-precaching/precacheAndRoute.mjs';\nimport {cleanupOutdatedCaches as workbox_precaching_cleanupOutdatedCaches} from '/Users/<USER>/Projects/ubuntu-connect/node_modules/workbox-precaching/cleanupOutdatedCaches.mjs';\nimport {NavigationRoute as workbox_routing_NavigationRoute} from '/Users/<USER>/Projects/ubuntu-connect/node_modules/workbox-routing/NavigationRoute.mjs';\nimport {createHandlerBoundToURL as workbox_precaching_createHandlerBoundToURL} from '/Users/<USER>/Projects/ubuntu-connect/node_modules/workbox-precaching/createHandlerBoundToURL.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"assets/AchievementShowcasePage-CmfOiW9q.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/AuthPage-Chhb6i1V.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/CrossCulturalCollaborationPage-CeaGM6MI.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/CulturalKnowledgePage-BnYvObkg.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/CulturalOnboardingPage-Cu7AFwSZ.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/culturalValidationService-CA9WZNCm.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/firebase-DLuFXYhP.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/HomePage-BlDCyb5y.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/index-DW0Ai7MP.css\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/index-nwrMOwxu.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/KnowledgeExchangePage-C23voltd.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/ui-ktUZWAxd.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/vendor-DtOhX2xw.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"index.html\",\n    \"revision\": \"93e15d5ea9f00baecb95a69e872d878b\"\n  },\n  {\n    \"url\": \"registerSW.js\",\n    \"revision\": \"1872c500de691dce40960bb85481de07\"\n  },\n  {\n    \"url\": \"manifest.webmanifest\",\n    \"revision\": \"2a66951ad2c66b0c2be392d52c7ebbf4\"\n  }\n], {});\nworkbox_precaching_cleanupOutdatedCaches();\nworkbox_routing_registerRoute(new workbox_routing_NavigationRoute(workbox_precaching_createHandlerBoundToURL(\"index.html\")));\n\n\nworkbox_routing_registerRoute(/^https:\\/\\/fonts\\.googleapis\\.com\\/.*/i, new workbox_strategies_CacheFirst({ \"cacheName\":\"google-fonts-cache\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 10, maxAgeSeconds: ******** })] }), 'GET');\nworkbox_routing_registerRoute(/^https:\\/\\/firestore\\.googleapis\\.com\\/.*/i, new workbox_strategies_NetworkFirst({ \"cacheName\":\"firestore-cache\",\"networkTimeoutSeconds\":3, plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 50, maxAgeSeconds: 300 })] }), 'GET');\n\n\n\n\n"], "names": ["self", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "url", "revision", "workbox_precaching_cleanupOutdatedCaches", "workbox", "registerRoute", "workbox_routing_NavigationRoute", "workbox_precaching_createHandlerBoundToURL", "workbox_routing_registerRoute", "workbox_strategies_CacheFirst", "cacheName", "plugins", "workbox_expiration_ExpirationPlugin", "maxEntries", "maxAgeSeconds", "workbox_strategies_NetworkFirst", "networkTimeoutSeconds"], "mappings": "0nBA2BAA,KAAKC,cAELC,EAAAA,eAQAC,EAAAA,iBAAoC,CAClC,CACEC,IAAO,6CACPC,SAAY,MAEd,CACED,IAAO,8BACPC,SAAY,MAEd,CACED,IAAO,oDACPC,SAAY,MAEd,CACED,IAAO,2CACPC,SAAY,MAEd,CACED,IAAO,4CACPC,SAAY,MAEd,CACED,IAAO,+CACPC,SAAY,MAEd,CACED,IAAO,8BACPC,SAAY,MAEd,CACED,IAAO,8BACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,2BACPC,SAAY,MAEd,CACED,IAAO,2CACPC,SAAY,MAEd,CACED,IAAO,wBACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,aACPC,SAAY,oCAEd,CACED,IAAO,gBACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,qCAEb,CAAE,GACLC,EAAAA,wBAC6BC,EAAAC,cAAC,IAAIC,EAAAA,gBAAgCC,EAAAA,wBAA2C,gBAG7GC,EAAAA,cAA8B,yCAA0C,IAAIC,aAA8B,CAAEC,UAAY,qBAAsBC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,aAAiB,OAClPN,EAAAA,cAA8B,6CAA8C,IAAIO,eAAgC,CAAEL,UAAY,kBAAkBM,sBAAwB,EAAGL,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,SAAY"}