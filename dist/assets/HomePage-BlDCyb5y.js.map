{"version": 3, "file": "HomePage-BlDCyb5y.js", "sources": ["../../src/pages/HomePage.tsx"], "sourcesContent": ["import React from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { useNavigate } from 'react-router-dom'\nimport { useAuth } from '@/features/auth/hooks/useAuth'\nimport Button from '@/components/ui/Button'\nimport Card, { CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card'\n\nconst HomePage: React.FC = () => {\n  const { t } = useTranslation()\n  const navigate = useNavigate()\n  const { user, logout, userDisplayName } = useAuth()\n\n  const handleLogout = async () => {\n    try {\n      await logout()\n    } catch (error) {\n      console.error('Logout failed:', error)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-cultural-50 via-white to-ubuntu-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-cultural-500 to-ubuntu-500 rounded-full\" />\n              <h1 className=\"text-xl font-bold text-cultural-gradient\">\n                {t('app.name')}\n              </h1>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">\n                Welcome, {userDisplayName}\n              </span>\n              <Button variant=\"outline\" size=\"sm\" onClick={handleLogout}>\n                {t('auth.logout')}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"space-y-8\">\n          {/* Welcome Section */}\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Welcome to Ubuntu Connect\n            </h2>\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              {t('app.tagline')}\n            </p>\n          </div>\n\n          {/* Feature Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            <Card variant=\"cultural\" hover>\n              <CardHeader>\n                <CardTitle>Cultural Heritage</CardTitle>\n                <CardDescription>\n                  Document and preserve South African cultural heritage for future generations\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Button\n                  variant=\"outline\"\n                  fullWidth\n                  onClick={() => navigate('/cultural-knowledge')}\n                >\n                  Explore Heritage\n                </Button>\n              </CardContent>\n            </Card>\n\n            <Card variant=\"cultural\" hover>\n              <CardHeader>\n                <CardTitle>Knowledge Sharing</CardTitle>\n                <CardDescription>\n                  Share skills and learn from mentors across cultural boundaries\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Button\n                  variant=\"outline\"\n                  fullWidth\n                  onClick={() => navigate('/cultural-knowledge')}\n                >\n                  Share & Learn\n                </Button>\n              </CardContent>\n            </Card>\n\n            <Card variant=\"cultural\" hover>\n              <CardHeader>\n                <CardTitle>Content Discovery</CardTitle>\n                <CardDescription>\n                  Discover personalized cultural content and trending topics\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Button\n                  variant=\"outline\"\n                  fullWidth\n                  onClick={() => navigate('/cultural-knowledge')}\n                >\n                  Discover Content\n                </Button>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* User Profile Summary */}\n          <Card variant=\"elevated\">\n            <CardHeader>\n              <CardTitle>Your Profile</CardTitle>\n              <CardDescription>\n                Complete your profile to get the most out of Ubuntu Connect\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium\">Email Verification</span>\n                  <span className={`text-sm px-2 py-1 rounded-full ${\n                    user?.emailVerified \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-yellow-100 text-yellow-800'\n                  }`}>\n                    {user?.emailVerified ? 'Verified' : 'Pending'}\n                  </span>\n                </div>\n                \n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium\">Cultural Profile</span>\n                  <Button variant=\"outline\" size=\"sm\">\n                    Complete Profile\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </main>\n    </div>\n  )\n}\n\nexport default HomePage\n"], "names": ["HomePage", "t", "useTranslation", "navigate", "useNavigate", "user", "logout", "userDisplayName", "useAuth", "handleLogout", "error", "jsxs", "jsx", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "mKAOA,MAAMA,EAAqB,IAAM,CACzB,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvBC,EAAWC,EAAY,EACvB,CAAE,KAAAC,EAAM,OAAAC,EAAQ,gBAAAC,CAAA,EAAoBC,EAAQ,EAE5CC,EAAe,SAAY,CAC3B,GAAA,CACF,MAAMH,EAAO,QACNI,EAAO,CACN,QAAA,MAAM,iBAAkBA,CAAK,CAAA,CAEzC,EAGE,OAAAC,EAAA,KAAC,MAAI,CAAA,UAAU,yEAEb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,8CAChB,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,yCACb,SAAAD,OAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,uEAAwE,CAAA,QACtF,KAAG,CAAA,UAAU,2CACX,SAAAX,EAAE,UAAU,CACf,CAAA,CAAA,EACF,EAEAU,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,wBAAwB,SAAA,CAAA,YAC5BJ,CAAA,EACZ,EACAK,EAAAA,IAACC,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,QAASJ,EAC1C,SAAER,EAAA,aAAa,CAClB,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,QAGC,OAAK,CAAA,UAAU,8CACd,SAACU,EAAA,KAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAEtD,4BAAA,QACC,IAAE,CAAA,UAAU,0CACV,SAAAX,EAAE,aAAa,CAClB,CAAA,CAAA,EACF,EAGAU,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,EAAA,KAACG,EAAK,CAAA,QAAQ,WAAW,MAAK,GAC5B,SAAA,CAAAH,OAACI,EACC,CAAA,SAAA,CAAAH,EAAAA,IAACI,GAAU,SAAiB,mBAAA,CAAA,EAC5BJ,EAAAA,IAACK,GAAgB,SAEjB,8EAAA,CAAA,CAAA,EACF,QACCC,EACC,CAAA,SAAAN,EAAA,IAACC,EAAA,CACC,QAAQ,UACR,UAAS,GACT,QAAS,IAAMV,EAAS,qBAAqB,EAC9C,SAAA,kBAAA,CAAA,CAGH,CAAA,CAAA,EACF,EAECQ,EAAA,KAAAG,EAAA,CAAK,QAAQ,WAAW,MAAK,GAC5B,SAAA,CAAAH,OAACI,EACC,CAAA,SAAA,CAAAH,EAAAA,IAACI,GAAU,SAAiB,mBAAA,CAAA,EAC5BJ,EAAAA,IAACK,GAAgB,SAEjB,gEAAA,CAAA,CAAA,EACF,QACCC,EACC,CAAA,SAAAN,EAAA,IAACC,EAAA,CACC,QAAQ,UACR,UAAS,GACT,QAAS,IAAMV,EAAS,qBAAqB,EAC9C,SAAA,eAAA,CAAA,CAGH,CAAA,CAAA,EACF,EAECQ,EAAA,KAAAG,EAAA,CAAK,QAAQ,WAAW,MAAK,GAC5B,SAAA,CAAAH,OAACI,EACC,CAAA,SAAA,CAAAH,EAAAA,IAACI,GAAU,SAAiB,mBAAA,CAAA,EAC5BJ,EAAAA,IAACK,GAAgB,SAEjB,4DAAA,CAAA,CAAA,EACF,QACCC,EACC,CAAA,SAAAN,EAAA,IAACC,EAAA,CACC,QAAQ,UACR,UAAS,GACT,QAAS,IAAMV,EAAS,qBAAqB,EAC9C,SAAA,kBAAA,CAAA,CAGH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAQ,EAAAA,KAACG,EAAK,CAAA,QAAQ,WACZ,SAAA,CAAAH,OAACI,EACC,CAAA,SAAA,CAAAH,EAAAA,IAACI,GAAU,SAAY,cAAA,CAAA,EACvBJ,EAAAA,IAACK,GAAgB,SAEjB,6DAAA,CAAA,CAAA,EACF,EACCL,MAAAM,EAAA,CACC,SAACP,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,sBAAsB,SAAkB,qBAAA,EACvDA,EAAA,IAAA,OAAA,CAAK,UAAW,kCACfP,GAAM,cACF,8BACA,+BACN,GACG,SAAAA,GAAM,cAAgB,WAAa,SACtC,CAAA,CAAA,EACF,EAEAM,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,sBAAsB,SAAgB,mBAAA,QACrDC,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,SAEpC,kBAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ"}