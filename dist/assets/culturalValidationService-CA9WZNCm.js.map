{"version": 3, "file": "culturalValidationService-CA9WZNCm.js", "sources": ["../../src/services/culturalValidationService.ts"], "sourcesContent": ["import { doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore'\nimport { db } from '@/services/firebase'\nimport { CulturalContent, CulturalModerationQueue } from '@/types/cultural'\n\ninterface CulturalValidationRequest {\n  userId: string\n  culturalIdentity: string\n  evidence: {\n    type: 'community_endorsement' | 'cultural_knowledge' | 'language_proficiency' | 'family_heritage'\n    description: string\n    supportingData?: any\n  }\n  submissionDate: Date\n}\n\ninterface CulturalRepresentative {\n  userId: string\n  culturalIdentity: string\n  verificationLevel: 'community_elder' | 'cultural_expert' | 'academic' | 'community_leader'\n  endorsements: number\n  activeStatus: boolean\n}\n\nclass CulturalValidationService {\n  // Submit cultural identity validation request\n  async submitValidationRequest(request: CulturalValidationRequest): Promise<string> {\n    try {\n      const requestId = `validation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      await setDoc(doc(db, 'cultural-validation-requests', requestId), {\n        ...request,\n        id: requestId,\n        status: 'pending',\n        assignedRepresentatives: [],\n        reviewNotes: [],\n        submissionDate: request.submissionDate,\n      })\n\n      // Notify relevant cultural representatives\n      await this.notifyCulturalRepresentatives(request.culturalIdentity, requestId)\n\n      return requestId\n    } catch (error) {\n      console.error('Error submitting validation request:', error)\n      throw new Error('Failed to submit validation request')\n    }\n  }\n\n  // Get cultural representatives for a specific cultural identity\n  async getCulturalRepresentatives(culturalIdentity: string): Promise<CulturalRepresentative[]> {\n    try {\n      const q = query(\n        collection(db, 'cultural-representatives'),\n        where('culturalIdentity', '==', culturalIdentity),\n        where('activeStatus', '==', true)\n      )\n      \n      const querySnapshot = await getDocs(q)\n      return querySnapshot.docs.map(doc => doc.data() as CulturalRepresentative)\n    } catch (error) {\n      console.error('Error fetching cultural representatives:', error)\n      return []\n    }\n  }\n\n  // Validate cultural content for accuracy and sensitivity\n  async validateCulturalContent(content: CulturalContent): Promise<{\n    isValid: boolean\n    concerns: string[]\n    recommendations: string[]\n    requiresReview: boolean\n  }> {\n    const concerns: string[] = []\n    const recommendations: string[] = []\n    let requiresReview = false\n\n    // Check for potentially sensitive content\n    const sensitiveKeywords = [\n      'sacred', 'ritual', 'ceremony', 'traditional medicine', 'spiritual',\n      'ancestral', 'initiation', 'burial', 'marriage customs'\n    ]\n\n    const contentText = `${content.title} ${content.content}`.toLowerCase()\n    const hasSensitiveContent = sensitiveKeywords.some(keyword => \n      contentText.includes(keyword)\n    )\n\n    if (hasSensitiveContent) {\n      requiresReview = true\n      concerns.push('Content contains culturally sensitive topics that require community review')\n      recommendations.push('Consider adding cultural context and sensitivity warnings')\n    }\n\n    // Check for cultural appropriation indicators\n    const appropriationIndicators = [\n      'costume', 'dress up', 'exotic', 'primitive', 'tribal fashion'\n    ]\n\n    const hasAppropriation = appropriationIndicators.some(indicator =>\n      contentText.includes(indicator)\n    )\n\n    if (hasAppropriation) {\n      concerns.push('Content may contain cultural appropriation elements')\n      recommendations.push('Review language and context to ensure respectful representation')\n      requiresReview = true\n    }\n\n    // Check author's cultural credibility\n    if (content.author.culturalCredibility === 'community_member') {\n      // Community members can share general cultural content\n      if (hasSensitiveContent) {\n        recommendations.push('Consider getting endorsement from cultural representatives')\n      }\n    } else if (content.author.culturalCredibility === 'cultural_expert') {\n      // Cultural experts have more leeway but still need review for sensitive content\n      if (hasSensitiveContent) {\n        recommendations.push('Expert content with sensitive topics should include cultural context')\n      }\n    }\n\n    // Language and tone analysis\n    const respectfulLanguage = this.analyzeLanguageRespectfulness(contentText)\n    if (!respectfulLanguage.isRespectful) {\n      concerns.push(...respectfulLanguage.issues)\n      recommendations.push(...respectfulLanguage.suggestions)\n      requiresReview = true\n    }\n\n    return {\n      isValid: concerns.length === 0,\n      concerns,\n      recommendations,\n      requiresReview,\n    }\n  }\n\n  // Analyze language for cultural respectfulness\n  private analyzeLanguageRespectfulness(text: string): {\n    isRespectful: boolean\n    issues: string[]\n    suggestions: string[]\n  } {\n    const issues: string[] = []\n    const suggestions: string[] = []\n\n    // Check for problematic terms\n    const problematicTerms = [\n      { term: 'primitive', suggestion: 'traditional' },\n      { term: 'savage', suggestion: 'warrior' },\n      { term: 'exotic', suggestion: 'unique' },\n      { term: 'weird', suggestion: 'different' },\n      { term: 'strange', suggestion: 'unfamiliar' },\n    ]\n\n    problematicTerms.forEach(({ term, suggestion }) => {\n      if (text.includes(term)) {\n        issues.push(`Use of potentially offensive term: \"${term}\"`)\n        suggestions.push(`Consider using \"${suggestion}\" instead of \"${term}\"`)\n      }\n    })\n\n    // Check for generalizations\n    const generalizationPatterns = [\n      'all [culture] people',\n      '[culture] people always',\n      '[culture] people never',\n    ]\n\n    // This is a simplified check - in practice, you'd use more sophisticated NLP\n    if (text.includes('all ') || text.includes(' always ') || text.includes(' never ')) {\n      issues.push('Content may contain cultural generalizations')\n      suggestions.push('Avoid broad generalizations about cultural groups')\n    }\n\n    return {\n      isRespectful: issues.length === 0,\n      issues,\n      suggestions,\n    }\n  }\n\n  // Submit content to moderation queue\n  async submitToModerationQueue(\n    content: CulturalContent,\n    reason: string,\n    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium'\n  ): Promise<string> {\n    try {\n      const queueId = `moderation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      const moderationItem: CulturalModerationQueue = {\n        id: queueId,\n        contentId: content.id,\n        submittedBy: content.author.userId,\n        culturalContext: content.culture,\n        moderationReason: reason,\n        priority,\n        culturalRepresentatives: await this.assignCulturalRepresentatives(content.culture),\n        submissionDate: new Date() as any,\n        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) as any, // 7 days\n        status: 'pending',\n      }\n\n      await setDoc(doc(db, 'cultural-moderation-queue', queueId), moderationItem)\n      \n      return queueId\n    } catch (error) {\n      console.error('Error submitting to moderation queue:', error)\n      throw new Error('Failed to submit to moderation queue')\n    }\n  }\n\n  // Assign cultural representatives for moderation\n  private async assignCulturalRepresentatives(culturalIdentity: string): Promise<string[]> {\n    try {\n      const representatives = await this.getCulturalRepresentatives(culturalIdentity)\n      \n      // Sort by endorsements and select top 2-3 representatives\n      const sortedReps = representatives\n        .sort((a, b) => b.endorsements - a.endorsements)\n        .slice(0, 3)\n      \n      return sortedReps.map(rep => rep.userId)\n    } catch (error) {\n      console.error('Error assigning cultural representatives:', error)\n      return []\n    }\n  }\n\n  // Notify cultural representatives of new validation requests\n  private async notifyCulturalRepresentatives(\n    culturalIdentity: string,\n    requestId: string\n  ): Promise<void> {\n    try {\n      const representatives = await this.getCulturalRepresentatives(culturalIdentity)\n      \n      // In a real implementation, you would send notifications\n      // For now, we'll just log the notification\n      console.log(`Notifying ${representatives.length} representatives for ${culturalIdentity} about request ${requestId}`)\n      \n      // You could implement email notifications, in-app notifications, etc.\n    } catch (error) {\n      console.error('Error notifying cultural representatives:', error)\n    }\n  }\n\n  // Get validation status for a user's cultural identity\n  async getValidationStatus(userId: string, culturalIdentity: string): Promise<{\n    status: 'not_requested' | 'pending' | 'verified' | 'rejected'\n    requestId?: string\n    verificationDate?: Date\n    verifiedBy?: string[]\n  }> {\n    try {\n      const q = query(\n        collection(db, 'cultural-validation-requests'),\n        where('userId', '==', userId),\n        where('culturalIdentity', '==', culturalIdentity)\n      )\n      \n      const querySnapshot = await getDocs(q)\n      \n      if (querySnapshot.empty) {\n        return { status: 'not_requested' }\n      }\n      \n      // Get the most recent request\n      const requests = querySnapshot.docs.map(doc => doc.data())\n      const latestRequest = requests.sort((a, b) => \n        b.submissionDate.toDate().getTime() - a.submissionDate.toDate().getTime()\n      )[0]\n      \n      return {\n        status: latestRequest.status,\n        requestId: latestRequest.id,\n        verificationDate: latestRequest.verificationDate?.toDate(),\n        verifiedBy: latestRequest.verifiedBy,\n      }\n    } catch (error) {\n      console.error('Error getting validation status:', error)\n      return { status: 'not_requested' }\n    }\n  }\n\n  // Calculate cultural credibility score\n  calculateCredibilityScore(user: {\n    culturalIdentities: string[]\n    verifiedIdentities: string[]\n    communityEndorsements: number\n    contentContributions: number\n    culturalKnowledgeScore: number\n  }): number {\n    let score = 0\n    \n    // Base score for cultural identities\n    score += user.culturalIdentities.length * 10\n    \n    // Bonus for verified identities\n    score += user.verifiedIdentities.length * 20\n    \n    // Community endorsements\n    score += Math.min(user.communityEndorsements * 5, 50)\n    \n    // Content contributions\n    score += Math.min(user.contentContributions * 2, 30)\n    \n    // Cultural knowledge score\n    score += user.culturalKnowledgeScore\n    \n    return Math.min(score, 100)\n  }\n}\n\nexport const culturalValidationService = new CulturalValidationService()\n"], "names": ["CulturalValidationService", "request", "requestId", "setDoc", "doc", "db", "error", "culturalIdentity", "q", "query", "collection", "where", "getDocs", "content", "concerns", "recommendations", "requires<PERSON><PERSON>iew", "sensitiveKeywords", "contentText", "hasSensitiveContent", "keyword", "indicator", "respectfulLanguage", "text", "issues", "suggestions", "term", "suggestion", "reason", "priority", "queueId", "moderationItem", "a", "b", "rep", "representatives", "userId", "querySnapshot", "latestRequest", "user", "score", "culturalValidationService"], "mappings": "iHAuBA,MAAMA,CAA0B,CAE9B,MAAM,wBAAwBC,EAAqD,CAC7E,GAAA,CACF,MAAMC,EAAY,cAAc,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAErF,aAAMC,EAAOC,EAAIC,EAAI,+BAAgCH,CAAS,EAAG,CAC/D,GAAGD,EACH,GAAIC,EACJ,OAAQ,UACR,wBAAyB,CAAC,EAC1B,YAAa,CAAC,EACd,eAAgBD,EAAQ,cAAA,CACzB,EAGD,MAAM,KAAK,8BAA8BA,EAAQ,iBAAkBC,CAAS,EAErEA,QACAI,EAAO,CACN,cAAA,MAAM,uCAAwCA,CAAK,EACrD,IAAI,MAAM,qCAAqC,CAAA,CACvD,CAIF,MAAM,2BAA2BC,EAA6D,CACxF,GAAA,CACF,MAAMC,EAAIC,EACRC,EAAWL,EAAI,0BAA0B,EACzCM,EAAM,mBAAoB,KAAMJ,CAAgB,EAChDI,EAAM,eAAgB,KAAM,EAAI,CAClC,EAGA,OADsB,MAAMC,EAAQJ,CAAC,GAChB,KAAK,IAAIJ,GAAOA,EAAI,MAAgC,QAClEE,EAAO,CACN,eAAA,MAAM,2CAA4CA,CAAK,EACxD,CAAC,CAAA,CACV,CAIF,MAAM,wBAAwBO,EAK3B,CACD,MAAMC,EAAqB,CAAC,EACtBC,EAA4B,CAAC,EACnC,IAAIC,EAAiB,GAGrB,MAAMC,EAAoB,CACxB,SAAU,SAAU,WAAY,uBAAwB,YACxD,YAAa,aAAc,SAAU,kBACvC,EAEMC,EAAc,GAAGL,EAAQ,KAAK,IAAIA,EAAQ,OAAO,GAAG,YAAY,EAChEM,EAAsBF,EAAkB,KAAKG,GACjDF,EAAY,SAASE,CAAO,CAC9B,EAEID,IACeH,EAAA,GACjBF,EAAS,KAAK,4EAA4E,EAC1FC,EAAgB,KAAK,2DAA2D,GAIlD,CAC9B,UAAW,WAAY,SAAU,YAAa,gBAChD,EAEiD,KAAKM,GACpDH,EAAY,SAASG,CAAS,CAChC,IAGEP,EAAS,KAAK,qDAAqD,EACnEC,EAAgB,KAAK,iEAAiE,EACrEC,EAAA,IAIfH,EAAQ,OAAO,sBAAwB,mBAErCM,GACFJ,EAAgB,KAAK,4DAA4D,EAE1EF,EAAQ,OAAO,sBAAwB,mBAE5CM,GACFJ,EAAgB,KAAK,sEAAsE,EAKzF,MAAAO,EAAqB,KAAK,8BAA8BJ,CAAW,EACrE,OAACI,EAAmB,eACbR,EAAA,KAAK,GAAGQ,EAAmB,MAAM,EAC1BP,EAAA,KAAK,GAAGO,EAAmB,WAAW,EACrCN,EAAA,IAGZ,CACL,QAASF,EAAS,SAAW,EAC7B,SAAAA,EACA,gBAAAC,EACA,eAAAC,CACF,CAAA,CAIM,8BAA8BO,EAIpC,CACA,MAAMC,EAAmB,CAAC,EACpBC,EAAwB,CAAC,EAW/B,MARyB,CACvB,CAAE,KAAM,YAAa,WAAY,aAAc,EAC/C,CAAE,KAAM,SAAU,WAAY,SAAU,EACxC,CAAE,KAAM,SAAU,WAAY,QAAS,EACvC,CAAE,KAAM,QAAS,WAAY,WAAY,EACzC,CAAE,KAAM,UAAW,WAAY,YAAa,CAC9C,EAEiB,QAAQ,CAAC,CAAE,KAAAC,EAAM,WAAAC,KAAiB,CAC7CJ,EAAK,SAASG,CAAI,IACbF,EAAA,KAAK,uCAAuCE,CAAI,GAAG,EAC1DD,EAAY,KAAK,mBAAmBE,CAAU,iBAAiBD,CAAI,GAAG,EACxE,CACD,GAUGH,EAAK,SAAS,MAAM,GAAKA,EAAK,SAAS,UAAU,GAAKA,EAAK,SAAS,SAAS,KAC/EC,EAAO,KAAK,8CAA8C,EAC1DC,EAAY,KAAK,mDAAmD,GAG/D,CACL,aAAcD,EAAO,SAAW,EAChC,OAAAA,EACA,YAAAC,CACF,CAAA,CAIF,MAAM,wBACJZ,EACAe,EACAC,EAAiD,SAChC,CACb,GAAA,CACF,MAAMC,EAAU,cAAc,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAE7EC,EAA0C,CAC9C,GAAID,EACJ,UAAWjB,EAAQ,GACnB,YAAaA,EAAQ,OAAO,OAC5B,gBAAiBA,EAAQ,QACzB,iBAAkBe,EAClB,SAAAC,EACA,wBAAyB,MAAM,KAAK,8BAA8BhB,EAAQ,OAAO,EACjF,mBAAoB,KACpB,SAAU,IAAI,KAAK,KAAK,IAAA,EAAQ,EAAI,GAAK,GAAK,GAAK,GAAI,EACvD,OAAQ,SACV,EAEA,aAAMV,EAAOC,EAAIC,EAAI,4BAA6ByB,CAAO,EAAGC,CAAc,EAEnED,QACAxB,EAAO,CACN,cAAA,MAAM,wCAAyCA,CAAK,EACtD,IAAI,MAAM,sCAAsC,CAAA,CACxD,CAIF,MAAc,8BAA8BC,EAA6C,CACnF,GAAA,CAQF,OAPwB,MAAM,KAAK,2BAA2BA,CAAgB,GAI3E,KAAK,CAACyB,EAAGC,IAAMA,EAAE,aAAeD,EAAE,YAAY,EAC9C,MAAM,EAAG,CAAC,EAEK,IAAWE,GAAAA,EAAI,MAAM,QAChC5B,EAAO,CACN,eAAA,MAAM,4CAA6CA,CAAK,EACzD,CAAC,CAAA,CACV,CAIF,MAAc,8BACZC,EACAL,EACe,CACX,GAAA,CACF,MAAMiC,EAAkB,MAAM,KAAK,2BAA2B5B,CAAgB,EAItE,QAAA,IAAI,aAAa4B,EAAgB,MAAM,wBAAwB5B,CAAgB,kBAAkBL,CAAS,EAAE,QAG7GI,EAAO,CACN,QAAA,MAAM,4CAA6CA,CAAK,CAAA,CAClE,CAIF,MAAM,oBAAoB8B,EAAgB7B,EAKvC,CACG,GAAA,CACF,MAAMC,EAAIC,EACRC,EAAWL,EAAI,8BAA8B,EAC7CM,EAAM,SAAU,KAAMyB,CAAM,EAC5BzB,EAAM,mBAAoB,KAAMJ,CAAgB,CAClD,EAEM8B,EAAgB,MAAMzB,EAAQJ,CAAC,EAErC,GAAI6B,EAAc,MACT,MAAA,CAAE,OAAQ,eAAgB,EAKnC,MAAMC,EADWD,EAAc,KAAK,IAAIjC,GAAOA,EAAI,MAAM,EAC1B,KAAK,CAAC4B,EAAGC,IACtCA,EAAE,eAAe,OAAA,EAAS,QAAA,EAAYD,EAAE,eAAe,SAAS,QAAQ,GACxE,CAAC,EAEI,MAAA,CACL,OAAQM,EAAc,OACtB,UAAWA,EAAc,GACzB,iBAAkBA,EAAc,kBAAkB,OAAO,EACzD,WAAYA,EAAc,UAC5B,QACOhC,EAAO,CACN,eAAA,MAAM,mCAAoCA,CAAK,EAChD,CAAE,OAAQ,eAAgB,CAAA,CACnC,CAIF,0BAA0BiC,EAMf,CACT,IAAIC,EAAQ,EAGH,OAAAA,GAAAD,EAAK,mBAAmB,OAAS,GAGjCC,GAAAD,EAAK,mBAAmB,OAAS,GAG1CC,GAAS,KAAK,IAAID,EAAK,sBAAwB,EAAG,EAAE,EAGpDC,GAAS,KAAK,IAAID,EAAK,qBAAuB,EAAG,EAAE,EAGnDC,GAASD,EAAK,uBAEP,KAAK,IAAIC,EAAO,GAAG,CAAA,CAE9B,CAEa,MAAAC,EAA4B,IAAIzC"}