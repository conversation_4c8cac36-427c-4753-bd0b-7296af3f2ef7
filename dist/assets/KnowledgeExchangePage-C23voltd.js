import{i as u,a as H,j as e}from"./index-nwrMOwxu.js";import{r as p}from"./vendor-DtOhX2xw.js";import{T as g,p as L,q as y,B as T,J as w,K as f,M as v,O as I,P as C,N as E,y as P,Q}from"./firebase-DLuFXYhP.js";import{c as Z}from"./culturalValidationService-CA9WZNCm.js";class ee{async createSkillProfile(t){try{const s=`skill-profile-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,r=await Z.validateCulturalContent({id:s,title:`Skill Profile for ${t.userId}`,content:JSON.stringify(t.culturalTeachingStyle),type:"skill_profile",culture:t.culturalTeachingStyle.primaryCulture,author:{userId:t.userId,culturalCredibility:"community_member"},verification:{status:"pending",reviewedBy:[],reviewNotes:[],approvedAt:null},engagement:{views:0,likes:0,shares:0,comments:0,crossCulturalViews:0},createdAt:g.now(),lastModified:g.now()}),a={...t,id:s,createdAt:g.now(),updatedAt:g.now()};return await L(y(u,"skill-profiles",s),a),s}catch(s){throw console.error("Error creating skill profile:",s),new Error("Failed to create skill profile")}}async updateSkillProfile(t,s){try{const r=y(u,"skill-profiles",t);await T(r,{...s,updatedAt:g.now()})}catch(r){throw console.error("Error updating skill profile:",r),new Error("Failed to update skill profile")}}async getSkillProfileByUserId(t){try{const s=w(f(u,"skill-profiles"),v("userId","==",t),I(1)),r=await C(s);return r.empty?null:r.docs[0].data()}catch(s){throw console.error("Error getting skill profile:",s),new Error("Failed to get skill profile")}}async searchMentors(t,s,r,a=20){try{let l=w(f(u,"skill-profiles"),v("skillsToTeach","array-contains-any",[t]),E("reputation.overallRating","desc"),I(a)),c=(await C(l)).docs.map(m=>m.data());return s&&s.length>0&&(c=c.filter(m=>s.some(x=>m.culturalTeachingStyle.primaryCulture===x||m.culturalTeachingStyle.culturalApproaches.some(b=>b.culturalOrigin===x)))),c}catch(l){throw console.error("Error searching mentors:",l),new Error("Failed to search mentors")}}async createMentorshipMatch(t){try{const s=`match-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,r={...t,id:s,createdAt:g.now()};return await L(y(u,"mentorship-matches",s),r),s}catch(s){throw console.error("Error creating mentorship match:",s),new Error("Failed to create mentorship match")}}async scheduleLearningSession(t){try{const s=`session-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,r={...t,id:s};return await L(y(u,"learning-sessions",s),r),s}catch(s){throw console.error("Error scheduling learning session:",s),new Error("Failed to schedule learning session")}}async completeLearningSession(t,s,r,a){try{const l=y(u,"learning-sessions",t),d=await P(l);if(!d.exists())throw new Error("Session not found");const m=d.data().duration/60*10,x=s.overallRating/5,b=a.length>0?1.2:1,j=Math.round(m*x*b);await T(l,{feedback:s,skillProgress:r,culturalInsights:a,timeCreditsAwarded:j,status:"completed"})}catch(l){throw console.error("Error completing learning session:",l),new Error("Failed to complete learning session")}}async getUserMentorshipMatches(t,s){try{const r=s==="mentor"?"mentorId":"menteeId",a=w(f(u,"mentorship-matches"),v(r,"==",t),E("createdAt","desc"));return(await C(a)).docs.map(d=>d.data())}catch(r){throw console.error("Error getting user mentorship matches:",r),new Error("Failed to get user mentorship matches")}}async getLearningSessionsForMatch(t){try{const s=w(f(u,"learning-sessions"),v("mentorshipMatchId","==",t),E("scheduledAt","desc"));return(await C(s)).docs.map(a=>a.data())}catch(s){throw console.error("Error getting learning sessions:",s),new Error("Failed to get learning sessions")}}async updateMentorshipMatchStatus(t,s,r){try{const a=y(u,"mentorship-matches",t);await T(a,{status:s,...r&&{statusNotes:r},updatedAt:g.now()})}catch(a){throw console.error("Error updating mentorship match status:",a),new Error("Failed to update mentorship match status")}}}const K=new ee;class se{async createTimeBankAccount(t){try{const s=`timebank-${t}`,r={id:s,userId:t,balance:100,totalEarned:100,totalSpent:0,totalReserved:0,qualityMultiplier:1,culturalBonusRate:.1,reputationScore:50,accountStatus:"active",createdAt:g.now(),lastActivity:g.now()};return await L(y(u,"timebank-accounts",s),r),await this.recordTransaction({type:"bonus",toUserId:t,amount:100,baseAmount:100,multipliers:[],description:"Welcome bonus for joining Ubuntu Connect",category:{primary:"other",secondary:"welcome_bonus"},status:"completed",timestamp:g.now(),metadata:{skillLevel:"beginner",culturalElements:["ubuntu_philosophy"],learningOutcomes:["Platform onboarding"]}}),s}catch(s){throw console.error("Error creating time bank account:",s),new Error("Failed to create time bank account")}}async calculateCredits(t,s){try{const r=t.duration/60*10,a=await this.getTimeBankAccount(s),l=a?.qualityMultiplier||1;let d=0;t.culturalContext?.crossCulturalExchange&&(d=r*(a?.culturalBonusRate||.1));const m=r*({beginner:1,intermediate:1.2,advanced:1.5,expert:2,master:2.5}[t.skillLevel]-1),x=1;let b=0;t.culturalContext?.communityBenefit&&(b=r*.15);let j=0;t.participantCount&&t.participantCount>1&&(j=r*.1*(t.participantCount-1));const S=Math.round(r*l*x+d+m+b+j),B=[{component:"Base Credits",value:r,explanation:`${t.duration} minutes at 10 credits/hour`},{component:"Quality Multiplier",value:l,explanation:`Based on provider reputation (${a?.reputationScore||50}/100)`},{component:"Cultural Bonus",value:d,explanation:"Cross-cultural exchange bonus"},{component:"Skill Complexity",value:m,explanation:`${t.skillLevel} level bonus`},{component:"Community Contribution",value:b,explanation:"Community benefit bonus"},{component:"Group Session",value:j,explanation:`${t.participantCount||1} participants`}];return{baseCredits:r,qualityMultiplier:l,culturalBonus:d,skillComplexityBonus:m,demandMultiplier:x,communityContributionBonus:b,finalCredits:S,breakdown:B}}catch(r){throw console.error("Error calculating credits:",r),new Error("Failed to calculate credits")}}async recordTransaction(t){try{const s=`tx-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,r={...t,id:s};return await Q(u,async a=>{const l=y(u,"time-transactions",s);a.set(l,r),t.type==="earn"||t.type==="bonus"?await this.updateAccountBalance(t.toUserId,t.amount,"add",a):t.type==="spend"?(await this.updateAccountBalance(t.toUserId,-t.amount,"subtract",a),t.fromUserId&&await this.updateAccountBalance(t.fromUserId,t.amount,"add",a)):t.type==="reserve"?await this.updateAccountReserved(t.toUserId,t.amount,"add",a):t.type==="release"&&await this.updateAccountReserved(t.toUserId,-t.amount,"subtract",a)}),s}catch(s){throw console.error("Error recording transaction:",s),new Error("Failed to record transaction")}}async getTimeBankAccount(t){try{const s=y(u,"timebank-accounts",`timebank-${t}`),r=await P(s);return r.exists()?r.data():null}catch(s){throw console.error("Error getting time bank account:",s),new Error("Failed to get time bank account")}}async getUserTransactionHistory(t,s=50){try{const r=w(f(u,"time-transactions"),v("toUserId","==",t),E("timestamp","desc"),I(s));return(await C(r)).docs.map(l=>l.data())}catch(r){throw console.error("Error getting user transaction history:",r),new Error("Failed to get user transaction history")}}async processExchangePayment(t,s,r,a,l){try{const d=await this.getTimeBankAccount(s);if(!d||d.balance<a)throw new Error("Insufficient credits");const c=await this.calculateCredits(l,r);return await this.recordTransaction({type:"spend",fromUserId:r,toUserId:s,amount:c.finalCredits,baseAmount:c.baseCredits,multipliers:[{type:"quality",value:c.qualityMultiplier,reason:"Provider quality rating",appliedBy:"system"},{type:"cultural",value:c.culturalBonus,reason:"Cross-cultural exchange",appliedBy:"system"},{type:"complexity",value:c.skillComplexityBonus,reason:"Skill complexity",appliedBy:"system"}],description:`Payment for knowledge exchange: ${t}`,category:{primary:"skill_teaching",culturalCategory:l.culturalContext?.primaryCulture},relatedExchangeId:t,culturalContext:l.culturalContext,status:"completed",timestamp:g.now(),metadata:{sessionDuration:l.duration,participantCount:l.participantCount||1,skillLevel:l.skillLevel,culturalElements:l.culturalContext?.culturalElements||[],learningOutcomes:l.learningOutcomes||[]}})}catch(d){throw console.error("Error processing exchange payment:",d),new Error("Failed to process exchange payment")}}async createDisputeCase(t){try{const s=`dispute-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,r={...t,id:s,createdAt:g.now()};return await L(y(u,"dispute-cases",s),r),t.transactionId&&await this.freezeTransaction(t.transactionId),s}catch(s){throw console.error("Error creating dispute case:",s),new Error("Failed to create dispute case")}}async resolveDisputeCase(t,s,r){try{const a=y(u,"dispute-cases",t);if(await T(a,{status:"resolved",resolution:s,mediatorId:r,resolvedAt:g.now()}),s.creditAdjustment!==0){const l=await P(a);if(l.exists()){const d=l.data();await this.recordTransaction({type:s.creditAdjustment>0?"bonus":"penalty",toUserId:d.disputantUserId,amount:Math.abs(s.creditAdjustment),baseAmount:Math.abs(s.creditAdjustment),multipliers:[],description:`Dispute resolution adjustment: ${s.explanation}`,category:{primary:"other",secondary:"dispute_resolution"},relatedExchangeId:d.transactionId,status:"completed",timestamp:g.now(),metadata:{skillLevel:"n/a",culturalElements:[],learningOutcomes:["Dispute resolved"]}})}}}catch(a){throw console.error("Error resolving dispute case:",a),new Error("Failed to resolve dispute case")}}async calculateFairExchangeMetrics(t){try{const s=await this.getUserTransactionHistory(t,100),r=this.calculateExchangeEquity(s),a=this.calculateCulturalContribution(s),l=this.calculateCommunityImpact(s),d=this.calculateCrossCulturalBridging(s),c=this.calculateKnowledgeSharing(s),m=this.calculateLearningEngagement(s),x=Math.round((r+a+l+d+c+m)/6),b={userId:t,exchangeEquity:r,culturalContribution:a,communityImpact:l,crossCulturalBridging:d,knowledgeSharing:c,learningEngagement:m,overallFairnessScore:x,lastCalculated:g.now()};return await L(y(u,"fair-exchange-metrics",t),b),b}catch(s){throw console.error("Error calculating fair exchange metrics:",s),new Error("Failed to calculate fair exchange metrics")}}async updateAccountBalance(t,s,r,a){const l=y(u,"timebank-accounts",`timebank-${t}`);if(a){const d=await a.get(l);if(d.exists()){const c=d.data().balance,m=r==="add"?c+s:c-s;a.update(l,{balance:Math.max(0,m),lastActivity:g.now(),...r==="add"&&{totalEarned:d.data().totalEarned+s},...r==="subtract"&&{totalSpent:d.data().totalSpent+s}})}}else{const d=await P(l);if(d.exists()){const c=d.data().balance,m=r==="add"?c+s:c-s;await T(l,{balance:Math.max(0,m),lastActivity:g.now(),...r==="add"&&{totalEarned:d.data().totalEarned+s},...r==="subtract"&&{totalSpent:d.data().totalSpent+s}})}}}async updateAccountReserved(t,s,r,a){const l=y(u,"timebank-accounts",`timebank-${t}`);if(a){const d=await a.get(l);if(d.exists()){const c=d.data().totalReserved,m=r==="add"?c+s:c-s;a.update(l,{totalReserved:Math.max(0,m),lastActivity:g.now()})}}}async freezeTransaction(t){const s=y(u,"time-transactions",t);await T(s,{status:"disputed"})}calculateExchangeEquity(t){const s=t.filter(c=>c.type==="earn"),r=t.filter(c=>c.type==="spend");if(s.length===0&&r.length===0)return 50;const a=s.reduce((c,m)=>c+m.amount,0),l=r.reduce((c,m)=>c+m.amount,0),d=a>0?l/a:0;return Math.min(100,Math.max(0,100-Math.abs(d-1)*100))}calculateCulturalContribution(t){const s=t.filter(r=>r.culturalContext?.crossCulturalExchange||r.culturalContext?.traditionalKnowledgeInvolved);return Math.min(100,s.length/Math.max(t.length,1)*100)}calculateCommunityImpact(t){const s=t.filter(r=>r.culturalContext?.communityBenefit||r.category.secondary==="community_service");return Math.min(100,s.length/Math.max(t.length,1)*100)}calculateCrossCulturalBridging(t){const s=t.filter(r=>r.culturalContext?.crossCulturalExchange);return Math.min(100,s.length/Math.max(t.length,1)*100)}calculateKnowledgeSharing(t){const s=t.filter(r=>r.type==="earn"&&r.category.primary==="skill_teaching");return Math.min(100,s.length/Math.max(t.length,1)*100)}calculateLearningEngagement(t){const s=t.filter(r=>r.type==="spend"&&r.category.primary==="skill_teaching");return Math.min(100,s.length/Math.max(t.length,1)*100)}}const _=new se,te=({onNavigateToMarketplace:k,onNavigateToTimeBank:t})=>{const{user:s}=H(),[r,a]=p.useState("overview"),[l,d]=p.useState(null),[c,m]=p.useState([]),[x,b]=p.useState([]),[j,S]=p.useState(null),[B,M]=p.useState(!0);p.useEffect(()=>{s&&R()},[s]);const R=async()=>{if(s){M(!0);try{const[n,h,A,O]=await Promise.all([K.getSkillProfileByUserId(s.uid),K.getUserMentorshipMatches(s.uid,"mentor"),K.getUserMentorshipMatches(s.uid,"mentee"),_.getTimeBankAccount(s.uid)]);if(d(n),m([...h,...A]),S(O),h.length>0||A.length>0){const Y=[...h,...A].slice(0,3).map(q=>K.getLearningSessionsForMatch(q.id)),V=(await Promise.all(Y)).flat().sort((q,z)=>z.scheduledAt.seconds-q.scheduledAt.seconds);b(V.slice(0,10))}}catch(n){console.error("Error loading dashboard data:",n)}finally{M(!1)}}},$=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-2xl mr-3",children:"🎓"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:l?.skillsToTeach.length||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Skills Teaching"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-2xl mr-3",children:"📚"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:l?.skillsToLearn.length||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Skills Learning"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-2xl mr-3",children:"🤝"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:c.filter(n=>n.status==="active").length}),e.jsx("div",{className:"text-sm text-gray-600",children:"Active Mentorships"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-2xl mr-3",children:"⏰"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:j?.balance||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Time Credits"})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Find a Mentor"}),e.jsx("p",{className:"text-blue-100 mb-4",children:"Connect with experienced mentors to accelerate your learning journey."}),e.jsx("button",{className:"bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors",children:"Browse Mentors"})]}),e.jsxs("div",{className:"bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Become a Mentor"}),e.jsx("p",{className:"text-green-100 mb-4",children:"Share your expertise and help others while earning time credits."}),e.jsx("button",{className:"bg-white text-green-600 px-4 py-2 rounded-lg hover:bg-green-50 transition-colors",children:"Start Mentoring"})]}),e.jsxs("div",{className:"bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Explore Marketplace"}),e.jsx("p",{className:"text-purple-100 mb-4",children:"Discover knowledge exchange opportunities in the marketplace."}),e.jsx("button",{onClick:k,className:"bg-white text-purple-600 px-4 py-2 rounded-lg hover:bg-purple-50 transition-colors",children:"Visit Marketplace"})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Learning Sessions"}),e.jsxs("div",{className:"space-y-4",children:[x.slice(0,5).map(n=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-gray-900",children:[n.sessionType.charAt(0).toUpperCase()+n.sessionType.slice(1)," Session"]}),e.jsxs("p",{className:"text-sm text-gray-600",children:[n.duration," minutes • ",n.location]}),e.jsx("div",{className:"flex items-center mt-1",children:n.culturalElements.map(h=>e.jsx("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded mr-2",children:h.culture},h.culture))})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:`text-sm font-medium ${n.status==="completed"?"text-green-600":n.status==="scheduled"?"text-blue-600":n.status==="in_progress"?"text-yellow-600":"text-gray-600"}`,children:n.status.replace("_"," ").toUpperCase()}),e.jsx("div",{className:"text-xs text-gray-500",children:new Date(n.scheduledAt.seconds*1e3).toLocaleDateString()})]})]},n.id)),x.length===0&&e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-2",children:"📅"}),e.jsx("p",{children:"No learning sessions yet"}),e.jsx("p",{className:"text-sm",children:"Start a mentorship to schedule your first session!"})]})]})]})]}),o=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Mentoring Activities"}),e.jsx("button",{className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"Update Teaching Profile"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Skills You Teach"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:l?.skillsToTeach.map(n=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h5",{className:"font-medium text-gray-900",children:n.skillName}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${n.proficiencyLevel==="expert"||n.proficiencyLevel==="master"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:n.proficiencyLevel})]}),e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[n.yearsOfExperience," years experience"]}),n.culturalContext&&n.culturalContext.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1",children:n.culturalContext.map(h=>e.jsx("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded",children:h.culture},h.culture))}),e.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm text-gray-600",children:[n.timeCreditsPerHour," credits/hour"]}),e.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-800",children:"Edit"})]})]},n.skillId))||e.jsxs("div",{className:"col-span-2 text-center py-8 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-2",children:"🎓"}),e.jsx("p",{children:"No teaching skills added yet"}),e.jsx("button",{className:"mt-2 text-blue-600 hover:text-blue-800",children:"Add your first teaching skill"})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Your Mentees"}),e.jsx("div",{className:"space-y-4",children:c.filter(n=>n.mentorId===s?.uid&&n.status==="active").map(n=>e.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h5",{className:"font-medium text-gray-900",children:[n.skillId," Mentorship"]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Progress: ",n.actualProgress.skillProgress,"% •",n.actualProgress.sessionsCompleted," sessions completed"]}),n.culturalContext&&e.jsxs("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded mt-1 inline-block",children:[n.culturalContext," Cultural Context"]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600",children:"Schedule Session"}),e.jsx("button",{className:"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50",children:"View Progress"})]})]})},n.id))})]})]}),D=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Learning Journey"}),e.jsx("button",{className:"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors",children:"Add Learning Goal"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Learning Goals"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:l?.skillsToLearn.map(n=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h5",{className:"font-medium text-gray-900",children:n.skillName}),e.jsxs("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded",children:[n.currentLevel," → ",n.targetLevel]})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:n.motivation}),e.jsxs("div",{className:"text-sm text-gray-600 mb-3",children:["Time commitment: ",n.timeCommitment," hours/week"]}),n.culturalPreferences&&n.culturalPreferences.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1 mb-3",children:n.culturalPreferences.map(h=>e.jsxs("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded",children:[h.culture," (",h.interest,"% interest)"]},h.culture))}),e.jsx("button",{className:"w-full px-3 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600",children:"Find Mentor"})]},n.skillId))||e.jsxs("div",{className:"col-span-2 text-center py-8 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-2",children:"📚"}),e.jsx("p",{children:"No learning goals set yet"}),e.jsx("button",{className:"mt-2 text-green-600 hover:text-green-800",children:"Set your first learning goal"})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Your Mentors"}),e.jsx("div",{className:"space-y-4",children:c.filter(n=>n.menteeId===s?.uid&&n.status==="active").map(n=>e.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h5",{className:"font-medium text-gray-900",children:[n.skillId," Learning"]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Progress: ",n.actualProgress.skillProgress,"% •",n.actualProgress.sessionsCompleted," sessions completed"]}),e.jsx("div",{className:"mt-1",children:e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${n.actualProgress.skillProgress}%`}})})}),n.culturalContext&&e.jsxs("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded mt-2 inline-block",children:[n.culturalContext," Cultural Learning"]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600",children:"Continue Learning"}),e.jsx("button",{className:"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50",children:"View Details"})]})]})},n.id))})]})]}),F=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Learning Sessions"}),e.jsx("button",{className:"px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors",children:"Schedule New Session"})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"space-y-4",children:[x.map(n=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-gray-900",children:[n.sessionType.charAt(0).toUpperCase()+n.sessionType.slice(1)," Session"]}),e.jsx("p",{className:"text-sm text-gray-600",children:new Date(n.scheduledAt.seconds*1e3).toLocaleString()})]}),e.jsx("span",{className:`px-3 py-1 rounded-full text-sm ${n.status==="completed"?"bg-green-100 text-green-800":n.status==="scheduled"?"bg-blue-100 text-blue-800":n.status==="in_progress"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:n.status.replace("_"," ")})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-3",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Duration:"}),e.jsxs("span",{className:"text-sm text-gray-600 ml-1",children:[n.duration," minutes"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Location:"}),e.jsx("span",{className:"text-sm text-gray-600 ml-1",children:n.location})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Credits:"}),e.jsx("span",{className:"text-sm text-gray-600 ml-1",children:n.timeCreditsAwarded||0})]})]}),n.learningObjectives.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Objectives:"}),e.jsx("ul",{className:"text-sm text-gray-600 ml-4 mt-1",children:n.learningObjectives.map((h,A)=>e.jsx("li",{className:"list-disc",children:h},A))})]}),n.culturalElements.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1 mb-3",children:n.culturalElements.map(h=>e.jsxs("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded",children:[h.culture," Culture"]},h.culture))}),n.status==="completed"&&n.feedback&&e.jsx("div",{className:"bg-gray-50 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Session Rating:"}),e.jsx("div",{className:"flex items-center",children:[1,2,3,4,5].map(h=>e.jsx("span",{className:`text-lg ${h<=n.feedback.overallRating?"text-yellow-400":"text-gray-300"}`,children:"⭐"},h))})]})})]},n.id)),x.length===0&&e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-2",children:"📅"}),e.jsx("p",{children:"No sessions scheduled yet"}),e.jsx("p",{className:"text-sm",children:"Start a mentorship to schedule your first session!"})]})]})})]});return B?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}):e.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Skill Sharing & Mentorship"}),e.jsx("p",{className:"text-gray-600",children:'Connect, learn, and share knowledge across cultures with Ubuntu philosophy: "I am because we are."'})]}),e.jsx("div",{className:"border-b border-gray-200 mb-6",children:e.jsx("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview",icon:"📊"},{id:"mentoring",label:"Mentoring",icon:"🎓"},{id:"learning",label:"Learning",icon:"📚"},{id:"sessions",label:"Sessions",icon:"📅"}].map(n=>e.jsxs("button",{onClick:()=>a(n.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${r===n.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx("span",{className:"mr-2",children:n.icon}),n.label]},n.id))})}),r==="overview"&&$(),r==="mentoring"&&o(),r==="learning"&&D(),r==="sessions"&&F()]})};class re{async createMarketplaceListing(t){try{const s=`listing-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;if(t.culturalContext&&t.culturalContext.length>0){const a=await Z.validateCulturalContent({id:s,title:t.title,content:t.description,type:"marketplace_listing",culture:t.culturalContext[0].culture,author:{userId:t.userId,culturalCredibility:"community_member"},verification:{status:"pending",reviewedBy:[],reviewNotes:[],approvedAt:null},engagement:{views:0,likes:0,shares:0,comments:0,crossCulturalViews:0},createdAt:g.now(),lastModified:g.now()});!a.isValid&&a.requiresReview&&await this.notifyCulturalRepresentatives(t.culturalContext.map(l=>l.culture),s)}const r={...t,id:s,views:0,responses:0,createdAt:g.now(),updatedAt:g.now()};return await L(y(u,"marketplace-listings",s),r),s}catch(s){throw console.error("Error creating marketplace listing:",s),new Error("Failed to create marketplace listing")}}async searchMarketplaceListings(t,s=20){try{let r=w(f(u,"marketplace-listings"),v("status","==","active"),E("createdAt","desc"),I(s));t.listingType&&(r=w(r,v("listingType","==",t.listingType)));let l=(await C(r)).docs.map(d=>d.data());if(t.query){const d=t.query.toLowerCase();l=l.filter(c=>c.title.toLowerCase().includes(d)||c.description.toLowerCase().includes(d))}return t.culturalContext&&(l=l.filter(d=>d.culturalContext.some(c=>c.culture===t.culturalContext))),t.priceRange&&(l=l.filter(d=>d.pricing.baseRate>=t.priceRange.min&&d.pricing.baseRate<=t.priceRange.max)),l}catch(r){throw console.error("Error searching marketplace listings:",r),new Error("Failed to search marketplace listings")}}async getPersonalizedRecommendations(t,s=10){try{const r=await this.getUserSkillProfile(t);if(!r)return this.getPopularListings(s);const a=[];for(const c of r.skillsToLearn){const m=await this.searchMarketplaceListings({query:c.skillName,listingType:"skill_offering"},5);a.push(...m)}const l=r.skillsToLearn.flatMap(c=>c.culturalPreferences?.map(m=>m.culture)||[]);for(const c of l){const m=await this.searchMarketplaceListings({culturalContext:c},3);a.push(...m)}return a.filter((c,m,x)=>m===x.findIndex(b=>b.id===c.id)).slice(0,s)}catch(r){throw console.error("Error getting personalized recommendations:",r),new Error("Failed to get personalized recommendations")}}async initiateKnowledgeExchange(t,s,r,a){try{const l=`exchange-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,d=await P(y(u,"marketplace-listings",t));if(!d.exists())throw new Error("Listing not found");const c=d.data(),m={id:l,marketplaceListingId:t,requesterId:s,providerId:c.userId,skillId:c.skillId,exchangeType:"negotiated",agreement:{learningObjectives:a.learningObjectives||[],deliverables:a.deliverables||[],timeline:a.timeline||[],compensationTerms:a.compensationTerms||{type:"time_credits",amount:c.pricing.baseRate,schedule:"per_session"},culturalGuidelines:a.culturalGuidelines||[],cancellationPolicy:a.cancellationPolicy||{noticePeriod:24,refundPolicy:"partial"},intellectualPropertyTerms:a.intellectualPropertyTerms||{ownershipRights:"shared",attributionRequired:!0},disputeResolutionProcess:a.disputeResolutionProcess||{steps:["direct_negotiation","mediation","arbitration"],culturalMediationAvailable:!0}},milestones:[],communications:[{id:`msg-${Date.now()}`,senderId:s,message:r,timestamp:g.now(),type:"proposal"}],payments:[],culturalElements:c.culturalContext.map(x=>({culture:x.culture,significance:x.significance,learningObjectives:[`Understand ${x.culture} cultural context`],respectGuidelines:["Approach with cultural humility","Ask questions respectfully"]})),status:"proposed",startDate:g.now(),expectedEndDate:g.fromDate(new Date(Date.now()+30*24*60*60*1e3)),satisfaction:{requesterRating:0,providerRating:0,culturalRespectRating:0,learningOutcomeRating:0,overallSatisfaction:0}};return await L(y(u,"knowledge-exchanges",l),m),await T(y(u,"marketplace-listings",t),{responses:c.responses+1,updatedAt:g.now()}),l}catch(l){throw console.error("Error initiating knowledge exchange:",l),new Error("Failed to initiate knowledge exchange")}}async updateKnowledgeExchangeStatus(t,s,r){try{const a=y(u,"knowledge-exchanges",t),l={status:s,updatedAt:g.now()};s==="agreed"?l.startDate=g.now():s==="completed"&&(l.actualEndDate=g.now()),r&&(l.statusNotes=r),await T(a,l)}catch(a){throw console.error("Error updating knowledge exchange status:",a),new Error("Failed to update knowledge exchange status")}}async getUserMarketplaceListings(t){try{const s=w(f(u,"marketplace-listings"),v("userId","==",t),E("createdAt","desc"));return(await C(s)).docs.map(a=>a.data())}catch(s){throw console.error("Error getting user marketplace listings:",s),new Error("Failed to get user marketplace listings")}}async getUserKnowledgeExchanges(t,s){try{let r;if(s==="requester")r=w(f(u,"knowledge-exchanges"),v("requesterId","==",t),E("createdAt","desc"));else if(s==="provider")r=w(f(u,"knowledge-exchanges"),v("providerId","==",t),E("createdAt","desc"));else{const l=w(f(u,"knowledge-exchanges"),v("requesterId","==",t),E("createdAt","desc")),d=w(f(u,"knowledge-exchanges"),v("providerId","==",t),E("createdAt","desc")),[c,m]=await Promise.all([C(l),C(d)]),x=c.docs.map(j=>j.data()),b=m.docs.map(j=>j.data());return[...x,...b].sort((j,S)=>S.createdAt.seconds-j.createdAt.seconds)}return(await C(r)).docs.map(l=>l.data())}catch(r){throw console.error("Error getting user knowledge exchanges:",r),new Error("Failed to get user knowledge exchanges")}}async getFeaturedListings(t=10){try{const s=w(f(u,"marketplace-listings"),v("status","==","active"),v("featuredUntil",">",g.now()),E("featuredUntil","desc"),I(t));return(await C(s)).docs.map(a=>a.data())}catch(s){throw console.error("Error getting featured listings:",s),new Error("Failed to get featured listings")}}async getPopularListings(t=10){try{const s=w(f(u,"marketplace-listings"),v("status","==","active"),E("views","desc"),I(t));return(await C(s)).docs.map(a=>a.data())}catch(s){throw console.error("Error getting popular listings:",s),new Error("Failed to get popular listings")}}async incrementListingViews(t){try{const s=y(u,"marketplace-listings",t),r=await P(s);if(r.exists()){const a=r.data().views||0;await T(s,{views:a+1,updatedAt:g.now()})}}catch(s){console.error("Error incrementing listing views:",s)}}async getUserSkillProfile(t){try{const s=w(f(u,"skill-profiles"),v("userId","==",t),I(1)),r=await C(s);return r.empty?null:r.docs[0].data()}catch(s){return console.error("Error getting user skill profile:",s),null}}async notifyCulturalRepresentatives(t,s){console.log(`Notifying cultural representatives for cultures: ${t.join(", ")} about listing: ${s}`)}}const U=new re,ae=({onNavigateToTimeBank:k,onNavigateToSkillSharing:t})=>{const{user:s}=H(),[r,a]=p.useState("browse"),[l,d]=p.useState([]),[c,m]=p.useState([]),[x,b]=p.useState([]),[j,S]=p.useState(null),[B,M]=p.useState(!0),[R,$]=p.useState(""),[o,D]=p.useState(""),[F,n]=p.useState(""),h=["Technology","Business","Arts & Crafts","Languages","Cooking","Music","Traditional Skills","Agriculture","Healthcare","Education"],A=["zulu","xhosa","afrikaans","english","sotho","tswana","tsonga","swati","venda","ndebele","coloured"];p.useEffect(()=>{s&&O()},[s]);const O=async()=>{if(s){M(!0);try{const[i,N,G,X]=await Promise.all([U.searchMarketplaceListings({},20),U.getUserMarketplaceListings(s.uid),U.getUserKnowledgeExchanges(s.uid),_.getTimeBankAccount(s.uid)]);d(i),m(N),b(G),S(X)}catch(i){console.error("Error loading marketplace data:",i)}finally{M(!1)}}},J=async()=>{M(!0);try{const i=await U.searchMarketplaceListings({query:R,category:o,culturalContext:F});d(i)}catch(i){console.error("Error searching listings:",i)}finally{M(!1)}},Y=async i=>{if(s)try{const G=await U.initiateKnowledgeExchange(i,s.uid,"I'm interested in this knowledge exchange opportunity. Let's discuss the details!",{learningObjectives:["Gain practical skills","Cultural understanding"],compensationTerms:{type:"time_credits",amount:0,schedule:"per_session"}});alert(`Exchange request sent! Exchange ID: ${G}`),O()}catch(N){console.error("Error initiating exchange:",N),alert("Failed to initiate exchange. Please try again.")}},W=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx("div",{className:"md:col-span-2",children:e.jsx("input",{type:"text",placeholder:"Search skills, knowledge, or expertise...",value:R,onChange:i=>$(i.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})}),e.jsxs("select",{value:o,onChange:i=>D(i.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Categories"}),h.map(i=>e.jsx("option",{value:i,children:i},i))]}),e.jsxs("select",{value:F,onChange:i=>n(i.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Cultures"}),A.map(i=>e.jsx("option",{value:i,className:"capitalize",children:i},i))]})]}),e.jsx("div",{className:"mt-4",children:e.jsx("button",{onClick:J,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"Search"})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Featured Knowledge Exchanges"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:l.slice(0,6).map(i=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900",children:i.title}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${i.listingType==="skill_offering"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:i.listingType==="skill_offering"?"Offering":"Seeking"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-medium text-orange-600",children:[i.pricing.baseRate," credits/hour"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:[i.views," views"]})]})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:i.description}),i.culturalContext.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1 mb-3",children:i.culturalContext.map(N=>e.jsx("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded",children:N.culture},N.culture))}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:[i.responses," responses"]}),e.jsx("button",{onClick:()=>Y(i.id),className:"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors",children:"Connect"})]})]},i.id))})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:l.length}),e.jsx("div",{className:"text-sm text-gray-600",children:"Active Listings"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:l.filter(i=>i.listingType==="skill_offering").length}),e.jsx("div",{className:"text-sm text-gray-600",children:"Skills Available"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:new Set(l.flatMap(i=>i.culturalContext.map(N=>N.culture))).size}),e.jsx("div",{className:"text-sm text-gray-600",children:"Cultures Represented"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:j?.balance||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Your Credits"})]})]})]}),V=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Marketplace Listings"}),e.jsx("button",{onClick:()=>a("create"),className:"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors",children:"Create New Listing"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[c.map(i=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900",children:i.title}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${i.status==="active"?"bg-green-100 text-green-800":i.status==="paused"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:i.status})]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-sm font-medium text-orange-600",children:[i.pricing.baseRate," credits/hour"]})})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-3",children:i.description}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-gray-500",children:"Views:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900 ml-1",children:i.views})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-gray-500",children:"Responses:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900 ml-1",children:i.responses})]})]}),i.culturalContext.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1 mb-4",children:i.culturalContext.map(N=>e.jsx("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded",children:N.culture},N.culture))}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50",children:"Edit"}),e.jsx("button",{className:"flex-1 px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600",children:"View Responses"})]})]},i.id)),c.length===0&&e.jsxs("div",{className:"col-span-2 text-center py-12 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📝"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No listings yet"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Create your first marketplace listing to start sharing your knowledge or find what you need to learn."}),e.jsx("button",{onClick:()=>a("create"),className:"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors",children:"Create Your First Listing"})]})]})]}),q=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Knowledge Exchanges"}),e.jsxs("div",{className:"space-y-4",children:[x.map(i=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"font-semibold text-gray-900",children:[i.skillId," Exchange"]}),e.jsx("p",{className:"text-sm text-gray-600",children:i.requesterId===s?.uid?"You are learning":"You are teaching"})]}),e.jsx("span",{className:`px-3 py-1 rounded-full text-sm ${i.status==="active"||i.status==="in_progress"?"bg-green-100 text-green-800":i.status==="completed"?"bg-blue-100 text-blue-800":i.status==="proposed"||i.status==="negotiating"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:i.status.replace("_"," ")})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Type:"}),e.jsx("span",{className:"text-sm text-gray-600 ml-1 capitalize",children:i.exchangeType.replace("_"," ")})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Start Date:"}),e.jsx("span",{className:"text-sm text-gray-600 ml-1",children:new Date(i.startDate.seconds*1e3).toLocaleDateString()})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Expected End:"}),e.jsx("span",{className:"text-sm text-gray-600 ml-1",children:new Date(i.expectedEndDate.seconds*1e3).toLocaleDateString()})]})]}),i.culturalElements.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Cultural Elements:"}),e.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:i.culturalElements.map(N=>e.jsx("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded",children:N.culture},N.culture))})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"px-4 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600",children:"View Details"}),e.jsx("button",{className:"px-4 py-2 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50",children:"Messages"}),i.status==="proposed"&&i.providerId===s?.uid&&e.jsx("button",{className:"px-4 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600",children:"Accept"})]})]},i.id)),x.length===0&&e.jsxs("div",{className:"text-center py-12 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-4",children:"🤝"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No exchanges yet"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Start connecting with others to begin your knowledge exchange journey."}),e.jsx("button",{onClick:()=>a("browse"),className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"Browse Marketplace"})]})]})]}),z=()=>e.jsx("div",{className:"max-w-2xl mx-auto",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Create Marketplace Listing"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Listing Type"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("label",{className:"flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50",children:[e.jsx("input",{type:"radio",name:"listingType",value:"skill_offering",className:"mr-3"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Offering Skills"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Share your expertise"})]})]}),e.jsxs("label",{className:"flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50",children:[e.jsx("input",{type:"radio",name:"listingType",value:"skill_request",className:"mr-3"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Seeking Skills"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Find what you need to learn"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),e.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Expert Zulu Language Tutoring with Cultural Context"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description *"}),e.jsx("textarea",{rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe what you're offering or seeking, including cultural context and learning approach..."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),e.jsxs("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"Select category"}),h.map(i=>e.jsx("option",{value:i,children:i},i))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cultural Context"}),e.jsxs("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"Select culture (optional)"}),A.map(i=>e.jsx("option",{value:i,className:"capitalize",children:i},i))]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rate (Time Credits per Hour)"}),e.jsx("input",{type:"number",min:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., 15"})]}),e.jsxs("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-orange-800 mb-2",children:"Cultural Sensitivity Guidelines"}),e.jsxs("ul",{className:"text-sm text-orange-700 space-y-1",children:[e.jsx("li",{children:"• Ensure respectful representation of cultural knowledge"}),e.jsx("li",{children:"• Provide proper cultural context and significance"}),e.jsx("li",{children:"• Respect traditional knowledge and intellectual property"}),e.jsx("li",{children:"• Follow Ubuntu principles of mutual respect and benefit"})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("button",{onClick:()=>a("browse"),className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{className:"flex-1 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600",children:"Create Listing"})]})]})]})});return B?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}):e.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Knowledge Exchange Marketplace"}),e.jsx("p",{className:"text-gray-600",children:"Discover, share, and exchange knowledge across cultures with fair time-based compensation."})]}),e.jsx("div",{className:"border-b border-gray-200 mb-6",children:e.jsx("nav",{className:"flex space-x-8",children:[{id:"browse",label:"Browse",icon:"🔍"},{id:"my_listings",label:"My Listings",icon:"📝"},{id:"exchanges",label:"Exchanges",icon:"🤝"},{id:"create",label:"Create",icon:"➕"}].map(i=>e.jsxs("button",{onClick:()=>a(i.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${r===i.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx("span",{className:"mr-2",children:i.icon}),i.label]},i.id))})}),r==="browse"&&W(),r==="my_listings"&&V(),r==="exchanges"&&q(),r==="create"&&z()]})},le=({onNavigateToMarketplace:k,onNavigateToSkillSharing:t})=>{const{user:s}=H(),[r,a]=p.useState("overview"),[l,d]=p.useState(null),[c,m]=p.useState([]),[x,b]=p.useState(null),[j,S]=p.useState(!0);p.useEffect(()=>{s&&B()},[s]);const B=async()=>{if(s){S(!0);try{const[o,D,F]=await Promise.all([_.getTimeBankAccount(s.uid),_.getUserTransactionHistory(s.uid,50),_.calculateFairExchangeMetrics(s.uid)]);d(o),m(D),b(F)}catch(o){console.error("Error loading time banking data:",o)}finally{S(!1)}}},M=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Time Credit Balance"}),e.jsx("div",{className:"text-3xl font-bold",children:l?.balance||0}),e.jsx("p",{className:"text-orange-100 mt-1",children:"Credits available for exchanges"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-orange-100 text-sm",children:"Quality Multiplier"}),e.jsxs("div",{className:"text-xl font-semibold",children:[l?.qualityMultiplier.toFixed(1)||"1.0","x"]}),e.jsx("div",{className:"text-orange-100 text-sm",children:"Cultural Bonus"}),e.jsxs("div",{className:"text-lg font-medium",children:[((l?.culturalBonusRate||0)*100).toFixed(0),"%"]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-2xl mr-3",children:"💰"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:l?.totalEarned||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Earned"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-2xl mr-3",children:"💸"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:l?.totalSpent||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Spent"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-2xl mr-3",children:"⭐"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:l?.reputationScore||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Reputation Score"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-2xl mr-3",children:"🔒"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:l?.totalReserved||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Reserved Credits"})]})]})})]}),x&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Fair Exchange Metrics"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-green-600 mb-1",children:[x.overallFairnessScore,"/100"]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Overall Fairness"}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:e.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${x.overallFairnessScore}%`}})})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-orange-600 mb-1",children:[x.culturalContribution,"/100"]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Cultural Contribution"}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:e.jsx("div",{className:"bg-orange-500 h-2 rounded-full",style:{width:`${x.culturalContribution}%`}})})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:[x.crossCulturalBridging,"/100"]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Cross-Cultural Bridging"}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:e.jsx("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${x.crossCulturalBridging}%`}})})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Transactions"}),e.jsxs("div",{className:"space-y-3",children:[c.slice(0,5).map(o=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`text-2xl mr-3 ${o.type==="earn"||o.type==="bonus"?"text-green-600":o.type==="spend"?"text-blue-600":"text-gray-600"}`,children:o.type==="earn"||o.type==="bonus"?"💰":o.type==="spend"?"💸":"⏰"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:o.description}),e.jsx("div",{className:"text-sm text-gray-600",children:new Date(o.timestamp.seconds*1e3).toLocaleDateString()}),o.culturalContext&&e.jsx("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded mt-1 inline-block",children:o.culturalContext.primaryCulture})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`font-semibold ${o.type==="earn"||o.type==="bonus"?"text-green-600":o.type==="spend"?"text-red-600":"text-gray-600"}`,children:[o.type==="earn"||o.type==="bonus"?"+":o.type==="spend"?"-":"",o.amount]}),e.jsx("div",{className:"text-xs text-gray-500",children:o.status})]})]},o.id)),c.length===0&&e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-2",children:"📊"}),e.jsx("p",{children:"No transactions yet"}),e.jsx("p",{className:"text-sm",children:"Start participating in knowledge exchanges to see your transaction history!"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-3xl mb-3",children:"🎓"}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Earn Credits"}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Share your skills and knowledge to earn time credits"}),e.jsx("button",{onClick:t,className:"w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors",children:"Start Teaching"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-3xl mb-3",children:"📚"}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Spend Credits"}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Use your credits to learn new skills and knowledge"}),e.jsx("button",{onClick:k,className:"w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"Find Learning"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-3xl mb-3",children:"🤝"}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Cultural Exchange"}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Participate in cross-cultural exchanges for bonus credits"}),e.jsx("button",{className:"w-full px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors",children:"Explore Cultures"})]})]})]}),R=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Transaction History"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("select",{className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[e.jsx("option",{value:"",children:"All Types"}),e.jsx("option",{value:"earn",children:"Earned"}),e.jsx("option",{value:"spend",children:"Spent"}),e.jsx("option",{value:"bonus",children:"Bonus"})]}),e.jsxs("select",{className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[e.jsx("option",{value:"",children:"All Time"}),e.jsx("option",{value:"week",children:"This Week"}),e.jsx("option",{value:"month",children:"This Month"}),e.jsx("option",{value:"year",children:"This Year"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Cultural Context"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(o=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(o.timestamp.seconds*1e3).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.jsx("div",{className:"max-w-xs truncate",children:o.description})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${o.type==="earn"||o.type==="bonus"?"bg-green-100 text-green-800":o.type==="spend"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:o.type})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("span",{className:o.type==="earn"||o.type==="bonus"?"text-green-600":o.type==="spend"?"text-red-600":"text-gray-600",children:[o.type==="earn"||o.type==="bonus"?"+":o.type==="spend"?"-":"",o.amount]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${o.status==="completed"?"bg-green-100 text-green-800":o.status==="pending"?"bg-yellow-100 text-yellow-800":o.status==="failed"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:o.status})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:o.culturalContext?e.jsx("span",{className:"bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs",children:o.culturalContext.primaryCulture}):"-"})]},o.id))})]})}),c.length===0&&e.jsxs("div",{className:"text-center py-12 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📊"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No transactions yet"}),e.jsx("p",{className:"text-gray-600",children:"Your transaction history will appear here as you participate in knowledge exchanges."})]})]})]}),$=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Fair Exchange Metrics"}),x?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-4xl font-bold text-green-600 mb-2",children:[x.overallFairnessScore,"/100"]}),e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Overall Fairness Score"}),e.jsx("p",{className:"text-gray-600",children:"Your overall fairness in knowledge exchanges based on Ubuntu principles"}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-4 mt-4",children:e.jsx("div",{className:"bg-green-500 h-4 rounded-full transition-all duration-500",style:{width:`${x.overallFairnessScore}%`}})})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Exchange Patterns"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:"Exchange Equity"}),e.jsxs("span",{children:[x.exchangeEquity,"/100"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${x.exchangeEquity}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:"Knowledge Sharing"}),e.jsxs("span",{children:[x.knowledgeSharing,"/100"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${x.knowledgeSharing}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:"Learning Engagement"}),e.jsxs("span",{children:[x.learningEngagement,"/100"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:`${x.learningEngagement}%`}})})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Cultural Impact"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:"Cultural Contribution"}),e.jsxs("span",{children:[x.culturalContribution,"/100"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-orange-500 h-2 rounded-full",style:{width:`${x.culturalContribution}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:"Cross-Cultural Bridging"}),e.jsxs("span",{children:[x.crossCulturalBridging,"/100"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${x.crossCulturalBridging}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:"Community Impact"}),e.jsxs("span",{children:[x.communityImpact,"/100"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${x.communityImpact}%`}})})]})]})]})]}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[e.jsx("h4",{className:"font-semibold text-blue-800 mb-3",children:"Improve Your Fairness Score"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Increase Cultural Contribution:"}),e.jsxs("ul",{className:"space-y-1",children:[e.jsx("li",{children:"• Participate in cross-cultural exchanges"}),e.jsx("li",{children:"• Share traditional knowledge respectfully"}),e.jsx("li",{children:"• Engage with diverse cultural communities"})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Enhance Exchange Equity:"}),e.jsxs("ul",{className:"space-y-1",children:[e.jsx("li",{children:"• Balance teaching and learning activities"}),e.jsx("li",{children:"• Provide fair value in all exchanges"}),e.jsx("li",{children:"• Complete commitments reliably"})]})]})]})]})]}):e.jsxs("div",{className:"text-center py-12 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📊"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No metrics available yet"}),e.jsx("p",{className:"text-gray-600",children:"Participate in knowledge exchanges to see your fairness metrics."})]})]});return j?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"})}):e.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Time Banking Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Manage your time credits and track fair exchange metrics based on Ubuntu principles."})]}),e.jsx("div",{className:"border-b border-gray-200 mb-6",children:e.jsx("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview",icon:"📊"},{id:"transactions",label:"Transactions",icon:"💰"},{id:"metrics",label:"Fairness Metrics",icon:"⚖️"},{id:"disputes",label:"Disputes",icon:"⚠️"}].map(o=>e.jsxs("button",{onClick:()=>a(o.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${r===o.id?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx("span",{className:"mr-2",children:o.icon}),o.label]},o.id))})}),r==="overview"&&M(),r==="transactions"&&R(),r==="metrics"&&$(),r==="disputes"&&e.jsxs("div",{className:"text-center py-12 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-4",children:"⚠️"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No disputes"}),e.jsx("p",{className:"text-gray-600",children:"Great! You have no active disputes. Keep up the fair exchanges!"})]})]})},oe=()=>{const[k,t]=p.useState("overview"),s=()=>e.jsx("div",{className:"bg-white shadow-sm border-b border-gray-200",children:e.jsx("div",{className:"max-w-6xl mx-auto px-6",children:e.jsx("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview",icon:"🏠",description:"Knowledge exchange hub overview"},{id:"skill_sharing",label:"Skill Sharing",icon:"🎓",description:"Mentorship and skill development"},{id:"marketplace",label:"Marketplace",icon:"🛒",description:"Knowledge exchange marketplace"},{id:"time_banking",label:"Time Banking",icon:"⏰",description:"Fair exchange credit system"}].map(c=>e.jsxs("button",{onClick:()=>t(c.id),className:`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${k===c.id?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,title:c.description,children:[e.jsx("span",{className:"mr-2",children:c.icon}),c.label]},c.id))})})}),r=()=>e.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Knowledge Exchange & Mentorship Platform"}),e.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Connect, learn, and share knowledge across cultures with fair time-based compensation and Ubuntu philosophy."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:"2,847"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Active Mentorships"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600 mb-2",children:"1,523"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Marketplace Listings"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600 mb-2",children:"45,892"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Credits Exchanged"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600 mb-2",children:"11"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Cultures Connected"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{onClick:()=>t("skill_sharing"),className:"bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white cursor-pointer hover:from-blue-600 hover:to-blue-700 transition-all",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🎓"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Cross-Cultural Skill Sharing"}),e.jsx("p",{className:"text-blue-100 mb-4",children:"Connect with mentors and mentees across cultures. Share expertise while learning about different traditions and approaches."}),e.jsx("div",{className:"flex items-center text-blue-100",children:e.jsx("span",{children:"Start Sharing →"})})]}),e.jsxs("div",{onClick:()=>t("marketplace"),className:"bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white cursor-pointer hover:from-green-600 hover:to-green-700 transition-all",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🛒"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Knowledge Exchange Marketplace"}),e.jsx("p",{className:"text-green-100 mb-4",children:"Discover knowledge exchange opportunities. Post what you can teach or find what you want to learn with fair compensation."}),e.jsx("div",{className:"flex items-center text-green-100",children:e.jsx("span",{children:"Explore Marketplace →"})})]}),e.jsxs("div",{onClick:()=>t("time_banking"),className:"bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white cursor-pointer hover:from-orange-600 hover:to-orange-700 transition-all",children:[e.jsx("div",{className:"text-3xl mb-4",children:"⏰"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Time Banking & Fair Exchange"}),e.jsx("p",{className:"text-orange-100 mb-4",children:"Fair time-based credit system that values all knowledge equally. Earn credits by teaching, spend them learning."}),e.jsx("div",{className:"flex items-center text-orange-100",children:e.jsx("span",{children:"Manage Credits →"})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6 text-center",children:"How Knowledge Exchange Works"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx("span",{className:"text-2xl",children:"👤"})}),e.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"1. Create Profile"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Set up your skill profile with what you can teach and want to learn, including cultural context."})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx("span",{className:"text-2xl",children:"🔍"})}),e.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"2. Find Matches"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Our algorithm finds optimal mentor-mentee matches based on skills, culture, and learning styles."})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx("span",{className:"text-2xl",children:"🤝"})}),e.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"3. Exchange Knowledge"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Engage in meaningful learning sessions with fair time credit compensation for all participants."})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx("span",{className:"text-2xl",children:"🌟"})}),e.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"4. Build Bridges"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Create lasting cross-cultural connections while contributing to community knowledge preservation."})]})]})]}),e.jsxs("div",{className:"bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-8",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("div",{className:"text-3xl mr-4",children:"🤲"}),e.jsx("h2",{className:"text-2xl font-bold text-orange-800",children:"Ubuntu Philosophy in Knowledge Exchange"})]}),e.jsx("p",{className:"text-orange-700 mb-6",children:'Our knowledge exchange platform embodies Ubuntu: "I am because we are." Every exchange strengthens our collective wisdom and creates bridges across cultural boundaries.'}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg p-6 border border-orange-200",children:[e.jsx("h3",{className:"font-semibold text-orange-800 mb-3",children:"🔄 Reciprocal Learning"}),e.jsx("p",{className:"text-orange-700 text-sm",children:"Every teaching moment is also a learning opportunity. Mentors gain cultural insights while sharing their expertise."})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 border border-orange-200",children:[e.jsx("h3",{className:"font-semibold text-orange-800 mb-3",children:"⚖️ Fair Value Exchange"}),e.jsx("p",{className:"text-orange-700 text-sm",children:"Time banking ensures all knowledge is valued equally, with cultural bonuses recognizing traditional wisdom."})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 border border-orange-200",children:[e.jsx("h3",{className:"font-semibold text-orange-800 mb-3",children:"🌍 Community Strengthening"}),e.jsx("p",{className:"text-orange-700 text-sm",children:"Each exchange contributes to community resilience and cross-cultural understanding in South Africa."})]})]})]}),e.jsxs("div",{className:"mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6 text-center",children:"Success Stories"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"border border-gray-200 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4",children:e.jsx("span",{className:"text-xl",children:"👩‍💻"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900",children:"Nomsa & James"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Zulu Traditional Beadwork ↔ Web Development"})]})]}),e.jsx("p",{className:"text-gray-700 text-sm",children:'"Through our exchange, I learned not just web development skills, but also the deep cultural significance behind traditional Zulu patterns. James now incorporates these patterns into his digital designs!"'}),e.jsxs("div",{className:"mt-3 flex items-center text-sm text-gray-500",children:[e.jsx("span",{className:"bg-orange-100 text-orange-800 px-2 py-1 rounded mr-2",children:"Cultural Bridge"}),e.jsx("span",{children:"150 credits exchanged"})]})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4",children:e.jsx("span",{className:"text-xl",children:"👨‍🍳"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900",children:"Pieter & Thandiwe"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Afrikaans Cooking ↔ Business Strategy"})]})]}),e.jsx("p",{className:"text-gray-700 text-sm",children:`"Learning traditional Afrikaans recipes opened my eyes to the history and culture behind the food. Thandiwe's business insights helped me start my own catering company celebrating diverse cuisines."`}),e.jsxs("div",{className:"mt-3 flex items-center text-sm text-gray-500",children:[e.jsx("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded mr-2",children:"Business Success"}),e.jsx("span",{children:"200 credits exchanged"})]})]})]})]})]}),a=()=>e.jsx(te,{onNavigateToMarketplace:()=>t("marketplace"),onNavigateToTimeBank:()=>t("time_banking")}),l=()=>e.jsx(ae,{onNavigateToTimeBank:()=>t("time_banking"),onNavigateToSkillSharing:()=>t("skill_sharing")}),d=()=>e.jsx(le,{onNavigateToMarketplace:()=>t("marketplace"),onNavigateToSkillSharing:()=>t("skill_sharing")});return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s(),e.jsxs("main",{children:[k==="overview"&&r(),k==="skill_sharing"&&a(),k==="marketplace"&&l(),k==="time_banking"&&d()]})]})};export{oe as KnowledgeExchangePage,oe as default};
//# sourceMappingURL=KnowledgeExchangePage-C23voltd.js.map
