import{c as he,j as o,u as Ae,a as ut,C as dt,b as ft,d as ht,e as mt,f as yt,B as we,g as Ot}from"./index-nwrMOwxu.js";import{b as H,r as b}from"./vendor-DtOhX2xw.js";import"./firebase-DLuFXYhP.js";var ge=e=>e.type==="checkbox",le=e=>e instanceof Date,P=e=>e==null;const gt=e=>typeof e=="object";var E=e=>!P(e)&&!Array.isArray(e)&&gt(e)&&!le(e),Tt=e=>E(e)&&e.target?ge(e.target)?e.target.checked:e.target.value:e,Pt=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,Bt=(e,r)=>e.has(Pt(r)),It=e=>{const r=e.constructor&&e.constructor.prototype;return E(r)&&r.hasOwnProperty("isPrototypeOf")},Pe=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function M(e){let r;const t=Array.isArray(e),a=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)r=new Date(e);else if(e instanceof Set)r=new Set(e);else if(!(Pe&&(e instanceof Blob||a))&&(t||E(e)))if(r=t?[]:{},!t&&!It(e))r=e;else for(const c in e)e.hasOwnProperty(c)&&(r[c]=M(e[c]));else return e;return r}var Fe=e=>Array.isArray(e)?e.filter(Boolean):[],S=e=>e===void 0,y=(e,r,t)=>{if(!r||!E(e))return t;const a=Fe(r.split(/[,[\].]+?/)).reduce((c,n)=>P(c)?c:c[n],e);return S(a)||a===e?S(e[r])?t:e[r]:a},J=e=>typeof e=="boolean",Be=e=>/^\w*$/.test(e),xt=e=>Fe(e.replace(/["|']|\]/g,"").split(/\.|\[/)),V=(e,r,t)=>{let a=-1;const c=Be(r)?[r]:xt(r),n=c.length,f=n-1;for(;++a<n;){const m=c[a];let k=t;if(a!==f){const R=e[m];k=E(R)||Array.isArray(R)?R:isNaN(+c[a+1])?{}:[]}if(m==="__proto__"||m==="constructor"||m==="prototype")return;e[m]=k,e=e[m]}};const Je={BLUR:"blur",FOCUS_OUT:"focusout"},Z={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},te={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};H.createContext(null);var Ut=(e,r,t,a=!0)=>{const c={defaultValues:r._defaultValues};for(const n in e)Object.defineProperty(c,n,{get:()=>{const f=n;return r._proxyFormState[f]!==Z.all&&(r._proxyFormState[f]=!a||Z.all),e[f]}});return c};const Wt=typeof window<"u"?b.useLayoutEffect:b.useEffect;var Q=e=>typeof e=="string",qt=(e,r,t,a,c)=>Q(e)?(a&&r.watch.add(e),y(t,e,c)):Array.isArray(e)?e.map(n=>(a&&r.watch.add(n),y(t,n))):(a&&(r.watchAll=!0),t),$t=(e,r,t,a,c)=>r?{...t[e],types:{...t[e]&&t[e].types?t[e].types:{},[a]:c||!0}}:{},me=e=>Array.isArray(e)?e:[e],Qe=()=>{let e=[];return{get observers(){return e},next:c=>{for(const n of e)n.next&&n.next(c)},subscribe:c=>(e.push(c),{unsubscribe:()=>{e=e.filter(n=>n!==c)}}),unsubscribe:()=>{e=[]}}},Me=e=>P(e)||!gt(e);function ae(e,r){if(Me(e)||Me(r))return e===r;if(le(e)&&le(r))return e.getTime()===r.getTime();const t=Object.keys(e),a=Object.keys(r);if(t.length!==a.length)return!1;for(const c of t){const n=e[c];if(!a.includes(c))return!1;if(c!=="ref"){const f=r[c];if(le(n)&&le(f)||E(n)&&E(f)||Array.isArray(n)&&Array.isArray(f)?!ae(n,f):n!==f)return!1}}return!0}var U=e=>E(e)&&!Object.keys(e).length,Ie=e=>e.type==="file",X=e=>typeof e=="function",je=e=>{if(!Pe)return!1;const r=e?e.ownerDocument:0;return e instanceof(r&&r.defaultView?r.defaultView.HTMLElement:HTMLElement)},vt=e=>e.type==="select-multiple",Ue=e=>e.type==="radio",Ht=e=>Ue(e)||ge(e),Le=e=>je(e)&&e.isConnected;function zt(e,r){const t=r.slice(0,-1).length;let a=0;for(;a<t;)e=S(e)?a++:e[r[a++]];return e}function Zt(e){for(const r in e)if(e.hasOwnProperty(r)&&!S(e[r]))return!1;return!0}function L(e,r){const t=Array.isArray(r)?r:Be(r)?[r]:xt(r),a=t.length===1?e:zt(e,t),c=t.length-1,n=t[c];return a&&delete a[n],c!==0&&(E(a)&&U(a)||Array.isArray(a)&&Zt(a))&&L(e,t.slice(0,-1)),e}var bt=e=>{for(const r in e)if(X(e[r]))return!0;return!1};function Ne(e,r={}){const t=Array.isArray(e);if(E(e)||t)for(const a in e)Array.isArray(e[a])||E(e[a])&&!bt(e[a])?(r[a]=Array.isArray(e[a])?[]:{},Ne(e[a],r[a])):P(e[a])||(r[a]=!0);return r}function wt(e,r,t){const a=Array.isArray(e);if(E(e)||a)for(const c in e)Array.isArray(e[c])||E(e[c])&&!bt(e[c])?S(r)||Me(t[c])?t[c]=Array.isArray(e[c])?Ne(e[c],[]):{...Ne(e[c])}:wt(e[c],P(r)?{}:r[c],t[c]):t[c]=!ae(e[c],r[c]);return t}var de=(e,r)=>wt(e,r,Ne(r));const et={value:!1,isValid:!1},tt={value:!0,isValid:!0};var pt=e=>{if(Array.isArray(e)){if(e.length>1){const r=e.filter(t=>t&&t.checked&&!t.disabled).map(t=>t.value);return{value:r,isValid:!!r.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!S(e[0].attributes.value)?S(e[0].value)||e[0].value===""?tt:{value:e[0].value,isValid:!0}:tt:et}return et},jt=(e,{valueAsNumber:r,valueAsDate:t,setValueAs:a})=>S(e)?e:r?e===""?NaN:e&&+e:t&&Q(e)?new Date(e):a?a(e):e;const rt={isValid:!1,value:null};var Nt=e=>Array.isArray(e)?e.reduce((r,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:r,rt):rt;function st(e){const r=e.ref;return Ie(r)?r.files:Ue(r)?Nt(e.refs).value:vt(r)?[...r.selectedOptions].map(({value:t})=>t):ge(r)?pt(e.refs).value:jt(S(r.value)?e.ref.value:r.value,e)}var Xt=(e,r,t,a)=>{const c={};for(const n of e){const f=y(r,n);f&&V(c,n,f._f)}return{criteriaMode:t,names:[...e],fields:c,shouldUseNativeValidation:a}},_e=e=>e instanceof RegExp,fe=e=>S(e)?e:_e(e)?e.source:E(e)?_e(e.value)?e.value.source:e.value:e,at=e=>({isOnSubmit:!e||e===Z.onSubmit,isOnBlur:e===Z.onBlur,isOnChange:e===Z.onChange,isOnAll:e===Z.all,isOnTouch:e===Z.onTouched});const it="AsyncFunction";var Kt=e=>!!e&&!!e.validate&&!!(X(e.validate)&&e.validate.constructor.name===it||E(e.validate)&&Object.values(e.validate).find(r=>r.constructor.name===it)),Yt=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),nt=(e,r,t)=>!t&&(r.watchAll||r.watch.has(e)||[...r.watch].some(a=>e.startsWith(a)&&/^\.\w+/.test(e.slice(a.length))));const ye=(e,r,t,a)=>{for(const c of t||Object.keys(e)){const n=y(e,c);if(n){const{_f:f,...m}=n;if(f){if(f.refs&&f.refs[0]&&r(f.refs[0],c)&&!a)return!0;if(f.ref&&r(f.ref,f.name)&&!a)return!0;if(ye(m,r))break}else if(E(m)&&ye(m,r))break}}};function lt(e,r,t){const a=y(e,t);if(a||Be(t))return{error:a,name:t};const c=t.split(".");for(;c.length;){const n=c.join("."),f=y(r,n),m=y(e,n);if(f&&!Array.isArray(f)&&t!==n)return{name:t};if(m&&m.type)return{name:n,error:m};if(m&&m.root&&m.root.type)return{name:`${n}.root`,error:m.root};c.pop()}return{name:t}}var Gt=(e,r,t,a)=>{t(e);const{name:c,...n}=e;return U(n)||Object.keys(n).length>=Object.keys(r).length||Object.keys(n).find(f=>r[f]===(!a||Z.all))},Jt=(e,r,t)=>!e||!r||e===r||me(e).some(a=>a&&(t?a===r:a.startsWith(r)||r.startsWith(a))),Qt=(e,r,t,a,c)=>c.isOnAll?!1:!t&&c.isOnTouch?!(r||e):(t?a.isOnBlur:c.isOnBlur)?!e:(t?a.isOnChange:c.isOnChange)?e:!0,er=(e,r)=>!Fe(y(e,r)).length&&L(e,r),tr=(e,r,t)=>{const a=me(y(e,t));return V(a,"root",r[t]),V(e,t,a),e},pe=e=>Q(e);function ot(e,r,t="validate"){if(pe(e)||Array.isArray(e)&&e.every(pe)||J(e)&&!e)return{type:t,message:pe(e)?e:"",ref:r}}var ce=e=>E(e)&&!_e(e)?e:{value:e,message:""},ct=async(e,r,t,a,c,n)=>{const{ref:f,refs:m,required:k,maxLength:R,minLength:v,min:_,max:x,pattern:O,validate:W,name:A,valueAsNumber:B,mount:q}=e._f,w=y(t,A);if(!q||r.has(A))return{};const ee=m?m[0]:f,K=p=>{c&&ee.reportValidity&&(ee.setCustomValidity(J(p)?"":p||""),ee.reportValidity())},D={},xe=Ue(f),re=ge(f),Ve=xe||re,z=(B||Ie(f))&&S(f.value)&&S(w)||je(f)&&f.value===""||w===""||Array.isArray(w)&&!w.length,ne=$t.bind(null,A,a,D),Y=(p,N,C,T=te.maxLength,I=te.minLength)=>{const G=p?N:C;D[A]={type:p?T:I,message:G,ref:f,...ne(p?T:I,G)}};if(n?!Array.isArray(w)||!w.length:k&&(!Ve&&(z||P(w))||J(w)&&!w||re&&!pt(m).isValid||xe&&!Nt(m).isValid)){const{value:p,message:N}=pe(k)?{value:!!k,message:k}:ce(k);if(p&&(D[A]={type:te.required,message:N,ref:ee,...ne(te.required,N)},!a))return K(N),D}if(!z&&(!P(_)||!P(x))){let p,N;const C=ce(x),T=ce(_);if(!P(w)&&!isNaN(w)){const I=f.valueAsNumber||w&&+w;P(C.value)||(p=I>C.value),P(T.value)||(N=I<T.value)}else{const I=f.valueAsDate||new Date(w),G=ve=>new Date(new Date().toDateString()+" "+ve),ue=f.type=="time",oe=f.type=="week";Q(C.value)&&w&&(p=ue?G(w)>G(C.value):oe?w>C.value:I>new Date(C.value)),Q(T.value)&&w&&(N=ue?G(w)<G(T.value):oe?w<T.value:I<new Date(T.value))}if((p||N)&&(Y(!!p,C.message,T.message,te.max,te.min),!a))return K(D[A].message),D}if((R||v)&&!z&&(Q(w)||n&&Array.isArray(w))){const p=ce(R),N=ce(v),C=!P(p.value)&&w.length>+p.value,T=!P(N.value)&&w.length<+N.value;if((C||T)&&(Y(C,p.message,N.message),!a))return K(D[A].message),D}if(O&&!z&&Q(w)){const{value:p,message:N}=ce(O);if(_e(p)&&!w.match(p)&&(D[A]={type:te.pattern,message:N,ref:f,...ne(te.pattern,N)},!a))return K(N),D}if(W){if(X(W)){const p=await W(w,t),N=ot(p,ee);if(N&&(D[A]={...N,...ne(te.validate,N.message)},!a))return K(N.message),D}else if(E(W)){let p={};for(const N in W){if(!U(p)&&!a)break;const C=ot(await W[N](w,t),ee,N);C&&(p={...C,...ne(N,C.message)},K(C.message),a&&(D[A]=p))}if(!U(p)&&(D[A]={ref:ee,...p},!a))return D}}return K(!0),D};const rr={mode:Z.onSubmit,reValidateMode:Z.onChange,shouldFocusError:!0};function sr(e={}){let r={...rr,...e},t={submitCount:0,isDirty:!1,isReady:!1,isLoading:X(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1};const a={};let c=E(r.defaultValues)||E(r.values)?M(r.defaultValues||r.values)||{}:{},n=r.shouldUnregister?{}:M(c),f={action:!1,mount:!1,watch:!1},m={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},k,R=0;const v={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let _={...v};const x={array:Qe(),state:Qe()},O=r.criteriaMode===Z.all,W=s=>i=>{clearTimeout(R),R=setTimeout(s,i)},A=async s=>{if(!r.disabled&&(v.isValid||_.isValid||s)){const i=r.resolver?U((await re()).errors):await z(a,!0);i!==t.isValid&&x.state.next({isValid:i})}},B=(s,i)=>{!r.disabled&&(v.isValidating||v.validatingFields||_.isValidating||_.validatingFields)&&((s||Array.from(m.mount)).forEach(l=>{l&&(i?V(t.validatingFields,l,i):L(t.validatingFields,l))}),x.state.next({validatingFields:t.validatingFields,isValidating:!U(t.validatingFields)}))},q=(s,i=[],l,h,d=!0,u=!0)=>{if(h&&l&&!r.disabled){if(f.action=!0,u&&Array.isArray(y(a,s))){const g=l(y(a,s),h.argA,h.argB);d&&V(a,s,g)}if(u&&Array.isArray(y(t.errors,s))){const g=l(y(t.errors,s),h.argA,h.argB);d&&V(t.errors,s,g),er(t.errors,s)}if((v.touchedFields||_.touchedFields)&&u&&Array.isArray(y(t.touchedFields,s))){const g=l(y(t.touchedFields,s),h.argA,h.argB);d&&V(t.touchedFields,s,g)}(v.dirtyFields||_.dirtyFields)&&(t.dirtyFields=de(c,n)),x.state.next({name:s,isDirty:Y(s,i),dirtyFields:t.dirtyFields,errors:t.errors,isValid:t.isValid})}else V(n,s,i)},w=(s,i)=>{V(t.errors,s,i),x.state.next({errors:t.errors})},ee=s=>{t.errors=s,x.state.next({errors:t.errors,isValid:!1})},K=(s,i,l,h)=>{const d=y(a,s);if(d){const u=y(n,s,S(l)?y(c,s):l);S(u)||h&&h.defaultChecked||i?V(n,s,i?u:st(d._f)):C(s,u),f.mount&&A()}},D=(s,i,l,h,d)=>{let u=!1,g=!1;const j={name:s};if(!r.disabled){if(!l||h){(v.isDirty||_.isDirty)&&(g=t.isDirty,t.isDirty=j.isDirty=Y(),u=g!==j.isDirty);const F=ae(y(c,s),i);g=!!y(t.dirtyFields,s),F?L(t.dirtyFields,s):V(t.dirtyFields,s,!0),j.dirtyFields=t.dirtyFields,u=u||(v.dirtyFields||_.dirtyFields)&&g!==!F}if(l){const F=y(t.touchedFields,s);F||(V(t.touchedFields,s,l),j.touchedFields=t.touchedFields,u=u||(v.touchedFields||_.touchedFields)&&F!==l)}u&&d&&x.state.next(j)}return u?j:{}},xe=(s,i,l,h)=>{const d=y(t.errors,s),u=(v.isValid||_.isValid)&&J(i)&&t.isValid!==i;if(r.delayError&&l?(k=W(()=>w(s,l)),k(r.delayError)):(clearTimeout(R),k=null,l?V(t.errors,s,l):L(t.errors,s)),(l?!ae(d,l):d)||!U(h)||u){const g={...h,...u&&J(i)?{isValid:i}:{},errors:t.errors,name:s};t={...t,...g},x.state.next(g)}},re=async s=>{B(s,!0);const i=await r.resolver(n,r.context,Xt(s||m.mount,a,r.criteriaMode,r.shouldUseNativeValidation));return B(s),i},Ve=async s=>{const{errors:i}=await re(s);if(s)for(const l of s){const h=y(i,l);h?V(t.errors,l,h):L(t.errors,l)}else t.errors=i;return i},z=async(s,i,l={valid:!0})=>{for(const h in s){const d=s[h];if(d){const{_f:u,...g}=d;if(u){const j=m.array.has(u.name),F=d._f&&Kt(d._f);F&&v.validatingFields&&B([h],!0);const $=await ct(d,m.disabled,n,O,r.shouldUseNativeValidation&&!i,j);if(F&&v.validatingFields&&B([h]),$[u.name]&&(l.valid=!1,i))break;!i&&(y($,u.name)?j?tr(t.errors,$,u.name):V(t.errors,u.name,$[u.name]):L(t.errors,u.name))}!U(g)&&await z(g,i,l)}}return l.valid},ne=()=>{for(const s of m.unMount){const i=y(a,s);i&&(i._f.refs?i._f.refs.every(l=>!Le(l)):!Le(i._f.ref))&&ke(s)}m.unMount=new Set},Y=(s,i)=>!r.disabled&&(s&&i&&V(n,s,i),!ae(ve(),c)),p=(s,i,l)=>qt(s,m,{...f.mount?n:S(i)?c:Q(s)?{[s]:i}:i},l,i),N=s=>Fe(y(f.mount?n:c,s,r.shouldUnregister?y(c,s,[]):[])),C=(s,i,l={})=>{const h=y(a,s);let d=i;if(h){const u=h._f;u&&(!u.disabled&&V(n,s,jt(i,u)),d=je(u.ref)&&P(i)?"":i,vt(u.ref)?[...u.ref.options].forEach(g=>g.selected=d.includes(g.value)):u.refs?ge(u.ref)?u.refs.forEach(g=>{(!g.defaultChecked||!g.disabled)&&(Array.isArray(d)?g.checked=!!d.find(j=>j===g.value):g.checked=d===g.value||!!d)}):u.refs.forEach(g=>g.checked=g.value===d):Ie(u.ref)?u.ref.value="":(u.ref.value=d,u.ref.type||x.state.next({name:s,values:M(n)})))}(l.shouldDirty||l.shouldTouch)&&D(s,d,l.shouldTouch,l.shouldDirty,!0),l.shouldValidate&&oe(s)},T=(s,i,l)=>{for(const h in i){if(!i.hasOwnProperty(h))return;const d=i[h],u=s+"."+h,g=y(a,u);(m.array.has(s)||E(d)||g&&!g._f)&&!le(d)?T(u,d,l):C(u,d,l)}},I=(s,i,l={})=>{const h=y(a,s),d=m.array.has(s),u=M(i);V(n,s,u),d?(x.array.next({name:s,values:M(n)}),(v.isDirty||v.dirtyFields||_.isDirty||_.dirtyFields)&&l.shouldDirty&&x.state.next({name:s,dirtyFields:de(c,n),isDirty:Y(s,u)})):h&&!h._f&&!P(u)?T(s,u,l):C(s,u,l),nt(s,m)&&x.state.next({...t}),x.state.next({name:f.mount?s:void 0,values:M(n)})},G=async s=>{f.mount=!0;const i=s.target;let l=i.name,h=!0;const d=y(a,l),u=F=>{h=Number.isNaN(F)||le(F)&&isNaN(F.getTime())||ae(F,y(n,l,F))},g=at(r.mode),j=at(r.reValidateMode);if(d){let F,$;const be=i.type?st(d._f):Tt(s),se=s.type===Je.BLUR||s.type===Je.FOCUS_OUT,Lt=!Yt(d._f)&&!r.resolver&&!y(t.errors,l)&&!d._f.deps||Qt(se,y(t.touchedFields,l),t.isSubmitted,j,g),Se=nt(l,m,se);V(n,l,be),se?(d._f.onBlur&&d._f.onBlur(s),k&&k(0)):d._f.onChange&&d._f.onChange(s);const De=D(l,be,se),Rt=!U(De)||Se;if(!se&&x.state.next({name:l,type:s.type,values:M(n)}),Lt)return(v.isValid||_.isValid)&&(r.mode==="onBlur"?se&&A():se||A()),Rt&&x.state.next({name:l,...Se?{}:De});if(!se&&Se&&x.state.next({...t}),r.resolver){const{errors:Ye}=await re([l]);if(u(be),h){const Mt=lt(t.errors,a,l),Ge=lt(Ye,a,Mt.name||l);F=Ge.error,l=Ge.name,$=U(Ye)}}else B([l],!0),F=(await ct(d,m.disabled,n,O,r.shouldUseNativeValidation))[l],B([l]),u(be),h&&(F?$=!1:(v.isValid||_.isValid)&&($=await z(a,!0)));h&&(d._f.deps&&oe(d._f.deps),xe(l,$,F,De))}},ue=(s,i)=>{if(y(t.errors,i)&&s.focus)return s.focus(),1},oe=async(s,i={})=>{let l,h;const d=me(s);if(r.resolver){const u=await Ve(S(s)?s:d);l=U(u),h=s?!d.some(g=>y(u,g)):l}else s?(h=(await Promise.all(d.map(async u=>{const g=y(a,u);return await z(g&&g._f?{[u]:g}:g)}))).every(Boolean),!(!h&&!t.isValid)&&A()):h=l=await z(a);return x.state.next({...!Q(s)||(v.isValid||_.isValid)&&l!==t.isValid?{}:{name:s},...r.resolver||!s?{isValid:l}:{},errors:t.errors}),i.shouldFocus&&!h&&ye(a,ue,s?d:m.mount),h},ve=s=>{const i={...f.mount?n:c};return S(s)?i:Q(s)?y(i,s):s.map(l=>y(i,l))},We=(s,i)=>({invalid:!!y((i||t).errors,s),isDirty:!!y((i||t).dirtyFields,s),error:y((i||t).errors,s),isValidating:!!y(t.validatingFields,s),isTouched:!!y((i||t).touchedFields,s)}),Ft=s=>{s&&me(s).forEach(i=>L(t.errors,i)),x.state.next({errors:s?t.errors:{}})},qe=(s,i,l)=>{const h=(y(a,s,{_f:{}})._f||{}).ref,d=y(t.errors,s)||{},{ref:u,message:g,type:j,...F}=d;V(t.errors,s,{...F,...i,ref:h}),x.state.next({name:s,errors:t.errors,isValid:!1}),l&&l.shouldFocus&&h&&h.focus&&h.focus()},Vt=(s,i)=>X(s)?x.state.subscribe({next:l=>s(p(void 0,i),l)}):p(s,i,!0),$e=s=>x.state.subscribe({next:i=>{Jt(s.name,i.name,s.exact)&&Gt(i,s.formState||v,Dt,s.reRenderRoot)&&s.callback({values:{...n},...t,...i})}}).unsubscribe,kt=s=>(f.mount=!0,_={..._,...s.formState},$e({...s,formState:_})),ke=(s,i={})=>{for(const l of s?me(s):m.mount)m.mount.delete(l),m.array.delete(l),i.keepValue||(L(a,l),L(n,l)),!i.keepError&&L(t.errors,l),!i.keepDirty&&L(t.dirtyFields,l),!i.keepTouched&&L(t.touchedFields,l),!i.keepIsValidating&&L(t.validatingFields,l),!r.shouldUnregister&&!i.keepDefaultValue&&L(c,l);x.state.next({values:M(n)}),x.state.next({...t,...i.keepDirty?{isDirty:Y()}:{}}),!i.keepIsValid&&A()},He=({disabled:s,name:i})=>{(J(s)&&f.mount||s||m.disabled.has(i))&&(s?m.disabled.add(i):m.disabled.delete(i))},Ce=(s,i={})=>{let l=y(a,s);const h=J(i.disabled)||J(r.disabled);return V(a,s,{...l||{},_f:{...l&&l._f?l._f:{ref:{name:s}},name:s,mount:!0,...i}}),m.mount.add(s),l?He({disabled:J(i.disabled)?i.disabled:r.disabled,name:s}):K(s,!0,i.value),{...h?{disabled:i.disabled||r.disabled}:{},...r.progressive?{required:!!i.required,min:fe(i.min),max:fe(i.max),minLength:fe(i.minLength),maxLength:fe(i.maxLength),pattern:fe(i.pattern)}:{},name:s,onChange:G,onBlur:G,ref:d=>{if(d){Ce(s,i),l=y(a,s);const u=S(d.value)&&d.querySelectorAll&&d.querySelectorAll("input,select,textarea")[0]||d,g=Ht(u),j=l._f.refs||[];if(g?j.find(F=>F===u):u===l._f.ref)return;V(a,s,{_f:{...l._f,...g?{refs:[...j.filter(Le),u,...Array.isArray(y(c,s))?[{}]:[]],ref:{type:u.type,name:s}}:{ref:u}}}),K(s,!1,void 0,u)}else l=y(a,s,{}),l._f&&(l._f.mount=!1),(r.shouldUnregister||i.shouldUnregister)&&!(Bt(m.array,s)&&f.action)&&m.unMount.add(s)}}},Ee=()=>r.shouldFocusError&&ye(a,ue,m.mount),Ct=s=>{J(s)&&(x.state.next({disabled:s}),ye(a,(i,l)=>{const h=y(a,l);h&&(i.disabled=h._f.disabled||s,Array.isArray(h._f.refs)&&h._f.refs.forEach(d=>{d.disabled=h._f.disabled||s}))},0,!1))},ze=(s,i)=>async l=>{let h;l&&(l.preventDefault&&l.preventDefault(),l.persist&&l.persist());let d=M(n);if(x.state.next({isSubmitting:!0}),r.resolver){const{errors:u,values:g}=await re();t.errors=u,d=g}else await z(a);if(m.disabled.size)for(const u of m.disabled)V(d,u,void 0);if(L(t.errors,"root"),U(t.errors)){x.state.next({errors:{}});try{await s(d,l)}catch(u){h=u}}else i&&await i({...t.errors},l),Ee(),setTimeout(Ee);if(x.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:U(t.errors)&&!h,submitCount:t.submitCount+1,errors:t.errors}),h)throw h},Et=(s,i={})=>{y(a,s)&&(S(i.defaultValue)?I(s,M(y(c,s))):(I(s,i.defaultValue),V(c,s,M(i.defaultValue))),i.keepTouched||L(t.touchedFields,s),i.keepDirty||(L(t.dirtyFields,s),t.isDirty=i.defaultValue?Y(s,M(y(c,s))):Y()),i.keepError||(L(t.errors,s),v.isValid&&A()),x.state.next({...t}))},Ze=(s,i={})=>{const l=s?M(s):c,h=M(l),d=U(s),u=d?c:h;if(i.keepDefaultValues||(c=l),!i.keepValues){if(i.keepDirtyValues){const g=new Set([...m.mount,...Object.keys(de(c,n))]);for(const j of Array.from(g))y(t.dirtyFields,j)?V(u,j,y(n,j)):I(j,y(u,j))}else{if(Pe&&S(s))for(const g of m.mount){const j=y(a,g);if(j&&j._f){const F=Array.isArray(j._f.refs)?j._f.refs[0]:j._f.ref;if(je(F)){const $=F.closest("form");if($){$.reset();break}}}}for(const g of m.mount)I(g,y(u,g))}n=M(u),x.array.next({values:{...u}}),x.state.next({values:{...u}})}m={mount:i.keepDirtyValues?m.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},f.mount=!v.isValid||!!i.keepIsValid||!!i.keepDirtyValues,f.watch=!!r.shouldUnregister,x.state.next({submitCount:i.keepSubmitCount?t.submitCount:0,isDirty:d?!1:i.keepDirty?t.isDirty:!!(i.keepDefaultValues&&!ae(s,c)),isSubmitted:i.keepIsSubmitted?t.isSubmitted:!1,dirtyFields:d?{}:i.keepDirtyValues?i.keepDefaultValues&&n?de(c,n):t.dirtyFields:i.keepDefaultValues&&s?de(c,s):i.keepDirty?t.dirtyFields:{},touchedFields:i.keepTouched?t.touchedFields:{},errors:i.keepErrors?t.errors:{},isSubmitSuccessful:i.keepIsSubmitSuccessful?t.isSubmitSuccessful:!1,isSubmitting:!1})},Xe=(s,i)=>Ze(X(s)?s(n):s,i),St=(s,i={})=>{const l=y(a,s),h=l&&l._f;if(h){const d=h.refs?h.refs[0]:h.ref;d.focus&&(d.focus(),i.shouldSelect&&X(d.select)&&d.select())}},Dt=s=>{t={...t,...s}},Ke={control:{register:Ce,unregister:ke,getFieldState:We,handleSubmit:ze,setError:qe,_subscribe:$e,_runSchema:re,_focusError:Ee,_getWatch:p,_getDirty:Y,_setValid:A,_setFieldArray:q,_setDisabledField:He,_setErrors:ee,_getFieldArray:N,_reset:Ze,_resetDefaultValues:()=>X(r.defaultValues)&&r.defaultValues().then(s=>{Xe(s,r.resetOptions),x.state.next({isLoading:!1})}),_removeUnmounted:ne,_disableForm:Ct,_subjects:x,_proxyFormState:v,get _fields(){return a},get _formValues(){return n},get _state(){return f},set _state(s){f=s},get _defaultValues(){return c},get _names(){return m},set _names(s){m=s},get _formState(){return t},get _options(){return r},set _options(s){r={...r,...s}}},subscribe:kt,trigger:oe,register:Ce,handleSubmit:ze,watch:Vt,setValue:I,getValues:ve,reset:Xe,resetField:Et,clearErrors:Ft,unregister:ke,setError:qe,setFocus:St,getFieldState:We};return{...Ke,formControl:Ke}}function _t(e={}){const r=H.useRef(void 0),t=H.useRef(void 0),[a,c]=H.useState({isDirty:!1,isValidating:!1,isLoading:X(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:X(e.defaultValues)?void 0:e.defaultValues});r.current||(r.current={...e.formControl?e.formControl:sr(e),formState:a},e.formControl&&e.defaultValues&&!X(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const n=r.current.control;return n._options=e,Wt(()=>{const f=n._subscribe({formState:n._proxyFormState,callback:()=>c({...n._formState}),reRenderRoot:!0});return c(m=>({...m,isReady:!0})),n._formState.isReady=!0,f},[n]),H.useEffect(()=>n._disableForm(e.disabled),[n,e.disabled]),H.useEffect(()=>{e.mode&&(n._options.mode=e.mode),e.reValidateMode&&(n._options.reValidateMode=e.reValidateMode)},[n,e.mode,e.reValidateMode]),H.useEffect(()=>{e.errors&&(n._setErrors(e.errors),n._focusError())},[n,e.errors]),H.useEffect(()=>{e.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})},[n,e.shouldUnregister]),H.useEffect(()=>{if(n._proxyFormState.isDirty){const f=n._getDirty();f!==a.isDirty&&n._subjects.state.next({isDirty:f})}},[n,a.isDirty]),H.useEffect(()=>{e.values&&!ae(e.values,t.current)?(n._reset(e.values,n._options.resetOptions),t.current=e.values,c(f=>({...f}))):n._resetDefaultValues()},[n,e.values]),H.useEffect(()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),r.current.formState=Ut(a,n),r.current}function ar({title:e,titleId:r,...t},a){return b.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?b.createElement("title",{id:r},e):null,b.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}const ir=b.forwardRef(ar);function nr({title:e,titleId:r,...t},a){return b.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?b.createElement("title",{id:r},e):null,b.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const At=b.forwardRef(nr);function lr({title:e,titleId:r,...t},a){return b.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?b.createElement("title",{id:r},e):null,b.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}const Oe=b.forwardRef(lr);function or({title:e,titleId:r,...t},a){return b.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?b.createElement("title",{id:r},e):null,b.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),b.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Te=b.forwardRef(or);function cr({title:e,titleId:r,...t},a){return b.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?b.createElement("title",{id:r},e):null,b.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802"}))}const ur=b.forwardRef(cr);function dr({title:e,titleId:r,...t},a){return b.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?b.createElement("title",{id:r},e):null,b.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const fr=b.forwardRef(dr);function hr({title:e,titleId:r,...t},a){return b.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?b.createElement("title",{id:r},e):null,b.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}const mr=b.forwardRef(hr),ie=H.forwardRef(({className:e,label:r,error:t,helperText:a,leftIcon:c,rightIcon:n,fullWidth:f=!1,id:m,...k},R)=>{const v=m||`input-${Math.random().toString(36).substr(2,9)}`,_=he("form-input","block px-3 py-2 border rounded-md shadow-sm","placeholder-gray-400 focus:outline-none transition-colors duration-200",c&&"pl-10",n&&"pr-10",t?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-cultural-500 focus:border-cultural-500",f?"w-full":"w-auto",e);return o.jsxs("div",{className:he("space-y-1",f&&"w-full"),children:[r&&o.jsxs("label",{htmlFor:v,className:"block text-sm font-medium text-gray-700",children:[r,k.required&&o.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),o.jsxs("div",{className:"relative",children:[c&&o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx("span",{className:"text-gray-400",children:c})}),o.jsx("input",{ref:R,id:v,className:_,...k}),n&&o.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:o.jsx("span",{className:"text-gray-400",children:n})})]}),t&&o.jsx("p",{className:"text-sm text-red-600",role:"alert",children:t}),a&&!t&&o.jsx("p",{className:"text-sm text-gray-500",children:a})]})});ie.displayName="Input";const yr=({onSuccess:e,onSwitchToLogin:r})=>{const{t}=Ae(),{register:a,isLoading:c,error:n}=ut(),[f,m]=b.useState(!1),[k,R]=b.useState(!1),{register:v,handleSubmit:_,watch:x,formState:{errors:O,isValid:W}}=_t({mode:"onChange",defaultValues:{privacyConsent:!1,culturalConsent:!1}}),A=x("password"),B=async q=>{try{const w={name:q.name,email:q.email,password:q.password,phoneNumber:q.phoneNumber,preferredLanguage:"en",privacyConsent:q.privacyConsent,culturalConsent:q.culturalConsent};await a(w),e?.()}catch(w){console.error("Registration failed:",w)}};return o.jsxs(dt,{className:"w-full max-w-md mx-auto",variant:"elevated",children:[o.jsxs(ft,{children:[o.jsx(ht,{className:"text-center text-cultural-gradient",children:t("auth.welcomeToUbuntu")}),o.jsx(mt,{className:"text-center",children:t("app.description")})]}),o.jsx(yt,{children:o.jsxs("form",{onSubmit:_(B),className:"space-y-4",children:[o.jsx(ie,{label:t("auth.name"),type:"text",leftIcon:o.jsx(mr,{className:"w-5 h-5"}),fullWidth:!0,error:O.name?.message,...v("name",{required:t("validation.required"),minLength:{value:2,message:t("validation.name")}})}),o.jsx(ie,{label:t("auth.email"),type:"email",leftIcon:o.jsx(At,{className:"w-5 h-5"}),fullWidth:!0,error:O.email?.message,...v("email",{required:t("validation.required"),pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:t("validation.email")}})}),o.jsx(ie,{label:`${t("auth.phoneNumber")} (${t("common.optional")})`,type:"tel",leftIcon:o.jsx(fr,{className:"w-5 h-5"}),fullWidth:!0,helperText:"South African format: +27 XX XXX XXXX",error:O.phoneNumber?.message,...v("phoneNumber",{pattern:{value:/^\+27[0-9]{9}$/,message:t("validation.phoneNumber")}})}),o.jsx(ie,{label:t("auth.password"),type:f?"text":"password",fullWidth:!0,error:O.password?.message,rightIcon:o.jsx("button",{type:"button",onClick:()=>m(!f),className:"text-gray-400 hover:text-gray-600",children:f?o.jsx(Oe,{className:"w-5 h-5"}):o.jsx(Te,{className:"w-5 h-5"})}),...v("password",{required:t("validation.required"),minLength:{value:8,message:t("validation.password")}})}),o.jsx(ie,{label:t("auth.confirmPassword"),type:k?"text":"password",fullWidth:!0,error:O.confirmPassword?.message,rightIcon:o.jsx("button",{type:"button",onClick:()=>R(!k),className:"text-gray-400 hover:text-gray-600",children:k?o.jsx(Oe,{className:"w-5 h-5"}):o.jsx(Te,{className:"w-5 h-5"})}),...v("confirmPassword",{required:t("validation.required"),validate:q=>q===A||t("validation.passwordMatch")})}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("label",{className:"flex items-start space-x-3",children:[o.jsx("input",{type:"checkbox",className:"mt-1 h-4 w-4 text-cultural-600 focus:ring-cultural-500 border-gray-300 rounded",...v("privacyConsent",{required:"Privacy consent is required"})}),o.jsxs("span",{className:"text-sm text-gray-700",children:[t("privacy.consent"),o.jsx("a",{href:"/privacy",className:"text-cultural-600 hover:text-cultural-700 ml-1",target:"_blank",rel:"noopener noreferrer",children:t("privacy.dataUsage")})]})]}),O.privacyConsent&&o.jsx("p",{className:"text-sm text-red-600",children:O.privacyConsent.message}),o.jsxs("label",{className:"flex items-start space-x-3",children:[o.jsx("input",{type:"checkbox",className:"mt-1 h-4 w-4 text-cultural-600 focus:ring-cultural-500 border-gray-300 rounded",...v("culturalConsent")}),o.jsxs("span",{className:"text-sm text-gray-700",children:[t("privacy.culturalConsent")," (",t("common.optional"),")"]})]})]}),n&&o.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md",children:o.jsx("p",{className:"text-sm text-red-600",children:n})}),o.jsx(we,{type:"submit",variant:"cultural",size:"lg",fullWidth:!0,isLoading:c,disabled:!W,children:t("auth.createAccount")}),o.jsx("div",{className:"text-center",children:o.jsxs("span",{className:"text-sm text-gray-600",children:[t("auth.alreadyHaveAccount")," ",o.jsx("button",{type:"button",onClick:r,className:"text-cultural-600 hover:text-cultural-700 font-medium",children:t("auth.login")})]})})]})})]})},gr=({onSuccess:e,onSwitchToRegister:r})=>{const{t}=Ae(),{login:a,loginWithProvider:c,isLoading:n,error:f}=ut(),[m,k]=b.useState(!1),{register:R,handleSubmit:v,formState:{errors:_,isValid:x}}=_t({mode:"onChange"}),O=async A=>{try{await a(A.email,A.password),e?.()}catch(B){console.error("Login failed:",B)}},W=async A=>{try{await c(A),e?.()}catch(B){console.error("Provider login failed:",B)}};return o.jsxs(dt,{className:"w-full max-w-md mx-auto",variant:"elevated",children:[o.jsxs(ft,{children:[o.jsx(ht,{className:"text-center text-cultural-gradient",children:t("auth.welcomeBack")}),o.jsx(mt,{className:"text-center",children:"Sign in to continue your cultural journey"})]}),o.jsx(yt,{children:o.jsxs("form",{onSubmit:v(O),className:"space-y-4",children:[o.jsx(ie,{label:t("auth.email"),type:"email",leftIcon:o.jsx(At,{className:"w-5 h-5"}),fullWidth:!0,error:_.email?.message,...R("email",{required:t("validation.required"),pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:t("validation.email")}})}),o.jsx(ie,{label:t("auth.password"),type:m?"text":"password",fullWidth:!0,error:_.password?.message,rightIcon:o.jsx("button",{type:"button",onClick:()=>k(!m),className:"text-gray-400 hover:text-gray-600",children:m?o.jsx(Oe,{className:"w-5 h-5"}):o.jsx(Te,{className:"w-5 h-5"})}),...R("password",{required:t("validation.required")})}),o.jsx("div",{className:"text-right",children:o.jsx("button",{type:"button",className:"text-sm text-cultural-600 hover:text-cultural-700",children:t("auth.forgotPassword")})}),f&&o.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md",children:o.jsx("p",{className:"text-sm text-red-600",children:f})}),o.jsx(we,{type:"submit",variant:"cultural",size:"lg",fullWidth:!0,isLoading:n,disabled:!x,children:t("auth.login")}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-0 flex items-center",children:o.jsx("div",{className:"w-full border-t border-gray-300"})}),o.jsx("div",{className:"relative flex justify-center text-sm",children:o.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(we,{type:"button",variant:"outline",size:"lg",fullWidth:!0,onClick:()=>W("google"),leftIcon:o.jsxs("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[o.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),o.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),o.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),o.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),children:t("auth.signInWith",{provider:"Google"})}),o.jsx(we,{type:"button",variant:"outline",size:"lg",fullWidth:!0,onClick:()=>W("facebook"),leftIcon:o.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),children:t("auth.signInWith",{provider:"Facebook"})})]}),o.jsx("div",{className:"text-center",children:o.jsxs("span",{className:"text-sm text-gray-600",children:[t("auth.dontHaveAccount")," ",o.jsx("button",{type:"button",onClick:r,className:"text-cultural-600 hover:text-cultural-700 font-medium",children:t("auth.register")})]})})]})})]})},Re=[{code:"en",name:"English",nativeName:"English",flag:"🇿🇦"},{code:"af",name:"Afrikaans",nativeName:"Afrikaans",flag:"🇿🇦"},{code:"zu",name:"Zulu",nativeName:"IsiZulu",flag:"🇿🇦"},{code:"xh",name:"Xhosa",nativeName:"IsiXhosa",flag:"🇿🇦"}],xr=()=>{const{i18n:e}=Ae(),[r,t]=b.useState(!1),a=Re.find(n=>n.code===e.language)||Re[0],c=n=>{e.changeLanguage(n),t(!1)};return o.jsxs("div",{className:"relative",children:[o.jsxs("button",{type:"button",className:he("flex items-center space-x-2 px-3 py-2 text-sm font-medium","text-gray-700 bg-white border border-gray-300 rounded-md","hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cultural-500","transition-colors duration-200"),onClick:()=>t(!r),"aria-expanded":r,"aria-haspopup":"listbox",children:[o.jsx(ur,{className:"w-4 h-4"}),o.jsx("span",{className:"hidden sm:inline",children:a.flag}),o.jsx("span",{className:"hidden md:inline",children:a.nativeName}),o.jsx(ir,{className:he("w-4 h-4 transition-transform duration-200",r&&"rotate-180")})]}),r&&o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"fixed inset-0 z-10",onClick:()=>t(!1)}),o.jsx("div",{className:"absolute right-0 z-20 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg",children:o.jsx("div",{className:"py-1",role:"listbox",children:Re.map(n=>o.jsxs("button",{type:"button",className:he("w-full px-4 py-2 text-left text-sm hover:bg-gray-100","focus:outline-none focus:bg-gray-100","flex items-center space-x-3",n.code===e.language&&"bg-cultural-50 text-cultural-700"),onClick:()=>c(n.code),role:"option","aria-selected":n.code===e.language,children:[o.jsx("span",{className:"text-lg",children:n.flag}),o.jsxs("div",{className:"flex-1",children:[o.jsx("div",{className:"font-medium",children:n.nativeName}),o.jsx("div",{className:"text-xs text-gray-500",children:n.name})]}),n.code===e.language&&o.jsx("div",{className:"w-2 h-2 bg-cultural-500 rounded-full"})]},n.code))})})]})]})},jr=()=>{const[e,r]=b.useState("register"),t=Ot(),{t:a}=Ae(),c=()=>{t(e==="register"?"/onboarding/cultural":"/")};return o.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-cultural-50 via-white to-ubuntu-50 flex flex-col",children:[o.jsxs("header",{className:"p-4 flex justify-between items-center",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-cultural-500 to-ubuntu-500 rounded-full"}),o.jsx("h1",{className:"text-xl font-bold text-cultural-gradient",children:a("app.name")})]}),o.jsx(xr,{})]}),o.jsx("main",{className:"flex-1 flex items-center justify-center p-4",children:o.jsx("div",{className:"w-full max-w-md",children:e==="register"?o.jsx(yr,{onSuccess:c,onSwitchToLogin:()=>r("login")}):o.jsx(gr,{onSuccess:c,onSwitchToRegister:()=>r("register")})})}),o.jsxs("footer",{className:"p-4 text-center text-sm text-gray-600",children:[o.jsx("p",{children:a("app.tagline")}),o.jsx("p",{className:"mt-1",children:"Building bridges across cultures • Celebrating diversity • Ubuntu philosophy"})]}),o.jsx("div",{id:"recaptcha-container"})]})};export{jr as default};
//# sourceMappingURL=AuthPage-Chhb6i1V.js.map
