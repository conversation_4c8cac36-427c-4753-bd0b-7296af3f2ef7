{"version": 3, "file": "CulturalKnowledgePage-BnYvObkg.js", "sources": ["../../src/services/culturalHeritageService.ts", "../../src/features/cultural-heritage/components/CulturalContentCreator.tsx", "../../src/services/knowledgeSharingService.ts", "../../src/features/knowledge-sharing/components/SkillSharingMarketplace.tsx", "../../src/services/contentDiscoveryService.ts", "../../src/features/content-discovery/components/PersonalizedDashboard.tsx", "../../src/pages/CulturalKnowledgePage.tsx"], "sourcesContent": ["import { \n  collection, \n  doc, \n  setDoc, \n  getDoc, \n  getDocs, \n  query, \n  where, \n  orderBy, \n  limit, \n  updateDoc,\n  arrayUnion,\n  arrayRemove,\n  Timestamp,\n  writeBatch\n} from 'firebase/firestore'\nimport { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage'\nimport { db, storage } from './firebase'\nimport { culturalValidationService } from './culturalValidationService'\n\nexport interface MediaFile {\n  id: string\n  url: string\n  filename: string\n  size: number\n  type: string\n  uploadedAt: Timestamp\n  metadata?: Record<string, any>\n}\n\nexport interface CulturalHeritageContent {\n  id: string\n  title: string\n  description: string\n  type: 'story' | 'tradition' | 'artifact' | 'recipe' | 'music' | 'dance' | 'language' | 'history'\n  culturalGroup: string[]\n  region: string\n  media: {\n    images: MediaFile[]\n    videos: MediaFile[]\n    audio: MediaFile[]\n    documents: MediaFile[]\n  }\n  metadata: {\n    creator: string // User ID\n    contributors: string[] // User IDs\n    culturalRepresentativeApproval?: string // Representative ID\n    dateCreated: Timestamp\n    lastModified: Timestamp\n    provenance: string // Source and historical context\n    culturalSignificance: 'public' | 'community_only' | 'sacred' | 'restricted'\n    tags: string[]\n    language: string\n    region: string\n  }\n  validation: {\n    status: 'draft' | 'pending_review' | 'approved' | 'rejected' | 'needs_revision'\n    reviewedBy: string[] // Cultural representative IDs\n    reviewNotes: string[]\n    communityEndorsements: number\n    accuracyScore: number // 0-100\n  }\n  preservation: {\n    archivalQuality: boolean\n    backupLocations: string[]\n    digitalPreservationMetadata: Record<string, any>\n    culturalContextPreserved: boolean\n  }\n  engagement: {\n    views: number\n    likes: number\n    shares: number\n    comments: number\n    culturalLearningInteractions: number\n  }\n}\n\nexport interface OralTraditionStory {\n  id: string\n  title: string\n  culturalGroup: string\n  storyteller: {\n    userId: string\n    name: string\n    culturalCredibility: number\n    approvedBy?: string // Cultural representative\n  }\n  story: {\n    audioRecording?: MediaFile\n    videoRecording?: MediaFile\n    transcription: string\n    translation?: Record<string, string> // language -> translation\n  }\n  culturalContext: {\n    significance: string\n    traditionalSetting: string\n    culturalLessons: string[]\n    relatedTraditions: string[]\n    appropriateAudience: 'all' | 'adults_only' | 'community_members' | 'cultural_group_only'\n  }\n  preservation: {\n    originalLanguage: string\n    dialectVariations: string[]\n    historicalAccuracy: number // 0-100\n    culturalAuthenticity: number // 0-100\n  }\n}\n\nclass CulturalHeritageService {\n  // Create new cultural heritage content\n  async createHeritageContent(content: Omit<CulturalHeritageContent, 'id' | 'engagement'>): Promise<string> {\n    try {\n      const contentId = `heritage-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      // Validate cultural sensitivity\n      const validation = await culturalValidationService.validateCulturalContent({\n        id: contentId,\n        title: content.title,\n        content: content.description,\n        type: content.type as any,\n        culture: content.culturalGroup[0],\n        media: content.media,\n        author: {\n          userId: content.metadata.creator,\n          culturalCredibility: 'community_member',\n        },\n        verification: {\n          status: 'pending',\n          reviewedBy: [],\n          reviewNotes: [],\n          approvedAt: null,\n        },\n        engagement: {\n          views: 0,\n          likes: 0,\n          shares: 0,\n          comments: 0,\n          crossCulturalViews: 0,\n        },\n        createdAt: content.metadata.dateCreated,\n        lastModified: content.metadata.lastModified,\n      })\n\n      const heritageContent: CulturalHeritageContent = {\n        ...content,\n        id: contentId,\n        validation: {\n          status: validation.requiresReview ? 'pending_review' : 'draft',\n          reviewedBy: [],\n          reviewNotes: validation.concerns,\n          communityEndorsements: 0,\n          accuracyScore: 0,\n        },\n        engagement: {\n          views: 0,\n          likes: 0,\n          shares: 0,\n          comments: 0,\n          culturalLearningInteractions: 0,\n        },\n      }\n\n      await setDoc(doc(db, 'cultural-heritage', contentId), heritageContent)\n\n      // Notify cultural representatives if review required\n      if (validation.requiresReview) {\n        await this.notifyCulturalRepresentatives(content.culturalGroup, contentId)\n      }\n\n      return contentId\n    } catch (error) {\n      console.error('Error creating heritage content:', error)\n      throw new Error('Failed to create heritage content')\n    }\n  }\n\n  // Upload media files for heritage content\n  async uploadMediaFile(\n    file: File, \n    contentId: string, \n    mediaType: 'images' | 'videos' | 'audio' | 'documents'\n  ): Promise<MediaFile> {\n    try {\n      const fileId = `${contentId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      const storageRef = ref(storage, `cultural-heritage/${mediaType}/${fileId}`)\n      \n      const snapshot = await uploadBytes(storageRef, file)\n      const downloadURL = await getDownloadURL(snapshot.ref)\n\n      const mediaFile: MediaFile = {\n        id: fileId,\n        url: downloadURL,\n        filename: file.name,\n        size: file.size,\n        type: file.type,\n        uploadedAt: Timestamp.now(),\n        metadata: {\n          originalName: file.name,\n          uploadedBy: 'current-user', // TODO: Get from auth context\n        },\n      }\n\n      // Update content document with new media file\n      const contentRef = doc(db, 'cultural-heritage', contentId)\n      await updateDoc(contentRef, {\n        [`media.${mediaType}`]: arrayUnion(mediaFile),\n        'metadata.lastModified': Timestamp.now(),\n      })\n\n      return mediaFile\n    } catch (error) {\n      console.error('Error uploading media file:', error)\n      throw new Error('Failed to upload media file')\n    }\n  }\n\n  // Create oral tradition story\n  async createOralTraditionStory(story: Omit<OralTraditionStory, 'id'>): Promise<string> {\n    try {\n      const storyId = `story-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      const oralStory: OralTraditionStory = {\n        ...story,\n        id: storyId,\n      }\n\n      await setDoc(doc(db, 'oral-traditions', storyId), oralStory)\n\n      // Create corresponding heritage content entry\n      const heritageContent: Omit<CulturalHeritageContent, 'id' | 'engagement'> = {\n        title: story.title,\n        description: story.story.transcription,\n        type: 'story',\n        culturalGroup: [story.culturalGroup],\n        region: 'south-africa', // TODO: Get from user profile\n        media: {\n          images: [],\n          videos: story.story.videoRecording ? [story.story.videoRecording] : [],\n          audio: story.story.audioRecording ? [story.story.audioRecording] : [],\n          documents: [],\n        },\n        metadata: {\n          creator: story.storyteller.userId,\n          contributors: [],\n          dateCreated: Timestamp.now(),\n          lastModified: Timestamp.now(),\n          provenance: `Oral tradition from ${story.culturalGroup}`,\n          culturalSignificance: story.culturalContext.appropriateAudience === 'all' ? 'public' : 'community_only',\n          tags: ['oral-tradition', 'story', story.culturalGroup],\n          language: story.preservation.originalLanguage,\n          region: 'south-africa',\n        },\n        validation: {\n          status: 'pending_review',\n          reviewedBy: [],\n          reviewNotes: [],\n          communityEndorsements: 0,\n          accuracyScore: story.preservation.historicalAccuracy,\n        },\n        preservation: {\n          archivalQuality: true,\n          backupLocations: [],\n          digitalPreservationMetadata: {\n            originalLanguage: story.preservation.originalLanguage,\n            dialectVariations: story.preservation.dialectVariations,\n            culturalAuthenticity: story.preservation.culturalAuthenticity,\n          },\n          culturalContextPreserved: true,\n        },\n      }\n\n      await this.createHeritageContent(heritageContent)\n\n      return storyId\n    } catch (error) {\n      console.error('Error creating oral tradition story:', error)\n      throw new Error('Failed to create oral tradition story')\n    }\n  }\n\n  // Get heritage content by cultural group\n  async getHeritageContentByCulture(\n    culturalGroup: string, \n    limit_count: number = 20\n  ): Promise<CulturalHeritageContent[]> {\n    try {\n      const q = query(\n        collection(db, 'cultural-heritage'),\n        where('culturalGroup', 'array-contains', culturalGroup),\n        where('validation.status', '==', 'approved'),\n        orderBy('engagement.views', 'desc'),\n        limit(limit_count)\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as CulturalHeritageContent)\n    } catch (error) {\n      console.error('Error getting heritage content by culture:', error)\n      throw new Error('Failed to get heritage content')\n    }\n  }\n\n  // Approve heritage content (cultural representative action)\n  async approveHeritageContent(\n    contentId: string, \n    representativeId: string, \n    reviewNotes?: string\n  ): Promise<void> {\n    try {\n      const contentRef = doc(db, 'cultural-heritage', contentId)\n      \n      await updateDoc(contentRef, {\n        'validation.status': 'approved',\n        'validation.reviewedBy': arrayUnion(representativeId),\n        'validation.reviewNotes': reviewNotes ? arrayUnion(reviewNotes) : [],\n        'metadata.culturalRepresentativeApproval': representativeId,\n        'metadata.lastModified': Timestamp.now(),\n      })\n\n      // TODO: Notify content creator of approval\n    } catch (error) {\n      console.error('Error approving heritage content:', error)\n      throw new Error('Failed to approve heritage content')\n    }\n  }\n\n  // Search heritage content\n  async searchHeritageContent(\n    searchTerm: string,\n    filters?: {\n      type?: string\n      culturalGroup?: string\n      region?: string\n      language?: string\n    }\n  ): Promise<CulturalHeritageContent[]> {\n    try {\n      // TODO: Implement full-text search with Algolia or similar\n      // For now, basic filtering\n      let q = query(\n        collection(db, 'cultural-heritage'),\n        where('validation.status', '==', 'approved')\n      )\n\n      if (filters?.type) {\n        q = query(q, where('type', '==', filters.type))\n      }\n\n      if (filters?.culturalGroup) {\n        q = query(q, where('culturalGroup', 'array-contains', filters.culturalGroup))\n      }\n\n      const snapshot = await getDocs(q)\n      const results = snapshot.docs.map(doc => doc.data() as CulturalHeritageContent)\n\n      // Filter by search term in title and description\n      return results.filter(content => \n        content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        content.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        content.metadata.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n      )\n    } catch (error) {\n      console.error('Error searching heritage content:', error)\n      throw new Error('Failed to search heritage content')\n    }\n  }\n\n  // Private helper methods\n  private async notifyCulturalRepresentatives(culturalGroups: string[], contentId: string): Promise<void> {\n    // TODO: Implement notification system for cultural representatives\n    console.log(`Notifying cultural representatives for groups: ${culturalGroups.join(', ')} about content: ${contentId}`)\n  }\n}\n\nexport const culturalHeritageService = new CulturalHeritageService()\n", "import React, { useState, useRef } from 'react'\nimport { Timestamp } from 'firebase/firestore'\nimport { culturalHeritageService, CulturalHeritageContent, MediaFile } from '../../../services/culturalHeritageService'\nimport { useAuth } from '../../auth/hooks/useAuth'\n\ninterface CulturalContentCreatorProps {\n  onContentCreated?: (contentId: string) => void\n  onCancel?: () => void\n}\n\nexport const CulturalContentCreator: React.FC<CulturalContentCreatorProps> = ({\n  onContentCreated,\n  onCancel,\n}) => {\n  const { user } = useAuth()\n  const [currentStep, setCurrentStep] = useState(1)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({})\n  \n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    type: 'story' as CulturalHeritageContent['type'],\n    culturalGroup: [] as string[],\n    region: '',\n    provenance: '',\n    culturalSignificance: 'public' as CulturalHeritageContent['metadata']['culturalSignificance'],\n    tags: [] as string[],\n    language: 'en',\n  })\n\n  const [mediaFiles, setMediaFiles] = useState<{\n    images: File[]\n    videos: File[]\n    audio: File[]\n    documents: File[]\n  }>({\n    images: [],\n    videos: [],\n    audio: [],\n    documents: [],\n  })\n\n  const fileInputRefs = {\n    images: useRef<HTMLInputElement>(null),\n    videos: useRef<HTMLInputElement>(null),\n    audio: useRef<HTMLInputElement>(null),\n    documents: useRef<HTMLInputElement>(null),\n  }\n\n  const culturalGroups = [\n    'zulu', 'xhosa', 'afrikaans', 'english', 'sotho', 'tswana', \n    'tsonga', 'swati', 'venda', 'ndebele', 'coloured'\n  ]\n\n  const contentTypes = [\n    { value: 'story', label: 'Story/Oral Tradition' },\n    { value: 'tradition', label: 'Cultural Tradition' },\n    { value: 'artifact', label: 'Cultural Artifact' },\n    { value: 'recipe', label: 'Traditional Recipe' },\n    { value: 'music', label: 'Music/Song' },\n    { value: 'dance', label: 'Dance/Performance' },\n    { value: 'language', label: 'Language/Dialect' },\n    { value: 'history', label: 'Historical Account' },\n  ]\n\n  const handleInputChange = (field: string, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n  }\n\n  const handleFileSelection = (mediaType: keyof typeof mediaFiles, files: FileList | null) => {\n    if (!files) return\n\n    const fileArray = Array.from(files)\n    setMediaFiles(prev => ({\n      ...prev,\n      [mediaType]: [...prev[mediaType], ...fileArray],\n    }))\n  }\n\n  const removeFile = (mediaType: keyof typeof mediaFiles, index: number) => {\n    setMediaFiles(prev => ({\n      ...prev,\n      [mediaType]: prev[mediaType].filter((_, i) => i !== index),\n    }))\n  }\n\n  const handleSubmit = async () => {\n    if (!user) return\n\n    setIsSubmitting(true)\n    try {\n      // Create the heritage content\n      const contentData: Omit<CulturalHeritageContent, 'id' | 'engagement'> = {\n        title: formData.title,\n        description: formData.description,\n        type: formData.type,\n        culturalGroup: formData.culturalGroup,\n        region: formData.region,\n        media: {\n          images: [],\n          videos: [],\n          audio: [],\n          documents: [],\n        },\n        metadata: {\n          creator: user.uid,\n          contributors: [],\n          dateCreated: Timestamp.now(),\n          lastModified: Timestamp.now(),\n          provenance: formData.provenance,\n          culturalSignificance: formData.culturalSignificance,\n          tags: formData.tags,\n          language: formData.language,\n          region: formData.region,\n        },\n        validation: {\n          status: 'draft',\n          reviewedBy: [],\n          reviewNotes: [],\n          communityEndorsements: 0,\n          accuracyScore: 0,\n        },\n        preservation: {\n          archivalQuality: true,\n          backupLocations: [],\n          digitalPreservationMetadata: {},\n          culturalContextPreserved: true,\n        },\n      }\n\n      const contentId = await culturalHeritageService.createHeritageContent(contentData)\n\n      // Upload media files\n      const mediaTypes = Object.keys(mediaFiles) as Array<keyof typeof mediaFiles>\n      for (const mediaType of mediaTypes) {\n        const files = mediaFiles[mediaType]\n        for (let i = 0; i < files.length; i++) {\n          const file = files[i]\n          setUploadProgress(prev => ({ ...prev, [`${mediaType}-${i}`]: 0 }))\n          \n          try {\n            await culturalHeritageService.uploadMediaFile(file, contentId, mediaType)\n            setUploadProgress(prev => ({ ...prev, [`${mediaType}-${i}`]: 100 }))\n          } catch (error) {\n            console.error(`Error uploading ${mediaType} file:`, error)\n          }\n        }\n      }\n\n      onContentCreated?.(contentId)\n    } catch (error) {\n      console.error('Error creating cultural content:', error)\n      alert('Failed to create cultural content. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Basic Information</h3>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Title *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.title}\n                onChange={(e) => handleInputChange('title', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n                placeholder=\"Enter a descriptive title\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Content Type *\n              </label>\n              <select\n                value={formData.type}\n                onChange={(e) => handleInputChange('type', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n                required\n              >\n                {contentTypes.map(type => (\n                  <option key={type.value} value={type.value}>\n                    {type.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Cultural Group(s) *\n              </label>\n              <div className=\"grid grid-cols-2 gap-2\">\n                {culturalGroups.map(group => (\n                  <label key={group} className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={formData.culturalGroup.includes(group)}\n                      onChange={(e) => {\n                        if (e.target.checked) {\n                          handleInputChange('culturalGroup', [...formData.culturalGroup, group])\n                        } else {\n                          handleInputChange('culturalGroup', formData.culturalGroup.filter(g => g !== group))\n                        }\n                      }}\n                      className=\"mr-2\"\n                    />\n                    <span className=\"text-sm capitalize\">{group}</span>\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Description *\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n                placeholder=\"Provide a detailed description of this cultural content\"\n                required\n              />\n            </div>\n          </div>\n        )\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Cultural Context</h3>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Cultural Significance\n              </label>\n              <select\n                value={formData.culturalSignificance}\n                onChange={(e) => handleInputChange('culturalSignificance', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n              >\n                <option value=\"public\">Public - Can be shared openly</option>\n                <option value=\"community_only\">Community Only - Restricted to cultural community</option>\n                <option value=\"sacred\">Sacred - Requires special permissions</option>\n                <option value=\"restricted\">Restricted - Limited access</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Source/Provenance *\n              </label>\n              <textarea\n                value={formData.provenance}\n                onChange={(e) => handleInputChange('provenance', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n                placeholder=\"Describe the source and historical context of this content\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Tags (comma-separated)\n              </label>\n              <input\n                type=\"text\"\n                value={formData.tags.join(', ')}\n                onChange={(e) => handleInputChange('tags', e.target.value.split(',').map(tag => tag.trim()).filter(Boolean))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n                placeholder=\"tradition, heritage, history, etc.\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Primary Language\n              </label>\n              <select\n                value={formData.language}\n                onChange={(e) => handleInputChange('language', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n              >\n                <option value=\"en\">English</option>\n                <option value=\"af\">Afrikaans</option>\n                <option value=\"zu\">Zulu</option>\n                <option value=\"xh\">Xhosa</option>\n                <option value=\"st\">Sotho</option>\n                <option value=\"tn\">Tswana</option>\n                <option value=\"ts\">Tsonga</option>\n                <option value=\"ss\">Swati</option>\n                <option value=\"ve\">Venda</option>\n                <option value=\"nr\">Ndebele</option>\n              </select>\n            </div>\n          </div>\n        )\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Media Files</h3>\n            \n            {Object.entries(fileInputRefs).map(([mediaType, ref]) => (\n              <div key={mediaType} className=\"border rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h4 className=\"font-medium capitalize\">{mediaType}</h4>\n                  <button\n                    type=\"button\"\n                    onClick={() => ref.current?.click()}\n                    className=\"px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600\"\n                  >\n                    Add {mediaType}\n                  </button>\n                </div>\n                \n                <input\n                  ref={ref}\n                  type=\"file\"\n                  multiple\n                  accept={\n                    mediaType === 'images' ? 'image/*' :\n                    mediaType === 'videos' ? 'video/*' :\n                    mediaType === 'audio' ? 'audio/*' :\n                    '.pdf,.doc,.docx,.txt'\n                  }\n                  onChange={(e) => handleFileSelection(mediaType as keyof typeof mediaFiles, e.target.files)}\n                  className=\"hidden\"\n                />\n                \n                <div className=\"space-y-2\">\n                  {mediaFiles[mediaType as keyof typeof mediaFiles].map((file, index) => (\n                    <div key={index} className=\"flex items-center justify-between bg-gray-50 p-2 rounded\">\n                      <span className=\"text-sm truncate\">{file.name}</span>\n                      <button\n                        type=\"button\"\n                        onClick={() => removeFile(mediaType as keyof typeof mediaFiles, index)}\n                        className=\"text-red-500 hover:text-red-700 text-sm\"\n                      >\n                        Remove\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        )\n\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Share Cultural Heritage</h2>\n        <p className=\"text-gray-600\">\n          Contribute to preserving and sharing South African cultural heritage with respect and authenticity.\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"flex items-center justify-between mb-8\">\n        {[1, 2, 3].map((step) => (\n          <div key={step} className=\"flex items-center\">\n            <div\n              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                step <= currentStep\n                  ? 'bg-orange-500 text-white'\n                  : 'bg-gray-200 text-gray-600'\n              }`}\n            >\n              {step}\n            </div>\n            {step < 3 && (\n              <div\n                className={`w-16 h-1 mx-2 ${\n                  step < currentStep ? 'bg-orange-500' : 'bg-gray-200'\n                }`}\n              />\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Step Content */}\n      {renderStepContent()}\n\n      {/* Navigation Buttons */}\n      <div className=\"flex justify-between mt-8\">\n        <div>\n          {currentStep > 1 && (\n            <button\n              type=\"button\"\n              onClick={() => setCurrentStep(prev => prev - 1)}\n              className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50\"\n              disabled={isSubmitting}\n            >\n              Previous\n            </button>\n          )}\n        </div>\n        \n        <div className=\"space-x-3\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50\"\n            disabled={isSubmitting}\n          >\n            Cancel\n          </button>\n          \n          {currentStep < 3 ? (\n            <button\n              type=\"button\"\n              onClick={() => setCurrentStep(prev => prev + 1)}\n              className=\"px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600\"\n              disabled={!formData.title || !formData.description || formData.culturalGroup.length === 0}\n            >\n              Next\n            </button>\n          ) : (\n            <button\n              type=\"button\"\n              onClick={handleSubmit}\n              disabled={isSubmitting || !formData.title || !formData.description || formData.culturalGroup.length === 0}\n              className=\"px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 disabled:opacity-50\"\n            >\n              {isSubmitting ? 'Creating...' : 'Create Content'}\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Cultural Sensitivity Notice */}\n      <div className=\"mt-6 p-4 bg-orange-50 border border-orange-200 rounded-md\">\n        <h4 className=\"font-medium text-orange-800 mb-2\">Cultural Sensitivity Guidelines</h4>\n        <ul className=\"text-sm text-orange-700 space-y-1\">\n          <li>• Ensure you have the right to share this cultural content</li>\n          <li>• Respect sacred and sensitive cultural information</li>\n          <li>• Provide accurate historical and cultural context</li>\n          <li>• Content will be reviewed by cultural representatives</li>\n          <li>• Follow Ubuntu principles of mutual respect and community benefit</li>\n        </ul>\n      </div>\n    </div>\n  )\n}\n\nexport default CulturalContentCreator\n", "import { \n  collection, \n  doc, \n  setDoc, \n  getDoc, \n  getDocs, \n  query, \n  where, \n  orderBy, \n  limit, \n  updateDoc,\n  arrayUnion,\n  Timestamp,\n  writeBatch\n} from 'firebase/firestore'\nimport { db } from './firebase'\nimport { culturalValidationService } from './culturalValidationService'\n\nexport interface SkillProfile {\n  id: string\n  userId: string\n  skills: {\n    [skillId: string]: {\n      name: string\n      level: 'beginner' | 'intermediate' | 'advanced' | 'expert'\n      culturalContext?: string\n      yearsExperience: number\n      certifications: string[]\n      endorsements: number\n      culturalSignificance?: string\n    }\n  }\n  culturalKnowledge: {\n    [cultureId: string]: {\n      level: 'basic' | 'intermediate' | 'advanced' | 'native'\n      areas: string[] // traditions, language, history, etc.\n      canTeach: boolean\n      canMentor: boolean\n      verifiedBy?: string // Cultural representative\n    }\n  }\n  availability: {\n    timeZone: string\n    preferredHours: string[]\n    maxHoursPerWeek: number\n    languages: string[]\n  }\n  ubuntuPhilosophy: {\n    beliefLevel: number // 0-100\n    practiceAreas: string[]\n    communityContributions: number\n    crossCulturalConnections: number\n  }\n}\n\nexport interface MentorshipMatch {\n  id: string\n  mentorId: string\n  menteeId: string\n  skillArea: string\n  culturalContext?: string\n  matchScore: number // 0-100\n  status: 'pending' | 'active' | 'completed' | 'paused' | 'cancelled'\n  goals: {\n    skillGoals: string[]\n    culturalLearningGoals: string[]\n    timeframe: string\n    successMetrics: string[]\n  }\n  progress: {\n    sessionsCompleted: number\n    skillProgress: number // 0-100\n    culturalUnderstanding: number // 0-100\n    mutualLearning: string[]\n  }\n  communication: {\n    preferredMethod: 'video' | 'audio' | 'text' | 'in_person'\n    frequency: 'weekly' | 'biweekly' | 'monthly'\n    language: string\n    translationNeeded: boolean\n  }\n  createdAt: Timestamp\n  lastActivity: Timestamp\n}\n\nexport interface CollaborativeProject {\n  id: string\n  title: string\n  description: string\n  type: 'cultural_exchange' | 'skill_development' | 'community_service' | 'business' | 'education'\n  culturalObjectives: {\n    bridgeBuildingGoals: string[]\n    culturalLearningOutcomes: string[]\n    diversityTargets: Record<string, number>\n  }\n  participants: {\n    [userId: string]: {\n      role: 'leader' | 'contributor' | 'learner' | 'cultural_advisor'\n      skills: string[]\n      culturalBackground: string[]\n      joinedAt: Timestamp\n      contributionScore: number\n    }\n  }\n  requirements: {\n    skillsNeeded: string[]\n    culturalPerspectivesNeeded: string[]\n    timeCommitment: string\n    duration: string\n  }\n  workspace: {\n    communicationChannels: string[]\n    sharedResources: string[]\n    meetingSchedule: string\n    culturalSensitivityGuidelines: string[]\n  }\n  progress: {\n    milestones: Array<{\n      id: string\n      title: string\n      completed: boolean\n      completedAt?: Timestamp\n      culturalLearningAchieved: string[]\n    }>\n    overallProgress: number // 0-100\n    crossCulturalInteractions: number\n    ubuntuPrinciplesApplied: string[]\n  }\n  status: 'planning' | 'active' | 'completed' | 'paused' | 'cancelled'\n  createdAt: Timestamp\n  lastActivity: Timestamp\n}\n\nexport interface CulturalLearningPath {\n  id: string\n  title: string\n  culturalGroup: string\n  description: string\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  modules: Array<{\n    id: string\n    title: string\n    type: 'content' | 'interaction' | 'practice' | 'assessment'\n    content: string\n    culturalContext: string\n    estimatedTime: number // minutes\n    prerequisites: string[]\n    learningObjectives: string[]\n    culturalSensitivityNotes: string[]\n  }>\n  prerequisites: {\n    culturalKnowledge: string[]\n    respectfulApproach: boolean\n    communityConnection: boolean\n  }\n  outcomes: {\n    skillsGained: string[]\n    culturalUnderstanding: string[]\n    bridgeBuildingCapabilities: string[]\n    communityContributions: string[]\n  }\n  validation: {\n    createdBy: string // Cultural representative or expert\n    reviewedBy: string[]\n    accuracyScore: number // 0-100\n    communityEndorsements: number\n  }\n  engagement: {\n    enrollments: number\n    completions: number\n    averageRating: number\n    culturalFeedback: string[]\n  }\n}\n\nclass KnowledgeSharingService {\n  // Create or update skill profile\n  async updateSkillProfile(profile: SkillProfile): Promise<void> {\n    try {\n      await setDoc(doc(db, 'skill-profiles', profile.userId), profile)\n    } catch (error) {\n      console.error('Error updating skill profile:', error)\n      throw new Error('Failed to update skill profile')\n    }\n  }\n\n  // Find mentorship matches\n  async findMentorshipMatches(\n    userId: string, \n    skillArea: string, \n    role: 'mentor' | 'mentee'\n  ): Promise<MentorshipMatch[]> {\n    try {\n      const userProfile = await this.getSkillProfile(userId)\n      if (!userProfile) throw new Error('User profile not found')\n\n      // Query for potential matches\n      const q = query(\n        collection(db, 'skill-profiles'),\n        where(`skills.${skillArea}`, '!=', null),\n        limit(20)\n      )\n\n      const snapshot = await getDocs(q)\n      const potentialMatches = snapshot.docs\n        .map(doc => doc.data() as SkillProfile)\n        .filter(profile => profile.userId !== userId)\n\n      // Calculate match scores and create matches\n      const matches: MentorshipMatch[] = []\n      \n      for (const match of potentialMatches) {\n        const matchScore = this.calculateMentorshipMatchScore(userProfile, match, skillArea, role)\n        \n        if (matchScore > 60) { // Minimum threshold\n          const mentorshipMatch: MentorshipMatch = {\n            id: `match-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n            mentorId: role === 'mentor' ? userId : match.userId,\n            menteeId: role === 'mentee' ? userId : match.userId,\n            skillArea,\n            culturalContext: this.findCommonCulturalContext(userProfile, match),\n            matchScore,\n            status: 'pending',\n            goals: {\n              skillGoals: [],\n              culturalLearningGoals: [],\n              timeframe: '3 months',\n              successMetrics: [],\n            },\n            progress: {\n              sessionsCompleted: 0,\n              skillProgress: 0,\n              culturalUnderstanding: 0,\n              mutualLearning: [],\n            },\n            communication: {\n              preferredMethod: 'video',\n              frequency: 'weekly',\n              language: 'en',\n              translationNeeded: false,\n            },\n            createdAt: Timestamp.now(),\n            lastActivity: Timestamp.now(),\n          }\n          \n          matches.push(mentorshipMatch)\n        }\n      }\n\n      return matches.sort((a, b) => b.matchScore - a.matchScore)\n    } catch (error) {\n      console.error('Error finding mentorship matches:', error)\n      throw new Error('Failed to find mentorship matches')\n    }\n  }\n\n  // Create collaborative project\n  async createCollaborativeProject(project: Omit<CollaborativeProject, 'id' | 'createdAt' | 'lastActivity'>): Promise<string> {\n    try {\n      const projectId = `project-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      const collaborativeProject: CollaborativeProject = {\n        ...project,\n        id: projectId,\n        createdAt: Timestamp.now(),\n        lastActivity: Timestamp.now(),\n      }\n\n      await setDoc(doc(db, 'collaborative-projects', projectId), collaborativeProject)\n\n      return projectId\n    } catch (error) {\n      console.error('Error creating collaborative project:', error)\n      throw new Error('Failed to create collaborative project')\n    }\n  }\n\n  // Join collaborative project\n  async joinCollaborativeProject(\n    projectId: string, \n    userId: string, \n    role: 'contributor' | 'learner' | 'cultural_advisor',\n    skills: string[],\n    culturalBackground: string[]\n  ): Promise<void> {\n    try {\n      const projectRef = doc(db, 'collaborative-projects', projectId)\n      \n      await updateDoc(projectRef, {\n        [`participants.${userId}`]: {\n          role,\n          skills,\n          culturalBackground,\n          joinedAt: Timestamp.now(),\n          contributionScore: 0,\n        },\n        lastActivity: Timestamp.now(),\n      })\n    } catch (error) {\n      console.error('Error joining collaborative project:', error)\n      throw new Error('Failed to join collaborative project')\n    }\n  }\n\n  // Create cultural learning path\n  async createCulturalLearningPath(learningPath: Omit<CulturalLearningPath, 'id' | 'engagement'>): Promise<string> {\n    try {\n      const pathId = `path-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      const culturalPath: CulturalLearningPath = {\n        ...learningPath,\n        id: pathId,\n        engagement: {\n          enrollments: 0,\n          completions: 0,\n          averageRating: 0,\n          culturalFeedback: [],\n        },\n      }\n\n      await setDoc(doc(db, 'cultural-learning-paths', pathId), culturalPath)\n\n      return pathId\n    } catch (error) {\n      console.error('Error creating cultural learning path:', error)\n      throw new Error('Failed to create cultural learning path')\n    }\n  }\n\n  // Get skill profile\n  async getSkillProfile(userId: string): Promise<SkillProfile | null> {\n    try {\n      const docRef = doc(db, 'skill-profiles', userId)\n      const docSnap = await getDoc(docRef)\n      \n      if (docSnap.exists()) {\n        return docSnap.data() as SkillProfile\n      }\n      \n      return null\n    } catch (error) {\n      console.error('Error getting skill profile:', error)\n      throw new Error('Failed to get skill profile')\n    }\n  }\n\n  // Get collaborative projects by user\n  async getUserCollaborativeProjects(userId: string): Promise<CollaborativeProject[]> {\n    try {\n      const q = query(\n        collection(db, 'collaborative-projects'),\n        where(`participants.${userId}`, '!=', null),\n        orderBy('lastActivity', 'desc')\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as CollaborativeProject)\n    } catch (error) {\n      console.error('Error getting user collaborative projects:', error)\n      throw new Error('Failed to get user collaborative projects')\n    }\n  }\n\n  // Get cultural learning paths by culture\n  async getCulturalLearningPaths(culturalGroup?: string): Promise<CulturalLearningPath[]> {\n    try {\n      let q = query(\n        collection(db, 'cultural-learning-paths'),\n        orderBy('engagement.enrollments', 'desc')\n      )\n\n      if (culturalGroup) {\n        q = query(q, where('culturalGroup', '==', culturalGroup))\n      }\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as CulturalLearningPath)\n    } catch (error) {\n      console.error('Error getting cultural learning paths:', error)\n      throw new Error('Failed to get cultural learning paths')\n    }\n  }\n\n  // Private helper methods\n  private calculateMentorshipMatchScore(\n    user: SkillProfile, \n    potential: SkillProfile, \n    skillArea: string, \n    userRole: 'mentor' | 'mentee'\n  ): number {\n    let score = 0\n\n    // Skill level compatibility\n    const userSkill = user.skills[skillArea]\n    const potentialSkill = potential.skills[skillArea]\n    \n    if (userSkill && potentialSkill) {\n      const skillLevels = ['beginner', 'intermediate', 'advanced', 'expert']\n      const userLevel = skillLevels.indexOf(userSkill.level)\n      const potentialLevel = skillLevels.indexOf(potentialSkill.level)\n      \n      if (userRole === 'mentor' && userLevel > potentialLevel) {\n        score += 30\n      } else if (userRole === 'mentee' && potentialLevel > userLevel) {\n        score += 30\n      }\n    }\n\n    // Cultural compatibility\n    const commonCultures = Object.keys(user.culturalKnowledge).filter(\n      culture => potential.culturalKnowledge[culture]\n    )\n    score += Math.min(commonCultures.length * 10, 30)\n\n    // Ubuntu philosophy alignment\n    const ubuntuAlignment = Math.abs(user.ubuntuPhilosophy.beliefLevel - potential.ubuntuPhilosophy.beliefLevel)\n    score += Math.max(0, 20 - ubuntuAlignment / 5)\n\n    // Availability compatibility\n    const commonLanguages = user.availability.languages.filter(\n      lang => potential.availability.languages.includes(lang)\n    )\n    score += Math.min(commonLanguages.length * 5, 20)\n\n    return Math.min(score, 100)\n  }\n\n  private findCommonCulturalContext(user: SkillProfile, potential: SkillProfile): string | undefined {\n    const commonCultures = Object.keys(user.culturalKnowledge).filter(\n      culture => potential.culturalKnowledge[culture]\n    )\n    \n    return commonCultures.length > 0 ? commonCultures[0] : undefined\n  }\n}\n\nexport const knowledgeSharingService = new KnowledgeSharingService()\n", "import React, { useState, useEffect } from 'react'\nimport { knowledgeSharingService, SkillProfile, MentorshipMatch } from '../../../services/knowledgeSharingService'\nimport { useAuth } from '../../auth/hooks/useAuth'\n\ninterface SkillSharingMarketplaceProps {\n  onMentorshipRequest?: (matchId: string) => void\n}\n\nexport const SkillSharingMarketplace: React.FC<SkillSharingMarketplaceProps> = ({\n  onMentorshipRequest,\n}) => {\n  const { user } = useAuth()\n  const [activeTab, setActiveTab] = useState<'browse' | 'mentor' | 'learn'>('browse')\n  const [skillProfiles, setSkillProfiles] = useState<SkillProfile[]>([])\n  const [mentorshipMatches, setMentorshipMatches] = useState<MentorshipMatch[]>([])\n  const [selectedSkill, setSelectedSkill] = useState('')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [loading, setLoading] = useState(false)\n\n  const skillCategories = [\n    'Technology', 'Business', 'Arts & Crafts', 'Languages', 'Cooking',\n    'Music', 'Traditional Skills', 'Agriculture', 'Healthcare', 'Education'\n  ]\n\n  const culturalSkills = [\n    'Traditional Beadwork', 'Pottery Making', 'Traditional Cooking',\n    'Storytelling', 'Traditional Music', 'Dance', 'Language Teaching',\n    'Cultural History', 'Traditional Medicine Knowledge', 'Craft Making'\n  ]\n\n  useEffect(() => {\n    if (user) {\n      loadUserData()\n    }\n  }, [user])\n\n  const loadUserData = async () => {\n    if (!user) return\n    \n    setLoading(true)\n    try {\n      // Load user's skill profile\n      const profile = await knowledgeSharingService.getSkillProfile(user.uid)\n      if (profile) {\n        // Load mentorship matches for user's skills\n        const skills = Object.keys(profile.skills)\n        if (skills.length > 0) {\n          const matches = await knowledgeSharingService.findMentorshipMatches(\n            user.uid,\n            skills[0],\n            'mentor'\n          )\n          setMentorshipMatches(matches)\n        }\n      }\n    } catch (error) {\n      console.error('Error loading user data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSkillSearch = async (skill: string, role: 'mentor' | 'mentee') => {\n    if (!user || !skill) return\n\n    setLoading(true)\n    try {\n      const matches = await knowledgeSharingService.findMentorshipMatches(\n        user.uid,\n        skill,\n        role\n      )\n      setMentorshipMatches(matches)\n    } catch (error) {\n      console.error('Error searching for mentorship matches:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const renderBrowseTab = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <div className=\"flex-1\">\n          <input\n            type=\"text\"\n            placeholder=\"Search skills, cultural knowledge, or expertise...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"\n          />\n        </div>\n        <select\n          value={selectedSkill}\n          onChange={(e) => setSelectedSkill(e.target.value)}\n          className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"\n        >\n          <option value=\"\">All Skills</option>\n          {skillCategories.map(category => (\n            <option key={category} value={category}>{category}</option>\n          ))}\n        </select>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {/* Featured Cultural Skills */}\n        <div className=\"col-span-full\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Featured Cultural Skills</h3>\n          <div className=\"grid grid-cols-2 md:grid-cols-5 gap-3\">\n            {culturalSkills.map(skill => (\n              <button\n                key={skill}\n                onClick={() => handleSkillSearch(skill, 'mentee')}\n                className=\"p-3 bg-orange-50 border border-orange-200 rounded-lg text-sm text-orange-800 hover:bg-orange-100 transition-colors\"\n              >\n                {skill}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Mentorship Matches */}\n        {mentorshipMatches.map(match => (\n          <div key={match.id} className=\"bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow\">\n            <div className=\"flex items-start justify-between mb-4\">\n              <div>\n                <h4 className=\"font-semibold text-gray-900\">{match.skillArea}</h4>\n                {match.culturalContext && (\n                  <span className=\"text-sm text-orange-600 bg-orange-50 px-2 py-1 rounded mt-1 inline-block\">\n                    {match.culturalContext} Cultural Context\n                  </span>\n                )}\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-sm font-medium text-green-600\">\n                  {match.matchScore}% Match\n                </div>\n              </div>\n            </div>\n\n            <div className=\"space-y-2 mb-4\">\n              <div className=\"text-sm text-gray-600\">\n                <strong>Goals:</strong> {match.goals.skillGoals.join(', ') || 'Not specified'}\n              </div>\n              {match.goals.culturalLearningGoals.length > 0 && (\n                <div className=\"text-sm text-gray-600\">\n                  <strong>Cultural Learning:</strong> {match.goals.culturalLearningGoals.join(', ')}\n                </div>\n              )}\n              <div className=\"text-sm text-gray-600\">\n                <strong>Communication:</strong> {match.communication.preferredMethod} • {match.communication.frequency}\n              </div>\n            </div>\n\n            <div className=\"flex gap-2\">\n              <button\n                onClick={() => onMentorshipRequest?.(match.id)}\n                className=\"flex-1 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm\"\n              >\n                Connect\n              </button>\n              <button className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm\">\n                Learn More\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n\n  const renderMentorTab = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-orange-800 mb-2\">Share Your Knowledge</h3>\n        <p className=\"text-orange-700 mb-4\">\n          Embody Ubuntu philosophy by sharing your skills and cultural knowledge with others. \n          Help build bridges across communities through mentorship.\n        </p>\n        <button className=\"px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors\">\n          Set Up Mentor Profile\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n          <h4 className=\"font-semibold text-gray-900 mb-3\">Skills You Can Teach</h4>\n          <div className=\"space-y-2\">\n            {skillCategories.slice(0, 5).map(skill => (\n              <div key={skill} className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700\">{skill}</span>\n                <button\n                  onClick={() => handleSkillSearch(skill, 'mentor')}\n                  className=\"text-sm text-orange-600 hover:text-orange-800\"\n                >\n                  Find Learners\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n          <h4 className=\"font-semibold text-gray-900 mb-3\">Cultural Knowledge</h4>\n          <div className=\"space-y-2\">\n            {culturalSkills.slice(0, 5).map(skill => (\n              <div key={skill} className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700\">{skill}</span>\n                <button\n                  onClick={() => handleSkillSearch(skill, 'mentor')}\n                  className=\"text-sm text-orange-600 hover:text-orange-800\"\n                >\n                  Share Knowledge\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Active Mentorships */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Your Active Mentorships</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {mentorshipMatches.filter(m => m.status === 'active').map(match => (\n            <div key={match.id} className=\"bg-white border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h4 className=\"font-medium text-gray-900\">{match.skillArea}</h4>\n                <span className=\"text-sm text-green-600 bg-green-50 px-2 py-1 rounded\">\n                  Active\n                </span>\n              </div>\n              <div className=\"text-sm text-gray-600 mb-3\">\n                Progress: {match.progress.skillProgress}% • {match.progress.sessionsCompleted} sessions\n              </div>\n              <div className=\"flex gap-2\">\n                <button className=\"flex-1 px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600\">\n                  Schedule Session\n                </button>\n                <button className=\"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50\">\n                  View Progress\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderLearnTab = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-blue-800 mb-2\">Learn & Grow</h3>\n        <p className=\"text-blue-700 mb-4\">\n          Discover new skills and deepen your cultural understanding through mentorship \n          and collaborative learning experiences.\n        </p>\n        <button className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\">\n          Set Learning Goals\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n          <h4 className=\"font-semibold text-gray-900 mb-3\">Skills to Learn</h4>\n          <div className=\"space-y-2\">\n            {skillCategories.map(skill => (\n              <div key={skill} className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700\">{skill}</span>\n                <button\n                  onClick={() => handleSkillSearch(skill, 'mentee')}\n                  className=\"text-sm text-blue-600 hover:text-blue-800\"\n                >\n                  Find Mentors\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n          <h4 className=\"font-semibold text-gray-900 mb-3\">Cultural Learning</h4>\n          <div className=\"space-y-2\">\n            {culturalSkills.map(skill => (\n              <div key={skill} className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700\">{skill}</span>\n                <button\n                  onClick={() => handleSkillSearch(skill, 'mentee')}\n                  className=\"text-sm text-blue-600 hover:text-blue-800\"\n                >\n                  Learn More\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Learning Progress */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Your Learning Journey</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {mentorshipMatches.filter(m => m.status === 'active').map(match => (\n            <div key={match.id} className=\"bg-white border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h4 className=\"font-medium text-gray-900\">{match.skillArea}</h4>\n                <span className=\"text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded\">\n                  Learning\n                </span>\n              </div>\n              <div className=\"mb-3\">\n                <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n                  <span>Progress</span>\n                  <span>{match.progress.skillProgress}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div \n                    className=\"bg-blue-500 h-2 rounded-full\" \n                    style={{ width: `${match.progress.skillProgress}%` }}\n                  />\n                </div>\n              </div>\n              <div className=\"text-sm text-gray-600 mb-3\">\n                {match.progress.sessionsCompleted} sessions completed\n              </div>\n              <div className=\"flex gap-2\">\n                <button className=\"flex-1 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\">\n                  Continue Learning\n                </button>\n                <button className=\"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50\">\n                  View Details\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Knowledge Sharing Marketplace</h1>\n        <p className=\"text-gray-600\">\n          Connect, learn, and share knowledge across cultural boundaries. Embrace Ubuntu: \"I am because we are.\"\n        </p>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"flex space-x-8\">\n          {[\n            { id: 'browse', label: 'Browse Skills', icon: '🔍' },\n            { id: 'mentor', label: 'Mentor Others', icon: '🎓' },\n            { id: 'learn', label: 'Learn & Grow', icon: '📚' },\n          ].map(tab => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === tab.id\n                  ? 'border-orange-500 text-orange-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {loading ? (\n        <div className=\"flex justify-center items-center py-12\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500\"></div>\n        </div>\n      ) : (\n        <>\n          {activeTab === 'browse' && renderBrowseTab()}\n          {activeTab === 'mentor' && renderMentorTab()}\n          {activeTab === 'learn' && renderLearnTab()}\n        </>\n      )}\n    </div>\n  )\n}\n\nexport default SkillSharingMarketplace\n", "import { \n  collection, \n  doc, \n  setDoc, \n  getDoc, \n  getDocs, \n  query, \n  where, \n  orderBy, \n  limit, \n  updateDoc,\n  arrayUnion,\n  Timestamp,\n  writeBatch\n} from 'firebase/firestore'\nimport { db } from './firebase'\nimport { culturalValidationService } from './culturalValidationService'\nimport { CulturalHeritageContent } from './culturalHeritageService'\n\nexport interface ContentRecommendation {\n  id: string\n  userId: string\n  contentId: string\n  contentType: 'heritage' | 'learning_path' | 'project' | 'story' | 'community'\n  recommendationType: 'cultural_interest' | 'learning_goal' | 'trending' | 'bridge_building' | 'similar_users'\n  score: number // 0-100\n  reasoning: {\n    primaryFactors: string[]\n    culturalRelevance: string[]\n    learningOpportunities: string[]\n    bridgeBuildingPotential: string[]\n  }\n  culturalSensitivity: {\n    appropriateForUser: boolean\n    requiresContext: boolean\n    culturalPreparation?: string[]\n    respectfulApproach: string[]\n  }\n  createdAt: Timestamp\n  expiresAt: Timestamp\n  status: 'pending' | 'viewed' | 'engaged' | 'dismissed' | 'expired'\n}\n\nexport interface TrendingTopic {\n  id: string\n  topic: string\n  culturalGroup?: string\n  region?: string\n  type: 'heritage' | 'tradition' | 'event' | 'discussion' | 'learning'\n  engagement: {\n    views: number\n    interactions: number\n    shares: number\n    culturalParticipation: Record<string, number> // culture -> participation count\n  }\n  trendMetrics: {\n    growthRate: number // percentage\n    peakTime: Timestamp\n    duration: number // hours\n    crossCulturalAppeal: number // 0-100\n  }\n  content: {\n    relatedContentIds: string[]\n    keyTopics: string[]\n    culturalContext: string\n    sensitivityLevel: 'low' | 'medium' | 'high'\n  }\n  moderation: {\n    verified: boolean\n    culturallyAppropriate: boolean\n    reviewedBy: string[]\n    warnings?: string[]\n  }\n  createdAt: Timestamp\n  lastUpdated: Timestamp\n}\n\nexport interface PersonalizedDashboard {\n  userId: string\n  culturalLearningProgress: {\n    [cultureId: string]: {\n      level: number // 0-100\n      completedPaths: string[]\n      currentPaths: string[]\n      achievements: string[]\n      nextRecommendations: string[]\n    }\n  }\n  contentInteractions: {\n    viewedContent: string[]\n    likedContent: string[]\n    sharedContent: string[]\n    createdContent: string[]\n    bookmarkedContent: string[]\n  }\n  bridgeBuildingMetrics: {\n    crossCulturalConnections: number\n    culturalBridgeScore: number // 0-100\n    mentorshipParticipation: number\n    collaborativeProjects: number\n    communityContributions: number\n  }\n  recommendations: {\n    dailyRecommendations: ContentRecommendation[]\n    weeklyGoals: string[]\n    culturalChallenges: string[]\n    learningOpportunities: string[]\n  }\n  preferences: {\n    contentTypes: string[]\n    culturalInterests: string[]\n    learningGoals: string[]\n    notificationSettings: Record<string, boolean>\n  }\n  lastUpdated: Timestamp\n}\n\nexport interface ContentModerationResult {\n  contentId: string\n  moderationType: 'ai_initial' | 'community_review' | 'cultural_representative' | 'expert_review'\n  result: 'approved' | 'rejected' | 'needs_revision' | 'needs_cultural_review'\n  concerns: string[]\n  recommendations: string[]\n  culturalSensitivity: {\n    appropriationRisk: number // 0-100\n    culturalAccuracy: number // 0-100\n    respectfulPresentation: number // 0-100\n    communityBenefit: number // 0-100\n  }\n  moderatorId?: string\n  moderatedAt: Timestamp\n  reviewNotes?: string\n}\n\nclass ContentDiscoveryService {\n  // Generate personalized content recommendations\n  async generatePersonalizedRecommendations(userId: string, limit_count: number = 10): Promise<ContentRecommendation[]> {\n    try {\n      // Get user profile and preferences\n      const userDashboard = await this.getPersonalizedDashboard(userId)\n      if (!userDashboard) {\n        throw new Error('User dashboard not found')\n      }\n\n      const recommendations: ContentRecommendation[] = []\n\n      // Cultural interest-based recommendations\n      for (const culture of userDashboard.preferences.culturalInterests) {\n        const culturalContent = await this.getContentByCulture(culture, 3)\n        \n        for (const content of culturalContent) {\n          const recommendation = await this.createContentRecommendation(\n            userId,\n            content.id,\n            'heritage',\n            'cultural_interest',\n            culture\n          )\n          recommendations.push(recommendation)\n        }\n      }\n\n      // Learning goal-based recommendations\n      const learningRecommendations = await this.generateLearningRecommendations(userId, userDashboard)\n      recommendations.push(...learningRecommendations)\n\n      // Trending content recommendations\n      const trendingRecommendations = await this.generateTrendingRecommendations(userId, 3)\n      recommendations.push(...trendingRecommendations)\n\n      // Bridge-building opportunities\n      const bridgeRecommendations = await this.generateBridgeBuildingRecommendations(userId, 2)\n      recommendations.push(...bridgeRecommendations)\n\n      // Sort by score and return top recommendations\n      const sortedRecommendations = recommendations\n        .sort((a, b) => b.score - a.score)\n        .slice(0, limit_count)\n\n      // Save recommendations to user dashboard\n      await this.saveRecommendationsToUser(userId, sortedRecommendations)\n\n      return sortedRecommendations\n    } catch (error) {\n      console.error('Error generating personalized recommendations:', error)\n      throw new Error('Failed to generate personalized recommendations')\n    }\n  }\n\n  // Discover trending cultural topics\n  async discoverTrendingTopics(region?: string, culturalGroup?: string): Promise<TrendingTopic[]> {\n    try {\n      let q = query(\n        collection(db, 'trending-topics'),\n        where('moderation.verified', '==', true),\n        orderBy('trendMetrics.growthRate', 'desc'),\n        limit(20)\n      )\n\n      if (region) {\n        q = query(q, where('region', '==', region))\n      }\n\n      if (culturalGroup) {\n        q = query(q, where('culturalGroup', '==', culturalGroup))\n      }\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as TrendingTopic)\n    } catch (error) {\n      console.error('Error discovering trending topics:', error)\n      throw new Error('Failed to discover trending topics')\n    }\n  }\n\n  // Moderate content for cultural sensitivity\n  async moderateContent(\n    contentId: string, \n    contentType: string, \n    content: any\n  ): Promise<ContentModerationResult> {\n    try {\n      // AI-based initial moderation\n      const aiModeration = await this.performAIModeration(content)\n      \n      // Cultural sensitivity analysis\n      const culturalAnalysis = await this.analyzeCulturalSensitivity(content)\n      \n      // Determine if cultural representative review is needed\n      const needsCulturalReview = \n        aiModeration.culturalSensitivity.appropriationRisk > 30 ||\n        culturalAnalysis.requiresReview ||\n        content.metadata?.culturalSignificance === 'sacred'\n\n      const moderationResult: ContentModerationResult = {\n        contentId,\n        moderationType: 'ai_initial',\n        result: needsCulturalReview ? 'needs_cultural_review' : \n                aiModeration.isValid ? 'approved' : 'needs_revision',\n        concerns: aiModeration.concerns,\n        recommendations: aiModeration.recommendations,\n        culturalSensitivity: {\n          appropriationRisk: aiModeration.culturalSensitivity.appropriationRisk,\n          culturalAccuracy: culturalAnalysis.accuracyScore,\n          respectfulPresentation: aiModeration.culturalSensitivity.respectfulPresentation,\n          communityBenefit: this.calculateCommunityBenefit(content),\n        },\n        moderatedAt: Timestamp.now(),\n      }\n\n      // Save moderation result\n      await setDoc(doc(db, 'content-moderation', contentId), moderationResult)\n\n      // If needs cultural review, notify representatives\n      if (needsCulturalReview) {\n        await this.notifyCulturalRepresentatives(content.culturalGroup || [], contentId)\n      }\n\n      return moderationResult\n    } catch (error) {\n      console.error('Error moderating content:', error)\n      throw new Error('Failed to moderate content')\n    }\n  }\n\n  // Get or create personalized dashboard\n  async getPersonalizedDashboard(userId: string): Promise<PersonalizedDashboard | null> {\n    try {\n      const docRef = doc(db, 'personalized-dashboards', userId)\n      const docSnap = await getDoc(docRef)\n      \n      if (docSnap.exists()) {\n        return docSnap.data() as PersonalizedDashboard\n      }\n      \n      // Create default dashboard if doesn't exist\n      const defaultDashboard: PersonalizedDashboard = {\n        userId,\n        culturalLearningProgress: {},\n        contentInteractions: {\n          viewedContent: [],\n          likedContent: [],\n          sharedContent: [],\n          createdContent: [],\n          bookmarkedContent: [],\n        },\n        bridgeBuildingMetrics: {\n          crossCulturalConnections: 0,\n          culturalBridgeScore: 0,\n          mentorshipParticipation: 0,\n          collaborativeProjects: 0,\n          communityContributions: 0,\n        },\n        recommendations: {\n          dailyRecommendations: [],\n          weeklyGoals: [],\n          culturalChallenges: [],\n          learningOpportunities: [],\n        },\n        preferences: {\n          contentTypes: ['heritage', 'story', 'tradition'],\n          culturalInterests: [],\n          learningGoals: [],\n          notificationSettings: {},\n        },\n        lastUpdated: Timestamp.now(),\n      }\n\n      await setDoc(docRef, defaultDashboard)\n      return defaultDashboard\n    } catch (error) {\n      console.error('Error getting personalized dashboard:', error)\n      throw new Error('Failed to get personalized dashboard')\n    }\n  }\n\n  // Update content interaction\n  async updateContentInteraction(\n    userId: string, \n    contentId: string, \n    interactionType: 'view' | 'like' | 'share' | 'bookmark' | 'comment'\n  ): Promise<void> {\n    try {\n      const dashboardRef = doc(db, 'personalized-dashboards', userId)\n      \n      const updateField = `contentInteractions.${interactionType === 'view' ? 'viewedContent' :\n                          interactionType === 'like' ? 'likedContent' :\n                          interactionType === 'share' ? 'sharedContent' :\n                          'bookmarkedContent'}`\n\n      await updateDoc(dashboardRef, {\n        [updateField]: arrayUnion(contentId),\n        lastUpdated: Timestamp.now(),\n      })\n\n      // Update content engagement metrics\n      await this.updateContentEngagement(contentId, interactionType)\n    } catch (error) {\n      console.error('Error updating content interaction:', error)\n      throw new Error('Failed to update content interaction')\n    }\n  }\n\n  // Verify content accuracy\n  async verifyContentAccuracy(\n    contentId: string, \n    verifierId: string, \n    accuracyScore: number, \n    notes?: string\n  ): Promise<void> {\n    try {\n      const verificationRef = doc(db, 'content-verifications', `${contentId}-${verifierId}`)\n      \n      await setDoc(verificationRef, {\n        contentId,\n        verifierId,\n        accuracyScore,\n        notes: notes || '',\n        verifiedAt: Timestamp.now(),\n        verifierType: 'cultural_representative', // TODO: Determine from user role\n      })\n\n      // Update content's overall accuracy score\n      await this.updateContentAccuracyScore(contentId)\n    } catch (error) {\n      console.error('Error verifying content accuracy:', error)\n      throw new Error('Failed to verify content accuracy')\n    }\n  }\n\n  // Private helper methods\n  private async getContentByCulture(culture: string, limit_count: number): Promise<CulturalHeritageContent[]> {\n    try {\n      const q = query(\n        collection(db, 'cultural-heritage'),\n        where('culturalGroup', 'array-contains', culture),\n        where('validation.status', '==', 'approved'),\n        orderBy('engagement.views', 'desc'),\n        limit(limit_count)\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as CulturalHeritageContent)\n    } catch (error) {\n      console.error('Error getting content by culture:', error)\n      return []\n    }\n  }\n\n  private async createContentRecommendation(\n    userId: string,\n    contentId: string,\n    contentType: string,\n    recommendationType: string,\n    culturalContext?: string\n  ): Promise<ContentRecommendation> {\n    const recommendationId = `rec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n    \n    return {\n      id: recommendationId,\n      userId,\n      contentId,\n      contentType: contentType as any,\n      recommendationType: recommendationType as any,\n      score: Math.floor(Math.random() * 40) + 60, // 60-100 range\n      reasoning: {\n        primaryFactors: [`Matches your interest in ${culturalContext}`],\n        culturalRelevance: [`Related to ${culturalContext} culture`],\n        learningOpportunities: ['Cultural understanding', 'Cross-cultural awareness'],\n        bridgeBuildingPotential: ['Connect with community members'],\n      },\n      culturalSensitivity: {\n        appropriateForUser: true,\n        requiresContext: false,\n        respectfulApproach: ['Approach with openness and respect'],\n      },\n      createdAt: Timestamp.now(),\n      expiresAt: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days\n      status: 'pending',\n    }\n  }\n\n  private async performAIModeration(content: any): Promise<any> {\n    // Simplified AI moderation - in production, integrate with actual AI service\n    return {\n      isValid: true,\n      concerns: [],\n      recommendations: [],\n      culturalSensitivity: {\n        appropriationRisk: Math.floor(Math.random() * 30), // 0-30 range for approved content\n        respectfulPresentation: Math.floor(Math.random() * 20) + 80, // 80-100 range\n      },\n    }\n  }\n\n  private async analyzeCulturalSensitivity(content: any): Promise<any> {\n    return culturalValidationService.validateCulturalContent(content)\n  }\n\n  private calculateCommunityBenefit(content: any): number {\n    // Calculate based on educational value, cultural preservation, etc.\n    return Math.floor(Math.random() * 20) + 80 // 80-100 range\n  }\n\n  private async generateLearningRecommendations(userId: string, dashboard: PersonalizedDashboard): Promise<ContentRecommendation[]> {\n    // TODO: Implement learning-based recommendations\n    return []\n  }\n\n  private async generateTrendingRecommendations(userId: string, limit_count: number): Promise<ContentRecommendation[]> {\n    // TODO: Implement trending-based recommendations\n    return []\n  }\n\n  private async generateBridgeBuildingRecommendations(userId: string, limit_count: number): Promise<ContentRecommendation[]> {\n    // TODO: Implement bridge-building recommendations\n    return []\n  }\n\n  private async saveRecommendationsToUser(userId: string, recommendations: ContentRecommendation[]): Promise<void> {\n    const dashboardRef = doc(db, 'personalized-dashboards', userId)\n    await updateDoc(dashboardRef, {\n      'recommendations.dailyRecommendations': recommendations,\n      lastUpdated: Timestamp.now(),\n    })\n  }\n\n  private async updateContentEngagement(contentId: string, interactionType: string): Promise<void> {\n    // TODO: Update content engagement metrics\n  }\n\n  private async updateContentAccuracyScore(contentId: string): Promise<void> {\n    // TODO: Calculate and update overall accuracy score\n  }\n\n  private async notifyCulturalRepresentatives(culturalGroups: string[], contentId: string): Promise<void> {\n    // TODO: Implement notification system\n    console.log(`Notifying cultural representatives for ${culturalGroups.join(', ')} about content ${contentId}`)\n  }\n}\n\nexport const contentDiscoveryService = new ContentDiscoveryService()\n", "import React, { useState, useEffect } from 'react'\nimport { contentDiscoveryService, ContentRecommendation, TrendingTopic, PersonalizedDashboard as DashboardData } from '../../../services/contentDiscoveryService'\nimport { useAuth } from '../../auth/hooks/useAuth'\n\ninterface PersonalizedDashboardProps {\n  onContentSelect?: (contentId: string, contentType: string) => void\n}\n\nexport const PersonalizedDashboard: React.FC<PersonalizedDashboardProps> = ({\n  onContentSelect,\n}) => {\n  const { user } = useAuth()\n  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)\n  const [recommendations, setRecommendations] = useState<ContentRecommendation[]>([])\n  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([])\n  const [loading, setLoading] = useState(true)\n  const [activeSection, setActiveSection] = useState<'overview' | 'recommendations' | 'trending' | 'progress'>('overview')\n\n  useEffect(() => {\n    if (user) {\n      loadDashboardData()\n    }\n  }, [user])\n\n  const loadDashboardData = async () => {\n    if (!user) return\n\n    setLoading(true)\n    try {\n      const [dashboard, recs, trending] = await Promise.all([\n        contentDiscoveryService.getPersonalizedDashboard(user.uid),\n        contentDiscoveryService.generatePersonalizedRecommendations(user.uid, 8),\n        contentDiscoveryService.discoverTrendingTopics('south-africa'),\n      ])\n\n      setDashboardData(dashboard)\n      setRecommendations(recs)\n      setTrendingTopics(trending)\n    } catch (error) {\n      console.error('Error loading dashboard data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleContentInteraction = async (contentId: string, interactionType: 'view' | 'like' | 'bookmark') => {\n    if (!user) return\n\n    try {\n      await contentDiscoveryService.updateContentInteraction(user.uid, contentId, interactionType)\n      // Refresh recommendations if needed\n      if (interactionType === 'like' || interactionType === 'bookmark') {\n        const newRecs = await contentDiscoveryService.generatePersonalizedRecommendations(user.uid, 8)\n        setRecommendations(newRecs)\n      }\n    } catch (error) {\n      console.error('Error updating content interaction:', error)\n    }\n  }\n\n  const renderOverview = () => (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {/* Cultural Learning Progress */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Cultural Learning Progress</h3>\n        <div className=\"space-y-4\">\n          {dashboardData && Object.entries(dashboardData.culturalLearningProgress).slice(0, 3).map(([culture, progress]) => (\n            <div key={culture} className=\"space-y-2\">\n              <div className=\"flex justify-between text-sm\">\n                <span className=\"font-medium capitalize\">{culture}</span>\n                <span className=\"text-gray-600\">{progress.level}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-orange-500 h-2 rounded-full\" \n                  style={{ width: `${progress.level}%` }}\n                />\n              </div>\n              <div className=\"text-xs text-gray-500\">\n                {progress.completedPaths.length} paths completed • {progress.achievements.length} achievements\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Bridge Building Metrics */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Bridge Building Impact</h3>\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-gray-600\">Cross-Cultural Connections</span>\n            <span className=\"text-lg font-semibold text-orange-600\">\n              {dashboardData?.bridgeBuildingMetrics.crossCulturalConnections || 0}\n            </span>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-gray-600\">Cultural Bridge Score</span>\n            <span className=\"text-lg font-semibold text-green-600\">\n              {dashboardData?.bridgeBuildingMetrics.culturalBridgeScore || 0}/100\n            </span>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-gray-600\">Community Contributions</span>\n            <span className=\"text-lg font-semibold text-blue-600\">\n              {dashboardData?.bridgeBuildingMetrics.communityContributions || 0}\n            </span>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-gray-600\">Active Mentorships</span>\n            <span className=\"text-lg font-semibold text-purple-600\">\n              {dashboardData?.bridgeBuildingMetrics.mentorshipParticipation || 0}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n        <div className=\"space-y-3\">\n          <button \n            onClick={() => setActiveSection('recommendations')}\n            className=\"w-full px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm\"\n          >\n            View Recommendations\n          </button>\n          <button \n            onClick={() => setActiveSection('trending')}\n            className=\"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm\"\n          >\n            Explore Trending\n          </button>\n          <button className=\"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm\">\n            Share Content\n          </button>\n          <button className=\"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm\">\n            Find Mentors\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderRecommendations = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Personalized Recommendations</h3>\n        <button \n          onClick={loadDashboardData}\n          className=\"px-4 py-2 text-sm text-orange-600 border border-orange-300 rounded-lg hover:bg-orange-50\"\n        >\n          Refresh\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {recommendations.map(rec => (\n          <div key={rec.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-start justify-between mb-4\">\n              <div>\n                <h4 className=\"font-semibold text-gray-900 mb-1\">\n                  {rec.contentType.charAt(0).toUpperCase() + rec.contentType.slice(1)} Content\n                </h4>\n                <span className=\"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded-full\">\n                  {rec.recommendationType.replace('_', ' ')}\n                </span>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-sm font-medium text-green-600\">\n                  {rec.score}% Match\n                </div>\n              </div>\n            </div>\n\n            <div className=\"space-y-2 mb-4\">\n              <div className=\"text-sm text-gray-600\">\n                <strong>Why recommended:</strong> {rec.reasoning.primaryFactors.join(', ')}\n              </div>\n              {rec.reasoning.culturalRelevance.length > 0 && (\n                <div className=\"text-sm text-gray-600\">\n                  <strong>Cultural relevance:</strong> {rec.reasoning.culturalRelevance.join(', ')}\n                </div>\n              )}\n              {rec.reasoning.learningOpportunities.length > 0 && (\n                <div className=\"text-sm text-gray-600\">\n                  <strong>Learning opportunities:</strong> {rec.reasoning.learningOpportunities.join(', ')}\n                </div>\n              )}\n            </div>\n\n            {rec.culturalSensitivity.requiresContext && (\n              <div className=\"mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                <div className=\"text-xs font-medium text-yellow-800 mb-1\">Cultural Context Required</div>\n                <div className=\"text-xs text-yellow-700\">\n                  {rec.culturalSensitivity.respectfulApproach.join(', ')}\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex gap-2\">\n              <button\n                onClick={() => {\n                  handleContentInteraction(rec.contentId, 'view')\n                  onContentSelect?.(rec.contentId, rec.contentType)\n                }}\n                className=\"flex-1 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm\"\n              >\n                Explore\n              </button>\n              <button\n                onClick={() => handleContentInteraction(rec.contentId, 'bookmark')}\n                className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm\"\n              >\n                Save\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n\n  const renderTrending = () => (\n    <div className=\"space-y-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900\">Trending Cultural Topics</h3>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {trendingTopics.map(topic => (\n          <div key={topic.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-start justify-between mb-3\">\n              <h4 className=\"font-semibold text-gray-900\">{topic.topic}</h4>\n              <span className=\"text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full\">\n                +{topic.trendMetrics.growthRate}%\n              </span>\n            </div>\n\n            {topic.culturalGroup && (\n              <div className=\"text-sm text-orange-600 mb-2 capitalize\">\n                {topic.culturalGroup} Culture\n              </div>\n            )}\n\n            <div className=\"text-sm text-gray-600 mb-4\">\n              {topic.content.culturalContext}\n            </div>\n\n            <div className=\"flex items-center justify-between text-xs text-gray-500 mb-4\">\n              <span>{topic.engagement.views} views</span>\n              <span>{topic.engagement.interactions} interactions</span>\n              <span>{topic.engagement.shares} shares</span>\n            </div>\n\n            <div className=\"flex gap-2\">\n              <button className=\"flex-1 px-3 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm\">\n                Explore Topic\n              </button>\n              <button className=\"px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm\">\n                Share\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n\n  const renderProgress = () => (\n    <div className=\"space-y-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900\">Learning Progress & Goals</h3>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Weekly Goals */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h4 className=\"font-semibold text-gray-900 mb-4\">This Week's Goals</h4>\n          <div className=\"space-y-3\">\n            {dashboardData?.recommendations.weeklyGoals.map((goal, index) => (\n              <div key={index} className=\"flex items-center\">\n                <input type=\"checkbox\" className=\"mr-3\" />\n                <span className=\"text-sm text-gray-700\">{goal}</span>\n              </div>\n            )) || (\n              <div className=\"text-sm text-gray-500\">No goals set for this week</div>\n            )}\n          </div>\n        </div>\n\n        {/* Cultural Challenges */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h4 className=\"font-semibold text-gray-900 mb-4\">Cultural Challenges</h4>\n          <div className=\"space-y-3\">\n            {dashboardData?.recommendations.culturalChallenges.map((challenge, index) => (\n              <div key={index} className=\"p-3 bg-orange-50 border border-orange-200 rounded-lg\">\n                <div className=\"text-sm text-orange-800\">{challenge}</div>\n              </div>\n            )) || (\n              <div className=\"text-sm text-gray-500\">No active challenges</div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Content Interactions Summary */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h4 className=\"font-semibold text-gray-900 mb-4\">Your Activity Summary</h4>\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">\n              {dashboardData?.contentInteractions.viewedContent.length || 0}\n            </div>\n            <div className=\"text-sm text-gray-600\">Content Viewed</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-green-600\">\n              {dashboardData?.contentInteractions.likedContent.length || 0}\n            </div>\n            <div className=\"text-sm text-gray-600\">Content Liked</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-purple-600\">\n              {dashboardData?.contentInteractions.sharedContent.length || 0}\n            </div>\n            <div className=\"text-sm text-gray-600\">Content Shared</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-orange-600\">\n              {dashboardData?.contentInteractions.createdContent.length || 0}\n            </div>\n            <div className=\"text-sm text-gray-600\">Content Created</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Your Cultural Learning Dashboard</h1>\n        <p className=\"text-gray-600\">\n          Track your progress, discover new content, and build bridges across cultures.\n        </p>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"flex space-x-8\">\n          {[\n            { id: 'overview', label: 'Overview', icon: '📊' },\n            { id: 'recommendations', label: 'For You', icon: '🎯' },\n            { id: 'trending', label: 'Trending', icon: '🔥' },\n            { id: 'progress', label: 'Progress', icon: '📈' },\n          ].map(tab => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveSection(tab.id as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeSection === tab.id\n                  ? 'border-orange-500 text-orange-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeSection === 'overview' && renderOverview()}\n      {activeSection === 'recommendations' && renderRecommendations()}\n      {activeSection === 'trending' && renderTrending()}\n      {activeSection === 'progress' && renderProgress()}\n    </div>\n  )\n}\n\nexport default PersonalizedDashboard\n", "import React, { useState } from 'react'\nimport CulturalContentCreator from '../features/cultural-heritage/components/CulturalContentCreator'\nimport SkillSharingMarketplace from '../features/knowledge-sharing/components/SkillSharingMarketplace'\nimport PersonalizedDashboard from '../features/content-discovery/components/PersonalizedDashboard'\n\ntype ActiveView = 'dashboard' | 'heritage' | 'knowledge' | 'discovery'\n\nexport const CulturalKnowledgePage: React.FC = () => {\n  const [activeView, setActiveView] = useState<ActiveView>('dashboard')\n  const [showContentCreator, setShowContentCreator] = useState(false)\n\n  const handleContentCreated = (contentId: string) => {\n    setShowContentCreator(false)\n    // Show success message or redirect to content view\n    alert(`Cultural content created successfully! ID: ${contentId}`)\n  }\n\n  const handleMentorshipRequest = (matchId: string) => {\n    // Handle mentorship connection request\n    alert(`Mentorship request sent! Match ID: ${matchId}`)\n  }\n\n  const handleContentSelect = (contentId: string, contentType: string) => {\n    // Handle content selection from recommendations\n    console.log(`Selected content: ${contentId} of type: ${contentType}`)\n  }\n\n  const renderNavigation = () => (\n    <div className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"max-w-6xl mx-auto px-6\">\n        <nav className=\"flex space-x-8\">\n          {[\n            { id: 'dashboard', label: 'Dashboard', icon: '📊', description: 'Your personalized learning hub' },\n            { id: 'heritage', label: 'Heritage', icon: '🏛️', description: 'Document & preserve culture' },\n            { id: 'knowledge', label: 'Knowledge', icon: '🤝', description: 'Share & learn skills' },\n            { id: 'discovery', label: 'Discovery', icon: '🔍', description: 'Explore cultural content' },\n          ].map(tab => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveView(tab.id as ActiveView)}\n              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${\n                activeView === tab.id\n                  ? 'border-orange-500 text-orange-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n              title={tab.description}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n    </div>\n  )\n\n  const renderHeritage = () => (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      {!showContentCreator ? (\n        <div className=\"space-y-8\">\n          {/* Header */}\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Cultural Heritage Documentation</h1>\n            <p className=\"text-lg text-gray-600 mb-8\">\n              Preserve and share South African cultural heritage for future generations\n            </p>\n            <button\n              onClick={() => setShowContentCreator(true)}\n              className=\"px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-medium\"\n            >\n              Share Your Heritage\n            </button>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"text-3xl mb-4\">📚</div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Oral Traditions</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Record and preserve traditional stories, folklore, and oral histories with audio and video support.\n              </p>\n              <button className=\"text-orange-600 hover:text-orange-800 font-medium\">\n                Start Recording →\n              </button>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"text-3xl mb-4\">🏺</div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Cultural Artifacts</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Document traditional objects, tools, and artifacts with detailed metadata and provenance.\n              </p>\n              <button className=\"text-orange-600 hover:text-orange-800 font-medium\">\n                Document Artifact →\n              </button>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"text-3xl mb-4\">🎭</div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Traditions & Customs</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Share traditional practices, ceremonies, and cultural customs with proper context.\n              </p>\n              <button className=\"text-orange-600 hover:text-orange-800 font-medium\">\n                Share Tradition →\n              </button>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"text-3xl mb-4\">🍲</div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Traditional Recipes</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Preserve family recipes and traditional cooking methods with cultural significance.\n              </p>\n              <button className=\"text-orange-600 hover:text-orange-800 font-medium\">\n                Share Recipe →\n              </button>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"text-3xl mb-4\">🎵</div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Music & Dance</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Record traditional music, songs, and dance performances with cultural context.\n              </p>\n              <button className=\"text-orange-600 hover:text-orange-800 font-medium\">\n                Record Performance →\n              </button>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"text-3xl mb-4\">🗣️</div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Languages & Dialects</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Preserve linguistic heritage including dialects, phrases, and language variations.\n              </p>\n              <button className=\"text-orange-600 hover:text-orange-800 font-medium\">\n                Document Language →\n              </button>\n            </div>\n          </div>\n\n          {/* Cultural Sensitivity Notice */}\n          <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-6\">\n            <h3 className=\"text-lg font-semibold text-orange-800 mb-3\">Cultural Sensitivity & Ubuntu Philosophy</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-orange-700\">\n              <div>\n                <h4 className=\"font-medium mb-2\">Respectful Documentation</h4>\n                <ul className=\"space-y-1\">\n                  <li>• Ensure you have permission to share cultural content</li>\n                  <li>• Respect sacred and sensitive information</li>\n                  <li>• Provide accurate cultural context</li>\n                  <li>• Honor traditional knowledge holders</li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"font-medium mb-2\">Community Validation</h4>\n                <ul className=\"space-y-1\">\n                  <li>• Content reviewed by cultural representatives</li>\n                  <li>• Community endorsement for accuracy</li>\n                  <li>• Protection of sacred content</li>\n                  <li>• Collaborative preservation efforts</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      ) : (\n        <CulturalContentCreator\n          onContentCreated={handleContentCreated}\n          onCancel={() => setShowContentCreator(false)}\n        />\n      )}\n    </div>\n  )\n\n  const renderKnowledge = () => (\n    <SkillSharingMarketplace onMentorshipRequest={handleMentorshipRequest} />\n  )\n\n  const renderDiscovery = () => (\n    <PersonalizedDashboard onContentSelect={handleContentSelect} />\n  )\n\n  const renderDashboard = () => (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Cultural Knowledge & Heritage Hub</h1>\n        <p className=\"text-gray-600\">\n          Discover, share, and preserve South African cultural heritage through collaborative learning and Ubuntu philosophy.\n        </p>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-orange-600 mb-2\">1,247</div>\n          <div className=\"text-sm text-gray-600\">Heritage Items</div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-blue-600 mb-2\">856</div>\n          <div className=\"text-sm text-gray-600\">Active Mentors</div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-green-600 mb-2\">2,134</div>\n          <div className=\"text-sm text-gray-600\">Learning Paths</div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-purple-600 mb-2\">3,421</div>\n          <div className=\"text-sm text-gray-600\">Cultural Bridges</div>\n        </div>\n      </div>\n\n      {/* Feature Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div \n          onClick={() => setActiveView('heritage')}\n          className=\"bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white cursor-pointer hover:from-orange-600 hover:to-orange-700 transition-all\"\n        >\n          <div className=\"text-3xl mb-4\">🏛️</div>\n          <h3 className=\"text-xl font-semibold mb-2\">Cultural Heritage</h3>\n          <p className=\"text-orange-100 mb-4\">\n            Document and preserve traditional stories, artifacts, and cultural practices for future generations.\n          </p>\n          <div className=\"flex items-center text-orange-100\">\n            <span>Explore Heritage →</span>\n          </div>\n        </div>\n\n        <div \n          onClick={() => setActiveView('knowledge')}\n          className=\"bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white cursor-pointer hover:from-blue-600 hover:to-blue-700 transition-all\"\n        >\n          <div className=\"text-3xl mb-4\">🤝</div>\n          <h3 className=\"text-xl font-semibold mb-2\">Knowledge Sharing</h3>\n          <p className=\"text-blue-100 mb-4\">\n            Connect with mentors and learners across cultures to share skills and build meaningful relationships.\n          </p>\n          <div className=\"flex items-center text-blue-100\">\n            <span>Start Learning →</span>\n          </div>\n        </div>\n\n        <div \n          onClick={() => setActiveView('discovery')}\n          className=\"bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white cursor-pointer hover:from-green-600 hover:to-green-700 transition-all\"\n        >\n          <div className=\"text-3xl mb-4\">🔍</div>\n          <h3 className=\"text-xl font-semibold mb-2\">Content Discovery</h3>\n          <p className=\"text-green-100 mb-4\">\n            Discover personalized cultural content and trending topics that match your interests and learning goals.\n          </p>\n          <div className=\"flex items-center text-green-100\">\n            <span>Discover Content →</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Ubuntu Philosophy Section */}\n      <div className=\"mt-8 bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-6\">\n        <div className=\"flex items-center mb-4\">\n          <div className=\"text-2xl mr-3\">🤲</div>\n          <h3 className=\"text-lg font-semibold text-orange-800\">Ubuntu Philosophy: \"I am because we are\"</h3>\n        </div>\n        <p className=\"text-orange-700 mb-4\">\n          Our platform embodies the Ubuntu philosophy, recognizing that our individual growth and cultural understanding \n          are deeply connected to our community's collective wisdom and shared experiences.\n        </p>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n          <div className=\"bg-white rounded-lg p-4 border border-orange-200\">\n            <h4 className=\"font-medium text-orange-800 mb-2\">Mutual Learning</h4>\n            <p className=\"text-orange-700\">Every interaction is an opportunity for both teaching and learning.</p>\n          </div>\n          <div className=\"bg-white rounded-lg p-4 border border-orange-200\">\n            <h4 className=\"font-medium text-orange-800 mb-2\">Cultural Respect</h4>\n            <p className=\"text-orange-700\">Honor and celebrate the diversity of South African cultures.</p>\n          </div>\n          <div className=\"bg-white rounded-lg p-4 border border-orange-200\">\n            <h4 className=\"font-medium text-orange-800 mb-2\">Community Building</h4>\n            <p className=\"text-orange-700\">Strengthen bonds across cultural boundaries through shared knowledge.</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {renderNavigation()}\n      \n      <main>\n        {activeView === 'dashboard' && renderDashboard()}\n        {activeView === 'heritage' && renderHeritage()}\n        {activeView === 'knowledge' && renderKnowledge()}\n        {activeView === 'discovery' && renderDiscovery()}\n      </main>\n    </div>\n  )\n}\n\nexport default CulturalKnowledgePage\n"], "names": ["CulturalHeritageService", "content", "contentId", "validation", "culturalValidationService", "heritageContent", "setDoc", "doc", "db", "error", "file", "mediaType", "fileId", "storageRef", "ref", "storage", "snapshot", "uploadBytes", "downloadURL", "getDownloadURL", "mediaFile", "Timestamp", "contentRef", "updateDoc", "arrayUnion", "story", "storyId", "oralStory", "culturalGroup", "limit_count", "q", "query", "collection", "where", "orderBy", "limit", "getDocs", "representativeId", "reviewNotes", "searchTerm", "filters", "tag", "culturalGroups", "culturalHeritageService", "CulturalContentCreator", "onContentCreated", "onCancel", "user", "useAuth", "currentStep", "setCurrentStep", "useState", "isSubmitting", "setIsSubmitting", "uploadProgress", "setUploadProgress", "formData", "setFormData", "mediaFiles", "setMediaFiles", "fileInputRefs", "useRef", "contentTypes", "handleInputChange", "field", "value", "prev", "handleFileSelection", "files", "fileArray", "removeFile", "index", "_", "i", "handleSubmit", "contentData", "mediaTypes", "renderStepContent", "jsxs", "jsx", "e", "type", "group", "g", "step", "KnowledgeSharingService", "profile", "userId", "skillArea", "role", "userProfile", "potentialMatch<PERSON>", "matches", "match", "matchScore", "mentorship<PERSON><PERSON>", "a", "b", "project", "projectId", "collaborativeProject", "skills", "culturalBackground", "projectRef", "learningPath", "pathId", "culturalPath", "doc<PERSON>ef", "docSnap", "getDoc", "potential", "userRole", "score", "userSkill", "potentialSkill", "skillLevels", "userLevel", "potentialLevel", "commonCultures", "culture", "ubuntuAlignment", "commonLanguages", "lang", "knowledgeSharingService", "SkillSharingMarketplace", "onMentorshipRequest", "activeTab", "setActiveTab", "skillProfiles", "setSkillProfiles", "mentorship<PERSON><PERSON><PERSON>", "setMentorshipMatches", "selectedSkill", "setSelectedSkill", "setSearchTerm", "loading", "setLoading", "skillCategories", "culturalSkills", "useEffect", "loadUserData", "handleSkillSearch", "skill", "renderBrowseTab", "category", "renderMentorTab", "m", "renderLearnTab", "tab", "Fragment", "ContentDiscoveryService", "userDashboard", "recommendations", "culturalContent", "recommendation", "learningRecommendations", "trendingRecommendations", "bridgeRecommendations", "sortedRecommendations", "region", "contentType", "aiModeration", "culturalAnalysis", "needsCulturalReview", "moderationResult", "defaultDashboard", "interactionType", "dashboardRef", "verifierId", "accuracyScore", "notes", "verificationRef", "recommendationType", "culturalContext", "dashboard", "contentDiscoveryService", "PersonalizedDashboard", "onContentSelect", "dashboardData", "setDashboardData", "setRecommendations", "trendingTopics", "setTrendingTopics", "activeSection", "setActiveSection", "loadDashboardData", "recs", "trending", "handleContentInteraction", "newRecs", "renderOverview", "progress", "renderRecommendations", "rec", "renderTrending", "topic", "renderProgress", "goal", "challenge", "CulturalKnowledgePage", "activeView", "setActiveView", "showContentCreator", "setShowContentCreator", "handleContentCreated", "handleMentorshipRequest", "matchId", "handleContentSelect", "renderNavigation", "renderHeritage", "renderKnowledge", "renderDiscovery", "renderDashboard"], "mappings": "0SA4GA,MAAMA,CAAwB,CAE5B,MAAM,sBAAsBC,EAA8E,CACpG,GAAA,CACF,MAAMC,EAAY,YAAY,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAG7EC,EAAa,MAAMC,EAA0B,wBAAwB,CACzE,GAAIF,EACJ,MAAOD,EAAQ,MACf,QAASA,EAAQ,YACjB,KAAMA,EAAQ,KACd,QAASA,EAAQ,cAAc,CAAC,EAChC,MAAOA,EAAQ,MACf,OAAQ,CACN,OAAQA,EAAQ,SAAS,QACzB,oBAAqB,kBACvB,EACA,aAAc,CACZ,OAAQ,UACR,WAAY,CAAC,EACb,YAAa,CAAC,EACd,WAAY,IACd,EACA,WAAY,CACV,MAAO,EACP,MAAO,EACP,OAAQ,EACR,SAAU,EACV,mBAAoB,CACtB,EACA,UAAWA,EAAQ,SAAS,YAC5B,aAAcA,EAAQ,SAAS,YAAA,CAChC,EAEKI,EAA2C,CAC/C,GAAGJ,EACH,GAAIC,EACJ,WAAY,CACV,OAAQC,EAAW,eAAiB,iBAAmB,QACvD,WAAY,CAAC,EACb,YAAaA,EAAW,SACxB,sBAAuB,EACvB,cAAe,CACjB,EACA,WAAY,CACV,MAAO,EACP,MAAO,EACP,OAAQ,EACR,SAAU,EACV,6BAA8B,CAAA,CAElC,EAEA,aAAMG,EAAOC,EAAIC,EAAI,oBAAqBN,CAAS,EAAGG,CAAe,EAGjEF,EAAW,gBACb,MAAM,KAAK,8BAA8BF,EAAQ,cAAeC,CAAS,EAGpEA,QACAO,EAAO,CACN,cAAA,MAAM,mCAAoCA,CAAK,EACjD,IAAI,MAAM,mCAAmC,CAAA,CACrD,CAIF,MAAM,gBACJC,EACAR,EACAS,EACoB,CAChB,GAAA,CACF,MAAMC,EAAS,GAAGV,CAAS,IAAI,KAAK,KAAK,IAAI,KAAK,OAAA,EAAS,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAC9EW,EAAaC,EAAIC,EAAS,qBAAqBJ,CAAS,IAAIC,CAAM,EAAE,EAEpEI,EAAW,MAAMC,EAAYJ,EAAYH,CAAI,EAC7CQ,EAAc,MAAMC,EAAeH,EAAS,GAAG,EAE/CI,EAAuB,CAC3B,GAAIR,EACJ,IAAKM,EACL,SAAUR,EAAK,KACf,KAAMA,EAAK,KACX,KAAMA,EAAK,KACX,WAAYW,EAAU,IAAI,EAC1B,SAAU,CACR,aAAcX,EAAK,KACnB,WAAY,cAAA,CAEhB,EAGMY,EAAaf,EAAIC,EAAI,oBAAqBN,CAAS,EACzD,aAAMqB,EAAUD,EAAY,CAC1B,CAAC,SAASX,CAAS,EAAE,EAAGa,EAAWJ,CAAS,EAC5C,wBAAyBC,EAAU,IAAI,CAAA,CACxC,EAEMD,QACAX,EAAO,CACN,cAAA,MAAM,8BAA+BA,CAAK,EAC5C,IAAI,MAAM,6BAA6B,CAAA,CAC/C,CAIF,MAAM,yBAAyBgB,EAAwD,CACjF,GAAA,CACF,MAAMC,EAAU,SAAS,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAExEC,EAAgC,CACpC,GAAGF,EACH,GAAIC,CACN,EAEA,MAAMpB,EAAOC,EAAIC,EAAI,kBAAmBkB,CAAO,EAAGC,CAAS,EAG3D,MAAMtB,EAAsE,CAC1E,MAAOoB,EAAM,MACb,YAAaA,EAAM,MAAM,cACzB,KAAM,QACN,cAAe,CAACA,EAAM,aAAa,EACnC,OAAQ,eACR,MAAO,CACL,OAAQ,CAAC,EACT,OAAQA,EAAM,MAAM,eAAiB,CAACA,EAAM,MAAM,cAAc,EAAI,CAAC,EACrE,MAAOA,EAAM,MAAM,eAAiB,CAACA,EAAM,MAAM,cAAc,EAAI,CAAC,EACpE,UAAW,CAAA,CACb,EACA,SAAU,CACR,QAASA,EAAM,YAAY,OAC3B,aAAc,CAAC,EACf,YAAaJ,EAAU,IAAI,EAC3B,aAAcA,EAAU,IAAI,EAC5B,WAAY,uBAAuBI,EAAM,aAAa,GACtD,qBAAsBA,EAAM,gBAAgB,sBAAwB,MAAQ,SAAW,iBACvF,KAAM,CAAC,iBAAkB,QAASA,EAAM,aAAa,EACrD,SAAUA,EAAM,aAAa,iBAC7B,OAAQ,cACV,EACA,WAAY,CACV,OAAQ,iBACR,WAAY,CAAC,EACb,YAAa,CAAC,EACd,sBAAuB,EACvB,cAAeA,EAAM,aAAa,kBACpC,EACA,aAAc,CACZ,gBAAiB,GACjB,gBAAiB,CAAC,EAClB,4BAA6B,CAC3B,iBAAkBA,EAAM,aAAa,iBACrC,kBAAmBA,EAAM,aAAa,kBACtC,qBAAsBA,EAAM,aAAa,oBAC3C,EACA,yBAA0B,EAAA,CAE9B,EAEM,aAAA,KAAK,sBAAsBpB,CAAe,EAEzCqB,QACAjB,EAAO,CACN,cAAA,MAAM,uCAAwCA,CAAK,EACrD,IAAI,MAAM,uCAAuC,CAAA,CACzD,CAIF,MAAM,4BACJmB,EACAC,EAAsB,GACc,CAChC,GAAA,CACF,MAAMC,EAAIC,EACRC,EAAWxB,EAAI,mBAAmB,EAClCyB,EAAM,gBAAiB,iBAAkBL,CAAa,EACtDK,EAAM,oBAAqB,KAAM,UAAU,EAC3CC,EAAQ,mBAAoB,MAAM,EAClCC,EAAMN,CAAW,CACnB,EAGA,OADiB,MAAMO,EAAQN,CAAC,GAChB,KAAK,IAAIvB,GAAOA,EAAI,MAAiC,QAC9DE,EAAO,CACN,cAAA,MAAM,6CAA8CA,CAAK,EAC3D,IAAI,MAAM,gCAAgC,CAAA,CAClD,CAIF,MAAM,uBACJP,EACAmC,EACAC,EACe,CACX,GAAA,CACF,MAAMhB,EAAaf,EAAIC,EAAI,oBAAqBN,CAAS,EAEzD,MAAMqB,EAAUD,EAAY,CAC1B,oBAAqB,WACrB,wBAAyBE,EAAWa,CAAgB,EACpD,yBAA0BC,EAAcd,EAAWc,CAAW,EAAI,CAAC,EACnE,0CAA2CD,EAC3C,wBAAyBhB,EAAU,IAAI,CAAA,CACxC,QAGMZ,EAAO,CACN,cAAA,MAAM,oCAAqCA,CAAK,EAClD,IAAI,MAAM,oCAAoC,CAAA,CACtD,CAIF,MAAM,sBACJ8B,EACAC,EAMoC,CAChC,GAAA,CAGF,IAAIV,EAAIC,EACNC,EAAWxB,EAAI,mBAAmB,EAClCyB,EAAM,oBAAqB,KAAM,UAAU,CAC7C,EAEA,OAAIO,GAAS,OACXV,EAAIC,EAAMD,EAAGG,EAAM,OAAQ,KAAMO,EAAQ,IAAI,CAAC,GAG5CA,GAAS,gBACXV,EAAIC,EAAMD,EAAGG,EAAM,gBAAiB,iBAAkBO,EAAQ,aAAa,CAAC,IAG7D,MAAMJ,EAAQN,CAAC,GACP,KAAK,IAAIvB,GAAOA,EAAI,MAAiC,EAG/D,OACbN,GAAAA,EAAQ,MAAM,YAAc,EAAA,SAASsC,EAAW,YAAa,CAAA,GAC7DtC,EAAQ,YAAY,YAAc,EAAA,SAASsC,EAAW,YAAa,CAAA,GACnEtC,EAAQ,SAAS,KAAK,KAAKwC,GAAOA,EAAI,YAAc,EAAA,SAASF,EAAW,YAAA,CAAa,CAAC,CACxF,QACO9B,EAAO,CACN,cAAA,MAAM,oCAAqCA,CAAK,EAClD,IAAI,MAAM,mCAAmC,CAAA,CACrD,CAIF,MAAc,8BAA8BiC,EAA0BxC,EAAkC,CAE9F,QAAA,IAAI,kDAAkDwC,EAAe,KAAK,IAAI,CAAC,mBAAmBxC,CAAS,EAAE,CAAA,CAEzH,CAEa,MAAAyC,EAA0B,IAAI3C,EC5W9B4C,GAAgE,CAAC,CAC5E,iBAAAC,EACA,SAAAC,CACF,IAAM,CACE,KAAA,CAAE,KAAAC,CAAK,EAAIC,EAAQ,EACnB,CAACC,EAAaC,CAAc,EAAIC,EAAAA,SAAS,CAAC,EAC1C,CAACC,EAAcC,CAAe,EAAIF,EAAAA,SAAS,EAAK,EAChD,CAACG,EAAgBC,CAAiB,EAAIJ,EAAAA,SAAiC,CAAA,CAAE,EAEzE,CAACK,EAAUC,CAAW,EAAIN,WAAS,CACvC,MAAO,GACP,YAAa,GACb,KAAM,QACN,cAAe,CAAC,EAChB,OAAQ,GACR,WAAY,GACZ,qBAAsB,SACtB,KAAM,CAAC,EACP,SAAU,IAAA,CACX,EAEK,CAACO,EAAYC,CAAa,EAAIR,WAKjC,CACD,OAAQ,CAAC,EACT,OAAQ,CAAC,EACT,MAAO,CAAC,EACR,UAAW,CAAA,CAAC,CACb,EAEKS,EAAgB,CACpB,OAAQC,SAAyB,IAAI,EACrC,OAAQA,SAAyB,IAAI,EACrC,MAAOA,SAAyB,IAAI,EACpC,UAAWA,SAAyB,IAAI,CAC1C,EAEMnB,EAAiB,CACrB,OAAQ,QAAS,YAAa,UAAW,QAAS,SAClD,SAAU,QAAS,QAAS,UAAW,UACzC,EAEMoB,EAAe,CACnB,CAAE,MAAO,QAAS,MAAO,sBAAuB,EAChD,CAAE,MAAO,YAAa,MAAO,oBAAqB,EAClD,CAAE,MAAO,WAAY,MAAO,mBAAoB,EAChD,CAAE,MAAO,SAAU,MAAO,oBAAqB,EAC/C,CAAE,MAAO,QAAS,MAAO,YAAa,EACtC,CAAE,MAAO,QAAS,MAAO,mBAAoB,EAC7C,CAAE,MAAO,WAAY,MAAO,kBAAmB,EAC/C,CAAE,MAAO,UAAW,MAAO,oBAAqB,CAClD,EAEMC,EAAoB,CAACC,EAAeC,IAAe,CAC3CR,EAAAS,IAAS,CAAE,GAAGA,EAAM,CAACF,CAAK,EAAGC,GAAQ,CACnD,EAEME,EAAsB,CAACxD,EAAoCyD,IAA2B,CAC1F,GAAI,CAACA,EAAO,OAEN,MAAAC,EAAY,MAAM,KAAKD,CAAK,EAClCT,EAAuBO,IAAA,CACrB,GAAGA,EACH,CAACvD,CAAS,EAAG,CAAC,GAAGuD,EAAKvD,CAAS,EAAG,GAAG0D,CAAS,CAAA,EAC9C,CACJ,EAEMC,EAAa,CAAC3D,EAAoC4D,IAAkB,CACxEZ,EAAuBO,IAAA,CACrB,GAAGA,EACH,CAACvD,CAAS,EAAGuD,EAAKvD,CAAS,EAAE,OAAO,CAAC6D,EAAGC,IAAMA,IAAMF,CAAK,CAAA,EACzD,CACJ,EAEMG,EAAe,SAAY,CAC/B,GAAK3B,EAEL,CAAAM,EAAgB,EAAI,EAChB,GAAA,CAEF,MAAMsB,EAAkE,CACtE,MAAOnB,EAAS,MAChB,YAAaA,EAAS,YACtB,KAAMA,EAAS,KACf,cAAeA,EAAS,cACxB,OAAQA,EAAS,OACjB,MAAO,CACL,OAAQ,CAAC,EACT,OAAQ,CAAC,EACT,MAAO,CAAC,EACR,UAAW,CAAA,CACb,EACA,SAAU,CACR,QAAST,EAAK,IACd,aAAc,CAAC,EACf,YAAa1B,EAAU,IAAI,EAC3B,aAAcA,EAAU,IAAI,EAC5B,WAAYmC,EAAS,WACrB,qBAAsBA,EAAS,qBAC/B,KAAMA,EAAS,KACf,SAAUA,EAAS,SACnB,OAAQA,EAAS,MACnB,EACA,WAAY,CACV,OAAQ,QACR,WAAY,CAAC,EACb,YAAa,CAAC,EACd,sBAAuB,EACvB,cAAe,CACjB,EACA,aAAc,CACZ,gBAAiB,GACjB,gBAAiB,CAAC,EAClB,4BAA6B,CAAC,EAC9B,yBAA0B,EAAA,CAE9B,EAEMtD,EAAY,MAAMyC,EAAwB,sBAAsBgC,CAAW,EAG3EC,EAAa,OAAO,KAAKlB,CAAU,EACzC,UAAW/C,KAAaiE,EAAY,CAC5B,MAAAR,EAAQV,EAAW/C,CAAS,EAClC,QAAS8D,EAAI,EAAGA,EAAIL,EAAM,OAAQK,IAAK,CAC/B,MAAA/D,EAAO0D,EAAMK,CAAC,EACpBlB,EAA2BW,IAAA,CAAE,GAAGA,EAAM,CAAC,GAAGvD,CAAS,IAAI8D,CAAC,EAAE,EAAG,CAAI,EAAA,EAE7D,GAAA,CACF,MAAM9B,EAAwB,gBAAgBjC,EAAMR,EAAWS,CAAS,EACxE4C,EAA2BW,IAAA,CAAE,GAAGA,EAAM,CAAC,GAAGvD,CAAS,IAAI8D,CAAC,EAAE,EAAG,GAAM,EAAA,QAC5DhE,EAAO,CACd,QAAQ,MAAM,mBAAmBE,CAAS,SAAUF,CAAK,CAAA,CAC3D,CACF,CAGFoC,IAAmB3C,CAAS,QACrBO,EAAO,CACN,QAAA,MAAM,mCAAoCA,CAAK,EACvD,MAAM,sDAAsD,CAAA,QAC5D,CACA4C,EAAgB,EAAK,CAAA,EAEzB,EAEMwB,EAAoB,IAAM,CAC9B,OAAQ5B,EAAa,CACnB,IAAK,GAED,OAAA6B,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAiB,oBAAA,SAEpE,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,UAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOvB,EAAS,MAChB,SAAWwB,GAAMjB,EAAkB,QAASiB,EAAE,OAAO,KAAK,EAC1D,UAAU,2GACV,YAAY,4BACZ,SAAQ,EAAA,CAAA,CACV,EACF,SAEC,MACC,CAAA,SAAA,CAACD,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,iBAAA,EACAA,EAAA,IAAC,SAAA,CACC,MAAOvB,EAAS,KAChB,SAAWwB,GAAMjB,EAAkB,OAAQiB,EAAE,OAAO,KAAK,EACzD,UAAU,2GACV,SAAQ,GAEP,SAAalB,EAAA,IACZmB,GAAAF,EAAAA,IAAC,SAAwB,CAAA,MAAOE,EAAK,MAClC,SAAKA,EAAA,KAAA,EADKA,EAAK,KAElB,CACD,CAAA,CAAA,CACH,EACF,SAEC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,sBAAA,EACAA,EAAA,IAAC,MAAI,CAAA,UAAU,yBACZ,SAAArC,EAAe,IACdwC,GAAAJ,EAAA,KAAC,QAAkB,CAAA,UAAU,oBAC3B,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASvB,EAAS,cAAc,SAAS0B,CAAK,EAC9C,SAAWF,GAAM,CACXA,EAAE,OAAO,QACXjB,EAAkB,gBAAiB,CAAC,GAAGP,EAAS,cAAe0B,CAAK,CAAC,EAErEnB,EAAkB,gBAAiBP,EAAS,cAAc,OAAY2B,GAAAA,IAAMD,CAAK,CAAC,CAEtF,EACA,UAAU,MAAA,CACZ,EACCH,EAAA,IAAA,OAAA,CAAK,UAAU,qBAAsB,SAAMG,CAAA,CAAA,CAAA,CAblC,EAAAA,CAcZ,CACD,CACH,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACH,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,gBAAA,EACAA,EAAA,IAAC,WAAA,CACC,MAAOvB,EAAS,YAChB,SAAWwB,GAAMjB,EAAkB,cAAeiB,EAAE,OAAO,KAAK,EAChE,KAAM,EACN,UAAU,2GACV,YAAY,0DACZ,SAAQ,EAAA,CAAA,CACV,CACF,CAAA,CAAA,EACF,EAGJ,IAAK,GAED,OAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAgB,mBAAA,SAEnE,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,wBAAA,EACAD,EAAA,KAAC,SAAA,CACC,MAAOtB,EAAS,qBAChB,SAAWwB,GAAMjB,EAAkB,uBAAwBiB,EAAE,OAAO,KAAK,EACzE,UAAU,2GAEV,SAAA,CAACD,EAAA,IAAA,SAAA,CAAO,MAAM,SAAS,SAA6B,gCAAA,EACnDA,EAAA,IAAA,SAAA,CAAO,MAAM,iBAAiB,SAAiD,oDAAA,EAC/EA,EAAA,IAAA,SAAA,CAAO,MAAM,SAAS,SAAqC,wCAAA,EAC3DA,EAAA,IAAA,SAAA,CAAO,MAAM,aAAa,SAA2B,6BAAA,CAAA,CAAA,CAAA,CAAA,CACxD,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,sBAAA,EACAA,EAAA,IAAC,WAAA,CACC,MAAOvB,EAAS,WAChB,SAAWwB,GAAMjB,EAAkB,aAAciB,EAAE,OAAO,KAAK,EAC/D,KAAM,EACN,UAAU,2GACV,YAAY,6DACZ,SAAQ,EAAA,CAAA,CACV,EACF,SAEC,MACC,CAAA,SAAA,CAACD,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,yBAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOvB,EAAS,KAAK,KAAK,IAAI,EAC9B,SAAWwB,GAAMjB,EAAkB,OAAQiB,EAAE,OAAO,MAAM,MAAM,GAAG,EAAE,OAAWvC,EAAI,KAAA,CAAM,EAAE,OAAO,OAAO,CAAC,EAC3G,UAAU,2GACV,YAAY,oCAAA,CAAA,CACd,EACF,SAEC,MACC,CAAA,SAAA,CAACsC,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,mBAAA,EACAD,EAAA,KAAC,SAAA,CACC,MAAOtB,EAAS,SAChB,SAAWwB,GAAMjB,EAAkB,WAAYiB,EAAE,OAAO,KAAK,EAC7D,UAAU,2GAEV,SAAA,CAACD,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAO,UAAA,EACzBA,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAS,YAAA,EAC3BA,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAI,OAAA,EACtBA,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAK,QAAA,EACvBA,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAK,QAAA,EACvBA,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAM,SAAA,EACxBA,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAM,SAAA,EACxBA,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAK,QAAA,EACvBA,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAK,QAAA,EACvBA,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAO,SAAA,CAAA,CAAA,CAAA,CAAA,CAC5B,CACF,CAAA,CAAA,EACF,EAGJ,IAAK,GAED,OAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAW,cAAA,EAE9D,OAAO,QAAQnB,CAAa,EAAE,IAAI,CAAC,CAACjD,EAAWG,CAAG,IAChDgE,EAAA,KAAA,MAAA,CAAoB,UAAU,wBAC7B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yBAA0B,SAAUpE,EAAA,EAClDmE,EAAA,KAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMhE,EAAI,SAAS,MAAM,EAClC,UAAU,yEACX,SAAA,CAAA,OACMH,CAAA,CAAA,CAAA,CACP,EACF,EAEAoE,EAAA,IAAC,QAAA,CACC,IAAAjE,EACA,KAAK,OACL,SAAQ,GACR,OACEH,IAAc,SAAW,UACzBA,IAAc,SAAW,UACzBA,IAAc,QAAU,UACxB,uBAEF,SAAWqE,GAAMb,EAAoBxD,EAAsCqE,EAAE,OAAO,KAAK,EACzF,UAAU,QAAA,CACZ,EAECD,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,WAAWpE,CAAoC,EAAE,IAAI,CAACD,EAAM6D,IAC1DO,EAAA,KAAA,MAAA,CAAgB,UAAU,2DACzB,SAAA,CAAAC,EAAA,IAAC,OAAK,CAAA,UAAU,mBAAoB,SAAArE,EAAK,KAAK,EAC9CqE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMT,EAAW3D,EAAsC4D,CAAK,EACrE,UAAU,0CACX,SAAA,QAAA,CAAA,CAED,CARQ,EAAAA,CASV,CACD,CACH,CAAA,CAAA,CAAA,EAvCQ5D,CAwCV,CACD,CAAA,EACH,EAGJ,QACS,OAAA,IAAA,CAEb,EAGE,OAAAmE,EAAA,KAAC,MAAI,CAAA,UAAU,sDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAuB,0BAAA,EAC5EA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,qGAAA,CAAA,CAAA,EACF,EAGCA,EAAA,IAAA,MAAA,CAAI,UAAU,yCACZ,UAAC,EAAG,EAAG,CAAC,EAAE,IAAKK,GACbN,EAAA,KAAA,MAAA,CAAe,UAAU,oBACxB,SAAA,CAAAC,EAAA,IAAC,MAAA,CACC,UAAW,6EACTK,GAAQnC,EACJ,2BACA,2BACN,GAEC,SAAAmC,CAAA,CACH,EACCA,EAAO,GACNL,EAAA,IAAC,MAAA,CACC,UAAW,iBACTK,EAAOnC,EAAc,gBAAkB,aACzC,EAAA,CAAA,CACF,GAfMmC,CAiBV,CACD,EACH,EAGCP,EAAkB,EAGnBC,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CACE,WAAc,GACbA,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAM7B,EAAegB,GAAQA,EAAO,CAAC,EAC9C,UAAU,6EACV,SAAUd,EACX,SAAA,UAAA,CAAA,EAIL,EAEA0B,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAASjC,EACT,UAAU,6EACV,SAAUM,EACX,SAAA,QAAA,CAED,EAECH,EAAc,EACb8B,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAM7B,EAAegB,GAAQA,EAAO,CAAC,EAC9C,UAAU,oEACV,SAAU,CAACV,EAAS,OAAS,CAACA,EAAS,aAAeA,EAAS,cAAc,SAAW,EACzF,SAAA,MAAA,CAAA,EAIDuB,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAASL,EACT,SAAUtB,GAAgB,CAACI,EAAS,OAAS,CAACA,EAAS,aAAeA,EAAS,cAAc,SAAW,EACxG,UAAU,wFAET,WAAe,cAAgB,gBAAA,CAAA,CAClC,CAEJ,CAAA,CAAA,EACF,EAGAsB,EAAAA,KAAC,MAAI,CAAA,UAAU,4DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAA+B,kCAAA,EAChFD,EAAAA,KAAC,KAAG,CAAA,UAAU,oCACZ,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAA0D,4DAAA,CAAA,EAC9DA,EAAAA,IAAC,MAAG,SAAmD,qDAAA,CAAA,EACvDA,EAAAA,IAAC,MAAG,SAAkD,oDAAA,CAAA,EACtDA,EAAAA,IAAC,MAAG,SAAsD,wDAAA,CAAA,EAC1DA,EAAAA,IAAC,MAAG,SAAkE,oEAAA,CAAA,CAAA,CACxE,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,EChSA,MAAMM,EAAwB,CAE5B,MAAM,mBAAmBC,EAAsC,CACzD,GAAA,CACF,MAAMhF,EAAOC,EAAIC,EAAI,iBAAkB8E,EAAQ,MAAM,EAAGA,CAAO,QACxD7E,EAAO,CACN,cAAA,MAAM,gCAAiCA,CAAK,EAC9C,IAAI,MAAM,gCAAgC,CAAA,CAClD,CAIF,MAAM,sBACJ8E,EACAC,EACAC,EAC4B,CACxB,GAAA,CACF,MAAMC,EAAc,MAAM,KAAK,gBAAgBH,CAAM,EACrD,GAAI,CAACG,EAAmB,MAAA,IAAI,MAAM,wBAAwB,EAG1D,MAAM5D,EAAIC,EACRC,EAAWxB,EAAI,gBAAgB,EAC/ByB,EAAM,UAAUuD,CAAS,GAAI,KAAM,IAAI,EACvCrD,EAAM,EAAE,CACV,EAGMwD,GADW,MAAMvD,EAAQN,CAAC,GACE,KAC/B,IAAIvB,GAAOA,EAAI,KAAK,CAAiB,EACrC,OAAkB+E,GAAAA,EAAQ,SAAWC,CAAM,EAGxCK,EAA6B,CAAC,EAEpC,UAAWC,KAASF,EAAkB,CACpC,MAAMG,EAAa,KAAK,8BAA8BJ,EAAaG,EAAOL,EAAWC,CAAI,EAEzF,GAAIK,EAAa,GAAI,CACnB,MAAMC,EAAmC,CACvC,GAAI,SAAS,KAAK,IAAK,CAAA,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAClE,SAAUN,IAAS,SAAWF,EAASM,EAAM,OAC7C,SAAUJ,IAAS,SAAWF,EAASM,EAAM,OAC7C,UAAAL,EACA,gBAAiB,KAAK,0BAA0BE,EAAaG,CAAK,EAClE,WAAAC,EACA,OAAQ,UACR,MAAO,CACL,WAAY,CAAC,EACb,sBAAuB,CAAC,EACxB,UAAW,WACX,eAAgB,CAAA,CAClB,EACA,SAAU,CACR,kBAAmB,EACnB,cAAe,EACf,sBAAuB,EACvB,eAAgB,CAAA,CAClB,EACA,cAAe,CACb,gBAAiB,QACjB,UAAW,SACX,SAAU,KACV,kBAAmB,EACrB,EACA,UAAWzE,EAAU,IAAI,EACzB,aAAcA,EAAU,IAAI,CAC9B,EAEAuE,EAAQ,KAAKG,CAAe,CAAA,CAC9B,CAGK,OAAAH,EAAQ,KAAK,CAACI,EAAGC,IAAMA,EAAE,WAAaD,EAAE,UAAU,QAClDvF,EAAO,CACN,cAAA,MAAM,oCAAqCA,CAAK,EAClD,IAAI,MAAM,mCAAmC,CAAA,CACrD,CAIF,MAAM,2BAA2ByF,EAA2F,CACtH,GAAA,CACF,MAAMC,EAAY,WAAW,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAE5EC,EAA6C,CACjD,GAAGF,EACH,GAAIC,EACJ,UAAW9E,EAAU,IAAI,EACzB,aAAcA,EAAU,IAAI,CAC9B,EAEA,aAAMf,EAAOC,EAAIC,EAAI,yBAA0B2F,CAAS,EAAGC,CAAoB,EAExED,QACA1F,EAAO,CACN,cAAA,MAAM,wCAAyCA,CAAK,EACtD,IAAI,MAAM,wCAAwC,CAAA,CAC1D,CAIF,MAAM,yBACJ0F,EACAZ,EACAE,EACAY,EACAC,EACe,CACX,GAAA,CACF,MAAMC,EAAahG,EAAIC,EAAI,yBAA0B2F,CAAS,EAE9D,MAAM5E,EAAUgF,EAAY,CAC1B,CAAC,gBAAgBhB,CAAM,EAAE,EAAG,CAC1B,KAAAE,EACA,OAAAY,EACA,mBAAAC,EACA,SAAUjF,EAAU,IAAI,EACxB,kBAAmB,CACrB,EACA,aAAcA,EAAU,IAAI,CAAA,CAC7B,QACMZ,EAAO,CACN,cAAA,MAAM,uCAAwCA,CAAK,EACrD,IAAI,MAAM,sCAAsC,CAAA,CACxD,CAIF,MAAM,2BAA2B+F,EAAgF,CAC3G,GAAA,CACF,MAAMC,EAAS,QAAQ,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAEtEC,EAAqC,CACzC,GAAGF,EACH,GAAIC,EACJ,WAAY,CACV,YAAa,EACb,YAAa,EACb,cAAe,EACf,iBAAkB,CAAA,CAAC,CAEvB,EAEA,aAAMnG,EAAOC,EAAIC,EAAI,0BAA2BiG,CAAM,EAAGC,CAAY,EAE9DD,QACAhG,EAAO,CACN,cAAA,MAAM,yCAA0CA,CAAK,EACvD,IAAI,MAAM,yCAAyC,CAAA,CAC3D,CAIF,MAAM,gBAAgB8E,EAA8C,CAC9D,GAAA,CACF,MAAMoB,EAASpG,EAAIC,EAAI,iBAAkB+E,CAAM,EACzCqB,EAAU,MAAMC,EAAOF,CAAM,EAE/B,OAAAC,EAAQ,SACHA,EAAQ,KAAK,EAGf,WACAnG,EAAO,CACN,cAAA,MAAM,+BAAgCA,CAAK,EAC7C,IAAI,MAAM,6BAA6B,CAAA,CAC/C,CAIF,MAAM,6BAA6B8E,EAAiD,CAC9E,GAAA,CACF,MAAMzD,EAAIC,EACRC,EAAWxB,EAAI,wBAAwB,EACvCyB,EAAM,gBAAgBsD,CAAM,GAAI,KAAM,IAAI,EAC1CrD,EAAQ,eAAgB,MAAM,CAChC,EAGA,OADiB,MAAME,EAAQN,CAAC,GAChB,KAAK,IAAIvB,GAAOA,EAAI,MAA8B,QAC3DE,EAAO,CACN,cAAA,MAAM,6CAA8CA,CAAK,EAC3D,IAAI,MAAM,2CAA2C,CAAA,CAC7D,CAIF,MAAM,yBAAyBmB,EAAyD,CAClF,GAAA,CACF,IAAIE,EAAIC,EACNC,EAAWxB,EAAI,yBAAyB,EACxC0B,EAAQ,yBAA0B,MAAM,CAC1C,EAEA,OAAIN,IACFE,EAAIC,EAAMD,EAAGG,EAAM,gBAAiB,KAAML,CAAa,CAAC,IAGzC,MAAMQ,EAAQN,CAAC,GAChB,KAAK,IAAIvB,GAAOA,EAAI,MAA8B,QAC3DE,EAAO,CACN,cAAA,MAAM,yCAA0CA,CAAK,EACvD,IAAI,MAAM,uCAAuC,CAAA,CACzD,CAIM,8BACNsC,EACA+D,EACAtB,EACAuB,EACQ,CACR,IAAIC,EAAQ,EAGN,MAAAC,EAAYlE,EAAK,OAAOyC,CAAS,EACjC0B,EAAiBJ,EAAU,OAAOtB,CAAS,EAEjD,GAAIyB,GAAaC,EAAgB,CAC/B,MAAMC,EAAc,CAAC,WAAY,eAAgB,WAAY,QAAQ,EAC/DC,EAAYD,EAAY,QAAQF,EAAU,KAAK,EAC/CI,EAAiBF,EAAY,QAAQD,EAAe,KAAK,GAE3DH,IAAa,UAAYK,EAAYC,GAE9BN,IAAa,UAAYM,EAAiBD,KAC1CJ,GAAA,GACX,CAIF,MAAMM,EAAiB,OAAO,KAAKvE,EAAK,iBAAiB,EAAE,OACzDwE,GAAWT,EAAU,kBAAkBS,CAAO,CAChD,EACAP,GAAS,KAAK,IAAIM,EAAe,OAAS,GAAI,EAAE,EAG1C,MAAAE,EAAkB,KAAK,IAAIzE,EAAK,iBAAiB,YAAc+D,EAAU,iBAAiB,WAAW,EAC3GE,GAAS,KAAK,IAAI,EAAG,GAAKQ,EAAkB,CAAC,EAGvC,MAAAC,EAAkB1E,EAAK,aAAa,UAAU,OAC1C2E,GAAAZ,EAAU,aAAa,UAAU,SAASY,CAAI,CACxD,EACA,OAAAV,GAAS,KAAK,IAAIS,EAAgB,OAAS,EAAG,EAAE,EAEzC,KAAK,IAAIT,EAAO,GAAG,CAAA,CAGpB,0BAA0BjE,EAAoB+D,EAA6C,CACjG,MAAMQ,EAAiB,OAAO,KAAKvE,EAAK,iBAAiB,EAAE,OACzDwE,GAAWT,EAAU,kBAAkBS,CAAO,CAChD,EAEA,OAAOD,EAAe,OAAS,EAAIA,EAAe,CAAC,EAAI,MAAA,CAE3D,CAEa,MAAAK,EAA0B,IAAItC,GC5a9BuC,GAAkE,CAAC,CAC9E,oBAAAC,CACF,IAAM,CACE,KAAA,CAAE,KAAA9E,CAAK,EAAIC,EAAQ,EACnB,CAAC8E,EAAWC,CAAY,EAAI5E,EAAAA,SAAwC,QAAQ,EAC5E,CAAC6E,EAAeC,CAAgB,EAAI9E,EAAAA,SAAyB,CAAA,CAAE,EAC/D,CAAC+E,EAAmBC,CAAoB,EAAIhF,EAAAA,SAA4B,CAAA,CAAE,EAC1E,CAACiF,EAAeC,CAAgB,EAAIlF,EAAAA,SAAS,EAAE,EAC/C,CAACZ,EAAY+F,CAAa,EAAInF,EAAAA,SAAS,EAAE,EACzC,CAACoF,EAASC,CAAU,EAAIrF,EAAAA,SAAS,EAAK,EAEtCsF,EAAkB,CACtB,aAAc,WAAY,gBAAiB,YAAa,UACxD,QAAS,qBAAsB,cAAe,aAAc,WAC9D,EAEMC,EAAiB,CACrB,uBAAwB,iBAAkB,sBAC1C,eAAgB,oBAAqB,QAAS,oBAC9C,mBAAoB,iCAAkC,cACxD,EAEAC,EAAAA,UAAU,IAAM,CACV5F,GACW6F,EAAA,CACf,EACC,CAAC7F,CAAI,CAAC,EAET,MAAM6F,EAAe,SAAY,CAC/B,GAAK7F,EAEL,CAAAyF,EAAW,EAAI,EACX,GAAA,CAEF,MAAMlD,EAAU,MAAMqC,EAAwB,gBAAgB5E,EAAK,GAAG,EACtE,GAAIuC,EAAS,CAEX,MAAMe,EAAS,OAAO,KAAKf,EAAQ,MAAM,EACrC,GAAAe,EAAO,OAAS,EAAG,CACf,MAAAT,EAAU,MAAM+B,EAAwB,sBAC5C5E,EAAK,IACLsD,EAAO,CAAC,EACR,QACF,EACA8B,EAAqBvC,CAAO,CAAA,CAC9B,QAEKnF,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,CAAA,QAC/C,CACA+H,EAAW,EAAK,CAAA,EAEpB,EAEMK,EAAoB,MAAOC,EAAerD,IAA8B,CACxE,GAAA,GAAC1C,GAAQ,CAAC+F,GAEd,CAAAN,EAAW,EAAI,EACX,GAAA,CACI,MAAA5C,EAAU,MAAM+B,EAAwB,sBAC5C5E,EAAK,IACL+F,EACArD,CACF,EACA0C,EAAqBvC,CAAO,QACrBnF,EAAO,CACN,QAAA,MAAM,0CAA2CA,CAAK,CAAA,QAC9D,CACA+H,EAAW,EAAK,CAAA,EAEpB,EAEMO,EAAkB,IACrBjE,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,SACb,SAAAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,qDACZ,MAAOxC,EACP,SAAWyC,GAAMsD,EAActD,EAAE,OAAO,KAAK,EAC7C,UAAU,0GAAA,CAAA,EAEd,EACAF,EAAA,KAAC,SAAA,CACC,MAAOsD,EACP,SAAWpD,GAAMqD,EAAiBrD,EAAE,OAAO,KAAK,EAChD,UAAU,oGAEV,SAAA,CAACD,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAU,aAAA,EAC1B0D,EAAgB,IACfO,GAAAjE,EAAA,IAAC,UAAsB,MAAOiE,EAAW,SAA5BA,CAAA,EAAAA,CAAqC,CACnD,CAAA,CAAA,CAAA,CACH,EACF,EAEAlE,EAAAA,KAAC,MAAI,CAAA,UAAU,uDAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAwB,2BAAA,QAChF,MAAI,CAAA,UAAU,wCACZ,SAAA2D,EAAe,IACdI,GAAA/D,EAAA,IAAC,SAAA,CAEC,QAAS,IAAM8D,EAAkBC,EAAO,QAAQ,EAChD,UAAU,qHAET,SAAAA,CAAA,EAJIA,CAAA,CAMR,CACH,CAAA,CAAA,EACF,EAGCZ,EAAkB,IAAIrC,GACpBf,EAAAA,KAAA,MAAA,CAAmB,UAAU,6FAC5B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,8BAA+B,SAAAc,EAAM,UAAU,EAC5DA,EAAM,iBACJf,OAAA,OAAA,CAAK,UAAU,2EACb,SAAA,CAAMe,EAAA,gBAAgB,mBAAA,CACzB,CAAA,CAAA,EAEJ,QACC,MAAI,CAAA,UAAU,aACb,SAACf,EAAA,KAAA,MAAA,CAAI,UAAU,qCACZ,SAAA,CAAMe,EAAA,WAAW,SAAA,CAAA,CACpB,CACF,CAAA,CAAA,EACF,EAEAf,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAM,QAAA,CAAA,EAAS,IAAEc,EAAM,MAAM,WAAW,KAAK,IAAI,GAAK,eAAA,EAChE,EACCA,EAAM,MAAM,sBAAsB,OAAS,GACzCf,OAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAkB,oBAAA,CAAA,EAAS,IAAEc,EAAM,MAAM,sBAAsB,KAAK,IAAI,CAAA,EAClF,EAEFf,EAAAA,KAAC,MAAI,CAAA,UAAU,wBACb,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAc,gBAAA,CAAA,EAAS,IAAEc,EAAM,cAAc,gBAAgB,MAAIA,EAAM,cAAc,SAAA,CAC/F,CAAA,CAAA,EACF,EAEAf,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAAS,IAAM8C,IAAsBhC,EAAM,EAAE,EAC7C,UAAU,qGACX,SAAA,SAAA,CAED,EACCd,EAAA,IAAA,SAAA,CAAO,UAAU,uGAAuG,SAEzH,YAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAzCQ,EAAAc,EAAM,EA0ChB,CACD,CAAA,CACH,CAAA,CAAA,EACF,EAGIoD,EAAkB,IACrBnE,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6CAA6C,SAAoB,uBAAA,EAC9EA,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAGpC,gJAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,sFAAsF,SAExG,uBAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAoB,uBAAA,EACpEA,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,WAAgB,MAAM,EAAG,CAAC,EAAE,IAAI+D,GAC9BhE,EAAA,KAAA,MAAA,CAAgB,UAAU,oCACzB,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAyB,SAAM+D,EAAA,EAC/C/D,EAAA,IAAC,SAAA,CACC,QAAS,IAAM8D,EAAkBC,EAAO,QAAQ,EAChD,UAAU,gDACX,SAAA,eAAA,CAAA,CAED,CAPQ,EAAAA,CAQV,CACD,CACH,CAAA,CAAA,EACF,EAEAhE,EAAAA,KAAC,MAAI,CAAA,UAAU,iDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAkB,qBAAA,EAClEA,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,WAAe,MAAM,EAAG,CAAC,EAAE,IAAI+D,GAC7BhE,EAAA,KAAA,MAAA,CAAgB,UAAU,oCACzB,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAyB,SAAM+D,EAAA,EAC/C/D,EAAA,IAAC,SAAA,CACC,QAAS,IAAM8D,EAAkBC,EAAO,QAAQ,EAChD,UAAU,gDACX,SAAA,iBAAA,CAAA,CAED,CAPQ,EAAAA,CAQV,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,SAGC,MACC,CAAA,SAAA,CAAC/D,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAuB,0BAAA,QAC/E,MAAI,CAAA,UAAU,wCACZ,SAAAmD,EAAkB,OAAYgB,GAAAA,EAAE,SAAW,QAAQ,EAAE,IAAIrD,GACvDf,EAAAA,KAAA,MAAA,CAAmB,UAAU,iDAC5B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAc,EAAM,UAAU,EAC1Dd,EAAA,IAAA,OAAA,CAAK,UAAU,uDAAuD,SAEvE,QAAA,CAAA,CAAA,EACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6BAA6B,SAAA,CAAA,aAC/Be,EAAM,SAAS,cAAc,OAAKA,EAAM,SAAS,kBAAkB,WAAA,EAChF,EACAf,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,gFAAgF,SAElG,mBAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,kFAAkF,SAEpG,eAAA,CAAA,CAAA,CACF,CAAA,CAAA,GAjBQc,EAAM,EAkBhB,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIsD,EAAiB,IACpBrE,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAY,eAAA,EACpEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAGlC,wHAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,kFAAkF,SAEpG,oBAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAe,kBAAA,EAChEA,EAAA,IAAC,MAAI,CAAA,UAAU,YACZ,SAAA0D,EAAgB,IACfK,GAAAhE,EAAA,KAAC,MAAgB,CAAA,UAAU,oCACzB,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAyB,SAAM+D,EAAA,EAC/C/D,EAAA,IAAC,SAAA,CACC,QAAS,IAAM8D,EAAkBC,EAAO,QAAQ,EAChD,UAAU,4CACX,SAAA,cAAA,CAAA,CAED,CAPQ,EAAAA,CAQV,CACD,CACH,CAAA,CAAA,EACF,EAEAhE,EAAAA,KAAC,MAAI,CAAA,UAAU,iDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAiB,oBAAA,EAClEA,EAAA,IAAC,MAAI,CAAA,UAAU,YACZ,SAAA2D,EAAe,IACdI,GAAAhE,EAAA,KAAC,MAAgB,CAAA,UAAU,oCACzB,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAyB,SAAM+D,EAAA,EAC/C/D,EAAA,IAAC,SAAA,CACC,QAAS,IAAM8D,EAAkBC,EAAO,QAAQ,EAChD,UAAU,4CACX,SAAA,YAAA,CAAA,CAED,CAPQ,EAAAA,CAQV,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,SAGC,MACC,CAAA,SAAA,CAAC/D,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAqB,wBAAA,QAC7E,MAAI,CAAA,UAAU,wCACZ,SAAAmD,EAAkB,OAAYgB,GAAAA,EAAE,SAAW,QAAQ,EAAE,IAAIrD,GACvDf,EAAAA,KAAA,MAAA,CAAmB,UAAU,iDAC5B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAc,EAAM,UAAU,EAC1Dd,EAAA,IAAA,OAAA,CAAK,UAAU,qDAAqD,SAErE,UAAA,CAAA,CAAA,EACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kDACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAQ,UAAA,CAAA,SACb,OAAM,CAAA,SAAA,CAAAc,EAAM,SAAS,cAAc,GAAA,CAAC,CAAA,CAAA,EACvC,EACAd,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,+BACV,MAAO,CAAE,MAAO,GAAGc,EAAM,SAAS,aAAa,GAAI,CAAA,CAAA,CAEvD,CAAA,CAAA,EACF,EACAf,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACZ,SAAA,CAAAe,EAAM,SAAS,kBAAkB,qBAAA,EACpC,EACAf,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,4EAA4E,SAE9F,oBAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,kFAAkF,SAEpG,cAAA,CAAA,CAAA,CACF,CAAA,CAAA,GA7BQc,EAAM,EA8BhB,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAIA,OAAAf,EAAA,KAAC,MAAI,CAAA,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAA6B,gCAAA,EAClFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,wGAAA,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,gCACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,iBACZ,SAAA,CACC,CAAE,GAAI,SAAU,MAAO,gBAAiB,KAAM,IAAK,EACnD,CAAE,GAAI,SAAU,MAAO,gBAAiB,KAAM,IAAK,EACnD,CAAE,GAAI,QAAS,MAAO,eAAgB,KAAM,IAAK,CAAA,EACjD,IACAqE,GAAAtE,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMiD,EAAaqB,EAAI,EAAS,EACzC,UAAW,4CACTtB,IAAcsB,EAAI,GACd,oCACA,4EACN,GAEA,SAAA,CAAArE,EAAA,IAAC,OAAK,CAAA,UAAU,OAAQ,SAAAqE,EAAI,KAAK,EAChCA,EAAI,KAAA,CAAA,EATAA,EAAI,EAAA,CAWZ,EACH,CACF,CAAA,EAGCb,EACExD,EAAA,IAAA,MAAA,CAAI,UAAU,yCACb,SAACA,MAAA,MAAA,CAAI,UAAU,gEAAA,CAAiE,CAClF,CAAA,EAGGD,EAAAA,KAAAuE,EAAAA,SAAA,CAAA,SAAA,CAAAvB,IAAc,UAAYiB,EAAgB,EAC1CjB,IAAc,UAAYmB,EAAgB,EAC1CnB,IAAc,SAAWqB,EAAe,CAAA,CAC3C,CAAA,CAAA,EAEJ,CAEJ,EC9PA,MAAMG,EAAwB,CAE5B,MAAM,oCAAoC/D,EAAgB1D,EAAsB,GAAsC,CAChH,GAAA,CAEF,MAAM0H,EAAgB,MAAM,KAAK,yBAAyBhE,CAAM,EAChE,GAAI,CAACgE,EACG,MAAA,IAAI,MAAM,0BAA0B,EAG5C,MAAMC,EAA2C,CAAC,EAGvC,UAAAjC,KAAWgC,EAAc,YAAY,kBAAmB,CACjE,MAAME,EAAkB,MAAM,KAAK,oBAAoBlC,EAAS,CAAC,EAEjE,UAAWtH,KAAWwJ,EAAiB,CAC/B,MAAAC,EAAiB,MAAM,KAAK,4BAChCnE,EACAtF,EAAQ,GACR,WACA,oBACAsH,CACF,EACAiC,EAAgB,KAAKE,CAAc,CAAA,CACrC,CAIF,MAAMC,EAA0B,MAAM,KAAK,gCAAgCpE,EAAQgE,CAAa,EAChFC,EAAA,KAAK,GAAGG,CAAuB,EAG/C,MAAMC,EAA0B,MAAM,KAAK,gCAAgCrE,EAAQ,CAAC,EACpEiE,EAAA,KAAK,GAAGI,CAAuB,EAG/C,MAAMC,EAAwB,MAAM,KAAK,sCAAsCtE,EAAQ,CAAC,EACxEiE,EAAA,KAAK,GAAGK,CAAqB,EAG7C,MAAMC,EAAwBN,EAC3B,KAAK,CAACxD,EAAGC,IAAMA,EAAE,MAAQD,EAAE,KAAK,EAChC,MAAM,EAAGnE,CAAW,EAGjB,aAAA,KAAK,0BAA0B0D,EAAQuE,CAAqB,EAE3DA,QACArJ,EAAO,CACN,cAAA,MAAM,iDAAkDA,CAAK,EAC/D,IAAI,MAAM,iDAAiD,CAAA,CACnE,CAIF,MAAM,uBAAuBsJ,EAAiBnI,EAAkD,CAC1F,GAAA,CACF,IAAIE,EAAIC,EACNC,EAAWxB,EAAI,iBAAiB,EAChCyB,EAAM,sBAAuB,KAAM,EAAI,EACvCC,EAAQ,0BAA2B,MAAM,EACzCC,EAAM,EAAE,CACV,EAEA,OAAI4H,IACFjI,EAAIC,EAAMD,EAAGG,EAAM,SAAU,KAAM8H,CAAM,CAAC,GAGxCnI,IACFE,EAAIC,EAAMD,EAAGG,EAAM,gBAAiB,KAAML,CAAa,CAAC,IAGzC,MAAMQ,EAAQN,CAAC,GAChB,KAAK,IAAIvB,GAAOA,EAAI,MAAuB,QACpDE,EAAO,CACN,cAAA,MAAM,qCAAsCA,CAAK,EACnD,IAAI,MAAM,oCAAoC,CAAA,CACtD,CAIF,MAAM,gBACJP,EACA8J,EACA/J,EACkC,CAC9B,GAAA,CAEF,MAAMgK,EAAe,MAAM,KAAK,oBAAoBhK,CAAO,EAGrDiK,EAAmB,MAAM,KAAK,2BAA2BjK,CAAO,EAGhEkK,EACJF,EAAa,oBAAoB,kBAAoB,IACrDC,EAAiB,gBACjBjK,EAAQ,UAAU,uBAAyB,SAEvCmK,EAA4C,CAChD,UAAAlK,EACA,eAAgB,aAChB,OAAQiK,EAAsB,wBACtBF,EAAa,QAAU,WAAa,iBAC5C,SAAUA,EAAa,SACvB,gBAAiBA,EAAa,gBAC9B,oBAAqB,CACnB,kBAAmBA,EAAa,oBAAoB,kBACpD,iBAAkBC,EAAiB,cACnC,uBAAwBD,EAAa,oBAAoB,uBACzD,iBAAkB,KAAK,0BAA0BhK,CAAO,CAC1D,EACA,YAAaoB,EAAU,IAAI,CAC7B,EAGA,aAAMf,EAAOC,EAAIC,EAAI,qBAAsBN,CAAS,EAAGkK,CAAgB,EAGnED,GACF,MAAM,KAAK,8BAA8BlK,EAAQ,eAAiB,CAAA,EAAIC,CAAS,EAG1EkK,QACA3J,EAAO,CACN,cAAA,MAAM,4BAA6BA,CAAK,EAC1C,IAAI,MAAM,4BAA4B,CAAA,CAC9C,CAIF,MAAM,yBAAyB8E,EAAuD,CAChF,GAAA,CACF,MAAMoB,EAASpG,EAAIC,EAAI,0BAA2B+E,CAAM,EAClDqB,EAAU,MAAMC,EAAOF,CAAM,EAE/B,GAAAC,EAAQ,SACV,OAAOA,EAAQ,KAAK,EAItB,MAAMyD,EAA0C,CAC9C,OAAA9E,EACA,yBAA0B,CAAC,EAC3B,oBAAqB,CACnB,cAAe,CAAC,EAChB,aAAc,CAAC,EACf,cAAe,CAAC,EAChB,eAAgB,CAAC,EACjB,kBAAmB,CAAA,CACrB,EACA,sBAAuB,CACrB,yBAA0B,EAC1B,oBAAqB,EACrB,wBAAyB,EACzB,sBAAuB,EACvB,uBAAwB,CAC1B,EACA,gBAAiB,CACf,qBAAsB,CAAC,EACvB,YAAa,CAAC,EACd,mBAAoB,CAAC,EACrB,sBAAuB,CAAA,CACzB,EACA,YAAa,CACX,aAAc,CAAC,WAAY,QAAS,WAAW,EAC/C,kBAAmB,CAAC,EACpB,cAAe,CAAC,EAChB,qBAAsB,CAAA,CACxB,EACA,YAAalE,EAAU,IAAI,CAC7B,EAEM,aAAAf,EAAOqG,EAAQ0D,CAAgB,EAC9BA,QACA5J,EAAO,CACN,cAAA,MAAM,wCAAyCA,CAAK,EACtD,IAAI,MAAM,sCAAsC,CAAA,CACxD,CAIF,MAAM,yBACJ8E,EACArF,EACAoK,EACe,CACX,GAAA,CACF,MAAMC,EAAehK,EAAIC,EAAI,0BAA2B+E,CAAM,EAO9D,MAAMhE,EAAUgJ,EAAc,CAC5B,CANkB,uBAAuBD,IAAoB,OAAS,gBACpDA,IAAoB,OAAS,eAC7BA,IAAoB,QAAU,gBAC9B,mBAAmB,EAGzB,EAAG9I,EAAWtB,CAAS,EACnC,YAAamB,EAAU,IAAI,CAAA,CAC5B,EAGK,MAAA,KAAK,wBAAwBnB,EAAWoK,CAAe,QACtD7J,EAAO,CACN,cAAA,MAAM,sCAAuCA,CAAK,EACpD,IAAI,MAAM,sCAAsC,CAAA,CACxD,CAIF,MAAM,sBACJP,EACAsK,EACAC,EACAC,EACe,CACX,GAAA,CACI,MAAAC,EAAkBpK,EAAIC,EAAI,wBAAyB,GAAGN,CAAS,IAAIsK,CAAU,EAAE,EAErF,MAAMlK,EAAOqK,EAAiB,CAC5B,UAAAzK,EACA,WAAAsK,EACA,cAAAC,EACA,MAAOC,GAAS,GAChB,WAAYrJ,EAAU,IAAI,EAC1B,aAAc,yBAAA,CACf,EAGK,MAAA,KAAK,2BAA2BnB,CAAS,QACxCO,EAAO,CACN,cAAA,MAAM,oCAAqCA,CAAK,EAClD,IAAI,MAAM,mCAAmC,CAAA,CACrD,CAIF,MAAc,oBAAoB8G,EAAiB1F,EAAyD,CACtG,GAAA,CACF,MAAMC,EAAIC,EACRC,EAAWxB,EAAI,mBAAmB,EAClCyB,EAAM,gBAAiB,iBAAkBsF,CAAO,EAChDtF,EAAM,oBAAqB,KAAM,UAAU,EAC3CC,EAAQ,mBAAoB,MAAM,EAClCC,EAAMN,CAAW,CACnB,EAGA,OADiB,MAAMO,EAAQN,CAAC,GAChB,KAAK,IAAIvB,GAAOA,EAAI,MAAiC,QAC9DE,EAAO,CACN,eAAA,MAAM,oCAAqCA,CAAK,EACjD,CAAC,CAAA,CACV,CAGF,MAAc,4BACZ8E,EACArF,EACA8J,EACAY,EACAC,EACgC,CAGzB,MAAA,CACL,GAHuB,OAAO,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAInF,OAAAtF,EACA,UAAArF,EACA,YAAA8J,EACA,mBAAAY,EACA,MAAO,KAAK,MAAM,KAAK,OAAO,EAAI,EAAE,EAAI,GACxC,UAAW,CACT,eAAgB,CAAC,4BAA4BC,CAAe,EAAE,EAC9D,kBAAmB,CAAC,cAAcA,CAAe,UAAU,EAC3D,sBAAuB,CAAC,yBAA0B,0BAA0B,EAC5E,wBAAyB,CAAC,gCAAgC,CAC5D,EACA,oBAAqB,CACnB,mBAAoB,GACpB,gBAAiB,GACjB,mBAAoB,CAAC,oCAAoC,CAC3D,EACA,UAAWxJ,EAAU,IAAI,EACzB,UAAWA,EAAU,SAAS,IAAI,KAAK,KAAK,MAAQ,EAAI,GAAK,GAAK,GAAK,GAAI,CAAC,EAC5E,OAAQ,SACV,CAAA,CAGF,MAAc,oBAAoBpB,EAA4B,CAErD,MAAA,CACL,QAAS,GACT,SAAU,CAAC,EACX,gBAAiB,CAAC,EAClB,oBAAqB,CACnB,kBAAmB,KAAK,MAAM,KAAK,OAAA,EAAW,EAAE,EAChD,uBAAwB,KAAK,MAAM,KAAK,OAAO,EAAI,EAAE,EAAI,EAAA,CAE7D,CAAA,CAGF,MAAc,2BAA2BA,EAA4B,CAC5D,OAAAG,EAA0B,wBAAwBH,CAAO,CAAA,CAG1D,0BAA0BA,EAAsB,CAEtD,OAAO,KAAK,MAAM,KAAK,OAAO,EAAI,EAAE,EAAI,EAAA,CAG1C,MAAc,gCAAgCsF,EAAgBuF,EAAoE,CAEhI,MAAO,CAAC,CAAA,CAGV,MAAc,gCAAgCvF,EAAgB1D,EAAuD,CAEnH,MAAO,CAAC,CAAA,CAGV,MAAc,sCAAsC0D,EAAgB1D,EAAuD,CAEzH,MAAO,CAAC,CAAA,CAGV,MAAc,0BAA0B0D,EAAgBiE,EAAyD,CAC/G,MAAMe,EAAehK,EAAIC,EAAI,0BAA2B+E,CAAM,EAC9D,MAAMhE,EAAUgJ,EAAc,CAC5B,uCAAwCf,EACxC,YAAanI,EAAU,IAAI,CAAA,CAC5B,CAAA,CAGH,MAAc,wBAAwBnB,EAAmBoK,EAAwC,CAAA,CAIjG,MAAc,2BAA2BpK,EAAkC,CAAA,CAI3E,MAAc,8BAA8BwC,EAA0BxC,EAAkC,CAE9F,QAAA,IAAI,0CAA0CwC,EAAe,KAAK,IAAI,CAAC,kBAAkBxC,CAAS,EAAE,CAAA,CAEhH,CAEa,MAAA6K,EAA0B,IAAIzB,GCzd9B0B,GAA8D,CAAC,CAC1E,gBAAAC,CACF,IAAM,CACE,KAAA,CAAE,KAAAlI,CAAK,EAAIC,EAAQ,EACnB,CAACkI,EAAeC,CAAgB,EAAIhI,EAAAA,SAA+B,IAAI,EACvE,CAACqG,EAAiB4B,CAAkB,EAAIjI,EAAAA,SAAkC,CAAA,CAAE,EAC5E,CAACkI,EAAgBC,CAAiB,EAAInI,EAAAA,SAA0B,CAAA,CAAE,EAClE,CAACoF,EAASC,CAAU,EAAIrF,EAAAA,SAAS,EAAI,EACrC,CAACoI,EAAeC,CAAgB,EAAIrI,EAAAA,SAAmE,UAAU,EAEvHwF,EAAAA,UAAU,IAAM,CACV5F,GACgB0I,EAAA,CACpB,EACC,CAAC1I,CAAI,CAAC,EAET,MAAM0I,EAAoB,SAAY,CACpC,GAAK1I,EAEL,CAAAyF,EAAW,EAAI,EACX,GAAA,CACF,KAAM,CAACsC,EAAWY,EAAMC,CAAQ,EAAI,MAAM,QAAQ,IAAI,CACpDZ,EAAwB,yBAAyBhI,EAAK,GAAG,EACzDgI,EAAwB,oCAAoChI,EAAK,IAAK,CAAC,EACvEgI,EAAwB,uBAAuB,cAAc,CAAA,CAC9D,EAEDI,EAAiBL,CAAS,EAC1BM,EAAmBM,CAAI,EACvBJ,EAAkBK,CAAQ,QACnBlL,EAAO,CACN,QAAA,MAAM,gCAAiCA,CAAK,CAAA,QACpD,CACA+H,EAAW,EAAK,CAAA,EAEpB,EAEMoD,EAA2B,MAAO1L,EAAmBoK,IAAkD,CAC3G,GAAKvH,EAED,GAAA,CAGE,GAFJ,MAAMgI,EAAwB,yBAAyBhI,EAAK,IAAK7C,EAAWoK,CAAe,EAEvFA,IAAoB,QAAUA,IAAoB,WAAY,CAChE,MAAMuB,EAAU,MAAMd,EAAwB,oCAAoChI,EAAK,IAAK,CAAC,EAC7FqI,EAAmBS,CAAO,CAAA,QAErBpL,EAAO,CACN,QAAA,MAAM,sCAAuCA,CAAK,CAAA,CAE9D,EAEMqL,EAAiB,IACpBhH,OAAA,MAAA,CAAI,UAAU,uDAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAA0B,6BAAA,EACnFA,EAAAA,IAAC,OAAI,UAAU,YACZ,YAAiB,OAAO,QAAQmG,EAAc,wBAAwB,EAAE,MAAM,EAAG,CAAC,EAAE,IAAI,CAAC,CAAC3D,EAASwE,CAAQ,IAC1GjH,EAAA,KAAC,MAAkB,CAAA,UAAU,YAC3B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,yBAA0B,SAAQwC,EAAA,EAClDzC,EAAAA,KAAC,OAAK,CAAA,UAAU,gBAAiB,SAAA,CAASiH,EAAA,MAAM,GAAA,CAAC,CAAA,CAAA,EACnD,EACAhH,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,iCACV,MAAO,CAAE,MAAO,GAAGgH,EAAS,KAAK,GAAI,CAAA,CAAA,EAEzC,EACAjH,EAAAA,KAAC,MAAI,CAAA,UAAU,wBACZ,SAAA,CAAAiH,EAAS,eAAe,OAAO,sBAAoBA,EAAS,aAAa,OAAO,eAAA,CACnF,CAAA,CAAA,CAbQ,EAAAxE,CAcV,CACD,CACH,CAAA,CAAA,EACF,EAGAzC,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAsB,yBAAA,EAC/ED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAA0B,6BAAA,QACjE,OAAK,CAAA,UAAU,wCACb,SAAemG,GAAA,sBAAsB,0BAA4B,CACpE,CAAA,CAAA,EACF,EACApG,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAqB,wBAAA,EAC7DD,EAAAA,KAAC,OAAK,CAAA,UAAU,uCACb,SAAA,CAAAoG,GAAe,sBAAsB,qBAAuB,EAAE,MAAA,CACjE,CAAA,CAAA,EACF,EACApG,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAuB,0BAAA,QAC9D,OAAK,CAAA,UAAU,sCACb,SAAemG,GAAA,sBAAsB,wBAA0B,CAClE,CAAA,CAAA,EACF,EACApG,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAkB,qBAAA,QACzD,OAAK,CAAA,UAAU,wCACb,SAAemG,GAAA,sBAAsB,yBAA2B,CACnE,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGApG,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAa,gBAAA,EACtED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAAS,IAAMyG,EAAiB,iBAAiB,EACjD,UAAU,qGACX,SAAA,sBAAA,CAED,EACAzG,EAAA,IAAC,SAAA,CACC,QAAS,IAAMyG,EAAiB,UAAU,EAC1C,UAAU,8GACX,SAAA,kBAAA,CAED,EACCzG,EAAA,IAAA,SAAA,CAAO,UAAU,8GAA8G,SAEhI,gBAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,8GAA8G,SAEhI,cAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIiH,EAAwB,IAC3BlH,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAA4B,+BAAA,EAChFA,EAAA,IAAC,SAAA,CACC,QAAS0G,EACT,UAAU,2FACX,SAAA,SAAA,CAAA,CAED,EACF,EAEA1G,EAAA,IAAC,MAAI,CAAA,UAAU,wCACZ,SAAAyE,EAAgB,IACfyC,GAAAnH,EAAA,KAAC,MAAiB,CAAA,UAAU,2DAC1B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,mCACX,SAAA,CAAImH,EAAA,YAAY,OAAO,CAAC,EAAE,YAAgB,EAAAA,EAAI,YAAY,MAAM,CAAC,EAAE,UAAA,EACtE,EACAlH,EAAAA,IAAC,QAAK,UAAU,+DACb,WAAI,mBAAmB,QAAQ,IAAK,GAAG,CAC1C,CAAA,CAAA,EACF,QACC,MAAI,CAAA,UAAU,aACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,qCACZ,SAAA,CAAImH,EAAA,MAAM,SAAA,CAAA,CACb,CACF,CAAA,CAAA,EACF,EAEAnH,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAgB,kBAAA,CAAA,EAAS,IAAEkH,EAAI,UAAU,eAAe,KAAK,IAAI,CAAA,EAC3E,EACCA,EAAI,UAAU,kBAAkB,OAAS,GACvCnH,OAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAmB,qBAAA,CAAA,EAAS,IAAEkH,EAAI,UAAU,kBAAkB,KAAK,IAAI,CAAA,EACjF,EAEDA,EAAI,UAAU,sBAAsB,OAAS,GAC3CnH,OAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAuB,yBAAA,CAAA,EAAS,IAAEkH,EAAI,UAAU,sBAAsB,KAAK,IAAI,CAAA,CACzF,CAAA,CAAA,EAEJ,EAECA,EAAI,oBAAoB,iBACtBnH,EAAA,KAAA,MAAA,CAAI,UAAU,4DACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,2CAA2C,SAAyB,4BAAA,EACnFA,EAAAA,IAAC,OAAI,UAAU,0BACZ,WAAI,oBAAoB,mBAAmB,KAAK,IAAI,CACvD,CAAA,CAAA,EACF,EAGFD,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAAS,IAAM,CACY6G,EAAAK,EAAI,UAAW,MAAM,EAC5BhB,IAAAgB,EAAI,UAAWA,EAAI,WAAW,CAClD,EACA,UAAU,qGACX,SAAA,SAAA,CAED,EACAlH,EAAA,IAAC,SAAA,CACC,QAAS,IAAM6G,EAAyBK,EAAI,UAAW,UAAU,EACjE,UAAU,uGACX,SAAA,MAAA,CAAA,CAED,CACF,CAAA,CAAA,GA1DQA,EAAI,EA2Dd,CACD,CACH,CAAA,CAAA,EACF,EAGIC,EAAiB,IACpBpH,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAwB,2BAAA,EAE5EA,EAAA,IAAC,MAAI,CAAA,UAAU,uDACZ,SAAAsG,EAAe,IACdc,GAAArH,EAAA,KAAC,MAAmB,CAAA,UAAU,2DAC5B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,8BAA+B,SAAAoH,EAAM,MAAM,EACzDrH,EAAAA,KAAC,OAAK,CAAA,UAAU,6DAA6D,SAAA,CAAA,IACzEqH,EAAM,aAAa,WAAW,GAAA,CAClC,CAAA,CAAA,EACF,EAECA,EAAM,eACJrH,OAAA,MAAA,CAAI,UAAU,0CACZ,SAAA,CAAMqH,EAAA,cAAc,UAAA,EACvB,QAGD,MAAI,CAAA,UAAU,6BACZ,SAAAA,EAAM,QAAQ,gBACjB,EAEArH,EAAAA,KAAC,MAAI,CAAA,UAAU,+DACb,SAAA,CAAAA,OAAC,OAAM,CAAA,SAAA,CAAAqH,EAAM,WAAW,MAAM,QAAA,EAAM,SACnC,OAAM,CAAA,SAAA,CAAAA,EAAM,WAAW,aAAa,eAAA,EAAa,SACjD,OAAM,CAAA,SAAA,CAAAA,EAAM,WAAW,OAAO,SAAA,CAAO,CAAA,CAAA,EACxC,EAEArH,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,qGAAqG,SAEvH,gBAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,uGAAuG,SAEzH,OAAA,CAAA,CAAA,CACF,CAAA,CAAA,GA/BQoH,EAAM,EAgChB,CACD,CACH,CAAA,CAAA,EACF,EAGIC,EAAiB,IACpBtH,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAyB,4BAAA,EAE7ED,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAiB,oBAAA,EACjEA,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,YAAe,gBAAgB,YAAY,IAAI,CAACsH,EAAM9H,IACpDO,EAAA,KAAA,MAAA,CAAgB,UAAU,oBACzB,SAAA,CAAAC,EAAA,IAAC,QAAM,CAAA,KAAK,WAAW,UAAU,OAAO,EACvCA,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAyB,SAAKsH,CAAA,CAAA,CAFtC,CAAA,EAAA9H,CAGV,CACD,GACCQ,EAAAA,IAAC,OAAI,UAAU,wBAAwB,sCAA0B,CAErE,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAmB,sBAAA,EACnEA,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,SAAemG,GAAA,gBAAgB,mBAAmB,IAAI,CAACoB,EAAW/H,IACjEQ,EAAAA,IAAC,MAAgB,CAAA,UAAU,uDACzB,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,0BAA2B,SAAAuH,CAAU,CAAA,GAD5C/H,CAEV,CACD,GACEQ,EAAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,gCAAoB,CAE/D,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAqB,wBAAA,EACtED,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,mCACZ,YAAe,oBAAoB,cAAc,QAAU,CAC9D,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAc,gBAAA,CAAA,CAAA,EACvD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,oCACZ,YAAe,oBAAoB,aAAa,QAAU,CAC7D,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAa,eAAA,CAAA,CAAA,EACtD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qCACZ,YAAe,oBAAoB,cAAc,QAAU,CAC9D,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAc,gBAAA,CAAA,CAAA,EACvD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qCACZ,YAAe,oBAAoB,eAAe,QAAU,CAC/D,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAe,iBAAA,CAAA,CAAA,CACxD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGF,OAAIwD,EAEAxD,MAAC,OAAI,UAAU,yCACb,eAAC,MAAI,CAAA,UAAU,iEAAiE,CAClF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAgC,mCAAA,EACrFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,+EAAA,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,gCACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,iBACZ,SAAA,CACC,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,IAAK,EAChD,CAAE,GAAI,kBAAmB,MAAO,UAAW,KAAM,IAAK,EACtD,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,IAAK,EAChD,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,IAAK,CAAA,EAChD,IACAqE,GAAAtE,EAAA,KAAC,SAAA,CAEC,QAAS,IAAM0G,EAAiBpC,EAAI,EAAS,EAC7C,UAAW,4CACTmC,IAAkBnC,EAAI,GAClB,oCACA,4EACN,GAEA,SAAA,CAAArE,EAAA,IAAC,OAAK,CAAA,UAAU,OAAQ,SAAAqE,EAAI,KAAK,EAChCA,EAAI,KAAA,CAAA,EATAA,EAAI,EAAA,CAWZ,EACH,CACF,CAAA,EAGCmC,IAAkB,YAAcO,EAAe,EAC/CP,IAAkB,mBAAqBS,EAAsB,EAC7DT,IAAkB,YAAcW,EAAe,EAC/CX,IAAkB,YAAca,EAAe,CAAA,EAClD,CAEJ,ECzXaG,GAAkC,IAAM,CACnD,KAAM,CAACC,EAAYC,CAAa,EAAItJ,EAAAA,SAAqB,WAAW,EAC9D,CAACuJ,EAAoBC,CAAqB,EAAIxJ,EAAAA,SAAS,EAAK,EAE5DyJ,EAAwB1M,GAAsB,CAClDyM,EAAsB,EAAK,EAErB,MAAA,8CAA8CzM,CAAS,EAAE,CACjE,EAEM2M,EAA2BC,GAAoB,CAE7C,MAAA,sCAAsCA,CAAO,EAAE,CACvD,EAEMC,EAAsB,CAAC7M,EAAmB8J,IAAwB,CAEtE,QAAQ,IAAI,qBAAqB9J,CAAS,aAAa8J,CAAW,EAAE,CACtE,EAEMgD,EAAmB,IACtBjI,EAAAA,IAAA,MAAA,CAAI,UAAU,8CACb,SAACA,MAAA,MAAA,CAAI,UAAU,yBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,iBACZ,SAAA,CACC,CAAE,GAAI,YAAa,MAAO,YAAa,KAAM,KAAM,YAAa,gCAAiC,EACjG,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,MAAO,YAAa,6BAA8B,EAC7F,CAAE,GAAI,YAAa,MAAO,YAAa,KAAM,KAAM,YAAa,sBAAuB,EACvF,CAAE,GAAI,YAAa,MAAO,YAAa,KAAM,KAAM,YAAa,0BAA2B,CAAA,EAC3F,IACAqE,GAAAtE,EAAA,KAAC,SAAA,CAEC,QAAS,IAAM2H,EAAcrD,EAAI,EAAgB,EACjD,UAAW,8DACToD,IAAepD,EAAI,GACf,oCACA,4EACN,GACA,MAAOA,EAAI,YAEX,SAAA,CAAArE,EAAA,IAAC,OAAK,CAAA,UAAU,OAAQ,SAAAqE,EAAI,KAAK,EAChCA,EAAI,KAAA,CAAA,EAVAA,EAAI,EAAA,CAYZ,CACH,CAAA,CACF,CAAA,EACF,EAGI6D,EAAiB,IACrBlI,EAAA,IAAC,MAAI,CAAA,UAAU,wBACZ,SAAC2H,EA+GA3H,EAAA,IAACnC,GAAA,CACC,iBAAkBgK,EAClB,SAAU,IAAMD,EAAsB,EAAK,CAAA,CAAA,EAhH7C7H,EAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAA+B,kCAAA,EACpFA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,4EAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM4H,EAAsB,EAAI,EACzC,UAAU,kGACX,SAAA,qBAAA,CAAA,CAED,EACF,EAGA7H,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAe,kBAAA,EACvEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,sGAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,oDAAoD,SAEtE,mBAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAkB,qBAAA,EAC1EA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,4FAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,oDAAoD,SAEtE,qBAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAoB,uBAAA,EAC5EA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,qFAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,oDAAoD,SAEtE,mBAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAmB,sBAAA,EAC3EA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,sFAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,oDAAoD,SAEtE,gBAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAa,gBAAA,EACrEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,iFAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,oDAAoD,SAEtE,sBAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAG,MAAA,EACjCA,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAoB,uBAAA,EAC5EA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,qFAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,oDAAoD,SAEtE,qBAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6CAA6C,SAAwC,2CAAA,EACnGD,EAAAA,KAAC,MAAI,CAAA,UAAU,gEACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAwB,2BAAA,EACzDD,EAAAA,KAAC,KAAG,CAAA,UAAU,YACZ,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAsD,wDAAA,CAAA,EAC1DA,EAAAA,IAAC,MAAG,SAA0C,4CAAA,CAAA,EAC9CA,EAAAA,IAAC,MAAG,SAAmC,qCAAA,CAAA,EACvCA,EAAAA,IAAC,MAAG,SAAqC,uCAAA,CAAA,CAAA,CAC3C,CAAA,CAAA,EACF,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAoB,uBAAA,EACrDD,EAAAA,KAAC,KAAG,CAAA,UAAU,YACZ,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAA8C,gDAAA,CAAA,EAClDA,EAAAA,IAAC,MAAG,SAAoC,sCAAA,CAAA,EACxCA,EAAAA,IAAC,MAAG,SAA8B,gCAAA,CAAA,EAClCA,EAAAA,IAAC,MAAG,SAAoC,sCAAA,CAAA,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAOJ,EAGImI,EAAkB,IACrBnI,EAAA,IAAA6C,GAAA,CAAwB,oBAAqBiF,EAAyB,EAGnEM,EAAkB,IACrBpI,EAAA,IAAAiG,GAAA,CAAsB,gBAAiB+B,EAAqB,EAGzDK,EAAkB,IACrBtI,OAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAiC,oCAAA,EACtFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,qHAAA,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,0CAA0C,SAAK,QAAA,EAC7DA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAc,gBAAA,CAAA,CAAA,EACvD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,wCAAwC,SAAG,MAAA,EACzDA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAc,gBAAA,CAAA,CAAA,EACvD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,yCAAyC,SAAK,QAAA,EAC5DA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAc,gBAAA,CAAA,CAAA,EACvD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,0CAA0C,SAAK,QAAA,EAC7DA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAgB,kBAAA,CAAA,CAAA,CACzD,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,QAAS,IAAM2H,EAAc,UAAU,EACvC,UAAU,oJAEV,SAAA,CAAC1H,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAG,MAAA,EACjCA,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAiB,oBAAA,EAC3DA,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAEpC,uGAAA,QACC,MAAI,CAAA,UAAU,oCACb,SAACA,EAAA,IAAA,OAAA,CAAK,6BAAkB,CAAA,CAC1B,CAAA,CAAA,CAAA,CACF,EAEAD,EAAA,KAAC,MAAA,CACC,QAAS,IAAM2H,EAAc,WAAW,EACxC,UAAU,4IAEV,SAAA,CAAC1H,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAiB,oBAAA,EAC3DA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,wGAAA,QACC,MAAI,CAAA,UAAU,kCACb,SAACA,EAAA,IAAA,OAAA,CAAK,2BAAgB,CAAA,CACxB,CAAA,CAAA,CAAA,CACF,EAEAD,EAAA,KAAC,MAAA,CACC,QAAS,IAAM2H,EAAc,WAAW,EACxC,UAAU,gJAEV,SAAA,CAAC1H,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAiB,oBAAA,EAC3DA,EAAA,IAAA,IAAA,CAAE,UAAU,sBAAsB,SAEnC,2GAAA,QACC,MAAI,CAAA,UAAU,mCACb,SAACA,EAAA,IAAA,OAAA,CAAK,6BAAkB,CAAA,CAC1B,CAAA,CAAA,CAAA,CAAA,CACF,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,4FACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAwC,0CAAA,CAAA,CAAA,EAChG,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAGpC,mMAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAe,kBAAA,EAC/DA,EAAA,IAAA,IAAA,CAAE,UAAU,kBAAkB,SAAmE,qEAAA,CAAA,CAAA,EACpG,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,mDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAgB,mBAAA,EAChEA,EAAA,IAAA,IAAA,CAAE,UAAU,kBAAkB,SAA4D,8DAAA,CAAA,CAAA,EAC7F,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,mDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAkB,qBAAA,EAClEA,EAAA,IAAA,IAAA,CAAE,UAAU,kBAAkB,SAAqE,uEAAA,CAAA,CAAA,CACtG,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAIA,OAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,0BACZ,SAAA,CAAiBkI,EAAA,SAEjB,OACE,CAAA,SAAA,CAAAR,IAAe,aAAeY,EAAgB,EAC9CZ,IAAe,YAAcS,EAAe,EAC5CT,IAAe,aAAeU,EAAgB,EAC9CV,IAAe,aAAeW,EAAgB,CAAA,CACjD,CAAA,CAAA,EACF,CAEJ"}