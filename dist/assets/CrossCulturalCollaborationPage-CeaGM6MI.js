import{j as e}from"./index-nwrMOwxu.js";import{r as o}from"./vendor-DtOhX2xw.js";import"./firebase-DLuFXYhP.js";class V{projects=new Map;tasks=new Map;teams=new Map;async createProject(t,r){const s={id:this.generateId(),title:t.title,description:t.description,vision:t.vision,objectives:t.objectives,culturalContext:{primaryCultures:t.culturalContext.primaryCultures||[],culturalObjectives:t.culturalContext.culturalObjectives||[],culturalSensitivities:t.culturalContext.culturalSensitivities||[],traditionalKnowledgeIntegration:t.culturalContext.traditionalKnowledgeIntegration||!1,languageRequirements:t.culturalContext.languageRequirements||[],culturalMilestones:t.timeline.culturalMilestones,communityEndorsements:[]},requiredSkills:t.requiredSkills.map(a=>({skill:a,level:"intermediate",priority:"medium"})),targetCommunities:t.targetCommunities.map(a=>({community:a,role:"participant",representation:"equal"})),timeline:{startDate:t.timeline.startDate,endDate:t.timeline.endDate,phases:[],culturalEvents:t.timeline.culturalMilestones},status:"planning",impact:this.initializeImpactMetrics(),team:this.initializeTeam(),culturalGuidelines:[],collaborationProtocols:[],createdBy:r,createdAt:new Date,updatedAt:new Date};return this.projects.set(s.id,s),this.tasks.set(s.id,[]),this.teams.set(s.id,s.team),s}async getProject(t){return this.projects.get(t)||null}async updateProject(t,r){const s=this.projects.get(t);if(!s)throw new Error("Project not found");const a={...s,...r,updatedAt:new Date};return this.projects.set(t,a),a}async deleteProject(t){const r=this.projects.delete(t);return this.tasks.delete(t),this.teams.delete(t),r}async getProjectsByUser(t){return Array.from(this.projects.values()).filter(r=>r.createdBy===t||r.team.members.some(s=>s.userId===t))}async getProjectsByCulturalContext(t){return Array.from(this.projects.values()).filter(r=>r.culturalContext.primaryCultures.includes(t))}async recommendTeamComposition(t){if(!this.projects.get(t))throw new Error("Project not found");return[{userId:"user1",culturalBackground:"Zulu",skills:["project_management","community_engagement"],culturalExpertise:["traditional_governance","ubuntu_philosophy"],recommendationScore:95,role:"project_lead",culturalContribution:"Leadership in Ubuntu principles and traditional decision-making"},{userId:"user2",culturalBackground:"Afrikaans",skills:["technical_development","innovation"],culturalExpertise:["agricultural_innovation","sustainable_practices"],recommendationScore:88,role:"skill_expert",culturalContribution:"Technical innovation with cultural sustainability focus"},{userId:"user3",culturalBackground:"Xhosa",skills:["community_liaison","cultural_preservation"],culturalExpertise:["oral_traditions","cultural_ceremonies"],recommendationScore:92,role:"cultural_representative",culturalContribution:"Cultural preservation and traditional knowledge integration"}]}async addTeamMember(t,r){const s=this.teams.get(t);if(!s)throw new Error("Project team not found");return s.members.push({...r,joinedAt:new Date}),s.culturalDiversityScore=this.calculateCulturalDiversityScore(s.members),this.teams.set(t,s),s}async removeTeamMember(t,r){const s=this.teams.get(t);if(!s)throw new Error("Project team not found");return s.members=s.members.filter(a=>a.userId!==r),s.culturalDiversityScore=this.calculateCulturalDiversityScore(s.members),this.teams.set(t,s),s}async createTask(t){const r={id:this.generateId(),projectId:t.projectId,title:t.title,description:t.description,culturalContext:{culturalRequirements:t.culturalRequirements,culturalSensitivities:[],culturalLearningObjectives:[]},assignedTo:[],requiredSkills:t.requiredSkills,culturalRequirements:t.culturalRequirements.map(a=>({requirement:a,priority:"medium"})),priority:t.priority,status:"backlog",estimatedHours:t.estimatedHours,culturalLearning:[],dependencies:[],dueDate:t.dueDate,createdAt:new Date,updatedAt:new Date},s=this.tasks.get(t.projectId)||[];return s.push(r),this.tasks.set(t.projectId,s),r}async getProjectTasks(t){return this.tasks.get(t)||[]}async updateTask(t,r){for(const[s,a]of this.tasks.entries()){const l=a.findIndex(h=>h.id===t);if(l!==-1)return a[l]={...a[l],...r,updatedAt:new Date},this.tasks.set(s,a),a[l]}throw new Error("Task not found")}async updateImpactMetrics(t,r){const s=this.projects.get(t);if(!s)throw new Error("Project not found");return s.impact={...s.impact,...r},this.projects.set(t,s),s.impact}async getCulturalImpactAssessment(t){const r=this.projects.get(t);if(!r)throw new Error("Project not found");return{culturalPreservation:r.impact.culturalPreservation,crossCulturalLearning:r.impact.learningOutcomes,communityEngagement:r.impact.communityReach,culturalInnovation:r.impact.culturalImpact}}generateId(){return Math.random().toString(36).substr(2,9)}initializeImpactMetrics(){return{communityReach:{communities:0,individuals:0,regions:[]},culturalImpact:{preservationScore:0,innovationScore:0,exchangeScore:0},socialImpact:{relationshipsFormed:0,understandingImproved:0,conflictsResolved:0},economicImpact:{opportunitiesCreated:0,valueGenerated:0,sustainabilityScore:0},learningOutcomes:[],sustainabilityMetrics:[],culturalPreservation:[]}}initializeTeam(){return{members:[],culturalRepresentatives:[],skillsMatrix:{skills:[],coverage:0},culturalDiversityScore:0,communicationPreferences:[],decisionMakingProcess:{type:"consensus",culturalConsiderations:[]},conflictResolutionProtocol:{approach:"mediation",culturalAdaptations:[]}}}calculateCulturalDiversityScore(t){const r=new Set(t.map(a=>a.culturalBackground.primaryCulture));return Math.min(r.size/11*100,100)}}const D=new V,z=({userId:m})=>{const[t,r]=o.useState([]),[s,a]=o.useState(null),[l,h]=o.useState([]),[g,f]=o.useState([]),[N,x]=o.useState(!0),[p,j]=o.useState("overview");o.useEffect(()=>{E()},[m]),o.useEffect(()=>{s&&b(s.id)},[s]);const E=async()=>{try{x(!0);const n=await D.getProjectsByUser(m);r(n),n.length>0&&a(n[0])}catch(n){console.error("Error loading projects:",n)}finally{x(!1)}},b=async n=>{try{const i=await D.getProjectTasks(n),u=await D.recommendTeamComposition(n);h(i),f(u)}catch(i){console.error("Error loading project details:",i)}},T=async()=>{console.log("Create new cross-cultural project")},y=async n=>{if(s)try{const i={userId:n.userId,role:n.role,culturalBackground:{primaryCulture:n.culturalBackground,secondaryCultures:[],languages:[],culturalExpertise:n.culturalExpertise,traditionalKnowledge:[]},skillContributions:n.skills.map(u=>({skill:u,level:"intermediate",experience:"2+ years",culturalContext:n.culturalBackground})),availabilityPattern:{hoursPerWeek:20,preferredDays:["Monday","Wednesday","Friday"],timeZone:"Africa/Johannesburg",culturalConstraints:[]},culturalResponsibilities:[{responsibility:n.culturalContribution,description:"Cultural guidance and representation",culturalContext:n.culturalBackground,accountability:"Team and community"}],joinedAt:new Date,commitmentLevel:"part_time"};await D.addTeamMember(s.id,i),await b(s.id)}catch(i){console.error("Error joining team:",i)}},P=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Project Vision"}),e.jsx("p",{className:"text-gray-700 mb-4",children:s?.vision}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Cultural Context"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s?.culturalContext.primaryCultures.map((n,i)=>e.jsx("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:n},i))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Target Communities"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s?.targetCommunities.map((n,i)=>e.jsx("span",{className:"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm",children:n.community},i))})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Cultural Milestones"}),e.jsx("div",{className:"space-y-3",children:s?.culturalContext.culturalMilestones.map((n,i)=>e.jsxs("div",{className:"border-l-4 border-orange-400 pl-4",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:n.title}),e.jsx("p",{className:"text-sm text-gray-600",children:n.culturalSignificance}),e.jsxs("p",{className:"text-xs text-gray-500",children:["Target: ",n.targetDate.toLocaleDateString()]})]},i))})]})]}),A=()=>e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Project Tasks"}),e.jsx("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Add Task"})]}),e.jsx("div",{className:"grid gap-4",children:l.map(n=>e.jsxs("div",{className:"bg-white rounded-lg shadow p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:n.title}),e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${n.status==="completed"?"bg-green-100 text-green-800":n.status==="in_progress"?"bg-blue-100 text-blue-800":n.status==="review"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:n.status.replace("_"," ")})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-3",children:n.description}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:n.culturalRequirements.map((i,u)=>e.jsx("span",{className:"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs",children:i.requirement},u))}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500",children:[e.jsxs("span",{children:["Due: ",n.dueDate.toLocaleDateString()]}),e.jsxs("span",{children:[n.estimatedHours,"h estimated"]})]})]},n.id))})]}),C=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Current Team"}),e.jsx("div",{className:"grid gap-4",children:s?.team.members.map((n,i)=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:n.role.replace("_"," ")}),e.jsxs("p",{className:"text-sm text-gray-600",children:[n.culturalBackground.primaryCulture," • ",n.commitmentLevel.replace("_"," ")]}),e.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:n.skillContributions.slice(0,3).map((u,v)=>e.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs",children:u.skill},v))})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["Joined ",n.joinedAt.toLocaleDateString()]}),e.jsxs("p",{className:"text-xs text-gray-400",children:[n.availabilityPattern.hoursPerWeek,"h/week"]})]})]},i))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recommended Team Members"}),e.jsx("div",{className:"grid gap-4",children:g.map((n,i)=>e.jsxs("div",{className:"p-4 border rounded-lg",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:n.role.replace("_"," ")}),e.jsx("p",{className:"text-sm text-gray-600",children:n.culturalBackground})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("span",{className:"text-lg font-semibold text-green-600",children:[n.recommendationScore,"%"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"match"})]})]}),e.jsx("p",{className:"text-sm text-gray-700 mb-3",children:n.culturalContribution}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:n.skills.map((u,v)=>e.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded text-xs",children:u},v))}),e.jsx("button",{onClick:()=>y(n),className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Invite to Team"})]},i))})]})]}),k=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Community Reach"}),e.jsx("p",{className:"text-3xl font-bold text-blue-600",children:s?.impact.communityReach.communities}),e.jsx("p",{className:"text-sm text-gray-600",children:"Communities Engaged"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Cultural Impact"}),e.jsxs("p",{className:"text-3xl font-bold text-green-600",children:[s?.impact.culturalImpact.preservationScore,"%"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Preservation Score"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Social Impact"}),e.jsx("p",{className:"text-3xl font-bold text-purple-600",children:s?.impact.socialImpact.relationshipsFormed}),e.jsx("p",{className:"text-sm text-gray-600",children:"Relationships Formed"})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Cultural Learning Outcomes"}),e.jsx("div",{className:"space-y-3",children:s?.impact.learningOutcomes.map((n,i)=>e.jsxs("div",{className:"border-l-4 border-indigo-400 pl-4",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:n.outcome}),e.jsx("p",{className:"text-sm text-gray-600",children:n.culturalContext}),e.jsxs("p",{className:"text-xs text-gray-500",children:["Type: ",n.learningType]})]},i))})]})]});return N?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Cross-Cultural Projects"}),e.jsx("button",{onClick:T,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium",children:"Create New Project"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"p-4 border-b",children:e.jsx("h2",{className:"font-semibold text-gray-900",children:"Your Projects"})}),e.jsx("div",{className:"p-4 space-y-2",children:t.map(n=>e.jsxs("button",{onClick:()=>a(n),className:`w-full text-left p-3 rounded-lg transition-colors ${s?.id===n.id?"bg-blue-100 text-blue-900":"hover:bg-gray-100"}`,children:[e.jsx("h3",{className:"font-medium truncate",children:n.title}),e.jsx("p",{className:"text-sm text-gray-600 truncate",children:n.status})]},n.id))})]})}),e.jsx("div",{className:"lg:col-span-3",children:s&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:s.title}),e.jsx("p",{className:"text-gray-600 mb-4",children:s.description}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("span",{className:`px-3 py-1 rounded-full text-sm ${s.status==="active"?"bg-green-100 text-green-800":s.status==="planning"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:s.status}),e.jsxs("span",{className:"text-sm text-gray-500",children:["Cultural Diversity: ",s.team.culturalDiversityScore,"%"]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"flex space-x-8 px-6",children:[{key:"overview",label:"Overview"},{key:"tasks",label:"Tasks"},{key:"team",label:"Team"},{key:"impact",label:"Impact"}].map(n=>e.jsx("button",{onClick:()=>j(n.key),className:`py-4 px-1 border-b-2 font-medium text-sm ${p===n.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:n.label},n.key))})}),e.jsxs("div",{className:"p-6",children:[p==="overview"&&P(),p==="tasks"&&A(),p==="team"&&C(),p==="impact"&&k()]})]})]})})]})]})};class H{conversations=new Map;messages=new Map;videoConferences=new Map;websockets=new Map;async createConversation(t,r){const s={id:this.generateId(),participants:t.participants.map(a=>({userId:a,culturalBackground:this.getUserCulturalBackground(a),preferredLanguage:this.getUserPreferredLanguage(a),communicationPreferences:this.getUserCommunicationPreferences(a),culturalRole:"participant",joinedAt:new Date,lastSeen:new Date,permissions:{canTranslate:!0,canModerate:!1,canInvite:!0}})),conversationType:t.conversationType,primaryLanguages:this.extractPrimaryLanguages(t.participants),culturalContext:{conversationCultures:t.culturalContext.conversationCultures||[],culturalSensitivities:t.culturalContext.culturalSensitivities||[],communicationProtocols:t.culturalContext.communicationProtocols||[],translationPreferences:t.culturalContext.translationPreferences||{}},translationSettings:{enabled:t.translationEnabled,autoTranslate:!0,preserveCulturalContext:!0,supportedLanguages:["en","af","zu","xh","st","tn","ss","ve","ts","nr","nd"]},culturalGuidanceEnabled:!0,communicationStyle:this.determineCommunicationStyle(t.participants),messages:[],culturalMoments:[],createdAt:new Date,lastActivity:new Date,archiveSettings:{autoArchive:!1,retentionPeriod:365}};return this.conversations.set(s.id,s),this.messages.set(s.id,[]),s}async getConversation(t){return this.conversations.get(t)||null}async updateConversationSettings(t,r){const s=this.conversations.get(t);if(!s)throw new Error("Conversation not found");const a={...s,...r,lastActivity:new Date};return this.conversations.set(t,a),a}async sendMessage(t,r,s){const a=this.conversations.get(t);if(!a)throw new Error("Conversation not found");const l={id:this.generateId(),conversationId:t,senderId:r,originalText:s.text,originalLanguage:s.language,translations:await this.generateTranslations(s,a),culturalContext:await this.analyzeCulturalContext(s.text,s.language),messageType:s.messageType,culturalAnnotations:await this.generateCulturalAnnotations(s.text,s.language),reactions:[],timestamp:new Date,editHistory:[],moderationFlags:[]},h=this.messages.get(t)||[];return h.push(l),this.messages.set(t,h),a.lastActivity=new Date,this.conversations.set(t,a),await this.broadcastMessage(t,l),l}async getMessages(t,r,s=50){const a=this.messages.get(t)||[];return r?a.slice(-s).map(l=>({...l,displayText:this.getTranslationForLanguage(l,r)})):a.slice(-s)}async translateMessage(t){return{language:t.targetLanguage,translatedText:await this.performTranslation(t.text,t.sourceLanguage,t.targetLanguage),culturalAdaptation:await this.adaptForCulture(t.text,t.targetLanguage,t.culturalContext),confidenceScore:this.calculateConfidenceScore(t.text,t.sourceLanguage,t.targetLanguage),culturalAccuracyScore:this.calculateCulturalAccuracyScore(t.text,t.culturalContext),translationMethod:"automatic",culturalNotes:await this.generateCulturalNotes(t.text,t.culturalContext),alternativePhrasings:await this.generateAlternativePhrasings(t.text,t.targetLanguage),reviewedBy:void 0}}async improveTranslation(t,r,s,a){console.log("Translation improvement recorded:",{original:t,improved:r,justification:s,reviewer:a})}async explainCulturalContext(t,r,s){return{reference:r,explanation:`Cultural context explanation for "${r}" in ${s} context`,significance:"High cultural significance in traditional practices",appropriateResponses:["Show respect","Ask questions respectfully","Acknowledge the cultural value"],potentialSensitivities:["Avoid appropriation","Respect sacred aspects","Credit cultural origins"]}}async createVideoConference(t){const r={id:this.generateId(),conversationId:this.generateId(),participants:t.participants.map(s=>({userId:s,culturalBackground:this.getUserCulturalBackground(s),videoEnabled:!0,audioEnabled:!0,culturalBackgroundVisible:t.culturalFeatures.culturalBackgroundSharing,preferredLanguage:this.getUserPreferredLanguage(s)})),culturalFeatures:{realTimeTranslation:t.culturalFeatures.translationEnabled,culturalEtiquetteGuidance:t.culturalFeatures.culturalEtiquetteGuidance,culturalBackgroundSharing:t.culturalFeatures.culturalBackgroundSharing,breakoutRoomCulturalGrouping:!0},translation:{enabled:t.culturalFeatures.translationEnabled,supportedLanguages:["en","af","zu","xh","st","tn","ss","ve","ts","nr","nd"],realTimeTranscription:!0,culturalContextPreservation:!0},recording:{enabled:!1,culturalMomentsHighlighted:!0,transcriptionWithTranslation:!0,culturalContextPreserved:!0},breakoutRooms:[],culturalActivities:[],startTime:t.scheduledTime||new Date,status:t.scheduledTime?"scheduled":"active"};return this.videoConferences.set(r.id,r),r}async getVideoConference(t){return this.videoConferences.get(t)||null}async shareCulturalMedia(t,r,s){return{id:this.generateId(),conversationId:t,uploaderId:r,mediaType:this.determineMediaType(s.file),fileName:s.file.name,fileUrl:await this.uploadFile(s.file),culturalContext:{culture:s.culturalContext,significance:s.culturalSignificance,traditionalKnowledge:!1,sharingRestrictions:[]},culturalSignificance:s.culturalSignificance,sharingPermissions:{allowedUsers:s.sharingPermissions,publicAccess:!1,culturalCommunityAccess:!0,commercialUse:!1},culturalAttribution:{originalCulture:s.culturalContext,knowledgeHolders:[r],communityPermissions:!0,attributionRequired:!0},annotations:[],accessLog:[],uploadedAt:new Date}}async getConversationAnalytics(t){const r=this.messages.get(t)||[],s=this.conversations.get(t);if(!s)throw new Error("Conversation not found");return{conversationId:t,culturalEngagement:this.calculateCulturalEngagement(r,s),translationQuality:this.calculateTranslationQuality(r),crossCulturalUnderstanding:this.calculateCrossCulturalUnderstanding(r,s),communicationEffectiveness:this.calculateCommunicationEffectiveness(r),relationshipBuilding:this.calculateRelationshipBuilding(r,s),culturalLearning:this.calculateCulturalLearning(r,s)}}async connectToConversation(t,r){const s=`${t}-${r}`;console.log(`WebSocket connected for user ${r} in conversation ${t}`),this.websockets.set(s,{})}async disconnectFromConversation(t,r){const s=`${t}-${r}`;this.websockets.delete(s),console.log(`WebSocket disconnected for user ${r} in conversation ${t}`)}generateId(){return Math.random().toString(36).substr(2,9)}async generateTranslations(t,r){const s=[];for(const a of r.participants)if(a.preferredLanguage!==t.language){const l=await this.translateMessage({text:t.text,sourceLanguage:t.language,targetLanguage:a.preferredLanguage,culturalContext:t.culturalContext||{},preserveCulturalNuance:!0});s.push(l)}return s}async performTranslation(t,r,s){return`[${s.toUpperCase()}] ${t}`}async adaptForCulture(t,r,s){return`[Culturally adapted for ${r}] ${t}`}calculateConfidenceScore(t,r,s){return Math.floor(Math.random()*20)+80}calculateCulturalAccuracyScore(t,r){return Math.floor(Math.random()*15)+85}async generateCulturalNotes(t,r){return["Cultural note: This expression has special significance in traditional contexts"]}async generateAlternativePhrasings(t,r){return[`Alternative phrasing in ${r}`,`More formal version in ${r}`]}async analyzeCulturalContext(t,r){return{culturalReferences:[],idioms:[],formalityLevel:"neutral",emotionalTone:"neutral"}}async generateCulturalAnnotations(t,r){return[]}async broadcastMessage(t,r){console.log(`Broadcasting message ${r.id} to conversation ${t}`)}getTranslationForLanguage(t,r){const s=t.translations.find(a=>a.language===r);return s?s.translatedText:t.originalText}getUserCulturalBackground(t){return{primaryCulture:"Zulu",secondaryCultures:["English"],languages:["zu","en"]}}getUserPreferredLanguage(t){return"en"}getUserCommunicationPreferences(t){return{style:"direct",formality:"informal",culturalProtocols:[]}}extractPrimaryLanguages(t){return["en","zu","af"]}determineCommunicationStyle(t){return{primary:"collaborative",cultural:"ubuntu",formality:"respectful"}}determineMediaType(t){return t.type.startsWith("image/")?"image":t.type.startsWith("video/")?"video":t.type.startsWith("audio/")?"audio":"document"}async uploadFile(t){return`https://storage.example.com/${t.name}`}calculateCulturalEngagement(t,r){return{score:85,culturalReferencesShared:12,culturalLearningMoments:8}}calculateTranslationQuality(t){return{averageConfidence:88,culturalAccuracy:92,userSatisfaction:87}}calculateCrossCulturalUnderstanding(t,r){return{understandingScore:78,misunderstandingsResolved:3,culturalBridgesMade:5}}calculateCommunicationEffectiveness(t){return{responseRate:94,clarificationRequests:2,successfulExchanges:45}}calculateRelationshipBuilding(t,r){return{relationshipStrength:72,culturalBonding:68,trustBuilding:75}}calculateCulturalLearning(t,r){return{learningMoments:15,culturalKnowledgeGained:8,appreciationGrowth:82}}}const S=new H,q=({userId:m,conversationId:t})=>{const[r,s]=o.useState([]),[a,l]=o.useState(null),[h,g]=o.useState([]),[f,N]=o.useState(""),[x,p]=o.useState("en"),[j,E]=o.useState(!0),[b,T]=o.useState(!0),[y,P]=o.useState(null),[A,C]=o.useState(!0),k=o.useRef(null),n=[{code:"en",name:"English"},{code:"af",name:"Afrikaans"},{code:"zu",name:"Zulu"},{code:"xh",name:"Xhosa"},{code:"st",name:"Sesotho"},{code:"tn",name:"Setswana"},{code:"ss",name:"Siswati"},{code:"ve",name:"Venda"},{code:"ts",name:"Tsonga"},{code:"nr",name:"Ndebele (South)"},{code:"nd",name:"Ndebele (North)"}];o.useEffect(()=>{i(),t&&u(t)},[m,t]),o.useEffect(()=>{a&&(v(a.id),B(a.id),S.connectToConversation(a.id,m))},[a]),o.useEffect(()=>{O()},[h]);const i=async()=>{try{C(!0),s([])}catch(c){console.error("Error loading conversations:",c)}finally{C(!1)}},u=async c=>{try{const d=await S.getConversation(c);d&&l(d)}catch(d){console.error("Error loading conversation:",d)}},v=async c=>{try{const d=await S.getMessages(c,j?x:void 0);g(d)}catch(d){console.error("Error loading messages:",d)}},B=async c=>{try{const d=await S.getConversationAnalytics(c);P(d)}catch(d){console.error("Error loading analytics:",d)}},O=()=>{k.current?.scrollIntoView({behavior:"smooth"})},R=async()=>{if(!(!f.trim()||!a))try{const c={text:f,language:x,messageType:"text"},d=await S.sendMessage(a.id,m,c);g(w=>[...w,d]),N("")}catch(c){console.error("Error sending message:",c)}},U=c=>{c.key==="Enter"&&!c.shiftKey&&(c.preventDefault(),R())},_=async()=>{try{const c={participants:[m],conversationType:"group_chat",culturalContext:{conversationCultures:["Zulu","English","Afrikaans"],culturalSensitivities:[],communicationProtocols:[],translationPreferences:{}},translationEnabled:!0},d=await S.createConversation(c,m);s(w=>[...w,d]),l(d)}catch(c){console.error("Error creating conversation:",c)}},F=c=>{if(!j||c.originalLanguage===x)return c.originalText;const d=c.translations.find(w=>w.language===x);return d?d.translatedText:c.originalText},$=c=>{const d=c.senderId===m,w=F(c),M=c.translations.find(L=>L.language===x);return e.jsx("div",{className:`flex ${d?"justify-end":"justify-start"} mb-4`,children:e.jsxs("div",{className:`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${d?"bg-blue-600 text-white":"bg-gray-200 text-gray-900"}`,children:[e.jsx("p",{className:"text-sm",children:w}),M&&M.culturalNotes&&M.culturalNotes.length>0&&e.jsxs("div",{className:"mt-2 p-2 bg-yellow-100 rounded text-xs text-gray-800",children:[e.jsx("strong",{children:"Cultural Note:"})," ",M.culturalNotes[0]]}),c.culturalAnnotations.length>0&&e.jsx("div",{className:"mt-2 space-y-1",children:c.culturalAnnotations.map((L,K)=>e.jsxs("div",{className:"p-2 bg-purple-100 rounded text-xs text-gray-800",children:[e.jsxs("strong",{children:[L.annotationType,":"]})," ",L.content]},K))}),e.jsxs("div",{className:"flex justify-between items-center mt-2 text-xs opacity-75",children:[e.jsx("span",{children:c.timestamp.toLocaleTimeString()}),c.originalLanguage!==x&&e.jsxs("span",{className:"ml-2",children:["Translated from ",c.originalLanguage.toUpperCase()]})]})]})},c.id)},W=()=>!b||!a?null:e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:[e.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Cultural Guidance"}),e.jsxs("div",{className:"space-y-2 text-sm text-blue-800",children:[e.jsxs("p",{children:["• This conversation includes participants from ",a.culturalContext.conversationCultures?.join(", ")," cultures"]}),e.jsx("p",{children:"• Consider using respectful greetings and showing appreciation for cultural sharing"}),e.jsx("p",{children:"• Ask questions about cultural references to show genuine interest"})]})]}),G=()=>y?e.jsxs("div",{className:"bg-white rounded-lg shadow p-4 mb-4",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Communication Insights"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:"Cultural Engagement"}),e.jsxs("p",{className:"font-semibold text-green-600",children:[y.culturalEngagement.score,"%"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:"Translation Quality"}),e.jsxs("p",{className:"font-semibold text-blue-600",children:[y.translationQuality.averageConfidence,"%"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:"Understanding Score"}),e.jsxs("p",{className:"font-semibold text-purple-600",children:[y.crossCulturalUnderstanding.understandingScore,"%"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:"Relationship Building"}),e.jsxs("p",{className:"font-semibold text-orange-600",children:[y.relationshipBuilding.relationshipStrength,"%"]})]})]})]}):null;return A?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):e.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Cross-Cultural Communication"}),e.jsx("button",{onClick:_,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium",children:"New Conversation"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"lg:col-span-1 space-y-4",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-4",children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Language Settings"}),e.jsx("select",{value:x,onChange:c=>p(c.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:n.map(c=>e.jsx("option",{value:c.code,children:c.name},c.code))}),e.jsxs("div",{className:"mt-3 space-y-2",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:j,onChange:c=>E(c.target.checked),className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"Enable Translation"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:b,onChange:c=>T(c.target.checked),className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"Cultural Guidance"})]})]})]}),G()]}),e.jsx("div",{className:"lg:col-span-3",children:a?e.jsxs("div",{className:"bg-white rounded-lg shadow h-96 flex flex-col",children:[e.jsxs("div",{className:"p-4 border-b border-gray-200",children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Cross-Cultural Conversation"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[a.participants.length," participants •",a.primaryLanguages.join(", ")," languages"]})]}),e.jsx("div",{className:"p-4",children:W()}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-4",children:[h.map($),e.jsx("div",{ref:k})]}),e.jsxs("div",{className:"p-4 border-t border-gray-200",children:[e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("textarea",{value:f,onChange:c=>N(c.target.value),onKeyPress:U,placeholder:`Type your message in ${n.find(c=>c.code===x)?.name}...`,className:"flex-1 p-2 border border-gray-300 rounded-md resize-none",rows:2}),e.jsx("button",{onClick:R,disabled:!f.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Send"})]}),e.jsx("div",{className:"mt-2 text-xs text-gray-500",children:j&&e.jsx("span",{children:"Messages will be automatically translated for all participants"})})]})]}):e.jsxs("div",{className:"bg-white rounded-lg shadow p-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Welcome to Cross-Cultural Communication"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Start a conversation to connect with people from different cultural backgrounds. Our platform provides real-time translation and cultural context to help you communicate effectively and build meaningful relationships."}),e.jsx("button",{onClick:_,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium",children:"Start Your First Conversation"})]})})]})]})};class Z{events=new Map;attendees=new Map;performances=new Map;workshops=new Map;promotions=new Map;async createEvent(t,r){const s={id:this.generateId(),title:t.title,description:t.description,culturalContext:{primaryCultures:t.culturalContext.primaryCultures,culturalSignificance:t.culturalContext.culturalSignificance,traditionalElements:t.culturalContext.traditionalElements.map(a=>({element:a,significance:"Traditional cultural practice",authenticity:"verified"})),culturalLearningObjectives:t.culturalContext.culturalLearningObjectives.map(a=>({objective:a,culturalContext:t.culturalContext.primaryCultures.join(", "),expectedOutcome:"Enhanced cultural understanding"})),culturalSensitivities:[],culturalProtocols:[],heritageConnections:[],crossCulturalGoals:[]},eventType:t.eventType,format:t.format,location:{venue:t.location.venue,address:t.location.address,virtualPlatform:t.location.virtualPlatform,culturalSignificance:t.location.culturalSignificance,accessibility:t.accessibility,culturalConsiderations:[]},dateTime:{startDate:t.dateTime.startDate,endDate:t.dateTime.endDate,timeZone:"Africa/Johannesburg",culturalCalendarAlignment:t.dateTime.culturalCalendarConsiderations,flexibilityOptions:[]},organizers:[{userId:r,role:"primary_organizer",culturalBackground:this.getUserCulturalBackground(r),responsibilities:["overall_coordination","cultural_oversight"],permissions:["edit_event","manage_attendees","approve_content"]}],targetCommunities:t.targetCommunities.map(a=>({community:a,targetParticipation:"balanced",outreachStrategy:"community_leaders",culturalApproach:"respectful_invitation"})),culturalRequirements:[],programming:this.initializeEventProgramming(),ticketing:{enabled:!1,pricing:{free:!0,tiers:[]},accessibility:{scholarships:!0,communitySupport:!0},culturalConsiderations:[]},accessibility:t.accessibility,promotion:this.initializePromotionSettings(),status:"planning",attendees:[],impact:this.initializeEventImpact(),culturalDocumentation:this.initializeCulturalDocumentation(),createdAt:new Date,updatedAt:new Date};return this.events.set(s.id,s),this.attendees.set(s.id,[]),this.performances.set(s.id,[]),this.workshops.set(s.id,[]),s}async getEvent(t){return this.events.get(t)||null}async updateEvent(t,r){const s=this.events.get(t);if(!s)throw new Error("Event not found");const a={...s,...r,updatedAt:new Date};return this.events.set(t,a),a}async deleteEvent(t){const r=this.events.delete(t);return this.attendees.delete(t),this.performances.delete(t),this.workshops.delete(t),this.promotions.delete(t),r}async getEventsByCulturalFocus(t){return Array.from(this.events.values()).filter(r=>r.culturalContext.primaryCultures.includes(t))}async getEventsByLocation(t){return Array.from(this.events.values()).filter(r=>r.location.address?.includes(t)||r.location.venue?.includes(t))}async getEventsByDateRange(t,r){return Array.from(this.events.values()).filter(s=>s.dateTime.startDate>=t&&s.dateTime.endDate<=r)}async rsvpToEvent(t){const r=this.events.get(t.eventId);if(!r)throw new Error("Event not found");const s={userId:t.userId,registrationDate:new Date,culturalBackground:{primaryCulture:t.culturalBackground,secondaryCultures:[],languages:[],culturalExpertise:[],traditionalKnowledge:[]},attendanceType:t.attendanceType,culturalContributions:t.culturalContributions.map(l=>({contributionType:"cultural_knowledge",description:l,culturalContext:t.culturalBackground,sharingPermissions:["event_participants"]})),dietaryRequirements:t.dietaryRequirements.map(l=>({requirement:l,culturalContext:t.culturalBackground,severity:"preference"})),accessibilityNeeds:t.accessibilityNeeds.map(l=>({need:l,accommodation:"required",culturalContext:""})),culturalInterests:t.culturalInterests.map(l=>({interest:l,level:"interested",culturalContext:t.culturalBackground})),networkingPreferences:[],checkInStatus:{checkedIn:!1,checkInTime:void 0,culturalWelcome:!1}},a=this.attendees.get(t.eventId)||[];return a.push(s),this.attendees.set(t.eventId,a),r.attendees.push(s),this.events.set(t.eventId,r),s}async getEventAttendees(t){return this.attendees.get(t)||[]}async updateAttendee(t,r,s){const a=this.attendees.get(t)||[],l=a.findIndex(h=>h.userId===r);if(l===-1)throw new Error("Attendee not found");return a[l]={...a[l],...s},this.attendees.set(t,a),a[l]}async addPerformance(t){const r={id:this.generateId(),eventId:t.eventId,performanceName:t.performanceName,culturalOrigin:t.culturalOrigin,performers:t.performers.map(a=>({performerId:a,role:"performer",culturalBackground:t.culturalOrigin,expertise:[]})),culturalSignificance:t.culturalSignificance,performanceType:t.performanceType,duration:t.duration,culturalContext:t.culturalContext,audience:t.audience,requirements:t.requirements.map(a=>({requirement:a,type:"technical",culturalImportance:"medium"})),scheduledTime:t.scheduledTime,status:"scheduled",culturalApprovals:[],documentation:{recordingPermitted:!1,culturalSensitivities:[],sharingRestrictions:[]}},s=this.performances.get(t.eventId)||[];return s.push(r),this.performances.set(t.eventId,s),r}async addWorkshop(t){const r={id:this.generateId(),eventId:t.eventId,workshopTitle:t.workshopTitle,culturalFocus:t.culturalFocus,facilitator:{facilitatorId:t.facilitator,culturalBackground:t.culturalFocus,expertise:t.culturalSkills,credentials:[]},learningObjectives:t.learningObjectives,culturalSkills:t.culturalSkills.map(a=>({skill:a,culturalContext:t.culturalFocus,difficulty:t.difficulty,traditionalKnowledge:!1})),participantLimit:t.participantLimit,materials:t.materials.map(a=>({material:a,culturalSignificance:"",source:"provided",culturalConsiderations:[]})),culturalPrerequisites:t.culturalPrerequisites||[],duration:t.duration,difficulty:t.difficulty,registeredParticipants:[],culturalGuidelines:[],learningOutcomes:[]},s=this.workshops.get(t.eventId)||[];return s.push(r),this.workshops.set(t.eventId,s),r}async getEventProgramming(t){const r=this.performances.get(t)||[],s=this.workshops.get(t)||[];return{schedule:this.generateSchedule(r,s),culturalPerformances:r,workshops:s,exhibitions:[],speakerSessions:[],interactiveActivities:[],culturalExperiences:[],networking:[]}}async launchPromotion(t,r){const s=this.events.get(t);if(!s)throw new Error("Event not found");const a={eventId:t,promotionChannels:[{channel:"community_networks",culturalApproach:"respectful_invitation",targetCommunities:s.targetCommunities.map(l=>l.community),status:"active"},{channel:"social_media",culturalApproach:"inclusive_messaging",targetCommunities:s.targetCommunities.map(l=>l.community),status:"active"}],targetCommunities:s.targetCommunities.map(l=>({community:l.community,outreachStrategy:"community_leaders",culturalMessaging:"authentic_invitation",responseTracking:{reached:0,engaged:0,registered:0}})),culturalInfluencers:[],partnerOrganizations:[],socialMediaCampaign:{platforms:["facebook","instagram","twitter"],culturalHashtags:s.culturalContext.primaryCultures.map(l=>`#${l}Culture`),contentStrategy:"authentic_representation",engagementGoals:{reach:1e3,engagement:100,registrations:50}},communityEndorsements:[],mediaKit:{culturallyAppropriateImages:[],culturalContextDescriptions:[],communityQuotes:[],culturalSignificanceExplanations:[]},promotionAnalytics:{reach:{total:0,byCommunity:{}},engagement:{total:0,byCommunity:{}},registrations:{total:0,byCommunity:{}},culturalResonance:{score:0,feedback:[]}}};return this.promotions.set(t,a),a}async getPromotionAnalytics(t){const r=this.promotions.get(t);if(!r)throw new Error("Promotion not found");return r.promotionAnalytics}async checkInAttendee(t,r){const s=this.attendees.get(t)||[],a=s.findIndex(l=>l.userId===r);if(a===-1)throw new Error("Attendee not found");return s[a].checkInStatus={checkedIn:!0,checkInTime:new Date,culturalWelcome:!0},this.attendees.set(t,s),{success:!0,attendee:s[a],culturalWelcome:"Welcome! We honor your presence at this cultural gathering."}}async getRealTimeCoordination(t){const r=this.events.get(t),s=this.attendees.get(t)||[];if(!r)throw new Error("Event not found");return{eventStatus:r.status,totalAttendees:s.length,checkedInAttendees:s.filter(a=>a.checkInStatus.checkedIn).length,currentActivities:this.getCurrentActivities(t),culturalMoments:this.getCulturalMoments(t),emergencyContacts:[],culturalLiaisons:[]}}async generateImpactReport(t){const r=this.events.get(t),s=this.attendees.get(t)||[];if(!r)throw new Error("Event not found");return{attendanceMetrics:{totalAttendees:s.length,culturalDiversity:this.calculateCulturalDiversity(s),communityRepresentation:this.calculateCommunityRepresentation(s),demographicBreakdown:this.calculateDemographicBreakdown(s)},culturalExchange:{culturalInteractions:25,knowledgeSharing:18,traditionalPracticesShared:8,crossCulturalConnections:32},communityEngagement:{communityParticipation:85,leadershipInvolvement:12,volunteerContributions:15,communityFeedback:4.2},culturalLearning:{learningObjectivesAchieved:90,culturalCompetencyIncrease:78,appreciationGrowth:88,knowledgeRetention:82},economicImpact:{localEconomicBenefit:5e3,culturalEntrepreneurSupport:8,artisanSales:2500,communityInvestment:1500},socialCohesion:{relationshipsFormed:45,communityBonding:82,culturalPrideBoosted:91,inclusionImproved:87},culturalPreservation:{traditionalKnowledgeDocumented:12,culturalPracticesPreserved:8,intergenerationalTransfer:15,culturalArtifactsShared:6},followUpActivities:[]}}async documentCulturalMoment(t,r){if(!this.events.get(t))throw new Error("Event not found");const a={id:this.generateId(),eventId:t,momentType:r.momentType,description:r.description,culturalSignificance:r.culturalSignificance,participants:r.participants,timestamp:new Date,preservationValue:r.preservationValue||8,documentation:{photos:[],videos:[],audioRecordings:[],writtenAccounts:[]}};return console.log("Cultural moment documented:",a),a}generateId(){return Math.random().toString(36).substr(2,9)}getUserCulturalBackground(t){return{primaryCulture:"Zulu",secondaryCultures:["English"],languages:["zu","en"],culturalExpertise:["traditional_music","ubuntu_philosophy"],traditionalKnowledge:["oral_traditions"]}}initializeEventProgramming(){return{schedule:[],culturalPerformances:[],workshops:[],exhibitions:[],speakerSessions:[],interactiveActivities:[],culturalExperiences:[],networking:[]}}initializePromotionSettings(){return{enabled:!0,culturalApproach:"respectful_invitation",targetChannels:["community_networks","social_media"],culturalMessaging:"authentic_representation"}}initializeEventImpact(){return{attendanceMetrics:{totalAttendees:0,culturalDiversity:0,communityRepresentation:{},demographicBreakdown:{}},culturalExchange:{culturalInteractions:0,knowledgeSharing:0,traditionalPracticesShared:0,crossCulturalConnections:0},communityEngagement:{communityParticipation:0,leadershipInvolvement:0,volunteerContributions:0,communityFeedback:0},culturalLearning:{learningObjectivesAchieved:0,culturalCompetencyIncrease:0,appreciationGrowth:0,knowledgeRetention:0},economicImpact:{localEconomicBenefit:0,culturalEntrepreneurSupport:0,artisanSales:0,communityInvestment:0},socialCohesion:{relationshipsFormed:0,communityBonding:0,culturalPrideBoosted:0,inclusionImproved:0},culturalPreservation:{traditionalKnowledgeDocumented:0,culturalPracticesPreserved:0,intergenerationalTransfer:0,culturalArtifactsShared:0},followUpActivities:[]}}initializeCulturalDocumentation(){return{eventId:"",culturalMoments:[],traditionalKnowledge:[],culturalPerformanceRecordings:[],participantStories:[],culturalArtifacts:[],communityTestimonials:[],preservationOutcomes:[]}}generateSchedule(t,r){const s=[];return t.forEach(a=>{s.push({id:a.id,type:"performance",title:a.performanceName,startTime:a.scheduledTime,duration:a.duration,culturalContext:a.culturalOrigin})}),r.forEach(a=>{s.push({id:a.id,type:"workshop",title:a.workshopTitle,duration:a.duration,culturalContext:a.culturalFocus})}),s.sort((a,l)=>a.startTime-l.startTime)}getCurrentActivities(t){return[{activity:"Traditional Zulu Dance Performance",status:"active",culturalContext:"Zulu"},{activity:"Ubuntu Philosophy Workshop",status:"upcoming",culturalContext:"Pan-African"}]}getCulturalMoments(t){return[{moment:"Intergenerational knowledge sharing",significance:"High",timestamp:new Date},{moment:"Cross-cultural collaboration breakthrough",significance:"Medium",timestamp:new Date}]}calculateCulturalDiversity(t){return new Set(t.map(s=>s.culturalBackground.primaryCulture)).size/11*100}calculateCommunityRepresentation(t){const r={};return t.forEach(s=>{const a=s.culturalBackground.primaryCulture;r[a]=(r[a]||0)+1}),r}calculateDemographicBreakdown(t){return{totalAttendees:t.length,byAttendanceType:this.groupBy(t,"attendanceType"),byCulturalBackground:this.groupBy(t,r=>r.culturalBackground.primaryCulture)}}groupBy(t,r){return t.reduce((s,a)=>{const l=typeof r=="function"?r(a):a[r];return s[l]=(s[l]||0)+1,s},{})}}const I=new Z,Q=({userId:m})=>{const[t,r]=o.useState([]),[s,a]=o.useState(null),[l,h]=o.useState([]),[g,f]=o.useState(null),[N,x]=o.useState(!0),[p,j]=o.useState("overview");o.useEffect(()=>{E()},[]),o.useEffect(()=>{s&&T(s.id)},[s]);const E=async()=>{try{x(!0);const i=await b();r(i),i.length>0&&a(i[0])}catch(i){console.error("Error loading events:",i)}finally{x(!1)}},b=async()=>{const i={title:"Ubuntu Cultural Festival",description:"A celebration of South African cultural diversity through music, dance, and storytelling",eventType:"festival",format:"hybrid",location:{venue:"Community Cultural Center",address:"Cape Town, South Africa",culturalSignificance:"Historic gathering place for multiple communities"},dateTime:{startDate:new Date(Date.now()+6048e5),endDate:new Date(Date.now()+6912e5),culturalCalendarConsiderations:["Heritage Month","Traditional harvest season"]},culturalContext:{primaryCultures:["Zulu","Xhosa","Afrikaans","English"],culturalSignificance:"Celebrating unity in diversity through Ubuntu philosophy",traditionalElements:["Traditional music","Storytelling","Cultural dances","Craft demonstrations"],culturalLearningObjectives:["Understanding Ubuntu philosophy","Appreciating cultural diversity","Learning traditional practices"]},targetCommunities:["Zulu","Xhosa","Afrikaans","English","Sotho"],accessibility:{wheelchairAccessible:!0,languageSupport:["en","af","zu","xh","st"],culturalAccommodations:["Prayer spaces","Dietary considerations","Cultural dress areas"]}};return[await I.createEvent(i,m)]},T=async i=>{try{const u=await I.getEventAttendees(i),v=await I.generateImpactReport(i);h(u),f(v)}catch(u){console.error("Error loading event details:",u)}},y=async()=>{console.log("Create new cultural event")},P=async()=>{if(s)try{const i={eventId:s.id,userId:m,attendanceType:"full_event",culturalBackground:"Zulu",culturalContributions:["Traditional music knowledge","Ubuntu philosophy"],dietaryRequirements:["Vegetarian"],accessibilityNeeds:[],culturalInterests:["Traditional music","Storytelling","Cultural dances"]},u=await I.rsvpToEvent(i);h(v=>[...v,u])}catch(i){console.error("Error RSVPing to event:",i)}},A=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Event Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Cultural Context"}),e.jsx("p",{className:"text-gray-700 mb-3",children:s?.culturalContext.culturalSignificance}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s?.culturalContext.primaryCultures.map((i,u)=>e.jsx("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:i},u))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Event Information"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Format:"})," ",s?.format.replace("_"," ")]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Venue:"})," ",s?.location.venue]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Date:"})," ",s?.dateTime.startDate.toLocaleDateString()]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Status:"})," ",s?.status]})]})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Traditional Elements"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s?.culturalContext.traditionalElements.map((i,u)=>e.jsxs("div",{className:"border-l-4 border-green-400 pl-4",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:i.element}),e.jsx("p",{className:"text-sm text-gray-600",children:i.significance}),e.jsx("span",{className:"text-xs text-green-600",children:i.authenticity})]},u))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Cultural Learning Objectives"}),e.jsx("div",{className:"space-y-3",children:s?.culturalContext.culturalLearningObjectives.map((i,u)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-purple-600 text-sm font-medium",children:u+1})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:i.objective}),e.jsx("p",{className:"text-sm text-gray-600",children:i.expectedOutcome})]})]},u))})]})]}),C=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Event Programming"}),e.jsxs("div",{className:"space-x-2",children:[e.jsx("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Add Performance"}),e.jsx("button",{className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:"Add Workshop"})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-4",children:"Cultural Performances"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h5",{className:"font-medium text-gray-900",children:"Traditional Zulu Dance"}),e.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded text-sm",children:"Scheduled"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Authentic Zulu dance performance showcasing traditional movements and cultural significance"}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500",children:[e.jsx("span",{children:"Cultural Origin: Zulu"}),e.jsx("span",{children:"Duration: 30 minutes"}),e.jsx("span",{children:"Audience: All ages"})]})]}),e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h5",{className:"font-medium text-gray-900",children:"Ubuntu Storytelling Circle"}),e.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded text-sm",children:"Scheduled"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Interactive storytelling session sharing Ubuntu philosophy and traditional wisdom"}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500",children:[e.jsx("span",{children:"Cultural Origin: Pan-African"}),e.jsx("span",{children:"Duration: 45 minutes"}),e.jsx("span",{children:"Audience: All ages"})]})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-4",children:"Cultural Workshops"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h5",{className:"font-medium text-gray-900",children:"Traditional Beadwork"}),e.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm",children:"Beginner"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Learn the art of traditional South African beadwork and its cultural meanings"}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500",children:[e.jsx("span",{children:"Cultural Focus: Zulu/Xhosa"}),e.jsx("span",{children:"Duration: 90 minutes"}),e.jsx("span",{children:"Limit: 20 participants"})]})]}),e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h5",{className:"font-medium text-gray-900",children:"Ubuntu Philosophy Discussion"}),e.jsx("span",{className:"px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-sm",children:"Intermediate"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Deep dive into Ubuntu philosophy and its application in modern community building"}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500",children:[e.jsx("span",{children:"Cultural Focus: Pan-African"}),e.jsx("span",{children:"Duration: 60 minutes"}),e.jsx("span",{children:"Limit: 30 participants"})]})]})]})]})]}),k=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Event Attendees"}),e.jsx("button",{onClick:P,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"RSVP to Event"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Total Attendees"}),e.jsx("p",{className:"text-3xl font-bold text-blue-600",children:l.length})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Cultural Diversity"}),e.jsx("p",{className:"text-3xl font-bold text-green-600",children:new Set(l.map(i=>i.culturalBackground.primaryCulture)).size}),e.jsx("p",{className:"text-sm text-gray-600",children:"Different cultures"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Volunteers"}),e.jsx("p",{className:"text-3xl font-bold text-purple-600",children:l.filter(i=>i.attendanceType==="volunteer").length})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-4",children:"Attendee List"}),e.jsx("div",{className:"space-y-3",children:l.map((i,u)=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-gray-900",children:i.attendanceType.replace("_"," ")}),e.jsxs("p",{className:"text-sm text-gray-600",children:[i.culturalBackground.primaryCulture," • Registered ",i.registrationDate.toLocaleDateString()]}),i.culturalContributions.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:i.culturalContributions.slice(0,2).map((v,B)=>e.jsx("span",{className:"px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs",children:v.description},B))})]}),e.jsx("div",{className:"text-right",children:e.jsx("span",{className:`px-2 py-1 rounded text-xs ${i.checkInStatus.checkedIn?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:i.checkInStatus.checkedIn?"Checked In":"Registered"})})]},u))})]})]}),n=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Event Impact Report"}),g&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Cultural Diversity"}),e.jsxs("p",{className:"text-2xl font-bold text-blue-600",children:[g.attendanceMetrics.culturalDiversity,"%"]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Cultural Interactions"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:g.culturalExchange.culturalInteractions})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Relationships Formed"}),e.jsx("p",{className:"text-2xl font-bold text-purple-600",children:g.socialCohesion.relationshipsFormed})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Economic Benefit"}),e.jsxs("p",{className:"text-2xl font-bold text-orange-600",children:["R",g.economicImpact.localEconomicBenefit]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-4",children:"Cultural Learning"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Learning Objectives Achieved"}),e.jsxs("span",{className:"font-medium",children:[g.culturalLearning.learningObjectivesAchieved,"%"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Cultural Competency Increase"}),e.jsxs("span",{className:"font-medium",children:[g.culturalLearning.culturalCompetencyIncrease,"%"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Appreciation Growth"}),e.jsxs("span",{className:"font-medium",children:[g.culturalLearning.appreciationGrowth,"%"]})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-4",children:"Cultural Preservation"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Knowledge Documented"}),e.jsx("span",{className:"font-medium",children:g.culturalPreservation.traditionalKnowledgeDocumented})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Practices Preserved"}),e.jsx("span",{className:"font-medium",children:g.culturalPreservation.culturalPracticesPreserved})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Intergenerational Transfer"}),e.jsx("span",{className:"font-medium",children:g.culturalPreservation.intergenerationalTransfer})]})]})]})]})]})]});return N?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Cultural Events"}),e.jsx("button",{onClick:y,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium",children:"Create New Event"})]}),s&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:s.title}),e.jsx("p",{className:"text-gray-600 mb-4",children:s.description}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("span",{className:`px-3 py-1 rounded-full text-sm ${s.status==="active"?"bg-green-100 text-green-800":s.status==="planning"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:s.status}),e.jsxs("span",{className:"text-sm text-gray-500",children:[s.format," • ",s.eventType]}),e.jsx("span",{className:"text-sm text-gray-500",children:s.dateTime.startDate.toLocaleDateString()})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"flex space-x-8 px-6",children:[{key:"overview",label:"Overview"},{key:"programming",label:"Programming"},{key:"attendees",label:"Attendees"},{key:"impact",label:"Impact"}].map(i=>e.jsx("button",{onClick:()=>j(i.key),className:`py-4 px-1 border-b-2 font-medium text-sm ${p===i.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:i.label},i.key))})}),e.jsxs("div",{className:"p-6",children:[p==="overview"&&A(),p==="programming"&&C(),p==="attendees"&&k(),p==="impact"&&n()]})]})]})]})},ee=({userId:m})=>{const[t,r]=o.useState("projects"),s=()=>{switch(t){case"projects":return e.jsx(z,{userId:m});case"communication":return e.jsx(q,{userId:m});case"events":return e.jsx(Q,{userId:m});default:return e.jsx(z,{userId:m})}};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Cross-Cultural Collaboration"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Connect, collaborate, and celebrate South Africa's cultural diversity"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[e.jsx("button",{onClick:()=>r("projects"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${t==="projects"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Projects"}),e.jsx("button",{onClick:()=>r("communication"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${t==="communication"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Communication"}),e.jsx("button",{onClick:()=>r("events"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${t==="events"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Events"})]})})]})})}),e.jsx("div",{className:"bg-blue-50 border-b border-blue-200",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:[t==="projects"&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Cross-Cultural Project Management"}),e.jsx("p",{className:"text-blue-700",children:"Create and manage projects that bring together diverse communities to solve challenges and build understanding through collaborative action."})]})]}),t==="communication"&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Real-Time Cross-Cultural Communication"}),e.jsx("p",{className:"text-blue-700",children:"Bridge language barriers and preserve cultural context with intelligent translation and cultural guidance for meaningful cross-cultural conversations."})]})]}),t==="events"&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Cultural Event Organization & Coordination"}),e.jsx("p",{className:"text-blue-700",children:"Organize and coordinate cultural events that celebrate heritage, facilitate exchange, and create inclusive celebrations that unite diverse communities."})]})]})]})}),e.jsx("div",{className:"py-8",children:s()}),e.jsx("div",{className:"bg-gray-900 text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-xl font-semibold mb-4",children:'Ubuntu: "I am because we are"'}),e.jsx("p",{className:"text-gray-300 max-w-3xl mx-auto",children:"Our cross-cultural collaboration tools are built on the Ubuntu philosophy, recognizing that our individual growth and success are interconnected with the wellbeing and prosperity of our communities. Through respectful collaboration, cultural exchange, and shared learning, we build bridges that strengthen the beautiful tapestry of South African diversity."})]}),e.jsxs("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"bg-blue-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),e.jsx("h4",{className:"font-medium mb-2",children:"Respect & Understanding"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Honor all cultural traditions and approaches to collaboration"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"bg-green-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})}),e.jsx("h4",{className:"font-medium mb-2",children:"Inclusive Collaboration"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Ensure all voices are heard and valued in decision-making processes"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"bg-purple-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),e.jsx("h4",{className:"font-medium mb-2",children:"Cultural Preservation"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Document and preserve cultural knowledge for future generations"})]})]})]})})]})};export{ee as default};
//# sourceMappingURL=CrossCulturalCollaborationPage-CeaGM6MI.js.map
