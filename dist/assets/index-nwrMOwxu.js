const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AuthPage-Chhb6i1V.js","assets/vendor-DtOhX2xw.js","assets/firebase-DLuFXYhP.js","assets/HomePage-BlDCyb5y.js","assets/CulturalOnboardingPage-Cu7AFwSZ.js","assets/CulturalKnowledgePage-BnYvObkg.js","assets/culturalValidationService-CA9WZNCm.js","assets/KnowledgeExchangePage-C23voltd.js","assets/CrossCulturalCollaborationPage-CeaGM6MI.js","assets/AchievementShowcasePage-CmfOiW9q.js"])))=>i.map(i=>d[i]);
import{r as w,a as $i,R as ji,g as Fi,b as A}from"./vendor-DtOhX2xw.js";import{_ as Re,C as Ae,r as fe,S as zi,F as Bt,g as or,a as me,b as je,c as Mi,d as Bi,E as lr,o as Vi,L as Hi,i as Ki,e as Wi,v as qi,f as un,h as Ji,j as Gi,k as Yi,l as Xi,m as Qi,n as Zi,u as cn,s as es,p as ht,q as X,t as ts,G as ns,w as rs,x as is,y as dn,R as ss,z as as,A as os,B as fn,D as ls,H as us}from"./firebase-DLuFXYhP.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();var ur={exports:{}},nt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cs=w,ds=Symbol.for("react.element"),fs=Symbol.for("react.fragment"),hs=Object.prototype.hasOwnProperty,ps=cs.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,gs={key:!0,ref:!0,__self:!0,__source:!0};function cr(t,e,n){var r,i={},s=null,a=null;n!==void 0&&(s=""+n),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(a=e.ref);for(r in e)hs.call(e,r)&&!gs.hasOwnProperty(r)&&(i[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)i[r]===void 0&&(i[r]=e[r]);return{$$typeof:ds,type:t,key:s,ref:a,props:i,_owner:ps.current}}nt.Fragment=fs;nt.jsx=cr;nt.jsxs=cr;ur.exports=nt;var b=ur.exports,_t={},hn=$i;_t.createRoot=hn.createRoot,_t.hydrateRoot=hn.hydrateRoot;const ms="modulepreload",ys=function(t){return"/"+t},pn={},G=function(e,n,r){let i=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),o=a?.nonce||a?.getAttribute("nonce");i=Promise.allSettled(n.map(l=>{if(l=ys(l),l in pn)return;pn[l]=!0;const u=l.endsWith(".css"),c=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${c}`))return;const f=document.createElement("link");if(f.rel=u?"stylesheet":ms,u||(f.as="script"),f.crossOrigin="",f.href=l,o&&f.setAttribute("nonce",o),document.head.appendChild(f),u)return new Promise((d,p)=>{f.addEventListener("load",d),f.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${l}`)))})}))}function s(a){const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=a,window.dispatchEvent(o),!o.defaultPrevented)throw a}return i.then(a=>{for(const o of a||[])o.status==="rejected"&&s(o.reason);return e().catch(s)})};/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ne(){return Ne=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ne.apply(this,arguments)}var J;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(J||(J={}));const gn="popstate";function vs(t){t===void 0&&(t={});function e(r,i){let{pathname:s,search:a,hash:o}=r.location;return Pt("",{pathname:s,search:a,hash:o},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:fr(i)}return bs(e,n,null,t)}function E(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function dr(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function ws(){return Math.random().toString(36).substr(2,8)}function mn(t,e){return{usr:t.state,key:t.key,idx:e}}function Pt(t,e,n,r){return n===void 0&&(n=null),Ne({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?ye(e):e,{state:n,key:e&&e.key||r||ws()})}function fr(t){let{pathname:e="/",search:n="",hash:r=""}=t;return n&&n!=="?"&&(e+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function ye(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}function bs(t,e,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,a=i.history,o=J.Pop,l=null,u=c();u==null&&(u=0,a.replaceState(Ne({},a.state,{idx:u}),""));function c(){return(a.state||{idx:null}).idx}function f(){o=J.Pop;let h=c(),v=h==null?null:h-u;u=h,l&&l({action:o,location:g.location,delta:v})}function d(h,v){o=J.Push;let y=Pt(g.location,h,v);u=c()+1;let k=mn(y,u),S=g.createHref(y);try{a.pushState(k,"",S)}catch(I){if(I instanceof DOMException&&I.name==="DataCloneError")throw I;i.location.assign(S)}s&&l&&l({action:o,location:g.location,delta:1})}function p(h,v){o=J.Replace;let y=Pt(g.location,h,v);u=c();let k=mn(y,u),S=g.createHref(y);a.replaceState(k,"",S),s&&l&&l({action:o,location:g.location,delta:0})}function m(h){let v=i.location.origin!=="null"?i.location.origin:i.location.href,y=typeof h=="string"?h:fr(h);return y=y.replace(/ $/,"%20"),E(v,"No window.location.(origin|href) available to create URL for href: "+y),new URL(y,v)}let g={get action(){return o},get location(){return t(i,a)},listen(h){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(gn,f),l=h,()=>{i.removeEventListener(gn,f),l=null}},createHref(h){return e(i,h)},createURL:m,encodeLocation(h){let v=m(h);return{pathname:v.pathname,search:v.search,hash:v.hash}},push:d,replace:p,go(h){return a.go(h)}};return g}var yn;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(yn||(yn={}));function ks(t,e,n){return n===void 0&&(n="/"),Ss(t,e,n)}function Ss(t,e,n,r){let i=typeof e=="string"?ye(e):e,s=gr(i.pathname||"/",n);if(s==null)return null;let a=hr(t);xs(a);let o=null;for(let l=0;o==null&&l<a.length;++l){let u=Us(s);o=Os(a[l],u)}return o}function hr(t,e,n,r){e===void 0&&(e=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,a,o)=>{let l={relativePath:o===void 0?s.path||"":o,caseSensitive:s.caseSensitive===!0,childrenIndex:a,route:s};l.relativePath.startsWith("/")&&(E(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=te([r,l.relativePath]),c=n.concat(l);s.children&&s.children.length>0&&(E(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),hr(s.children,e,c,u)),!(s.path==null&&!s.index)&&e.push({path:u,score:As(u,s.index),routesMeta:c})};return t.forEach((s,a)=>{var o;if(s.path===""||!((o=s.path)!=null&&o.includes("?")))i(s,a);else for(let l of pr(s.path))i(s,a,l)}),e}function pr(t){let e=t.split("/");if(e.length===0)return[];let[n,...r]=e,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let a=pr(r.join("/")),o=[];return o.push(...a.map(l=>l===""?s:[s,l].join("/"))),i&&o.push(...a),o.map(l=>t.startsWith("/")&&l===""?"/":l)}function xs(t){t.sort((e,n)=>e.score!==n.score?n.score-e.score:Ns(e.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Is=/^:[\w-]+$/,Cs=3,_s=2,Ps=1,Es=10,Rs=-2,vn=t=>t==="*";function As(t,e){let n=t.split("/"),r=n.length;return n.some(vn)&&(r+=Rs),e&&(r+=_s),n.filter(i=>!vn(i)).reduce((i,s)=>i+(Is.test(s)?Cs:s===""?Ps:Es),r)}function Ns(t,e){return t.length===e.length&&t.slice(0,-1).every((r,i)=>r===e[i])?t[t.length-1]-e[e.length-1]:0}function Os(t,e,n){let{routesMeta:r}=t,i={},s="/",a=[];for(let o=0;o<r.length;++o){let l=r[o],u=o===r.length-1,c=s==="/"?e:e.slice(s.length)||"/",f=Ts({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),d=l.route;if(!f)return null;Object.assign(i,f.params),a.push({params:i,pathname:te([s,f.pathname]),pathnameBase:Fs(te([s,f.pathnameBase])),route:d}),f.pathnameBase!=="/"&&(s=te([s,f.pathnameBase]))}return a}function Ts(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[n,r]=Ls(t.path,t.caseSensitive,t.end),i=e.match(n);if(!i)return null;let s=i[0],a=s.replace(/(.)\/+$/,"$1"),o=i.slice(1);return{params:r.reduce((u,c,f)=>{let{paramName:d,isOptional:p}=c;if(d==="*"){let g=o[f]||"";a=s.slice(0,s.length-g.length).replace(/(.)\/+$/,"$1")}const m=o[f];return p&&!m?u[d]=void 0:u[d]=(m||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:a,pattern:t}}function Ls(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!0),dr(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let r=[],i="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,o,l)=>(r.push({paramName:o,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(r.push({paramName:"*"}),i+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":t!==""&&t!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),r]}function Us(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return dr(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function gr(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,r=t.charAt(n);return r&&r!=="/"?null:t.slice(n)||"/"}function Ds(t,e){e===void 0&&(e="/");let{pathname:n,search:r="",hash:i=""}=typeof t=="string"?ye(t):t;return{pathname:n?n.startsWith("/")?n:$s(n,e):e,search:zs(r),hash:Ms(i)}}function $s(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function pt(t,e,n,r){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function js(t){return t.filter((e,n)=>n===0||e.route.path&&e.route.path.length>0)}function mr(t,e){let n=js(t);return e?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function yr(t,e,n,r){r===void 0&&(r=!1);let i;typeof t=="string"?i=ye(t):(i=Ne({},t),E(!i.pathname||!i.pathname.includes("?"),pt("?","pathname","search",i)),E(!i.pathname||!i.pathname.includes("#"),pt("#","pathname","hash",i)),E(!i.search||!i.search.includes("#"),pt("#","search","hash",i)));let s=t===""||i.pathname==="",a=s?"/":i.pathname,o;if(a==null)o=n;else{let f=e.length-1;if(!r&&a.startsWith("..")){let d=a.split("/");for(;d[0]==="..";)d.shift(),f-=1;i.pathname=d.join("/")}o=f>=0?e[f]:"/"}let l=Ds(i,o),u=a&&a!=="/"&&a.endsWith("/"),c=(s||a===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const te=t=>t.join("/").replace(/\/\/+/g,"/"),Fs=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),zs=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Ms=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function Bs(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const vr=["post","put","patch","delete"];new Set(vr);const Vs=["get",...vr];new Set(Vs);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Oe(){return Oe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Oe.apply(this,arguments)}const Vt=w.createContext(null),Hs=w.createContext(null),Fe=w.createContext(null),rt=w.createContext(null),le=w.createContext({outlet:null,matches:[],isDataRoute:!1}),wr=w.createContext(null);function ze(){return w.useContext(rt)!=null}function Ht(){return ze()||E(!1),w.useContext(rt).location}function br(t){w.useContext(Fe).static||w.useLayoutEffect(t)}function Ks(){let{isDataRoute:t}=w.useContext(le);return t?ia():Ws()}function Ws(){ze()||E(!1);let t=w.useContext(Vt),{basename:e,future:n,navigator:r}=w.useContext(Fe),{matches:i}=w.useContext(le),{pathname:s}=Ht(),a=JSON.stringify(mr(i,n.v7_relativeSplatPath)),o=w.useRef(!1);return br(()=>{o.current=!0}),w.useCallback(function(u,c){if(c===void 0&&(c={}),!o.current)return;if(typeof u=="number"){r.go(u);return}let f=yr(u,JSON.parse(a),s,c.relative==="path");t==null&&e!=="/"&&(f.pathname=f.pathname==="/"?e:te([e,f.pathname])),(c.replace?r.replace:r.push)(f,c.state,c)},[e,r,a,s,t])}function qs(t,e){return Js(t,e)}function Js(t,e,n,r){ze()||E(!1);let{navigator:i}=w.useContext(Fe),{matches:s}=w.useContext(le),a=s[s.length-1],o=a?a.params:{};a&&a.pathname;let l=a?a.pathnameBase:"/";a&&a.route;let u=Ht(),c;if(e){var f;let h=typeof e=="string"?ye(e):e;l==="/"||(f=h.pathname)!=null&&f.startsWith(l)||E(!1),c=h}else c=u;let d=c.pathname||"/",p=d;if(l!=="/"){let h=l.replace(/^\//,"").split("/");p="/"+d.replace(/^\//,"").split("/").slice(h.length).join("/")}let m=ks(t,{pathname:p}),g=Zs(m&&m.map(h=>Object.assign({},h,{params:Object.assign({},o,h.params),pathname:te([l,i.encodeLocation?i.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?l:te([l,i.encodeLocation?i.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),s,n,r);return e&&g?w.createElement(rt.Provider,{value:{location:Oe({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:J.Pop}},g):g}function Gs(){let t=ra(),e=Bs(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},e),n?w.createElement("pre",{style:i},n):null,null)}const Ys=w.createElement(Gs,null);class Xs extends w.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return n.location!==e.location||n.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:n.error,location:n.location,revalidation:e.revalidation||n.revalidation}}componentDidCatch(e,n){console.error("React Router caught the following error during render",e,n)}render(){return this.state.error!==void 0?w.createElement(le.Provider,{value:this.props.routeContext},w.createElement(wr.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Qs(t){let{routeContext:e,match:n,children:r}=t,i=w.useContext(Vt);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),w.createElement(le.Provider,{value:e},r)}function Zs(t,e,n,r){var i;if(e===void 0&&(e=[]),n===void 0&&(n=null),r===void 0&&(r=null),t==null){var s;if(!n)return null;if(n.errors)t=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&e.length===0&&!n.initialized&&n.matches.length>0)t=n.matches;else return null}let a=t,o=(i=n)==null?void 0:i.errors;if(o!=null){let c=a.findIndex(f=>f.route.id&&o?.[f.route.id]!==void 0);c>=0||E(!1),a=a.slice(0,Math.min(a.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<a.length;c++){let f=a[c];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=c),f.route.id){let{loaderData:d,errors:p}=n,m=f.route.loader&&d[f.route.id]===void 0&&(!p||p[f.route.id]===void 0);if(f.route.lazy||m){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((c,f,d)=>{let p,m=!1,g=null,h=null;n&&(p=o&&f.route.id?o[f.route.id]:void 0,g=f.route.errorElement||Ys,l&&(u<0&&d===0?(sa("route-fallback"),m=!0,h=null):u===d&&(m=!0,h=f.route.hydrateFallbackElement||null)));let v=e.concat(a.slice(0,d+1)),y=()=>{let k;return p?k=g:m?k=h:f.route.Component?k=w.createElement(f.route.Component,null):f.route.element?k=f.route.element:k=c,w.createElement(Qs,{match:f,routeContext:{outlet:c,matches:v,isDataRoute:n!=null},children:k})};return n&&(f.route.ErrorBoundary||f.route.errorElement||d===0)?w.createElement(Xs,{location:n.location,revalidation:n.revalidation,component:g,error:p,children:y(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):y()},null)}var kr=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(kr||{}),Sr=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(Sr||{});function ea(t){let e=w.useContext(Vt);return e||E(!1),e}function ta(t){let e=w.useContext(Hs);return e||E(!1),e}function na(t){let e=w.useContext(le);return e||E(!1),e}function xr(t){let e=na(),n=e.matches[e.matches.length-1];return n.route.id||E(!1),n.route.id}function ra(){var t;let e=w.useContext(wr),n=ta(),r=xr();return e!==void 0?e:(t=n.errors)==null?void 0:t[r]}function ia(){let{router:t}=ea(kr.UseNavigateStable),e=xr(Sr.UseNavigateStable),n=w.useRef(!1);return br(()=>{n.current=!0}),w.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?t.navigate(i):t.navigate(i,Oe({fromRouteId:e},s)))},[t,e])}const wn={};function sa(t,e,n){wn[t]||(wn[t]=!0)}function aa(t,e){t?.v7_startTransition,t?.v7_relativeSplatPath}function Kt(t){let{to:e,replace:n,state:r,relative:i}=t;ze()||E(!1);let{future:s,static:a}=w.useContext(Fe),{matches:o}=w.useContext(le),{pathname:l}=Ht(),u=Ks(),c=yr(e,mr(o,s.v7_relativeSplatPath),l,i==="path"),f=JSON.stringify(c);return w.useEffect(()=>u(JSON.parse(f),{replace:n,state:r,relative:i}),[u,f,i,n,r]),null}function B(t){E(!1)}function oa(t){let{basename:e="/",children:n=null,location:r,navigationType:i=J.Pop,navigator:s,static:a=!1,future:o}=t;ze()&&E(!1);let l=e.replace(/^\/*/,"/"),u=w.useMemo(()=>({basename:l,navigator:s,static:a,future:Oe({v7_relativeSplatPath:!1},o)}),[l,o,s,a]);typeof r=="string"&&(r=ye(r));let{pathname:c="/",search:f="",hash:d="",state:p=null,key:m="default"}=r,g=w.useMemo(()=>{let h=gr(c,l);return h==null?null:{location:{pathname:h,search:f,hash:d,state:p,key:m},navigationType:i}},[l,c,f,d,p,m,i]);return g==null?null:w.createElement(Fe.Provider,{value:u},w.createElement(rt.Provider,{children:n,value:g}))}function la(t){let{children:e,location:n}=t;return qs(Et(e),n)}new Promise(()=>{});function Et(t,e){e===void 0&&(e=[]);let n=[];return w.Children.forEach(t,(r,i)=>{if(!w.isValidElement(r))return;let s=[...e,i];if(r.type===w.Fragment){n.push.apply(n,Et(r.props.children,s));return}r.type!==B&&E(!1),!r.props.index||!r.props.children||E(!1);let a={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=Et(r.props.children,s)),n.push(a)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const ua="6";try{window.__reactRouterVersion=ua}catch{}const ca="startTransition",bn=ji[ca];function da(t){let{basename:e,children:n,future:r,window:i}=t,s=w.useRef();s.current==null&&(s.current=vs({window:i,v5Compat:!0}));let a=s.current,[o,l]=w.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},c=w.useCallback(f=>{u&&bn?bn(()=>l(f)):l(f)},[l,u]);return w.useLayoutEffect(()=>a.listen(c),[a,c]),w.useEffect(()=>aa(r),[r]),w.createElement(oa,{basename:e,children:n,location:o.location,navigationType:o.action,navigator:a,future:r})}var kn;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(kn||(kn={}));var Sn;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(Sn||(Sn={}));function fa(){if(console&&console.warn){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];typeof e[0]=="string"&&(e[0]=`react-i18next:: ${e[0]}`),console.warn(...e)}}const xn={};function Rt(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];typeof e[0]=="string"&&xn[e[0]]||(typeof e[0]=="string"&&(xn[e[0]]=new Date),fa(...e))}const Ir=(t,e)=>()=>{if(t.isInitialized)e();else{const n=()=>{setTimeout(()=>{t.off("initialized",n)},0),e()};t.on("initialized",n)}};function In(t,e,n){t.loadNamespaces(e,Ir(t,n))}function Cn(t,e,n,r){typeof n=="string"&&(n=[n]),n.forEach(i=>{t.options.ns.indexOf(i)<0&&t.options.ns.push(i)}),t.loadLanguages(e,Ir(t,r))}function ha(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const r=e.languages[0],i=e.options?e.options.fallbackLng:!1,s=e.languages[e.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const a=(o,l)=>{const u=e.services.backendConnector.state[`${o}|${l}`];return u===-1||u===2};return n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&e.services.backendConnector.backend&&e.isLanguageChangingTo&&!a(e.isLanguageChangingTo,t)?!1:!!(e.hasResourceBundle(r,t)||!e.services.backendConnector.backend||e.options.resources&&!e.options.partialBundledLanguages||a(r,t)&&(!i||a(s,t)))}function pa(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return!e.languages||!e.languages.length?(Rt("i18n.languages were undefined or empty",e.languages),!0):e.options.ignoreJSONStructure!==void 0?e.hasLoadedNamespace(t,{lng:n.lng,precheck:(i,s)=>{if(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&i.services.backendConnector.backend&&i.isLanguageChangingTo&&!s(i.isLanguageChangingTo,t))return!1}}):ha(t,e,n)}const ga=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,ma={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},ya=t=>ma[t],va=t=>t.replace(ga,ya);let At={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:va};function wa(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};At={...At,...t}}function ba(){return At}let Cr;function ka(t){Cr=t}function Sa(){return Cr}const xa={type:"3rdParty",init(t){wa(t.options.react),ka(t)}},Ia=w.createContext();class Ca{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(n=>{this.usedNamespaces[n]||(this.usedNamespaces[n]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const _a=(t,e)=>{const n=w.useRef();return w.useEffect(()=>{n.current=t},[t,e]),n.current};function Pa(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{i18n:n}=e,{i18n:r,defaultNS:i}=w.useContext(Ia)||{},s=n||r||Sa();if(s&&!s.reportNamespaces&&(s.reportNamespaces=new Ca),!s){Rt("You will need to pass in an i18next instance by using initReactI18next");const k=(I,C)=>typeof C=="string"?C:C&&typeof C=="object"&&typeof C.defaultValue=="string"?C.defaultValue:Array.isArray(I)?I[I.length-1]:I,S=[k,{},!1];return S.t=k,S.i18n={},S.ready=!1,S}s.options.react&&s.options.react.wait!==void 0&&Rt("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...ba(),...s.options.react,...e},{useSuspense:o,keyPrefix:l}=a;let u=i||s.options&&s.options.defaultNS;u=typeof u=="string"?[u]:u||["translation"],s.reportNamespaces.addUsedNamespaces&&s.reportNamespaces.addUsedNamespaces(u);const c=(s.isInitialized||s.initializedStoreOnce)&&u.every(k=>pa(k,s,a));function f(){return s.getFixedT(e.lng||null,a.nsMode==="fallback"?u:u[0],l)}const[d,p]=w.useState(f);let m=u.join();e.lng&&(m=`${e.lng}${m}`);const g=_a(m),h=w.useRef(!0);w.useEffect(()=>{const{bindI18n:k,bindI18nStore:S}=a;h.current=!0,!c&&!o&&(e.lng?Cn(s,e.lng,u,()=>{h.current&&p(f)}):In(s,u,()=>{h.current&&p(f)})),c&&g&&g!==m&&h.current&&p(f);function I(){h.current&&p(f)}return k&&s&&s.on(k,I),S&&s&&s.store.on(S,I),()=>{h.current=!1,k&&s&&k.split(" ").forEach(C=>s.off(C,I)),S&&s&&S.split(" ").forEach(C=>s.store.off(C,I))}},[s,m]);const v=w.useRef(!0);w.useEffect(()=>{h.current&&!v.current&&p(f),v.current=!1},[s,l]);const y=[d,s,c];if(y.t=d,y.i18n=s,y.ready=c,c||!c&&!o)return y;throw new Promise(k=>{e.lng?Cn(s,e.lng,u,()=>k()):In(s,u,()=>k())})}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const _r="firebasestorage.googleapis.com",Pr="storageBucket",Ea=2*60*1e3,Ra=10*60*1e3;/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class P extends Bt{constructor(e,n,r=0){super(gt(e),`Firebase Storage: ${n} (${gt(e)})`),this.status_=r,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,P.prototype)}get status(){return this.status_}set status(e){this.status_=e}_codeEquals(e){return gt(e)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(e){this.customData.serverResponse=e,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}}var _;(function(t){t.UNKNOWN="unknown",t.OBJECT_NOT_FOUND="object-not-found",t.BUCKET_NOT_FOUND="bucket-not-found",t.PROJECT_NOT_FOUND="project-not-found",t.QUOTA_EXCEEDED="quota-exceeded",t.UNAUTHENTICATED="unauthenticated",t.UNAUTHORIZED="unauthorized",t.UNAUTHORIZED_APP="unauthorized-app",t.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",t.INVALID_CHECKSUM="invalid-checksum",t.CANCELED="canceled",t.INVALID_EVENT_NAME="invalid-event-name",t.INVALID_URL="invalid-url",t.INVALID_DEFAULT_BUCKET="invalid-default-bucket",t.NO_DEFAULT_BUCKET="no-default-bucket",t.CANNOT_SLICE_BLOB="cannot-slice-blob",t.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",t.NO_DOWNLOAD_URL="no-download-url",t.INVALID_ARGUMENT="invalid-argument",t.INVALID_ARGUMENT_COUNT="invalid-argument-count",t.APP_DELETED="app-deleted",t.INVALID_ROOT_OPERATION="invalid-root-operation",t.INVALID_FORMAT="invalid-format",t.INTERNAL_ERROR="internal-error",t.UNSUPPORTED_ENVIRONMENT="unsupported-environment"})(_||(_={}));function gt(t){return"storage/"+t}function Wt(){const t="An unknown error occurred, please check the error payload for server response.";return new P(_.UNKNOWN,t)}function Aa(t){return new P(_.OBJECT_NOT_FOUND,"Object '"+t+"' does not exist.")}function Na(t){return new P(_.QUOTA_EXCEEDED,"Quota for bucket '"+t+"' exceeded, please view quota on https://firebase.google.com/pricing/.")}function Oa(){const t="User is not authenticated, please authenticate using Firebase Authentication and try again.";return new P(_.UNAUTHENTICATED,t)}function Ta(){return new P(_.UNAUTHORIZED_APP,"This app does not have permission to access Firebase Storage on this project.")}function La(t){return new P(_.UNAUTHORIZED,"User does not have permission to access '"+t+"'.")}function Ua(){return new P(_.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.")}function Da(){return new P(_.CANCELED,"User canceled the upload/download.")}function $a(t){return new P(_.INVALID_URL,"Invalid URL '"+t+"'.")}function ja(t){return new P(_.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+t+"'.")}function Fa(){return new P(_.NO_DEFAULT_BUCKET,"No default bucket found. Did you set the '"+Pr+"' property when initializing the app?")}function za(){return new P(_.CANNOT_SLICE_BLOB,"Cannot slice blob for upload. Please retry the upload.")}function Ma(){return new P(_.NO_DOWNLOAD_URL,"The given file does not have any download URLs.")}function Ba(t){return new P(_.UNSUPPORTED_ENVIRONMENT,`${t} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`)}function Nt(t){return new P(_.INVALID_ARGUMENT,t)}function Er(){return new P(_.APP_DELETED,"The Firebase app was deleted.")}function Va(t){return new P(_.INVALID_ROOT_OPERATION,"The operation '"+t+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}function _e(t,e){return new P(_.INVALID_FORMAT,"String does not match format '"+t+"': "+e)}function Se(t){throw new P(_.INTERNAL_ERROR,"Internal error: "+t)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class U{constructor(e,n){this.bucket=e,this.path_=n}get path(){return this.path_}get isRoot(){return this.path.length===0}fullServerUrl(){const e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o/"+e(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(e,n){let r;try{r=U.makeFromUrl(e,n)}catch{return new U(e,"")}if(r.path==="")return r;throw ja(e)}static makeFromUrl(e,n){let r=null;const i="([A-Za-z0-9.\\-_]+)";function s(S){S.path.charAt(S.path.length-1)==="/"&&(S.path_=S.path_.slice(0,-1))}const a="(/(.*))?$",o=new RegExp("^gs://"+i+a,"i"),l={bucket:1,path:3};function u(S){S.path_=decodeURIComponent(S.path)}const c="v[A-Za-z0-9_]+",f=n.replace(/[.]/g,"\\."),d="(/([^?#]*).*)?$",p=new RegExp(`^https?://${f}/${c}/b/${i}/o${d}`,"i"),m={bucket:1,path:3},g=n===_r?"(?:storage.googleapis.com|storage.cloud.google.com)":n,h="([^?#]*)",v=new RegExp(`^https?://${g}/${i}/${h}`,"i"),k=[{regex:o,indices:l,postModify:s},{regex:p,indices:m,postModify:u},{regex:v,indices:{bucket:1,path:2},postModify:u}];for(let S=0;S<k.length;S++){const I=k[S],C=I.regex.exec(e);if(C){const R=C[I.indices.bucket];let K=C[I.indices.path];K||(K=""),r=new U(R,K),I.postModify(r);break}}if(r==null)throw $a(e);return r}}class Ha{constructor(e){this.promise_=Promise.reject(e)}getPromise(){return this.promise_}cancel(e=!1){}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Ka(t,e,n){let r=1,i=null,s=null,a=!1,o=0;function l(){return o===2}let u=!1;function c(...h){u||(u=!0,e.apply(null,h))}function f(h){i=setTimeout(()=>{i=null,t(p,l())},h)}function d(){s&&clearTimeout(s)}function p(h,...v){if(u){d();return}if(h){d(),c.call(null,h,...v);return}if(l()||a){d(),c.call(null,h,...v);return}r<64&&(r*=2);let k;o===1?(o=2,k=0):k=(r+Math.random())*1e3,f(k)}let m=!1;function g(h){m||(m=!0,d(),!u&&(i!==null?(h||(o=2),clearTimeout(i),f(0)):h||(o=1)))}return f(0),s=setTimeout(()=>{a=!0,g(!0)},n),g}function Wa(t){t(!1)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function qa(t){return t!==void 0}function Ja(t){return typeof t=="object"&&!Array.isArray(t)}function qt(t){return typeof t=="string"||t instanceof String}function _n(t){return Jt()&&t instanceof Blob}function Jt(){return typeof Blob<"u"}function Pn(t,e,n,r){if(r<e)throw Nt(`Invalid value for '${t}'. Expected ${e} or greater.`);if(r>n)throw Nt(`Invalid value for '${t}'. Expected ${n} or less.`)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Gt(t,e,n){let r=e;return n==null&&(r=`https://${e}`),`${n}://${r}/v0${t}`}function Rr(t){const e=encodeURIComponent;let n="?";for(const r in t)if(t.hasOwnProperty(r)){const i=e(r)+"="+e(t[r]);n=n+i+"&"}return n=n.slice(0,-1),n}var ne;(function(t){t[t.NO_ERROR=0]="NO_ERROR",t[t.NETWORK_ERROR=1]="NETWORK_ERROR",t[t.ABORT=2]="ABORT"})(ne||(ne={}));/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Ga(t,e){const n=t>=500&&t<600,i=[408,429].indexOf(t)!==-1,s=e.indexOf(t)!==-1;return n||i||s}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Ya{constructor(e,n,r,i,s,a,o,l,u,c,f,d=!0){this.url_=e,this.method_=n,this.headers_=r,this.body_=i,this.successCodes_=s,this.additionalRetryCodes_=a,this.callback_=o,this.errorCallback_=l,this.timeout_=u,this.progressCallback_=c,this.connectionFactory_=f,this.retry=d,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((p,m)=>{this.resolve_=p,this.reject_=m,this.start_()})}start_(){const e=(r,i)=>{if(i){r(!1,new Be(!1,null,!0));return}const s=this.connectionFactory_();this.pendingConnection_=s;const a=o=>{const l=o.loaded,u=o.lengthComputable?o.total:-1;this.progressCallback_!==null&&this.progressCallback_(l,u)};this.progressCallback_!==null&&s.addUploadProgressListener(a),s.send(this.url_,this.method_,this.body_,this.headers_).then(()=>{this.progressCallback_!==null&&s.removeUploadProgressListener(a),this.pendingConnection_=null;const o=s.getErrorCode()===ne.NO_ERROR,l=s.getStatus();if(!o||Ga(l,this.additionalRetryCodes_)&&this.retry){const c=s.getErrorCode()===ne.ABORT;r(!1,new Be(!1,null,c));return}const u=this.successCodes_.indexOf(l)!==-1;r(!0,new Be(u,s))})},n=(r,i)=>{const s=this.resolve_,a=this.reject_,o=i.connection;if(i.wasSuccessCode)try{const l=this.callback_(o,o.getResponse());qa(l)?s(l):s()}catch(l){a(l)}else if(o!==null){const l=Wt();l.serverResponse=o.getErrorText(),this.errorCallback_?a(this.errorCallback_(o,l)):a(l)}else if(i.canceled){const l=this.appDelete_?Er():Da();a(l)}else{const l=Ua();a(l)}};this.canceled_?n(!1,new Be(!1,null,!0)):this.backoffId_=Ka(e,n,this.timeout_)}getPromise(){return this.promise_}cancel(e){this.canceled_=!0,this.appDelete_=e||!1,this.backoffId_!==null&&Wa(this.backoffId_),this.pendingConnection_!==null&&this.pendingConnection_.abort()}}class Be{constructor(e,n,r){this.wasSuccessCode=e,this.connection=n,this.canceled=!!r}}function Xa(t,e){e!==null&&e.length>0&&(t.Authorization="Firebase "+e)}function Qa(t,e){t["X-Firebase-Storage-Version"]="webjs/"+(e??"AppManager")}function Za(t,e){e&&(t["X-Firebase-GMPID"]=e)}function eo(t,e){e!==null&&(t["X-Firebase-AppCheck"]=e)}function to(t,e,n,r,i,s,a=!0){const o=Rr(t.urlParams),l=t.url+o,u=Object.assign({},t.headers);return Za(u,e),Xa(u,n),Qa(u,s),eo(u,r),new Ya(l,t.method,u,t.body,t.successCodes,t.additionalRetryCodes,t.handler,t.errorHandler,t.timeout,t.progressCallback,i,a)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function no(){return typeof BlobBuilder<"u"?BlobBuilder:typeof WebKitBlobBuilder<"u"?WebKitBlobBuilder:void 0}function ro(...t){const e=no();if(e!==void 0){const n=new e;for(let r=0;r<t.length;r++)n.append(t[r]);return n.getBlob()}else{if(Jt())return new Blob(t);throw new P(_.UNSUPPORTED_ENVIRONMENT,"This browser doesn't seem to support creating Blobs")}}function io(t,e,n){return t.webkitSlice?t.webkitSlice(e,n):t.mozSlice?t.mozSlice(e,n):t.slice?t.slice(e,n):null}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function so(t){if(typeof atob>"u")throw Ba("base-64");return atob(t)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const z={RAW:"raw",BASE64:"base64",BASE64URL:"base64url",DATA_URL:"data_url"};class mt{constructor(e,n){this.data=e,this.contentType=n||null}}function ao(t,e){switch(t){case z.RAW:return new mt(Ar(e));case z.BASE64:case z.BASE64URL:return new mt(Nr(t,e));case z.DATA_URL:return new mt(lo(e),uo(e))}throw Wt()}function Ar(t){const e=[];for(let n=0;n<t.length;n++){let r=t.charCodeAt(n);if(r<=127)e.push(r);else if(r<=2047)e.push(192|r>>6,128|r&63);else if((r&64512)===55296)if(!(n<t.length-1&&(t.charCodeAt(n+1)&64512)===56320))e.push(239,191,189);else{const s=r,a=t.charCodeAt(++n);r=65536|(s&1023)<<10|a&1023,e.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|r&63)}else(r&64512)===56320?e.push(239,191,189):e.push(224|r>>12,128|r>>6&63,128|r&63)}return new Uint8Array(e)}function oo(t){let e;try{e=decodeURIComponent(t)}catch{throw _e(z.DATA_URL,"Malformed data URL.")}return Ar(e)}function Nr(t,e){switch(t){case z.BASE64:{const i=e.indexOf("-")!==-1,s=e.indexOf("_")!==-1;if(i||s)throw _e(t,"Invalid character '"+(i?"-":"_")+"' found: is it base64url encoded?");break}case z.BASE64URL:{const i=e.indexOf("+")!==-1,s=e.indexOf("/")!==-1;if(i||s)throw _e(t,"Invalid character '"+(i?"+":"/")+"' found: is it base64 encoded?");e=e.replace(/-/g,"+").replace(/_/g,"/");break}}let n;try{n=so(e)}catch(i){throw i.message.includes("polyfill")?i:_e(t,"Invalid character found")}const r=new Uint8Array(n.length);for(let i=0;i<n.length;i++)r[i]=n.charCodeAt(i);return r}class Or{constructor(e){this.base64=!1,this.contentType=null;const n=e.match(/^data:([^,]+)?,/);if(n===null)throw _e(z.DATA_URL,"Must be formatted 'data:[<mediatype>][;base64],<data>");const r=n[1]||null;r!=null&&(this.base64=co(r,";base64"),this.contentType=this.base64?r.substring(0,r.length-7):r),this.rest=e.substring(e.indexOf(",")+1)}}function lo(t){const e=new Or(t);return e.base64?Nr(z.BASE64,e.rest):oo(e.rest)}function uo(t){return new Or(t).contentType}function co(t,e){return t.length>=e.length?t.substring(t.length-e.length)===e:!1}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class q{constructor(e,n){let r=0,i="";_n(e)?(this.data_=e,r=e.size,i=e.type):e instanceof ArrayBuffer?(n?this.data_=new Uint8Array(e):(this.data_=new Uint8Array(e.byteLength),this.data_.set(new Uint8Array(e))),r=this.data_.length):e instanceof Uint8Array&&(n?this.data_=e:(this.data_=new Uint8Array(e.length),this.data_.set(e)),r=e.length),this.size_=r,this.type_=i}size(){return this.size_}type(){return this.type_}slice(e,n){if(_n(this.data_)){const r=this.data_,i=io(r,e,n);return i===null?null:new q(i)}else{const r=new Uint8Array(this.data_.buffer,e,n-e);return new q(r,!0)}}static getBlob(...e){if(Jt()){const n=e.map(r=>r instanceof q?r.data_:r);return new q(ro.apply(null,n))}else{const n=e.map(a=>qt(a)?ao(z.RAW,a).data:a.data_);let r=0;n.forEach(a=>{r+=a.byteLength});const i=new Uint8Array(r);let s=0;return n.forEach(a=>{for(let o=0;o<a.length;o++)i[s++]=a[o]}),new q(i,!0)}}uploadData(){return this.data_}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Tr(t){let e;try{e=JSON.parse(t)}catch{return null}return Ja(e)?e:null}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function fo(t){if(t.length===0)return null;const e=t.lastIndexOf("/");return e===-1?"":t.slice(0,e)}function ho(t,e){const n=e.split("/").filter(r=>r.length>0).join("/");return t.length===0?n:t+"/"+n}function Lr(t){const e=t.lastIndexOf("/",t.length-2);return e===-1?t:t.slice(e+1)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function po(t,e){return e}class O{constructor(e,n,r,i){this.server=e,this.local=n||e,this.writable=!!r,this.xform=i||po}}let Ve=null;function go(t){return!qt(t)||t.length<2?t:Lr(t)}function Ur(){if(Ve)return Ve;const t=[];t.push(new O("bucket")),t.push(new O("generation")),t.push(new O("metageneration")),t.push(new O("name","fullPath",!0));function e(s,a){return go(a)}const n=new O("name");n.xform=e,t.push(n);function r(s,a){return a!==void 0?Number(a):a}const i=new O("size");return i.xform=r,t.push(i),t.push(new O("timeCreated")),t.push(new O("updated")),t.push(new O("md5Hash",null,!0)),t.push(new O("cacheControl",null,!0)),t.push(new O("contentDisposition",null,!0)),t.push(new O("contentEncoding",null,!0)),t.push(new O("contentLanguage",null,!0)),t.push(new O("contentType",null,!0)),t.push(new O("metadata","customMetadata",!0)),Ve=t,Ve}function mo(t,e){function n(){const r=t.bucket,i=t.fullPath,s=new U(r,i);return e._makeStorageReference(s)}Object.defineProperty(t,"ref",{get:n})}function yo(t,e,n){const r={};r.type="file";const i=n.length;for(let s=0;s<i;s++){const a=n[s];r[a.local]=a.xform(r,e[a.server])}return mo(r,t),r}function Dr(t,e,n){const r=Tr(e);return r===null?null:yo(t,r,n)}function vo(t,e,n,r){const i=Tr(e);if(i===null||!qt(i.downloadTokens))return null;const s=i.downloadTokens;if(s.length===0)return null;const a=encodeURIComponent;return s.split(",").map(u=>{const c=t.bucket,f=t.fullPath,d="/b/"+a(c)+"/o/"+a(f),p=Gt(d,n,r),m=Rr({alt:"media",token:u});return p+m})[0]}function wo(t,e){const n={},r=e.length;for(let i=0;i<r;i++){const s=e[i];s.writable&&(n[s.server]=t[s.local])}return JSON.stringify(n)}class $r{constructor(e,n,r,i){this.url=e,this.method=n,this.handler=r,this.timeout=i,this.urlParams={},this.headers={},this.body=null,this.errorHandler=null,this.progressCallback=null,this.successCodes=[200],this.additionalRetryCodes=[]}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function jr(t){if(!t)throw Wt()}function bo(t,e){function n(r,i){const s=Dr(t,i,e);return jr(s!==null),s}return n}function ko(t,e){function n(r,i){const s=Dr(t,i,e);return jr(s!==null),vo(s,i,t.host,t._protocol)}return n}function Fr(t){function e(n,r){let i;return n.getStatus()===401?n.getErrorText().includes("Firebase App Check token is invalid")?i=Ta():i=Oa():n.getStatus()===402?i=Na(t.bucket):n.getStatus()===403?i=La(t.path):i=r,i.status=n.getStatus(),i.serverResponse=r.serverResponse,i}return e}function So(t){const e=Fr(t);function n(r,i){let s=e(r,i);return r.getStatus()===404&&(s=Aa(t.path)),s.serverResponse=i.serverResponse,s}return n}function xo(t,e,n){const r=e.fullServerUrl(),i=Gt(r,t.host,t._protocol),s="GET",a=t.maxOperationRetryTime,o=new $r(i,s,ko(t,n),a);return o.errorHandler=So(e),o}function Io(t,e){return t&&t.contentType||e&&e.type()||"application/octet-stream"}function Co(t,e,n){const r=Object.assign({},n);return r.fullPath=t.path,r.size=e.size(),r.contentType||(r.contentType=Io(null,e)),r}function _o(t,e,n,r,i){const s=e.bucketOnlyServerUrl(),a={"X-Goog-Upload-Protocol":"multipart"};function o(){let k="";for(let S=0;S<2;S++)k=k+Math.random().toString().slice(2);return k}const l=o();a["Content-Type"]="multipart/related; boundary="+l;const u=Co(e,r,i),c=wo(u,n),f="--"+l+`\r
Content-Type: application/json; charset=utf-8\r
\r
`+c+`\r
--`+l+`\r
Content-Type: `+u.contentType+`\r
\r
`,d=`\r
--`+l+"--",p=q.getBlob(f,r,d);if(p===null)throw za();const m={name:u.fullPath},g=Gt(s,t.host,t._protocol),h="POST",v=t.maxUploadRetryTime,y=new $r(g,h,bo(t,n),v);return y.urlParams=m,y.headers=a,y.body=p.uploadData(),y.errorHandler=Fr(e),y}class Po{constructor(){this.sent_=!1,this.xhr_=new XMLHttpRequest,this.initXhr(),this.errorCode_=ne.NO_ERROR,this.sendPromise_=new Promise(e=>{this.xhr_.addEventListener("abort",()=>{this.errorCode_=ne.ABORT,e()}),this.xhr_.addEventListener("error",()=>{this.errorCode_=ne.NETWORK_ERROR,e()}),this.xhr_.addEventListener("load",()=>{e()})})}send(e,n,r,i){if(this.sent_)throw Se("cannot .send() more than once");if(this.sent_=!0,this.xhr_.open(n,e,!0),i!==void 0)for(const s in i)i.hasOwnProperty(s)&&this.xhr_.setRequestHeader(s,i[s].toString());return r!==void 0?this.xhr_.send(r):this.xhr_.send(),this.sendPromise_}getErrorCode(){if(!this.sent_)throw Se("cannot .getErrorCode() before sending");return this.errorCode_}getStatus(){if(!this.sent_)throw Se("cannot .getStatus() before sending");try{return this.xhr_.status}catch{return-1}}getResponse(){if(!this.sent_)throw Se("cannot .getResponse() before sending");return this.xhr_.response}getErrorText(){if(!this.sent_)throw Se("cannot .getErrorText() before sending");return this.xhr_.statusText}abort(){this.xhr_.abort()}getResponseHeader(e){return this.xhr_.getResponseHeader(e)}addUploadProgressListener(e){this.xhr_.upload!=null&&this.xhr_.upload.addEventListener("progress",e)}removeUploadProgressListener(e){this.xhr_.upload!=null&&this.xhr_.upload.removeEventListener("progress",e)}}class Eo extends Po{initXhr(){this.xhr_.responseType="text"}}function zr(){return new Eo}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class re{constructor(e,n){this._service=e,n instanceof U?this._location=n:this._location=U.makeFromUrl(n,e.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(e,n){return new re(e,n)}get root(){const e=new U(this._location.bucket,"");return this._newRef(this._service,e)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return Lr(this._location.path)}get storage(){return this._service}get parent(){const e=fo(this._location.path);if(e===null)return null;const n=new U(this._location.bucket,e);return new re(this._service,n)}_throwIfRoot(e){if(this._location.path==="")throw Va(e)}}function Ro(t,e,n){t._throwIfRoot("uploadBytes");const r=_o(t.storage,t._location,Ur(),new q(e,!0),n);return t.storage.makeRequestWithTokens(r,zr).then(i=>({metadata:i,ref:t}))}function Ao(t){t._throwIfRoot("getDownloadURL");const e=xo(t.storage,t._location,Ur());return t.storage.makeRequestWithTokens(e,zr).then(n=>{if(n===null)throw Ma();return n})}function No(t,e){const n=ho(t._location.path,e),r=new U(t._location.bucket,n);return new re(t.storage,r)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Oo(t){return/^[A-Za-z]+:\/\//.test(t)}function To(t,e){return new re(t,e)}function Mr(t,e){if(t instanceof Yt){const n=t;if(n._bucket==null)throw Fa();const r=new re(n,n._bucket);return e!=null?Mr(r,e):r}else return e!==void 0?No(t,e):t}function Lo(t,e){if(e&&Oo(e)){if(t instanceof Yt)return To(t,e);throw Nt("To use ref(service, url), the first argument must be a Storage instance.")}else return Mr(t,e)}function En(t,e){const n=e?.[Pr];return n==null?null:U.makeFromBucketSpec(n,t)}function Uo(t,e,n,r={}){t.host=`${e}:${n}`,t._protocol="http";const{mockUserToken:i}=r;i&&(t._overrideAuthToken=typeof i=="string"?i:Bi(i,t.app.options.projectId))}class Yt{constructor(e,n,r,i,s){this.app=e,this._authProvider=n,this._appCheckProvider=r,this._url=i,this._firebaseVersion=s,this._bucket=null,this._host=_r,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=Ea,this._maxUploadRetryTime=Ra,this._requests=new Set,i!=null?this._bucket=U.makeFromBucketSpec(i,this._host):this._bucket=En(this._host,this.app.options)}get host(){return this._host}set host(e){this._host=e,this._url!=null?this._bucket=U.makeFromBucketSpec(this._url,e):this._bucket=En(e,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(e){Pn("time",0,Number.POSITIVE_INFINITY,e),this._maxUploadRetryTime=e}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(e){Pn("time",0,Number.POSITIVE_INFINITY,e),this._maxOperationRetryTime=e}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;const e=this._authProvider.getImmediate({optional:!0});if(e){const n=await e.getToken();if(n!==null)return n.accessToken}return null}async _getAppCheckToken(){const e=this._appCheckProvider.getImmediate({optional:!0});return e?(await e.getToken()).token:null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(e=>e.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(e){return new re(this,e)}_makeRequest(e,n,r,i,s=!0){if(this._deleted)return new Ha(Er());{const a=to(e,this._appId,r,i,n,this._firebaseVersion,s);return this._requests.add(a),a.getPromise().then(()=>this._requests.delete(a),()=>this._requests.delete(a)),a}}async makeRequestWithTokens(e,n){const[r,i]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(e,n,r,i).getPromise()}}const Rn="@firebase/storage",An="0.13.2";/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Br="storage";function Bd(t,e,n){return t=me(t),Ro(t,e,n)}function Vd(t){return t=me(t),Ao(t)}function Hd(t,e){return t=me(t),Lo(t,e)}function Do(t=or(),e){t=me(t);const r=je(t,Br).getImmediate({identifier:e}),i=Mi("storage");return i&&$o(r,...i),r}function $o(t,e,n,r={}){Uo(t,e,n,r)}function jo(t,{instanceIdentifier:e}){const n=t.getProvider("app").getImmediate(),r=t.getProvider("auth-internal"),i=t.getProvider("app-check-internal");return new Yt(n,r,i,e,zi)}function Fo(){Re(new Ae(Br,jo,"PUBLIC").setMultipleInstances(!0)),fe(Rn,An,""),fe(Rn,An,"esm2017")}Fo();const Vr="@firebase/installations",Xt="0.6.9";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Hr=1e4,Kr=`w:${Xt}`,Wr="FIS_v2",zo="https://firebaseinstallations.googleapis.com/v1",Mo=60*60*1e3,Bo="installations",Vo="Installations";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ho={"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."},ie=new lr(Bo,Vo,Ho);function qr(t){return t instanceof Bt&&t.code.includes("request-failed")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Jr({projectId:t}){return`${zo}/projects/${t}/installations`}function Gr(t){return{token:t.token,requestStatus:2,expiresIn:Wo(t.expiresIn),creationTime:Date.now()}}async function Yr(t,e){const r=(await e.json()).error;return ie.create("request-failed",{requestName:t,serverCode:r.code,serverMessage:r.message,serverStatus:r.status})}function Xr({apiKey:t}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t})}function Ko(t,{refreshToken:e}){const n=Xr(t);return n.append("Authorization",qo(e)),n}async function Qr(t){const e=await t();return e.status>=500&&e.status<600?t():e}function Wo(t){return Number(t.replace("s","000"))}function qo(t){return`${Wr} ${t}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Jo({appConfig:t,heartbeatServiceProvider:e},{fid:n}){const r=Jr(t),i=Xr(t),s=e.getImmediate({optional:!0});if(s){const u=await s.getHeartbeatsHeader();u&&i.append("x-firebase-client",u)}const a={fid:n,authVersion:Wr,appId:t.appId,sdkVersion:Kr},o={method:"POST",headers:i,body:JSON.stringify(a)},l=await Qr(()=>fetch(r,o));if(l.ok){const u=await l.json();return{fid:u.fid||n,registrationStatus:2,refreshToken:u.refreshToken,authToken:Gr(u.authToken)}}else throw await Yr("Create Installation",l)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Zr(t){return new Promise(e=>{setTimeout(e,t)})}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Go(t){return btoa(String.fromCharCode(...t)).replace(/\+/g,"-").replace(/\//g,"_")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Yo=/^[cdef][\w-]{21}$/,Ot="";function Xo(){try{const t=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(t),t[0]=112+t[0]%16;const n=Qo(t);return Yo.test(n)?n:Ot}catch{return Ot}}function Qo(t){return Go(t).substr(0,22)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function it(t){return`${t.appName}!${t.appId}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ei=new Map;function ti(t,e){const n=it(t);ni(n,e),Zo(n,e)}function ni(t,e){const n=ei.get(t);if(n)for(const r of n)r(e)}function Zo(t,e){const n=el();n&&n.postMessage({key:t,fid:e}),tl()}let ee=null;function el(){return!ee&&"BroadcastChannel"in self&&(ee=new BroadcastChannel("[Firebase] FID Change"),ee.onmessage=t=>{ni(t.data.key,t.data.fid)}),ee}function tl(){ei.size===0&&ee&&(ee.close(),ee=null)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const nl="firebase-installations-database",rl=1,se="firebase-installations-store";let yt=null;function Qt(){return yt||(yt=Vi(nl,rl,{upgrade:(t,e)=>{switch(e){case 0:t.createObjectStore(se)}}})),yt}async function qe(t,e){const n=it(t),i=(await Qt()).transaction(se,"readwrite"),s=i.objectStore(se),a=await s.get(n);return await s.put(e,n),await i.done,(!a||a.fid!==e.fid)&&ti(t,e.fid),e}async function ri(t){const e=it(t),r=(await Qt()).transaction(se,"readwrite");await r.objectStore(se).delete(e),await r.done}async function st(t,e){const n=it(t),i=(await Qt()).transaction(se,"readwrite"),s=i.objectStore(se),a=await s.get(n),o=e(a);return o===void 0?await s.delete(n):await s.put(o,n),await i.done,o&&(!a||a.fid!==o.fid)&&ti(t,o.fid),o}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Zt(t){let e;const n=await st(t.appConfig,r=>{const i=il(r),s=sl(t,i);return e=s.registrationPromise,s.installationEntry});return n.fid===Ot?{installationEntry:await e}:{installationEntry:n,registrationPromise:e}}function il(t){const e=t||{fid:Xo(),registrationStatus:0};return ii(e)}function sl(t,e){if(e.registrationStatus===0){if(!navigator.onLine){const i=Promise.reject(ie.create("app-offline"));return{installationEntry:e,registrationPromise:i}}const n={fid:e.fid,registrationStatus:1,registrationTime:Date.now()},r=al(t,n);return{installationEntry:n,registrationPromise:r}}else return e.registrationStatus===1?{installationEntry:e,registrationPromise:ol(t)}:{installationEntry:e}}async function al(t,e){try{const n=await Jo(t,e);return qe(t.appConfig,n)}catch(n){throw qr(n)&&n.customData.serverCode===409?await ri(t.appConfig):await qe(t.appConfig,{fid:e.fid,registrationStatus:0}),n}}async function ol(t){let e=await Nn(t.appConfig);for(;e.registrationStatus===1;)await Zr(100),e=await Nn(t.appConfig);if(e.registrationStatus===0){const{installationEntry:n,registrationPromise:r}=await Zt(t);return r||n}return e}function Nn(t){return st(t,e=>{if(!e)throw ie.create("installation-not-found");return ii(e)})}function ii(t){return ll(t)?{fid:t.fid,registrationStatus:0}:t}function ll(t){return t.registrationStatus===1&&t.registrationTime+Hr<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function ul({appConfig:t,heartbeatServiceProvider:e},n){const r=cl(t,n),i=Ko(t,n),s=e.getImmediate({optional:!0});if(s){const u=await s.getHeartbeatsHeader();u&&i.append("x-firebase-client",u)}const a={installation:{sdkVersion:Kr,appId:t.appId}},o={method:"POST",headers:i,body:JSON.stringify(a)},l=await Qr(()=>fetch(r,o));if(l.ok){const u=await l.json();return Gr(u)}else throw await Yr("Generate Auth Token",l)}function cl(t,{fid:e}){return`${Jr(t)}/${e}/authTokens:generate`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function en(t,e=!1){let n;const r=await st(t.appConfig,s=>{if(!si(s))throw ie.create("not-registered");const a=s.authToken;if(!e&&hl(a))return s;if(a.requestStatus===1)return n=dl(t,e),s;{if(!navigator.onLine)throw ie.create("app-offline");const o=gl(s);return n=fl(t,o),o}});return n?await n:r.authToken}async function dl(t,e){let n=await On(t.appConfig);for(;n.authToken.requestStatus===1;)await Zr(100),n=await On(t.appConfig);const r=n.authToken;return r.requestStatus===0?en(t,e):r}function On(t){return st(t,e=>{if(!si(e))throw ie.create("not-registered");const n=e.authToken;return ml(n)?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e})}async function fl(t,e){try{const n=await ul(t,e),r=Object.assign(Object.assign({},e),{authToken:n});return await qe(t.appConfig,r),n}catch(n){if(qr(n)&&(n.customData.serverCode===401||n.customData.serverCode===404))await ri(t.appConfig);else{const r=Object.assign(Object.assign({},e),{authToken:{requestStatus:0}});await qe(t.appConfig,r)}throw n}}function si(t){return t!==void 0&&t.registrationStatus===2}function hl(t){return t.requestStatus===2&&!pl(t)}function pl(t){const e=Date.now();return e<t.creationTime||t.creationTime+t.expiresIn<e+Mo}function gl(t){const e={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},t),{authToken:e})}function ml(t){return t.requestStatus===1&&t.requestTime+Hr<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function yl(t){const e=t,{installationEntry:n,registrationPromise:r}=await Zt(e);return r?r.catch(console.error):en(e).catch(console.error),n.fid}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function vl(t,e=!1){const n=t;return await wl(n),(await en(n,e)).token}async function wl(t){const{registrationPromise:e}=await Zt(t);e&&await e}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function bl(t){if(!t||!t.options)throw vt("App Configuration");if(!t.name)throw vt("App Name");const e=["projectId","apiKey","appId"];for(const n of e)if(!t.options[n])throw vt(n);return{appName:t.name,projectId:t.options.projectId,apiKey:t.options.apiKey,appId:t.options.appId}}function vt(t){return ie.create("missing-app-config-values",{valueName:t})}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ai="installations",kl="installations-internal",Sl=t=>{const e=t.getProvider("app").getImmediate(),n=bl(e),r=je(e,"heartbeat");return{app:e,appConfig:n,heartbeatServiceProvider:r,_delete:()=>Promise.resolve()}},xl=t=>{const e=t.getProvider("app").getImmediate(),n=je(e,ai).getImmediate();return{getId:()=>yl(n),getToken:i=>vl(n,i)}};function Il(){Re(new Ae(ai,Sl,"PUBLIC")),Re(new Ae(kl,xl,"PRIVATE"))}Il();fe(Vr,Xt);fe(Vr,Xt,"esm2017");/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Je="analytics",Cl="firebase_id",_l="origin",Pl=60*1e3,El="https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig",tn="https://www.googletagmanager.com/gtag/js";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const T=new Hi("@firebase/analytics");/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Rl={"already-exists":"A Firebase Analytics instance with the appId {$id}  already exists. Only one Firebase Analytics instance can be created for each appId.","already-initialized":"initializeAnalytics() cannot be called again with different options than those it was initially called with. It can be called again with the same options to return the existing instance, or getAnalytics() can be used to get a reference to the already-initialized instance.","already-initialized-settings":"Firebase Analytics has already been initialized.settings() must be called before initializing any Analytics instanceor it will have no effect.","interop-component-reg-failed":"Firebase Analytics Interop Component failed to instantiate: {$reason}","invalid-analytics-context":"Firebase Analytics is not supported in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","indexeddb-unavailable":"IndexedDB unavailable or restricted in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","fetch-throttle":"The config fetch request timed out while in an exponential backoff state. Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.","config-fetch-failed":"Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}","no-api-key":'The "apiKey" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid API key.',"no-app-id":'The "appId" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid app ID.',"no-client-id":'The "client_id" field is empty.',"invalid-gtag-resource":"Trusted Types detected an invalid gtag resource: {$gtagURL}."},D=new lr("analytics","Analytics",Rl);/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Al(t){if(!t.startsWith(tn)){const e=D.create("invalid-gtag-resource",{gtagURL:t});return T.warn(e.message),""}return t}function oi(t){return Promise.all(t.map(e=>e.catch(n=>n)))}function Nl(t,e){let n;return window.trustedTypes&&(n=window.trustedTypes.createPolicy(t,e)),n}function Ol(t,e){const n=Nl("firebase-js-sdk-policy",{createScriptURL:Al}),r=document.createElement("script"),i=`${tn}?l=${t}&id=${e}`;r.src=n?n?.createScriptURL(i):i,r.async=!0,document.head.appendChild(r)}function Tl(t){let e=[];return Array.isArray(window[t])?e=window[t]:window[t]=e,e}async function Ll(t,e,n,r,i,s){const a=r[i];try{if(a)await e[a];else{const l=(await oi(n)).find(u=>u.measurementId===i);l&&await e[l.appId]}}catch(o){T.error(o)}t("config",i,s)}async function Ul(t,e,n,r,i){try{let s=[];if(i&&i.send_to){let a=i.send_to;Array.isArray(a)||(a=[a]);const o=await oi(n);for(const l of a){const u=o.find(f=>f.measurementId===l),c=u&&e[u.appId];if(c)s.push(c);else{s=[];break}}}s.length===0&&(s=Object.values(e)),await Promise.all(s),t("event",r,i||{})}catch(s){T.error(s)}}function Dl(t,e,n,r){async function i(s,...a){try{if(s==="event"){const[o,l]=a;await Ul(t,e,n,o,l)}else if(s==="config"){const[o,l]=a;await Ll(t,e,n,r,o,l)}else if(s==="consent"){const[o,l]=a;t("consent",o,l)}else if(s==="get"){const[o,l,u]=a;t("get",o,l,u)}else if(s==="set"){const[o]=a;t("set",o)}else t(s,...a)}catch(o){T.error(o)}}return i}function $l(t,e,n,r,i){let s=function(...a){window[r].push(arguments)};return window[i]&&typeof window[i]=="function"&&(s=window[i]),window[i]=Dl(s,t,e,n),{gtagCore:s,wrappedGtag:window[i]}}function jl(t){const e=window.document.getElementsByTagName("script");for(const n of Object.values(e))if(n.src&&n.src.includes(tn)&&n.src.includes(t))return n;return null}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Fl=30,zl=1e3;class Ml{constructor(e={},n=zl){this.throttleMetadata=e,this.intervalMillis=n}getThrottleMetadata(e){return this.throttleMetadata[e]}setThrottleMetadata(e,n){this.throttleMetadata[e]=n}deleteThrottleMetadata(e){delete this.throttleMetadata[e]}}const li=new Ml;function Bl(t){return new Headers({Accept:"application/json","x-goog-api-key":t})}async function Vl(t){var e;const{appId:n,apiKey:r}=t,i={method:"GET",headers:Bl(r)},s=El.replace("{app-id}",n),a=await fetch(s,i);if(a.status!==200&&a.status!==304){let o="";try{const l=await a.json();!((e=l.error)===null||e===void 0)&&e.message&&(o=l.error.message)}catch{}throw D.create("config-fetch-failed",{httpStatus:a.status,responseMessage:o})}return a.json()}async function Hl(t,e=li,n){const{appId:r,apiKey:i,measurementId:s}=t.options;if(!r)throw D.create("no-app-id");if(!i){if(s)return{measurementId:s,appId:r};throw D.create("no-api-key")}const a=e.getThrottleMetadata(r)||{backoffCount:0,throttleEndTimeMillis:Date.now()},o=new ql;return setTimeout(async()=>{o.abort()},Pl),ui({appId:r,apiKey:i,measurementId:s},a,o,e)}async function ui(t,{throttleEndTimeMillis:e,backoffCount:n},r,i=li){var s;const{appId:a,measurementId:o}=t;try{await Kl(r,e)}catch(l){if(o)return T.warn(`Timed out fetching this Firebase app's measurement ID from the server. Falling back to the measurement ID ${o} provided in the "measurementId" field in the local Firebase config. [${l?.message}]`),{appId:a,measurementId:o};throw l}try{const l=await Vl(t);return i.deleteThrottleMetadata(a),l}catch(l){const u=l;if(!Wl(u)){if(i.deleteThrottleMetadata(a),o)return T.warn(`Failed to fetch this Firebase app's measurement ID from the server. Falling back to the measurement ID ${o} provided in the "measurementId" field in the local Firebase config. [${u?.message}]`),{appId:a,measurementId:o};throw l}const c=Number((s=u?.customData)===null||s===void 0?void 0:s.httpStatus)===503?un(n,i.intervalMillis,Fl):un(n,i.intervalMillis),f={throttleEndTimeMillis:Date.now()+c,backoffCount:n+1};return i.setThrottleMetadata(a,f),T.debug(`Calling attemptFetch again in ${c} millis`),ui(t,f,r,i)}}function Kl(t,e){return new Promise((n,r)=>{const i=Math.max(e-Date.now(),0),s=setTimeout(n,i);t.addEventListener(()=>{clearTimeout(s),r(D.create("fetch-throttle",{throttleEndTimeMillis:e}))})})}function Wl(t){if(!(t instanceof Bt)||!t.customData)return!1;const e=Number(t.customData.httpStatus);return e===429||e===500||e===503||e===504}class ql{constructor(){this.listeners=[]}addEventListener(e){this.listeners.push(e)}abort(){this.listeners.forEach(e=>e())}}async function Jl(t,e,n,r,i){if(i&&i.global){t("event",n,r);return}else{const s=await e,a=Object.assign(Object.assign({},r),{send_to:s});t("event",n,a)}}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Gl(){if(Wi())try{await qi()}catch(t){return T.warn(D.create("indexeddb-unavailable",{errorInfo:t?.toString()}).message),!1}else return T.warn(D.create("indexeddb-unavailable",{errorInfo:"IndexedDB is not available in this environment."}).message),!1;return!0}async function Yl(t,e,n,r,i,s,a){var o;const l=Hl(t);l.then(p=>{n[p.measurementId]=p.appId,t.options.measurementId&&p.measurementId!==t.options.measurementId&&T.warn(`The measurement ID in the local Firebase config (${t.options.measurementId}) does not match the measurement ID fetched from the server (${p.measurementId}). To ensure analytics events are always sent to the correct Analytics property, update the measurement ID field in the local config or remove it from the local config.`)}).catch(p=>T.error(p)),e.push(l);const u=Gl().then(p=>{if(p)return r.getId()}),[c,f]=await Promise.all([l,u]);jl(s)||Ol(s,c.measurementId),i("js",new Date);const d=(o=a?.config)!==null&&o!==void 0?o:{};return d[_l]="firebase",d.update=!0,f!=null&&(d[Cl]=f),i("config",c.measurementId,d),c.measurementId}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Xl{constructor(e){this.app=e}_delete(){return delete Pe[this.app.options.appId],Promise.resolve()}}let Pe={},Tn=[];const Ln={};let wt="dataLayer",Ql="gtag",Un,ci,Dn=!1;function Zl(){const t=[];if(Ki()&&t.push("This is a browser extension environment."),Gi()||t.push("Cookies are not available."),t.length>0){const e=t.map((r,i)=>`(${i+1}) ${r}`).join(" "),n=D.create("invalid-analytics-context",{errorInfo:e});T.warn(n.message)}}function eu(t,e,n){Zl();const r=t.options.appId;if(!r)throw D.create("no-app-id");if(!t.options.apiKey)if(t.options.measurementId)T.warn(`The "apiKey" field is empty in the local Firebase config. This is needed to fetch the latest measurement ID for this Firebase app. Falling back to the measurement ID ${t.options.measurementId} provided in the "measurementId" field in the local Firebase config.`);else throw D.create("no-api-key");if(Pe[r]!=null)throw D.create("already-exists",{id:r});if(!Dn){Tl(wt);const{wrappedGtag:s,gtagCore:a}=$l(Pe,Tn,Ln,wt,Ql);ci=s,Un=a,Dn=!0}return Pe[r]=Yl(t,Tn,Ln,e,Un,wt,n),new Xl(t)}function tu(t=or()){t=me(t);const e=je(t,Je);return e.isInitialized()?e.getImmediate():nu(t)}function nu(t,e={}){const n=je(t,Je);if(n.isInitialized()){const i=n.getImmediate();if(Ji(e,n.getOptions()))return i;throw D.create("already-initialized")}return n.initialize({options:e})}function ru(t,e,n,r){t=me(t),Jl(ci,Pe[t.app.options.appId],e,n,r).catch(i=>T.error(i))}const $n="@firebase/analytics",jn="0.10.8";function iu(){Re(new Ae(Je,(e,{options:n})=>{const r=e.getProvider("app").getImmediate(),i=e.getProvider("installations-internal").getImmediate();return eu(r,i,n)},"PUBLIC")),Re(new Ae("analytics-internal",t,"PRIVATE")),fe($n,jn),fe($n,jn,"esm2017");function t(e){try{const n=e.getProvider(Je).getImmediate();return{logEvent:(r,i,s)=>ru(n,r,i,s)}}catch(n){throw D.create("interop-component-reg-failed",{reason:n})}}}iu();const su={apiKey:void 0,authDomain:void 0,projectId:void 0,storageBucket:void 0,messagingSenderId:void 0,appId:void 0,measurementId:void 0},at=Yi(su),V=Xi(at),Q=Qi(at),Kd=Do(at);typeof window<"u"&&tu(at);const au={},Fn=t=>{let e;const n=new Set,r=(c,f)=>{const d=typeof c=="function"?c(e):c;if(!Object.is(d,e)){const p=e;e=f??(typeof d!="object"||d===null)?d:Object.assign({},e,d),n.forEach(m=>m(e,p))}},i=()=>e,l={setState:r,getState:i,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{(au?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=e=t(r,i,l);return l},ou=t=>t?Fn(t):Fn;var di={exports:{}},fi={},hi={exports:{}},pi={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var he=w;function lu(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var uu=typeof Object.is=="function"?Object.is:lu,cu=he.useState,du=he.useEffect,fu=he.useLayoutEffect,hu=he.useDebugValue;function pu(t,e){var n=e(),r=cu({inst:{value:n,getSnapshot:e}}),i=r[0].inst,s=r[1];return fu(function(){i.value=n,i.getSnapshot=e,bt(i)&&s({inst:i})},[t,n,e]),du(function(){return bt(i)&&s({inst:i}),t(function(){bt(i)&&s({inst:i})})},[t]),hu(n),n}function bt(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!uu(t,n)}catch{return!0}}function gu(t,e){return e()}var mu=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?gu:pu;pi.useSyncExternalStore=he.useSyncExternalStore!==void 0?he.useSyncExternalStore:mu;hi.exports=pi;var yu=hi.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ot=w,vu=yu;function wu(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var bu=typeof Object.is=="function"?Object.is:wu,ku=vu.useSyncExternalStore,Su=ot.useRef,xu=ot.useEffect,Iu=ot.useMemo,Cu=ot.useDebugValue;fi.useSyncExternalStoreWithSelector=function(t,e,n,r,i){var s=Su(null);if(s.current===null){var a={hasValue:!1,value:null};s.current=a}else a=s.current;s=Iu(function(){function l(p){if(!u){if(u=!0,c=p,p=r(p),i!==void 0&&a.hasValue){var m=a.value;if(i(m,p))return f=m}return f=p}if(m=f,bu(c,p))return m;var g=r(p);return i!==void 0&&i(m,g)?(c=p,m):(c=p,f=g)}var u=!1,c,f,d=n===void 0?null:n;return[function(){return l(e())},d===null?void 0:function(){return l(d())}]},[e,n,r,i]);var o=ku(t,s[0],s[1]);return xu(function(){a.hasValue=!0,a.value=o},[o]),Cu(o),o};di.exports=fi;var _u=di.exports;const Pu=Fi(_u),gi={},{useDebugValue:Eu}=A,{useSyncExternalStoreWithSelector:Ru}=Pu;let zn=!1;const Au=t=>t;function Nu(t,e=Au,n){(gi?"production":void 0)!=="production"&&n&&!zn&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),zn=!0);const r=Ru(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,n);return Eu(r),r}const Ou=t=>{(gi?"production":void 0)!=="production"&&typeof t!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const e=typeof t=="function"?ou(t):t,n=(r,i)=>Nu(e,r,i);return Object.assign(n,e),n},Tu=t=>Ou,We={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},Tt=new Map,He=t=>{const e=Tt.get(t);return e?Object.fromEntries(Object.entries(e.stores).map(([n,r])=>[n,r.getState()])):{}},Lu=(t,e,n)=>{if(t===void 0)return{type:"untracked",connection:e.connect(n)};const r=Tt.get(n.name);if(r)return{type:"tracked",store:t,...r};const i={connection:e.connect(n),stores:{}};return Tt.set(n.name,i),{type:"tracked",store:t,...i}},Uu=(t,e={})=>(n,r,i)=>{const{enabled:s,anonymousActionType:a,store:o,...l}=e;let u;try{u=(s??(We?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!u)return(We?"production":void 0)!=="production"&&s&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),t(n,r,i);const{connection:c,...f}=Lu(o,u,l);let d=!0;i.setState=(g,h,v)=>{const y=n(g,h);if(!d)return y;const k=v===void 0?{type:a||"anonymous"}:typeof v=="string"?{type:v}:v;return o===void 0?(c?.send(k,r()),y):(c?.send({...k,type:`${o}/${k.type}`},{...He(l.name),[o]:i.getState()}),y)};const p=(...g)=>{const h=d;d=!1,n(...g),d=h},m=t(i.setState,r,i);if(f.type==="untracked"?c?.init(m):(f.stores[f.store]=i,c?.init(Object.fromEntries(Object.entries(f.stores).map(([g,h])=>[g,g===f.store?m:h.getState()])))),i.dispatchFromDevtools&&typeof i.dispatch=="function"){let g=!1;const h=i.dispatch;i.dispatch=(...v)=>{(We?"production":void 0)!=="production"&&v[0].type==="__setState"&&!g&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),g=!0),h(...v)}}return c.subscribe(g=>{var h;switch(g.type){case"ACTION":if(typeof g.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return kt(g.payload,v=>{if(v.type==="__setState"){if(o===void 0){p(v.state);return}Object.keys(v.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const y=v.state[o];if(y==null)return;JSON.stringify(i.getState())!==JSON.stringify(y)&&p(y);return}i.dispatchFromDevtools&&typeof i.dispatch=="function"&&i.dispatch(v)});case"DISPATCH":switch(g.payload.type){case"RESET":return p(m),o===void 0?c?.init(i.getState()):c?.init(He(l.name));case"COMMIT":if(o===void 0){c?.init(i.getState());return}return c?.init(He(l.name));case"ROLLBACK":return kt(g.state,v=>{if(o===void 0){p(v),c?.init(i.getState());return}p(v[o]),c?.init(He(l.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return kt(g.state,v=>{if(o===void 0){p(v);return}JSON.stringify(i.getState())!==JSON.stringify(v[o])&&p(v[o])});case"IMPORT_STATE":{const{nextLiftedState:v}=g.payload,y=(h=v.computedStates.slice(-1)[0])==null?void 0:h.state;if(!y)return;p(o===void 0?y:y[o]),c?.send(null,v);return}case"PAUSE_RECORDING":return d=!d}return}}),m},Du=Uu,kt=(t,e)=>{let n;try{n=JSON.parse(t)}catch(r){console.error("[zustand devtools middleware] Could not parse the received json",r)}n!==void 0&&e(n)};function $u(t,e){let n;try{n=t()}catch{return}return{getItem:i=>{var s;const a=l=>l===null?null:JSON.parse(l,void 0),o=(s=n.getItem(i))!=null?s:null;return o instanceof Promise?o.then(a):a(o)},setItem:(i,s)=>n.setItem(i,JSON.stringify(s,void 0)),removeItem:i=>n.removeItem(i)}}const Te=t=>e=>{try{const n=t(e);return n instanceof Promise?n:{then(r){return Te(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return Te(r)(n)}}}},ju=(t,e)=>(n,r,i)=>{let s={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:h=>h,version:0,merge:(h,v)=>({...v,...h}),...e},a=!1;const o=new Set,l=new Set;let u;try{u=s.getStorage()}catch{}if(!u)return t((...h)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...h)},r,i);const c=Te(s.serialize),f=()=>{const h=s.partialize({...r()});let v;const y=c({state:h,version:s.version}).then(k=>u.setItem(s.name,k)).catch(k=>{v=k});if(v)throw v;return y},d=i.setState;i.setState=(h,v)=>{d(h,v),f()};const p=t((...h)=>{n(...h),f()},r,i);let m;const g=()=>{var h;if(!u)return;a=!1,o.forEach(y=>y(r()));const v=((h=s.onRehydrateStorage)==null?void 0:h.call(s,r()))||void 0;return Te(u.getItem.bind(u))(s.name).then(y=>{if(y)return s.deserialize(y)}).then(y=>{if(y)if(typeof y.version=="number"&&y.version!==s.version){if(s.migrate)return s.migrate(y.state,y.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return y.state}).then(y=>{var k;return m=s.merge(y,(k=r())!=null?k:p),n(m,!0),f()}).then(()=>{v?.(m,void 0),a=!0,l.forEach(y=>y(m))}).catch(y=>{v?.(void 0,y)})};return i.persist={setOptions:h=>{s={...s,...h},h.getStorage&&(u=h.getStorage())},clearStorage:()=>{u?.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>g(),hasHydrated:()=>a,onHydrate:h=>(o.add(h),()=>{o.delete(h)}),onFinishHydration:h=>(l.add(h),()=>{l.delete(h)})},g(),m||p},Fu=(t,e)=>(n,r,i)=>{let s={storage:$u(()=>localStorage),partialize:g=>g,version:0,merge:(g,h)=>({...h,...g}),...e},a=!1;const o=new Set,l=new Set;let u=s.storage;if(!u)return t((...g)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...g)},r,i);const c=()=>{const g=s.partialize({...r()});return u.setItem(s.name,{state:g,version:s.version})},f=i.setState;i.setState=(g,h)=>{f(g,h),c()};const d=t((...g)=>{n(...g),c()},r,i);i.getInitialState=()=>d;let p;const m=()=>{var g,h;if(!u)return;a=!1,o.forEach(y=>{var k;return y((k=r())!=null?k:d)});const v=((h=s.onRehydrateStorage)==null?void 0:h.call(s,(g=r())!=null?g:d))||void 0;return Te(u.getItem.bind(u))(s.name).then(y=>{if(y)if(typeof y.version=="number"&&y.version!==s.version){if(s.migrate)return[!0,s.migrate(y.state,y.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,y.state];return[!1,void 0]}).then(y=>{var k;const[S,I]=y;if(p=s.merge(I,(k=r())!=null?k:d),n(p,!0),S)return c()}).then(()=>{v?.(p,void 0),p=r(),a=!0,l.forEach(y=>y(p))}).catch(y=>{v?.(void 0,y)})};return i.persist={setOptions:g=>{s={...s,...g},g.storage&&(u=g.storage)},clearStorage:()=>{u?.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>m(),hasHydrated:()=>a,onHydrate:g=>(o.add(g),()=>{o.delete(g)}),onFinishHydration:g=>(l.add(g),()=>{l.delete(g)})},s.skipHydration||m(),p||d},zu=(t,e)=>"getStorage"in e||"serialize"in e||"deserialize"in e?((We?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),ju(t,e)):Fu(t,e),Mu=zu;var mi=Symbol.for("immer-nothing"),Mn=Symbol.for("immer-draftable"),$=Symbol.for("immer-state");function F(t,...e){throw new Error(`[Immer] minified error nr: ${t}. Full error at: https://bit.ly/3cXEKWf`)}var pe=Object.getPrototypeOf;function ge(t){return!!t&&!!t[$]}function ae(t){return t?yi(t)||Array.isArray(t)||!!t[Mn]||!!t.constructor?.[Mn]||ut(t)||ct(t):!1}var Bu=Object.prototype.constructor.toString();function yi(t){if(!t||typeof t!="object")return!1;const e=pe(t);if(e===null)return!0;const n=Object.hasOwnProperty.call(e,"constructor")&&e.constructor;return n===Object?!0:typeof n=="function"&&Function.toString.call(n)===Bu}function Ge(t,e){lt(t)===0?Reflect.ownKeys(t).forEach(n=>{e(n,t[n],t)}):t.forEach((n,r)=>e(r,n,t))}function lt(t){const e=t[$];return e?e.type_:Array.isArray(t)?1:ut(t)?2:ct(t)?3:0}function Lt(t,e){return lt(t)===2?t.has(e):Object.prototype.hasOwnProperty.call(t,e)}function vi(t,e,n){const r=lt(t);r===2?t.set(e,n):r===3?t.add(n):t[e]=n}function Vu(t,e){return t===e?t!==0||1/t===1/e:t!==t&&e!==e}function ut(t){return t instanceof Map}function ct(t){return t instanceof Set}function Z(t){return t.copy_||t.base_}function Ut(t,e){if(ut(t))return new Map(t);if(ct(t))return new Set(t);if(Array.isArray(t))return Array.prototype.slice.call(t);const n=yi(t);if(e===!0||e==="class_only"&&!n){const r=Object.getOwnPropertyDescriptors(t);delete r[$];let i=Reflect.ownKeys(r);for(let s=0;s<i.length;s++){const a=i[s],o=r[a];o.writable===!1&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(r[a]={configurable:!0,writable:!0,enumerable:o.enumerable,value:t[a]})}return Object.create(pe(t),r)}else{const r=pe(t);if(r!==null&&n)return{...t};const i=Object.create(r);return Object.assign(i,t)}}function nn(t,e=!1){return dt(t)||ge(t)||!ae(t)||(lt(t)>1&&(t.set=t.add=t.clear=t.delete=Hu),Object.freeze(t),e&&Object.entries(t).forEach(([n,r])=>nn(r,!0))),t}function Hu(){F(2)}function dt(t){return Object.isFrozen(t)}var Ku={};function oe(t){const e=Ku[t];return e||F(0,t),e}var Le;function wi(){return Le}function Wu(t,e){return{drafts_:[],parent_:t,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Bn(t,e){e&&(oe("Patches"),t.patches_=[],t.inversePatches_=[],t.patchListener_=e)}function Dt(t){$t(t),t.drafts_.forEach(qu),t.drafts_=null}function $t(t){t===Le&&(Le=t.parent_)}function Vn(t){return Le=Wu(Le,t)}function qu(t){const e=t[$];e.type_===0||e.type_===1?e.revoke_():e.revoked_=!0}function Hn(t,e){e.unfinalizedDrafts_=e.drafts_.length;const n=e.drafts_[0];return t!==void 0&&t!==n?(n[$].modified_&&(Dt(e),F(4)),ae(t)&&(t=Ye(e,t),e.parent_||Xe(e,t)),e.patches_&&oe("Patches").generateReplacementPatches_(n[$].base_,t,e.patches_,e.inversePatches_)):t=Ye(e,n,[]),Dt(e),e.patches_&&e.patchListener_(e.patches_,e.inversePatches_),t!==mi?t:void 0}function Ye(t,e,n){if(dt(e))return e;const r=e[$];if(!r)return Ge(e,(i,s)=>Kn(t,r,e,i,s,n)),e;if(r.scope_!==t)return e;if(!r.modified_)return Xe(t,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const i=r.copy_;let s=i,a=!1;r.type_===3&&(s=new Set(i),i.clear(),a=!0),Ge(s,(o,l)=>Kn(t,r,i,o,l,n,a)),Xe(t,i,!1),n&&t.patches_&&oe("Patches").generatePatches_(r,n,t.patches_,t.inversePatches_)}return r.copy_}function Kn(t,e,n,r,i,s,a){if(ge(i)){const o=s&&e&&e.type_!==3&&!Lt(e.assigned_,r)?s.concat(r):void 0,l=Ye(t,i,o);if(vi(n,r,l),ge(l))t.canAutoFreeze_=!1;else return}else a&&n.add(i);if(ae(i)&&!dt(i)){if(!t.immer_.autoFreeze_&&t.unfinalizedDrafts_<1)return;Ye(t,i),(!e||!e.scope_.parent_)&&typeof r!="symbol"&&Object.prototype.propertyIsEnumerable.call(n,r)&&Xe(t,i)}}function Xe(t,e,n=!1){!t.parent_&&t.immer_.autoFreeze_&&t.canAutoFreeze_&&nn(e,n)}function Ju(t,e){const n=Array.isArray(t),r={type_:n?1:0,scope_:e?e.scope_:wi(),modified_:!1,finalized_:!1,assigned_:{},parent_:e,base_:t,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=r,s=rn;n&&(i=[r],s=Ue);const{revoke:a,proxy:o}=Proxy.revocable(i,s);return r.draft_=o,r.revoke_=a,o}var rn={get(t,e){if(e===$)return t;const n=Z(t);if(!Lt(n,e))return Gu(t,n,e);const r=n[e];return t.finalized_||!ae(r)?r:r===St(t.base_,e)?(xt(t),t.copy_[e]=Ft(r,t)):r},has(t,e){return e in Z(t)},ownKeys(t){return Reflect.ownKeys(Z(t))},set(t,e,n){const r=bi(Z(t),e);if(r?.set)return r.set.call(t.draft_,n),!0;if(!t.modified_){const i=St(Z(t),e),s=i?.[$];if(s&&s.base_===n)return t.copy_[e]=n,t.assigned_[e]=!1,!0;if(Vu(n,i)&&(n!==void 0||Lt(t.base_,e)))return!0;xt(t),jt(t)}return t.copy_[e]===n&&(n!==void 0||e in t.copy_)||Number.isNaN(n)&&Number.isNaN(t.copy_[e])||(t.copy_[e]=n,t.assigned_[e]=!0),!0},deleteProperty(t,e){return St(t.base_,e)!==void 0||e in t.base_?(t.assigned_[e]=!1,xt(t),jt(t)):delete t.assigned_[e],t.copy_&&delete t.copy_[e],!0},getOwnPropertyDescriptor(t,e){const n=Z(t),r=Reflect.getOwnPropertyDescriptor(n,e);return r&&{writable:!0,configurable:t.type_!==1||e!=="length",enumerable:r.enumerable,value:n[e]}},defineProperty(){F(11)},getPrototypeOf(t){return pe(t.base_)},setPrototypeOf(){F(12)}},Ue={};Ge(rn,(t,e)=>{Ue[t]=function(){return arguments[0]=arguments[0][0],e.apply(this,arguments)}});Ue.deleteProperty=function(t,e){return Ue.set.call(this,t,e,void 0)};Ue.set=function(t,e,n){return rn.set.call(this,t[0],e,n,t[0])};function St(t,e){const n=t[$];return(n?Z(n):t)[e]}function Gu(t,e,n){const r=bi(e,n);return r?"value"in r?r.value:r.get?.call(t.draft_):void 0}function bi(t,e){if(!(e in t))return;let n=pe(t);for(;n;){const r=Object.getOwnPropertyDescriptor(n,e);if(r)return r;n=pe(n)}}function jt(t){t.modified_||(t.modified_=!0,t.parent_&&jt(t.parent_))}function xt(t){t.copy_||(t.copy_=Ut(t.base_,t.scope_.immer_.useStrictShallowCopy_))}var Yu=class{constructor(t){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,n,r)=>{if(typeof e=="function"&&typeof n!="function"){const s=n;n=e;const a=this;return function(l=s,...u){return a.produce(l,c=>n.call(this,c,...u))}}typeof n!="function"&&F(6),r!==void 0&&typeof r!="function"&&F(7);let i;if(ae(e)){const s=Vn(this),a=Ft(e,void 0);let o=!0;try{i=n(a),o=!1}finally{o?Dt(s):$t(s)}return Bn(s,r),Hn(i,s)}else if(!e||typeof e!="object"){if(i=n(e),i===void 0&&(i=e),i===mi&&(i=void 0),this.autoFreeze_&&nn(i,!0),r){const s=[],a=[];oe("Patches").generateReplacementPatches_(e,i,s,a),r(s,a)}return i}else F(1,e)},this.produceWithPatches=(e,n)=>{if(typeof e=="function")return(a,...o)=>this.produceWithPatches(a,l=>e(l,...o));let r,i;return[this.produce(e,n,(a,o)=>{r=a,i=o}),r,i]},typeof t?.autoFreeze=="boolean"&&this.setAutoFreeze(t.autoFreeze),typeof t?.useStrictShallowCopy=="boolean"&&this.setUseStrictShallowCopy(t.useStrictShallowCopy)}createDraft(t){ae(t)||F(8),ge(t)&&(t=Xu(t));const e=Vn(this),n=Ft(t,void 0);return n[$].isManual_=!0,$t(e),n}finishDraft(t,e){const n=t&&t[$];(!n||!n.isManual_)&&F(9);const{scope_:r}=n;return Bn(r,e),Hn(void 0,r)}setAutoFreeze(t){this.autoFreeze_=t}setUseStrictShallowCopy(t){this.useStrictShallowCopy_=t}applyPatches(t,e){let n;for(n=e.length-1;n>=0;n--){const i=e[n];if(i.path.length===0&&i.op==="replace"){t=i.value;break}}n>-1&&(e=e.slice(n+1));const r=oe("Patches").applyPatches_;return ge(t)?r(t,e):this.produce(t,i=>r(i,e))}};function Ft(t,e){const n=ut(t)?oe("MapSet").proxyMap_(t,e):ct(t)?oe("MapSet").proxySet_(t,e):Ju(t,e);return(e?e.scope_:wi()).drafts_.push(n),n}function Xu(t){return ge(t)||F(10,t),ki(t)}function ki(t){if(!ae(t)||dt(t))return t;const e=t[$];let n;if(e){if(!e.modified_)return e.base_;e.finalized_=!0,n=Ut(t,e.scope_.immer_.useStrictShallowCopy_)}else n=Ut(t,!0);return Ge(n,(r,i)=>{vi(n,r,ki(i))}),e&&(e.finalized_=!1),n}var j=new Yu,Qu=j.produce;j.produceWithPatches.bind(j);j.setAutoFreeze.bind(j);j.setUseStrictShallowCopy.bind(j);j.applyPatches.bind(j);j.createDraft.bind(j);j.finishDraft.bind(j);const Zu=t=>(e,n,r)=>(r.setState=(i,s,...a)=>{const o=typeof i=="function"?Qu(i):i;return e(o,s,...a)},t(r.setState,n,r)),ec=Zu;class tc{phoneConfirmationResult=null;recaptchaVerifier=null;mapFirebaseUser(e){return{uid:e.uid,email:e.email,displayName:e.displayName,photoURL:e.photoURL,emailVerified:e.emailVerified,phoneNumber:e.phoneNumber}}async createUserWithEmailAndPassword(e){try{const{user:n}=await Zi(V,e.email,e.password);await cn(n,{displayName:e.name}),await es(n);const r={uid:n.uid,email:e.email,displayName:e.name,culturalIdentities:e.culturalIdentities||[],culturalPreferences:{openToDiversity:!0,preferredLanguage:e.preferredLanguage,culturalExchangeInterest:!0},privacy:{profileVisibility:"communities",culturalIdentityVisible:!1,locationSharing:!1},createdAt:new Date,lastActive:new Date};return await ht(X(Q,"users",n.uid),r),this.mapFirebaseUser(n)}catch(n){throw console.error("Registration error:",n),this.handleAuthError(n)}}async signInWithEmailAndPassword(e,n){try{const{user:r}=await ts(V,e,n);return await this.updateLastActive(r.uid),this.mapFirebaseUser(r)}catch(r){throw console.error("Login error:",r),this.handleAuthError(r)}}async signInWithProvider(e){try{let n;if(e==="google")n=new ns,n.addScope("profile"),n.addScope("email");else if(e==="facebook")n=new rs,n.addScope("email");else throw new Error("Unsupported provider");const{user:r}=await is(V,n);if((await dn(X(Q,"users",r.uid))).exists())await this.updateLastActive(r.uid);else{const s={uid:r.uid,email:r.email||"",displayName:r.displayName||"",culturalIdentities:[],culturalPreferences:{openToDiversity:!0,preferredLanguage:"en",culturalExchangeInterest:!0},privacy:{profileVisibility:"communities",culturalIdentityVisible:!1,locationSharing:!1},createdAt:new Date,lastActive:new Date};await ht(X(Q,"users",r.uid),s)}return this.mapFirebaseUser(r)}catch(n){throw console.error("Provider login error:",n),this.handleAuthError(n)}}async signInWithPhoneNumber(e){try{this.recaptchaVerifier||(this.recaptchaVerifier=new ss(V,"recaptcha-container",{size:"invisible",callback:()=>{}})),this.phoneConfirmationResult=await as(V,e,this.recaptchaVerifier)}catch(n){throw console.error("Phone authentication error:",n),this.handleAuthError(n)}}async verifyPhoneCode(e){try{if(!this.phoneConfirmationResult)throw new Error("No phone confirmation result available");const{user:n}=await this.phoneConfirmationResult.confirm(e);if((await dn(X(Q,"users",n.uid))).exists())await this.updateLastActive(n.uid);else{const i={uid:n.uid,email:n.email||"",displayName:n.displayName||n.phoneNumber||"",culturalIdentities:[],culturalPreferences:{openToDiversity:!0,preferredLanguage:"en",culturalExchangeInterest:!0},privacy:{profileVisibility:"communities",culturalIdentityVisible:!1,locationSharing:!1},createdAt:new Date,lastActive:new Date};await ht(X(Q,"users",n.uid),i)}return this.mapFirebaseUser(n)}catch(n){throw console.error("Phone verification error:",n),this.handleAuthError(n)}}async sendPasswordResetEmail(e){try{await os(V,e)}catch(n){throw console.error("Password reset error:",n),this.handleAuthError(n)}}async updateUserProfile(e){try{const n=V.currentUser;if(!n)throw new Error("No authenticated user");e.displayName!==void 0&&await cn(n,{displayName:e.displayName});const r={};return e.displayName!==void 0&&(r.displayName=e.displayName),await fn(X(Q,"users",n.uid),r),this.mapFirebaseUser(n)}catch(n){throw console.error("Profile update error:",n),this.handleAuthError(n)}}async signOut(){try{await ls(V),this.phoneConfirmationResult=null,this.recaptchaVerifier=null}catch(e){throw console.error("Sign out error:",e),this.handleAuthError(e)}}async updateLastActive(e){try{await fn(X(Q,"users",e),{lastActive:new Date})}catch(n){console.error("Error updating last active:",n)}}handleAuthError(e){let n="An authentication error occurred";if(e.code)switch(e.code){case"auth/user-not-found":n="No account found with this email address";break;case"auth/wrong-password":n="Incorrect password";break;case"auth/email-already-in-use":n="An account with this email already exists";break;case"auth/weak-password":n="Password should be at least 6 characters";break;case"auth/invalid-email":n="Invalid email address";break;case"auth/too-many-requests":n="Too many failed attempts. Please try again later";break;case"auth/network-request-failed":n="Network error. Please check your connection";break;default:n=e.message||n}return new Error(n)}}const W=new tc,nc=(t,e)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,login:async(n,r)=>{t(i=>{i.isLoading=!0,i.error=null});try{const i=await W.signInWithEmailAndPassword(n,r);t(s=>{s.user=i,s.isAuthenticated=!0,s.isLoading=!1})}catch(i){throw t(s=>{s.error=i instanceof Error?i.message:"Login failed",s.isLoading=!1}),i}},loginWithProvider:async n=>{t(r=>{r.isLoading=!0,r.error=null});try{const r=await W.signInWithProvider(n);t(i=>{i.user=r,i.isAuthenticated=!0,i.isLoading=!1})}catch(r){throw t(i=>{i.error=r instanceof Error?r.message:"Provider login failed",i.isLoading=!1}),r}},loginWithPhone:async n=>{t(r=>{r.isLoading=!0,r.error=null});try{await W.signInWithPhoneNumber(n),t(r=>{r.isLoading=!1})}catch(r){throw t(i=>{i.error=r instanceof Error?r.message:"Phone login failed",i.isLoading=!1}),r}},verifyPhoneCode:async n=>{t(r=>{r.isLoading=!0,r.error=null});try{const r=await W.verifyPhoneCode(n);t(i=>{i.user=r,i.isAuthenticated=!0,i.isLoading=!1})}catch(r){throw t(i=>{i.error=r instanceof Error?r.message:"Phone verification failed",i.isLoading=!1}),r}},register:async n=>{t(r=>{r.isLoading=!0,r.error=null});try{const r=await W.createUserWithEmailAndPassword(n);t(i=>{i.user=r,i.isAuthenticated=!0,i.isLoading=!1})}catch(r){throw t(i=>{i.error=r instanceof Error?r.message:"Registration failed",i.isLoading=!1}),r}},logout:async()=>{t(n=>{n.isLoading=!0,n.error=null});try{await W.signOut(),t(n=>{n.user=null,n.isAuthenticated=!1,n.isLoading=!1})}catch(n){throw t(r=>{r.error=n instanceof Error?n.message:"Logout failed",r.isLoading=!1}),n}},resetPassword:async n=>{t(r=>{r.isLoading=!0,r.error=null});try{await W.sendPasswordResetEmail(n),t(r=>{r.isLoading=!1})}catch(r){throw t(i=>{i.error=r instanceof Error?r.message:"Password reset failed",i.isLoading=!1}),r}},updateProfile:async n=>{t(r=>{r.isLoading=!0,r.error=null});try{const r=await W.updateUserProfile(n);t(i=>{i.user=r,i.isLoading=!1})}catch(r){throw t(i=>{i.error=r instanceof Error?r.message:"Profile update failed",i.isLoading=!1}),r}},setUser:n=>{t(r=>{r.user=n,r.isAuthenticated=!!n})},setLoading:n=>{t(r=>{r.isLoading=n})},setError:n=>{t(r=>{r.error=n})},clearError:()=>{t(n=>{n.error=null})}}),rc=(t,e)=>({userCulturalIdentities:[],selectedCulture:null,culturalPreferences:{openToDiversity:!0,preferredLanguage:"en",culturalExchangeInterest:!0,respectfulInteractionMode:!0},crossCulturalInteractions:{totalInteractions:0,diversityScore:0,bridgeConnections:0,culturalLearningProgress:{}},culturalContent:{bookmarkedContent:[],contributedContent:[],moderationQueue:[]},setCulturalIdentities:n=>{t(r=>{r.userCulturalIdentities=n})},addCulturalIdentity:n=>{t(r=>{r.userCulturalIdentities.includes(n)||r.userCulturalIdentities.push(n)})},removeCulturalIdentity:n=>{t(r=>{r.userCulturalIdentities=r.userCulturalIdentities.filter(i=>i!==n)})},setSelectedCulture:n=>{t(r=>{r.selectedCulture=n})},updateCulturalPreferences:n=>{t(r=>{r.culturalPreferences={...r.culturalPreferences,...n}})},trackCrossCulturalInteraction:n=>{t(r=>{r.crossCulturalInteractions.totalInteractions+=1;const i=n.sourceCulture!==n.targetCulture?5:1,s=Math.min(100,r.crossCulturalInteractions.diversityScore+i);if(r.crossCulturalInteractions.diversityScore=s,n.targetCulture&&n.sourceCulture!==n.targetCulture){const a=r.crossCulturalInteractions.culturalLearningProgress[n.targetCulture]||0;r.crossCulturalInteractions.culturalLearningProgress[n.targetCulture]=Math.min(100,a+2)}})},addCulturalBookmark:n=>{t(r=>{r.culturalContent.bookmarkedContent.includes(n)||r.culturalContent.bookmarkedContent.push(n)})},removeCulturalBookmark:n=>{t(r=>{r.culturalContent.bookmarkedContent=r.culturalContent.bookmarkedContent.filter(i=>i!==n)})},addContributedContent:n=>{t(r=>{r.culturalContent.contributedContent.includes(n)||r.culturalContent.contributedContent.push(n)})},updateDiversityScore:n=>{t(r=>{r.crossCulturalInteractions.diversityScore=Math.max(0,Math.min(100,n))})},incrementBridgeConnections:()=>{t(n=>{n.crossCulturalInteractions.bridgeConnections+=1})},updateCulturalLearningProgress:(n,r)=>{t(i=>{i.crossCulturalInteractions.culturalLearningProgress[n]=Math.max(0,Math.min(100,r))})}}),ic=(t,e)=>({language:"en",theme:"light",notifications:[],modals:[],isOffline:!1,networkQuality:"fast",setLanguage:n=>{t(r=>{r.language=n}),G(async()=>{const{default:r}=await Promise.resolve().then(()=>jd);return{default:r}},void 0).then(({default:r})=>{r.changeLanguage(n)})},setTheme:n=>{t(i=>{i.theme=n});const r=document.documentElement;n==="dark"?r.classList.add("dark"):n==="light"?r.classList.remove("dark"):window.matchMedia("(prefers-color-scheme: dark)").matches?r.classList.add("dark"):r.classList.remove("dark")},showNotification:n=>{const r=`notification-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,i={id:r,duration:5e3,...n};t(s=>{s.notifications.push(i)}),i.duration&&i.duration>0&&setTimeout(()=>{e().hideNotification(r)},i.duration)},hideNotification:n=>{t(r=>{r.notifications=r.notifications.filter(i=>i.id!==n)})},clearAllNotifications:()=>{t(n=>{n.notifications=[]})},openModal:n=>{const r={...n,isOpen:!0};t(i=>{i.modals=i.modals.filter(s=>s.id!==n.id),i.modals.push(r)})},closeModal:n=>{t(r=>{const i=r.modals.find(s=>s.id===n);i&&(i.isOpen=!1)}),setTimeout(()=>{t(r=>{r.modals=r.modals.filter(i=>i.id!==n)})},300)},closeAllModals:()=>{t(n=>{n.modals.forEach(r=>{r.isOpen=!1})}),setTimeout(()=>{t(n=>{n.modals=[]})},300)},setNetworkStatus:(n,r)=>{t(i=>{i.isOffline=n,i.networkQuality=r})}}),Si=Tu()(Du(Mu(ec((...t)=>({...nc(...t),...rc(...t),...ic(...t)})),{name:"ubuntu-connect-store",partialize:t=>({auth:{user:t.user,isAuthenticated:t.isAuthenticated},cultural:{userCulturalIdentities:t.userCulturalIdentities,culturalPreferences:t.culturalPreferences,selectedCulture:t.selectedCulture},ui:{language:t.language,theme:t.theme}}),skipHydration:!1}),{name:"ubuntu-connect-store",enabled:!1})),sc=()=>Si(t=>({user:t.user,isAuthenticated:t.isAuthenticated,isLoading:t.isLoading,error:t.error,login:t.login,logout:t.logout,register:t.register,updateProfile:t.updateProfile})),Wd=()=>Si(t=>({userCulturalIdentities:t.userCulturalIdentities,selectedCulture:t.selectedCulture,culturalPreferences:t.culturalPreferences,crossCulturalInteractions:t.crossCulturalInteractions,culturalContent:t.culturalContent,setCulturalIdentities:t.setCulturalIdentities,updateCulturalPreferences:t.updateCulturalPreferences,trackCrossCulturalInteraction:t.trackCrossCulturalInteraction,addCulturalBookmark:t.addCulturalBookmark})),xi=()=>{const t=sc(),e=n=>({uid:n.uid,email:n.email,displayName:n.displayName,photoURL:n.photoURL,emailVerified:n.emailVerified,phoneNumber:n.phoneNumber});return w.useEffect(()=>{const n=us(V,r=>{if(r){const i=e(r);t.setUser(i)}else t.setUser(null);t.setLoading(!1)});return()=>n()},[t]),{...t,isEmailVerified:t.user?.emailVerified??!1,hasPhoneNumber:!!t.user?.phoneNumber,userDisplayName:t.user?.displayName||t.user?.email||"User"}};function Ii(t){var e,n,r="";if(typeof t=="string"||typeof t=="number")r+=t;else if(typeof t=="object")if(Array.isArray(t)){var i=t.length;for(e=0;e<i;e++)t[e]&&(n=Ii(t[e]))&&(r&&(r+=" "),r+=n)}else for(n in t)t[n]&&(r&&(r+=" "),r+=n);return r}function H(){for(var t,e,n=0,r="",i=arguments.length;n<i;n++)(t=arguments[n])&&(e=Ii(t))&&(r&&(r+=" "),r+=e);return r}const sn=({size:t="md",className:e,message:n="Loading..."})=>{const r={sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"};return b.jsxs("div",{className:H("flex flex-col items-center justify-center p-8",e),children:[b.jsx("div",{className:H("spinner border-2 border-gray-200 border-t-cultural-500 rounded-full animate-spin",r[t])}),n&&b.jsx("p",{className:"mt-4 text-sm text-gray-600 animate-pulse",children:n})]})},zt=A.forwardRef(({className:t,variant:e="primary",size:n="md",isLoading:r=!1,leftIcon:i,rightIcon:s,fullWidth:a=!1,children:o,disabled:l,...u},c)=>{const f=["inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed","touch-target"],d={primary:["bg-cultural-500 hover:bg-cultural-600 text-white","focus:ring-cultural-500","shadow-sm hover:shadow-md"],secondary:["bg-ubuntu-500 hover:bg-ubuntu-600 text-white","focus:ring-ubuntu-500","shadow-sm hover:shadow-md"],outline:["border-2 border-cultural-500 text-cultural-500","hover:bg-cultural-500 hover:text-white","focus:ring-cultural-500"],ghost:["text-gray-700 hover:bg-gray-100","focus:ring-gray-500"],cultural:["bg-gradient-to-r from-cultural-500 to-ubuntu-500","hover:from-cultural-600 hover:to-ubuntu-600","text-white shadow-md hover:shadow-lg","focus:ring-cultural-500"]},p={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"},m=H(f,d[e],p[n],a&&"w-full",t);return b.jsx("button",{ref:c,className:m,disabled:l||r,...u,children:r?b.jsxs(b.Fragment,{children:[b.jsx("div",{className:"spinner w-4 h-4 mr-2"}),"Loading..."]}):b.jsxs(b.Fragment,{children:[i&&b.jsx("span",{className:"mr-2",children:i}),o,s&&b.jsx("span",{className:"ml-2",children:s})]})})});zt.displayName="Button";const Ci=A.forwardRef(({className:t,variant:e="default",padding:n="md",hover:r=!1,children:i,...s},a)=>{const o=["rounded-lg border transition-all duration-200"],l={default:["bg-white border-gray-200",r&&"hover:shadow-md"],cultural:["bg-gradient-to-br from-cultural-50 to-ubuntu-50","border-cultural-200",r&&"hover:shadow-lg hover:from-cultural-100 hover:to-ubuntu-100"],elevated:["bg-white border-gray-200 shadow-md",r&&"hover:shadow-lg"]},u={none:"",sm:"p-3",md:"p-4",lg:"p-6"},c=H(o,l[e],u[n],t);return b.jsx("div",{ref:a,className:c,...s,children:i})});Ci.displayName="Card";const _i=A.forwardRef(({className:t,...e},n)=>b.jsx("div",{ref:n,className:H("flex flex-col space-y-1.5 pb-4",t),...e}));_i.displayName="CardHeader";const Pi=A.forwardRef(({className:t,...e},n)=>b.jsx("h3",{ref:n,className:H("text-lg font-semibold leading-none tracking-tight",t),...e}));Pi.displayName="CardTitle";const ac=A.forwardRef(({className:t,...e},n)=>b.jsx("p",{ref:n,className:H("text-sm text-gray-600",t),...e}));ac.displayName="CardDescription";const Ei=A.forwardRef(({className:t,...e},n)=>b.jsx("div",{ref:n,className:H("pt-0",t),...e}));Ei.displayName="CardContent";const oc=A.forwardRef(({className:t,...e},n)=>b.jsx("div",{ref:n,className:H("flex items-center pt-4",t),...e}));oc.displayName="CardFooter";class lc extends w.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,n){console.error("Error caught by boundary:",e,n),this.setState({error:e,errorInfo:n})}handleReload=()=>{window.location.reload()};handleReset=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})};render(){return this.state.hasError?b.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:b.jsxs(Ci,{className:"w-full max-w-md",variant:"elevated",children:[b.jsx(_i,{children:b.jsx(Pi,{className:"text-center text-red-600",children:"Oops! Something went wrong"})}),b.jsxs(Ei,{className:"space-y-4",children:[b.jsx("p",{className:"text-gray-600 text-center",children:"We're sorry, but something unexpected happened. Please try refreshing the page."}),!1,b.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[b.jsx(zt,{variant:"outline",onClick:this.handleReset,fullWidth:!0,children:"Try Again"}),b.jsx(zt,{variant:"primary",onClick:this.handleReload,fullWidth:!0,children:"Reload Page"})]})]})]})}):this.props.children}}const uc=A.lazy(()=>G(()=>import("./AuthPage-Chhb6i1V.js"),__vite__mapDeps([0,1,2]))),cc=A.lazy(()=>G(()=>import("./HomePage-BlDCyb5y.js"),__vite__mapDeps([3,1,2]))),dc=A.lazy(()=>G(()=>import("./CulturalOnboardingPage-Cu7AFwSZ.js"),__vite__mapDeps([4,1,2]))),fc=A.lazy(()=>G(()=>import("./CulturalKnowledgePage-BnYvObkg.js"),__vite__mapDeps([5,1,2,6]))),hc=A.lazy(()=>G(()=>import("./KnowledgeExchangePage-C23voltd.js"),__vite__mapDeps([7,1,2,6]))),pc=A.lazy(()=>G(()=>import("./CrossCulturalCollaborationPage-CeaGM6MI.js"),__vite__mapDeps([8,1,2]))),gc=A.lazy(()=>G(()=>import("./AchievementShowcasePage-CmfOiW9q.js"),__vite__mapDeps([9,1,2]))),ue=({children:t})=>{const{isAuthenticated:e,isLoading:n}=xi();return n?b.jsx(sn,{}):e?b.jsx(b.Fragment,{children:t}):b.jsx(Kt,{to:"/auth",replace:!0})},mc=({children:t})=>{const{isAuthenticated:e,isLoading:n}=xi();return n?b.jsx(sn,{}):e?b.jsx(Kt,{to:"/",replace:!0}):b.jsx(b.Fragment,{children:t})};function yc(){const{i18n:t}=Pa();return A.useEffect(()=>{document.documentElement.lang=t.language},[t.language]),b.jsx(lc,{children:b.jsx(da,{children:b.jsx("div",{className:"min-h-screen bg-gray-50",children:b.jsx(w.Suspense,{fallback:b.jsx(sn,{}),children:b.jsxs(la,{children:[b.jsx(B,{path:"/auth",element:b.jsx(mc,{children:b.jsx(uc,{})})}),b.jsx(B,{path:"/",element:b.jsx(ue,{children:b.jsx(cc,{})})}),b.jsx(B,{path:"/onboarding/cultural",element:b.jsx(ue,{children:b.jsx(dc,{})})}),b.jsx(B,{path:"/cultural-knowledge",element:b.jsx(ue,{children:b.jsx(fc,{})})}),b.jsx(B,{path:"/knowledge-exchange",element:b.jsx(ue,{children:b.jsx(hc,{})})}),b.jsx(B,{path:"/cross-cultural-collaboration",element:b.jsx(ue,{children:b.jsx(pc,{userId:"current-user-id"})})}),b.jsx(B,{path:"/achievement-showcase",element:b.jsx(ue,{children:b.jsx(gc,{userId:"current-user-id",userRole:"community_member"})})}),b.jsx(B,{path:"*",element:b.jsx(Kt,{to:"/",replace:!0})})]})})})})})}const x=t=>typeof t=="string",xe=()=>{let t,e;const n=new Promise((r,i)=>{t=r,e=i});return n.resolve=t,n.reject=e,n},Wn=t=>t==null?"":""+t,vc=(t,e,n)=>{t.forEach(r=>{e[r]&&(n[r]=e[r])})},wc=/###/g,qn=t=>t&&t.indexOf("###")>-1?t.replace(wc,"."):t,Jn=t=>!t||x(t),Ee=(t,e,n)=>{const r=x(e)?e.split("."):e;let i=0;for(;i<r.length-1;){if(Jn(t))return{};const s=qn(r[i]);!t[s]&&n&&(t[s]=new n),Object.prototype.hasOwnProperty.call(t,s)?t=t[s]:t={},++i}return Jn(t)?{}:{obj:t,k:qn(r[i])}},Gn=(t,e,n)=>{const{obj:r,k:i}=Ee(t,e,Object);if(r!==void 0||e.length===1){r[i]=n;return}let s=e[e.length-1],a=e.slice(0,e.length-1),o=Ee(t,a,Object);for(;o.obj===void 0&&a.length;)s=`${a[a.length-1]}.${s}`,a=a.slice(0,a.length-1),o=Ee(t,a,Object),o&&o.obj&&typeof o.obj[`${o.k}.${s}`]<"u"&&(o.obj=void 0);o.obj[`${o.k}.${s}`]=n},bc=(t,e,n,r)=>{const{obj:i,k:s}=Ee(t,e,Object);i[s]=i[s]||[],i[s].push(n)},Qe=(t,e)=>{const{obj:n,k:r}=Ee(t,e);if(n)return n[r]},kc=(t,e,n)=>{const r=Qe(t,n);return r!==void 0?r:Qe(e,n)},Ri=(t,e,n)=>{for(const r in e)r!=="__proto__"&&r!=="constructor"&&(r in t?x(t[r])||t[r]instanceof String||x(e[r])||e[r]instanceof String?n&&(t[r]=e[r]):Ri(t[r],e[r],n):t[r]=e[r]);return t},ce=t=>t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Sc={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const xc=t=>x(t)?t.replace(/[&<>"'\/]/g,e=>Sc[e]):t;class Ic{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const n=this.regExpMap.get(e);if(n!==void 0)return n;const r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}const Cc=[" ",",","?","!",";"],_c=new Ic(20),Pc=(t,e,n)=>{e=e||"",n=n||"";const r=Cc.filter(a=>e.indexOf(a)<0&&n.indexOf(a)<0);if(r.length===0)return!0;const i=_c.getRegExp(`(${r.map(a=>a==="?"?"\\?":a).join("|")})`);let s=!i.test(t);if(!s){const a=t.indexOf(n);a>0&&!i.test(t.substring(0,a))&&(s=!0)}return s},Mt=function(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!t)return;if(t[e])return t[e];const r=e.split(n);let i=t;for(let s=0;s<r.length;){if(!i||typeof i!="object")return;let a,o="";for(let l=s;l<r.length;++l)if(l!==s&&(o+=n),o+=r[l],a=i[o],a!==void 0){if(["string","number","boolean"].indexOf(typeof a)>-1&&l<r.length-1)continue;s+=l-s+1;break}i=a}return i},Ze=t=>t&&t.replace("_","-"),Ec={type:"logger",log(t){this.output("log",t)},warn(t){this.output("warn",t)},error(t){this.output("error",t)},output(t,e){console&&console[t]&&console[t].apply(console,e)}};class et{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(e,n)}init(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=n.prefix||"i18next:",this.logger=e||Ec,this.options=n,this.debug=n.debug}log(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"log","",!0)}warn(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"warn","",!0)}error(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"error","")}deprecate(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"warn","WARNING DEPRECATED: ",!0)}forward(e,n,r,i){return i&&!this.debug?null:(x(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[n](e))}create(e){return new et(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new et(this.logger,e)}}var M=new et;class ft{constructor(){this.observers={}}on(e,n){return e.split(" ").forEach(r=>{this.observers[r]||(this.observers[r]=new Map);const i=this.observers[r].get(n)||0;this.observers[r].set(n,i+1)}),this}off(e,n){if(this.observers[e]){if(!n){delete this.observers[e];return}this.observers[e].delete(n)}}emit(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(a=>{let[o,l]=a;for(let u=0;u<l;u++)o(...r)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(a=>{let[o,l]=a;for(let u=0;u<l;u++)o.apply(o,[e,...r])})}}class Yn extends ft{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const n=this.options.ns.indexOf(e);n>-1&&this.options.ns.splice(n,1)}getResource(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const s=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,a=i.ignoreJSONStructure!==void 0?i.ignoreJSONStructure:this.options.ignoreJSONStructure;let o;e.indexOf(".")>-1?o=e.split("."):(o=[e,n],r&&(Array.isArray(r)?o.push(...r):x(r)&&s?o.push(...r.split(s)):o.push(r)));const l=Qe(this.data,o);return!l&&!n&&!r&&e.indexOf(".")>-1&&(e=o[0],n=o[1],r=o.slice(2).join(".")),l||!a||!x(r)?l:Mt(this.data&&this.data[e]&&this.data[e][n],r,s)}addResource(e,n,r,i){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1};const a=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator;let o=[e,n];r&&(o=o.concat(a?r.split(a):r)),e.indexOf(".")>-1&&(o=e.split("."),i=n,n=o[1]),this.addNamespaces(n),Gn(this.data,o,i),s.silent||this.emit("added",e,n,r,i)}addResources(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(const s in r)(x(r[s])||Array.isArray(r[s]))&&this.addResource(e,n,s,r[s],{silent:!0});i.silent||this.emit("added",e,n,r)}addResourceBundle(e,n,r,i,s){let a=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1,skipCopy:!1},o=[e,n];e.indexOf(".")>-1&&(o=e.split("."),i=r,r=n,n=o[1]),this.addNamespaces(n);let l=Qe(this.data,o)||{};a.skipCopy||(r=JSON.parse(JSON.stringify(r))),i?Ri(l,r,s):l={...l,...r},Gn(this.data,o,l),a.silent||this.emit("added",e,n,r)}removeResourceBundle(e,n){this.hasResourceBundle(e,n)&&delete this.data[e][n],this.removeNamespaces(n),this.emit("removed",e,n)}hasResourceBundle(e,n){return this.getResource(e,n)!==void 0}getResourceBundle(e,n){return n||(n=this.options.defaultNS),this.options.compatibilityAPI==="v1"?{...this.getResource(e,n)}:this.getResource(e,n)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const n=this.getDataByLanguage(e);return!!(n&&Object.keys(n)||[]).find(i=>n[i]&&Object.keys(n[i]).length>0)}toJSON(){return this.data}}var Ai={processors:{},addPostProcessor(t){this.processors[t.name]=t},handle(t,e,n,r,i){return t.forEach(s=>{this.processors[s]&&(e=this.processors[s].process(e,n,r,i))}),e}};const Xn={};class tt extends ft{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),vc(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=M.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(e==null)return!1;const r=this.resolve(e,n);return r&&r.res!==void 0}extractFromKey(e,n){let r=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const i=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let s=n.ns||this.options.defaultNS||[];const a=r&&e.indexOf(r)>-1,o=!this.options.userDefinedKeySeparator&&!n.keySeparator&&!this.options.userDefinedNsSeparator&&!n.nsSeparator&&!Pc(e,r,i);if(a&&!o){const l=e.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:e,namespaces:x(s)?[s]:s};const u=e.split(r);(r!==i||r===i&&this.options.ns.indexOf(u[0])>-1)&&(s=u.shift()),e=u.join(i)}return{key:e,namespaces:x(s)?[s]:s}}translate(e,n,r){if(typeof n!="object"&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),typeof n=="object"&&(n={...n}),n||(n={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const i=n.returnDetails!==void 0?n.returnDetails:this.options.returnDetails,s=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,{key:a,namespaces:o}=this.extractFromKey(e[e.length-1],n),l=o[o.length-1],u=n.lng||this.language,c=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u&&u.toLowerCase()==="cimode"){if(c){const S=n.nsSeparator||this.options.nsSeparator;return i?{res:`${l}${S}${a}`,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(n)}:`${l}${S}${a}`}return i?{res:a,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(n)}:a}const f=this.resolve(e,n);let d=f&&f.res;const p=f&&f.usedKey||a,m=f&&f.exactUsedKey||a,g=Object.prototype.toString.apply(d),h=["[object Number]","[object Function]","[object RegExp]"],v=n.joinArrays!==void 0?n.joinArrays:this.options.joinArrays,y=!this.i18nFormat||this.i18nFormat.handleAsObject,k=!x(d)&&typeof d!="boolean"&&typeof d!="number";if(y&&d&&k&&h.indexOf(g)<0&&!(x(v)&&Array.isArray(d))){if(!n.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const S=this.options.returnedObjectHandler?this.options.returnedObjectHandler(p,d,{...n,ns:o}):`key '${a} (${this.language})' returned an object instead of string.`;return i?(f.res=S,f.usedParams=this.getUsedParamsDetails(n),f):S}if(s){const S=Array.isArray(d),I=S?[]:{},C=S?m:p;for(const R in d)if(Object.prototype.hasOwnProperty.call(d,R)){const K=`${C}${s}${R}`;I[R]=this.translate(K,{...n,joinArrays:!1,ns:o}),I[R]===K&&(I[R]=d[R])}d=I}}else if(y&&x(v)&&Array.isArray(d))d=d.join(v),d&&(d=this.extendTranslation(d,e,n,r));else{let S=!1,I=!1;const C=n.count!==void 0&&!x(n.count),R=tt.hasDefaultValue(n),K=C?this.pluralResolver.getSuffix(u,n.count,n):"",Ui=n.ordinal&&C?this.pluralResolver.getSuffix(u,n.count,{ordinal:!1}):"",an=C&&!n.ordinal&&n.count===0&&this.pluralResolver.shouldUseIntlApi(),ve=an&&n[`defaultValue${this.options.pluralSeparator}zero`]||n[`defaultValue${K}`]||n[`defaultValue${Ui}`]||n.defaultValue;!this.isValidLookup(d)&&R&&(S=!0,d=ve),this.isValidLookup(d)||(I=!0,d=a);const Di=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&I?void 0:d,we=R&&ve!==d&&this.options.updateMissing;if(I||S||we){if(this.logger.log(we?"updateKey":"missingKey",u,l,a,we?ve:d),s){const L=this.resolve(a,{...n,keySeparator:!1});L&&L.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let be=[];const Me=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if(this.options.saveMissingTo==="fallback"&&Me&&Me[0])for(let L=0;L<Me.length;L++)be.push(Me[L]);else this.options.saveMissingTo==="all"?be=this.languageUtils.toResolveHierarchy(n.lng||this.language):be.push(n.lng||this.language);const on=(L,Y,ke)=>{const ln=R&&ke!==d?ke:Di;this.options.missingKeyHandler?this.options.missingKeyHandler(L,l,Y,ln,we,n):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(L,l,Y,ln,we,n),this.emit("missingKey",L,l,Y,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&C?be.forEach(L=>{const Y=this.pluralResolver.getSuffixes(L,n);an&&n[`defaultValue${this.options.pluralSeparator}zero`]&&Y.indexOf(`${this.options.pluralSeparator}zero`)<0&&Y.push(`${this.options.pluralSeparator}zero`),Y.forEach(ke=>{on([L],a+ke,n[`defaultValue${ke}`]||ve)})}):on(be,a,ve))}d=this.extendTranslation(d,e,n,f,r),I&&d===a&&this.options.appendNamespaceToMissingKey&&(d=`${l}:${a}`),(I||S)&&this.options.parseMissingKeyHandler&&(this.options.compatibilityAPI!=="v1"?d=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${a}`:a,S?d:void 0):d=this.options.parseMissingKeyHandler(d))}return i?(f.res=d,f.usedParams=this.getUsedParamsDetails(n),f):d}extendTranslation(e,n,r,i,s){var a=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||i.usedLng,i.usedNS,i.usedKey,{resolved:i});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const u=x(e)&&(r&&r.interpolation&&r.interpolation.skipOnVariables!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let c;if(u){const d=e.match(this.interpolator.nestingRegexp);c=d&&d.length}let f=r.replace&&!x(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(f={...this.options.interpolation.defaultVariables,...f}),e=this.interpolator.interpolate(e,f,r.lng||this.language||i.usedLng,r),u){const d=e.match(this.interpolator.nestingRegexp),p=d&&d.length;c<p&&(r.nest=!1)}!r.lng&&this.options.compatibilityAPI!=="v1"&&i&&i.res&&(r.lng=this.language||i.usedLng),r.nest!==!1&&(e=this.interpolator.nest(e,function(){for(var d=arguments.length,p=new Array(d),m=0;m<d;m++)p[m]=arguments[m];return s&&s[0]===p[0]&&!r.context?(a.logger.warn(`It seems you are nesting recursively key: ${p[0]} in key: ${n[0]}`),null):a.translate(...p,n)},r)),r.interpolation&&this.interpolator.reset()}const o=r.postProcess||this.options.postProcess,l=x(o)?[o]:o;return e!=null&&l&&l.length&&r.applyPostProcessor!==!1&&(e=Ai.handle(l,e,n,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...i,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r,i,s,a,o;return x(e)&&(e=[e]),e.forEach(l=>{if(this.isValidLookup(r))return;const u=this.extractFromKey(l,n),c=u.key;i=c;let f=u.namespaces;this.options.fallbackNS&&(f=f.concat(this.options.fallbackNS));const d=n.count!==void 0&&!x(n.count),p=d&&!n.ordinal&&n.count===0&&this.pluralResolver.shouldUseIntlApi(),m=n.context!==void 0&&(x(n.context)||typeof n.context=="number")&&n.context!=="",g=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);f.forEach(h=>{this.isValidLookup(r)||(o=h,!Xn[`${g[0]}-${h}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(o)&&(Xn[`${g[0]}-${h}`]=!0,this.logger.warn(`key "${i}" for languages "${g.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),g.forEach(v=>{if(this.isValidLookup(r))return;a=v;const y=[c];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(y,c,v,h,n);else{let S;d&&(S=this.pluralResolver.getSuffix(v,n.count,n));const I=`${this.options.pluralSeparator}zero`,C=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(d&&(y.push(c+S),n.ordinal&&S.indexOf(C)===0&&y.push(c+S.replace(C,this.options.pluralSeparator)),p&&y.push(c+I)),m){const R=`${c}${this.options.contextSeparator}${n.context}`;y.push(R),d&&(y.push(R+S),n.ordinal&&S.indexOf(C)===0&&y.push(R+S.replace(C,this.options.pluralSeparator)),p&&y.push(R+I))}}let k;for(;k=y.pop();)this.isValidLookup(r)||(s=k,r=this.getResource(v,h,k,n))}))})}),{res:r,usedKey:i,exactUsedKey:s,usedLng:a,usedNS:o}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,n,r,i):this.resourceStore.getResource(e,n,r,i)}getUsedParamsDetails(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const n=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=e.replace&&!x(e.replace);let i=r?e.replace:e;if(r&&typeof e.count<"u"&&(i.count=e.count),this.options.interpolation.defaultVariables&&(i={...this.options.interpolation.defaultVariables,...i}),!r){i={...i};for(const s of n)delete i[s]}return i}static hasDefaultValue(e){const n="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&n===r.substring(0,n.length)&&e[r]!==void 0)return!0;return!1}}const It=t=>t.charAt(0).toUpperCase()+t.slice(1);class Qn{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=M.create("languageUtils")}getScriptPartFromCode(e){if(e=Ze(e),!e||e.indexOf("-")<0)return null;const n=e.split("-");return n.length===2||(n.pop(),n[n.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(n.join("-"))}getLanguagePartFromCode(e){if(e=Ze(e),!e||e.indexOf("-")<0)return e;const n=e.split("-");return this.formatLanguageCode(n[0])}formatLanguageCode(e){if(x(e)&&e.indexOf("-")>-1){if(typeof Intl<"u"&&typeof Intl.getCanonicalLocales<"u")try{let i=Intl.getCanonicalLocales(e)[0];if(i&&this.options.lowerCaseLng&&(i=i.toLowerCase()),i)return i}catch{}const n=["hans","hant","latn","cyrl","cans","mong","arab"];let r=e.split("-");return this.options.lowerCaseLng?r=r.map(i=>i.toLowerCase()):r.length===2?(r[0]=r[0].toLowerCase(),r[1]=r[1].toUpperCase(),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=It(r[1].toLowerCase()))):r.length===3&&(r[0]=r[0].toLowerCase(),r[1].length===2&&(r[1]=r[1].toUpperCase()),r[0]!=="sgn"&&r[2].length===2&&(r[2]=r[2].toUpperCase()),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=It(r[1].toLowerCase())),n.indexOf(r[2].toLowerCase())>-1&&(r[2]=It(r[2].toLowerCase()))),r.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let n;return e.forEach(r=>{if(n)return;const i=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(i))&&(n=i)}),!n&&this.options.supportedLngs&&e.forEach(r=>{if(n)return;const i=this.getLanguagePartFromCode(r);if(this.isSupportedCode(i))return n=i;n=this.options.supportedLngs.find(s=>{if(s===i)return s;if(!(s.indexOf("-")<0&&i.indexOf("-")<0)&&(s.indexOf("-")>0&&i.indexOf("-")<0&&s.substring(0,s.indexOf("-"))===i||s.indexOf(i)===0&&i.length>1))return s})}),n||(n=this.getFallbackCodes(this.options.fallbackLng)[0]),n}getFallbackCodes(e,n){if(!e)return[];if(typeof e=="function"&&(e=e(n)),x(e)&&(e=[e]),Array.isArray(e))return e;if(!n)return e.default||[];let r=e[n];return r||(r=e[this.getScriptPartFromCode(n)]),r||(r=e[this.formatLanguageCode(n)]),r||(r=e[this.getLanguagePartFromCode(n)]),r||(r=e.default),r||[]}toResolveHierarchy(e,n){const r=this.getFallbackCodes(n||this.options.fallbackLng||[],e),i=[],s=a=>{a&&(this.isSupportedCode(a)?i.push(a):this.logger.warn(`rejecting language code not found in supportedLngs: ${a}`))};return x(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&s(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&s(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&s(this.getLanguagePartFromCode(e))):x(e)&&s(this.formatLanguageCode(e)),r.forEach(a=>{i.indexOf(a)<0&&s(this.formatLanguageCode(a))}),i}}let Rc=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],Ac={1:t=>+(t>1),2:t=>+(t!=1),3:t=>0,4:t=>t%10==1&&t%100!=11?0:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?1:2,5:t=>t==0?0:t==1?1:t==2?2:t%100>=3&&t%100<=10?3:t%100>=11?4:5,6:t=>t==1?0:t>=2&&t<=4?1:2,7:t=>t==1?0:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?1:2,8:t=>t==1?0:t==2?1:t!=8&&t!=11?2:3,9:t=>+(t>=2),10:t=>t==1?0:t==2?1:t<7?2:t<11?3:4,11:t=>t==1||t==11?0:t==2||t==12?1:t>2&&t<20?2:3,12:t=>+(t%10!=1||t%100==11),13:t=>+(t!==0),14:t=>t==1?0:t==2?1:t==3?2:3,15:t=>t%10==1&&t%100!=11?0:t%10>=2&&(t%100<10||t%100>=20)?1:2,16:t=>t%10==1&&t%100!=11?0:t!==0?1:2,17:t=>t==1||t%10==1&&t%100!=11?0:1,18:t=>t==0?0:t==1?1:2,19:t=>t==1?0:t==0||t%100>1&&t%100<11?1:t%100>10&&t%100<20?2:3,20:t=>t==1?0:t==0||t%100>0&&t%100<20?1:2,21:t=>t%100==1?1:t%100==2?2:t%100==3||t%100==4?3:0,22:t=>t==1?0:t==2?1:(t<0||t>10)&&t%10==0?2:3};const Nc=["v1","v2","v3"],Oc=["v4"],Zn={zero:0,one:1,two:2,few:3,many:4,other:5},Tc=()=>{const t={};return Rc.forEach(e=>{e.lngs.forEach(n=>{t[n]={numbers:e.nr,plurals:Ac[e.fc]}})}),t};class Lc{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=e,this.options=n,this.logger=M.create("pluralResolver"),(!this.options.compatibilityJSON||Oc.includes(this.options.compatibilityJSON))&&(typeof Intl>"u"||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=Tc(),this.pluralRulesCache={}}addRule(e,n){this.rules[e]=n}clearCache(){this.pluralRulesCache={}}getRule(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.shouldUseIntlApi()){const r=Ze(e==="dev"?"en":e),i=n.ordinal?"ordinal":"cardinal",s=JSON.stringify({cleanedCode:r,type:i});if(s in this.pluralRulesCache)return this.pluralRulesCache[s];let a;try{a=new Intl.PluralRules(r,{type:i})}catch{if(!e.match(/-|_/))return;const l=this.languageUtils.getLanguagePartFromCode(e);a=this.getRule(l,n)}return this.pluralRulesCache[s]=a,a}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(e,n);return this.shouldUseIntlApi()?r&&r.resolvedOptions().pluralCategories.length>1:r&&r.numbers.length>1}getPluralFormsOfKey(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(e,r).map(i=>`${n}${i}`)}getSuffixes(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(e,n);return r?this.shouldUseIntlApi()?r.resolvedOptions().pluralCategories.sort((i,s)=>Zn[i]-Zn[s]).map(i=>`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${i}`):r.numbers.map(i=>this.getSuffix(e,i,n)):[]}getSuffix(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const i=this.getRule(e,r);return i?this.shouldUseIntlApi()?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${i.select(n)}`:this.getSuffixRetroCompatible(i,n):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,n){const r=e.noAbs?e.plurals(n):e.plurals(Math.abs(n));let i=e.numbers[r];this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1&&(i===2?i="plural":i===1&&(i=""));const s=()=>this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString();return this.options.compatibilityJSON==="v1"?i===1?"":typeof i=="number"?`_plural_${i.toString()}`:s():this.options.compatibilityJSON==="v2"||this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1?s():this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString()}shouldUseIntlApi(){return!Nc.includes(this.options.compatibilityJSON)}}const er=function(t,e,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,s=kc(t,e,n);return!s&&i&&x(n)&&(s=Mt(t,n,r),s===void 0&&(s=Mt(e,n,r))),s},Ct=t=>t.replace(/\$/g,"$$$$");class Uc{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=M.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(n=>n),this.init(e)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:n,escapeValue:r,useRawValueToEscape:i,prefix:s,prefixEscaped:a,suffix:o,suffixEscaped:l,formatSeparator:u,unescapeSuffix:c,unescapePrefix:f,nestingPrefix:d,nestingPrefixEscaped:p,nestingSuffix:m,nestingSuffixEscaped:g,nestingOptionsSeparator:h,maxReplaces:v,alwaysFormat:y}=e.interpolation;this.escape=n!==void 0?n:xc,this.escapeValue=r!==void 0?r:!0,this.useRawValueToEscape=i!==void 0?i:!1,this.prefix=s?ce(s):a||"{{",this.suffix=o?ce(o):l||"}}",this.formatSeparator=u||",",this.unescapePrefix=c?"":f||"-",this.unescapeSuffix=this.unescapePrefix?"":c||"",this.nestingPrefix=d?ce(d):p||ce("$t("),this.nestingSuffix=m?ce(m):g||ce(")"),this.nestingOptionsSeparator=h||",",this.maxReplaces=v||1e3,this.alwaysFormat=y!==void 0?y:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(n,r)=>n&&n.source===r?(n.lastIndex=0,n):new RegExp(r,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,n,r,i){let s,a,o;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=p=>{if(p.indexOf(this.formatSeparator)<0){const v=er(n,l,p,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(v,void 0,r,{...i,...n,interpolationkey:p}):v}const m=p.split(this.formatSeparator),g=m.shift().trim(),h=m.join(this.formatSeparator).trim();return this.format(er(n,l,g,this.options.keySeparator,this.options.ignoreJSONStructure),h,r,{...i,...n,interpolationkey:g})};this.resetRegExp();const c=i&&i.missingInterpolationHandler||this.options.missingInterpolationHandler,f=i&&i.interpolation&&i.interpolation.skipOnVariables!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:p=>Ct(p)},{regex:this.regexp,safeValue:p=>this.escapeValue?Ct(this.escape(p)):Ct(p)}].forEach(p=>{for(o=0;s=p.regex.exec(e);){const m=s[1].trim();if(a=u(m),a===void 0)if(typeof c=="function"){const h=c(e,s,i);a=x(h)?h:""}else if(i&&Object.prototype.hasOwnProperty.call(i,m))a="";else if(f){a=s[0];continue}else this.logger.warn(`missed to pass in variable ${m} for interpolating ${e}`),a="";else!x(a)&&!this.useRawValueToEscape&&(a=Wn(a));const g=p.safeValue(a);if(e=e.replace(s[0],g),f?(p.regex.lastIndex+=a.length,p.regex.lastIndex-=s[0].length):p.regex.lastIndex=0,o++,o>=this.maxReplaces)break}}),e}nest(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i,s,a;const o=(l,u)=>{const c=this.nestingOptionsSeparator;if(l.indexOf(c)<0)return l;const f=l.split(new RegExp(`${c}[ ]*{`));let d=`{${f[1]}`;l=f[0],d=this.interpolate(d,a);const p=d.match(/'/g),m=d.match(/"/g);(p&&p.length%2===0&&!m||m.length%2!==0)&&(d=d.replace(/'/g,'"'));try{a=JSON.parse(d),u&&(a={...u,...a})}catch(g){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,g),`${l}${c}${d}`}return a.defaultValue&&a.defaultValue.indexOf(this.prefix)>-1&&delete a.defaultValue,l};for(;i=this.nestingRegexp.exec(e);){let l=[];a={...r},a=a.replace&&!x(a.replace)?a.replace:a,a.applyPostProcessor=!1,delete a.defaultValue;let u=!1;if(i[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(i[1])){const c=i[1].split(this.formatSeparator).map(f=>f.trim());i[1]=c.shift(),l=c,u=!0}if(s=n(o.call(this,i[1].trim(),a),a),s&&i[0]===e&&!x(s))return s;x(s)||(s=Wn(s)),s||(this.logger.warn(`missed to resolve ${i[1]} for nesting ${e}`),s=""),u&&(s=l.reduce((c,f)=>this.format(c,f,r.lng,{...r,interpolationkey:i[1].trim()}),s.trim())),e=e.replace(i[0],s),this.regexp.lastIndex=0}return e}}const Dc=t=>{let e=t.toLowerCase().trim();const n={};if(t.indexOf("(")>-1){const r=t.split("(");e=r[0].toLowerCase().trim();const i=r[1].substring(0,r[1].length-1);e==="currency"&&i.indexOf(":")<0?n.currency||(n.currency=i.trim()):e==="relativetime"&&i.indexOf(":")<0?n.range||(n.range=i.trim()):i.split(";").forEach(a=>{if(a){const[o,...l]=a.split(":"),u=l.join(":").trim().replace(/^'+|'+$/g,""),c=o.trim();n[c]||(n[c]=u),u==="false"&&(n[c]=!1),u==="true"&&(n[c]=!0),isNaN(u)||(n[c]=parseInt(u,10))}})}return{formatName:e,formatOptions:n}},de=t=>{const e={};return(n,r,i)=>{let s=i;i&&i.interpolationkey&&i.formatParams&&i.formatParams[i.interpolationkey]&&i[i.interpolationkey]&&(s={...s,[i.interpolationkey]:void 0});const a=r+JSON.stringify(s);let o=e[a];return o||(o=t(Ze(r),i),e[a]=o),o(n)}};class $c{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=M.create("formatter"),this.options=e,this.formats={number:de((n,r)=>{const i=new Intl.NumberFormat(n,{...r});return s=>i.format(s)}),currency:de((n,r)=>{const i=new Intl.NumberFormat(n,{...r,style:"currency"});return s=>i.format(s)}),datetime:de((n,r)=>{const i=new Intl.DateTimeFormat(n,{...r});return s=>i.format(s)}),relativetime:de((n,r)=>{const i=new Intl.RelativeTimeFormat(n,{...r});return s=>i.format(s,r.range||"day")}),list:de((n,r)=>{const i=new Intl.ListFormat(n,{...r});return s=>i.format(s)})},this.init(e)}init(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};this.formatSeparator=n.interpolation.formatSeparator||","}add(e,n){this.formats[e.toLowerCase().trim()]=n}addCached(e,n){this.formats[e.toLowerCase().trim()]=de(n)}format(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const s=n.split(this.formatSeparator);if(s.length>1&&s[0].indexOf("(")>1&&s[0].indexOf(")")<0&&s.find(o=>o.indexOf(")")>-1)){const o=s.findIndex(l=>l.indexOf(")")>-1);s[0]=[s[0],...s.splice(1,o)].join(this.formatSeparator)}return s.reduce((o,l)=>{const{formatName:u,formatOptions:c}=Dc(l);if(this.formats[u]){let f=o;try{const d=i&&i.formatParams&&i.formatParams[i.interpolationkey]||{},p=d.locale||d.lng||i.locale||i.lng||r;f=this.formats[u](o,p,{...c,...i,...d})}catch(d){this.logger.warn(d)}return f}else this.logger.warn(`there was no format function for ${u}`);return o},e)}}const jc=(t,e)=>{t.pending[e]!==void 0&&(delete t.pending[e],t.pendingCount--)};class Fc extends ft{constructor(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=e,this.store=n,this.services=r,this.languageUtils=r.languageUtils,this.options=i,this.logger=M.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(r,i.backend,i)}queueLoad(e,n,r,i){const s={},a={},o={},l={};return e.forEach(u=>{let c=!0;n.forEach(f=>{const d=`${u}|${f}`;!r.reload&&this.store.hasResourceBundle(u,f)?this.state[d]=2:this.state[d]<0||(this.state[d]===1?a[d]===void 0&&(a[d]=!0):(this.state[d]=1,c=!1,a[d]===void 0&&(a[d]=!0),s[d]===void 0&&(s[d]=!0),l[f]===void 0&&(l[f]=!0)))}),c||(o[u]=!0)}),(Object.keys(s).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(s),pending:Object.keys(a),toLoadLanguages:Object.keys(o),toLoadNamespaces:Object.keys(l)}}loaded(e,n,r){const i=e.split("|"),s=i[0],a=i[1];n&&this.emit("failedLoading",s,a,n),!n&&r&&this.store.addResourceBundle(s,a,r,void 0,void 0,{skipCopy:!0}),this.state[e]=n?-1:2,n&&r&&(this.state[e]=0);const o={};this.queue.forEach(l=>{bc(l.loaded,[s],a),jc(l,e),n&&l.errors.push(n),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(u=>{o[u]||(o[u]={});const c=l.loaded[u];c.length&&c.forEach(f=>{o[u][f]===void 0&&(o[u][f]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",o),this.queue=this.queue.filter(l=>!l.done)}read(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,a=arguments.length>5?arguments[5]:void 0;if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:n,fcName:r,tried:i,wait:s,callback:a});return}this.readingCalls++;const o=(u,c)=>{if(this.readingCalls--,this.waitingReads.length>0){const f=this.waitingReads.shift();this.read(f.lng,f.ns,f.fcName,f.tried,f.wait,f.callback)}if(u&&c&&i<this.maxRetries){setTimeout(()=>{this.read.call(this,e,n,r,i+1,s*2,a)},s);return}a(u,c)},l=this.backend[r].bind(this.backend);if(l.length===2){try{const u=l(e,n);u&&typeof u.then=="function"?u.then(c=>o(null,c)).catch(o):o(null,u)}catch(u){o(u)}return}return l(e,n,o)}prepareLoading(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),i&&i();x(e)&&(e=this.languageUtils.toResolveHierarchy(e)),x(n)&&(n=[n]);const s=this.queueLoad(e,n,r,i);if(!s.toLoad.length)return s.pending.length||i(),null;s.toLoad.forEach(a=>{this.loadOne(a)})}load(e,n,r){this.prepareLoading(e,n,{},r)}reload(e,n,r){this.prepareLoading(e,n,{reload:!0},r)}loadOne(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const r=e.split("|"),i=r[0],s=r[1];this.read(i,s,"read",void 0,void 0,(a,o)=>{a&&this.logger.warn(`${n}loading namespace ${s} for language ${i} failed`,a),!a&&o&&this.logger.log(`${n}loaded namespace ${s} for language ${i}`,o),this.loaded(e,a,o)})}saveMissing(e,n,r,i,s){let a=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},o=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(n)){this.logger.warn(`did not save key "${r}" as the namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if(this.backend&&this.backend.create){const l={...a,isUpdate:s},u=this.backend.create.bind(this.backend);if(u.length<6)try{let c;u.length===5?c=u(e,n,r,i,l):c=u(e,n,r,i),c&&typeof c.then=="function"?c.then(f=>o(null,f)).catch(o):o(null,c)}catch(c){o(c)}else u(e,n,r,i,o,l)}!e||!e[0]||this.store.addResource(e[0],n,r,i)}}}const tr=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:t=>{let e={};if(typeof t[1]=="object"&&(e=t[1]),x(t[1])&&(e.defaultValue=t[1]),x(t[2])&&(e.tDescription=t[2]),typeof t[2]=="object"||typeof t[3]=="object"){const n=t[3]||t[2];Object.keys(n).forEach(r=>{e[r]=n[r]})}return e},interpolation:{escapeValue:!0,format:t=>t,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),nr=t=>(x(t.ns)&&(t.ns=[t.ns]),x(t.fallbackLng)&&(t.fallbackLng=[t.fallbackLng]),x(t.fallbackNS)&&(t.fallbackNS=[t.fallbackNS]),t.supportedLngs&&t.supportedLngs.indexOf("cimode")<0&&(t.supportedLngs=t.supportedLngs.concat(["cimode"])),t),Ke=()=>{},zc=t=>{Object.getOwnPropertyNames(Object.getPrototypeOf(t)).forEach(n=>{typeof t[n]=="function"&&(t[n]=t[n].bind(t))})};class De extends ft{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(super(),this.options=nr(e),this.services={},this.logger=M,this.modules={external:[]},zc(this),n&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,n),this;setTimeout(()=>{this.init(e,n)},0)}}init(){var e=this;let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,typeof n=="function"&&(r=n,n={}),!n.defaultNS&&n.defaultNS!==!1&&n.ns&&(x(n.ns)?n.defaultNS=n.ns:n.ns.indexOf("translation")<0&&(n.defaultNS=n.ns[0]));const i=tr();this.options={...i,...this.options,...nr(n)},this.options.compatibilityAPI!=="v1"&&(this.options.interpolation={...i.interpolation,...this.options.interpolation}),n.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=n.keySeparator),n.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=n.nsSeparator);const s=c=>c?typeof c=="function"?new c:c:null;if(!this.options.isClone){this.modules.logger?M.init(s(this.modules.logger),this.options):M.init(null,this.options);let c;this.modules.formatter?c=this.modules.formatter:typeof Intl<"u"&&(c=$c);const f=new Qn(this.options);this.store=new Yn(this.options.resources,this.options);const d=this.services;d.logger=M,d.resourceStore=this.store,d.languageUtils=f,d.pluralResolver=new Lc(f,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),c&&(!this.options.interpolation.format||this.options.interpolation.format===i.interpolation.format)&&(d.formatter=s(c),d.formatter.init(d,this.options),this.options.interpolation.format=d.formatter.format.bind(d.formatter)),d.interpolator=new Uc(this.options),d.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},d.backendConnector=new Fc(s(this.modules.backend),d.resourceStore,d,this.options),d.backendConnector.on("*",function(p){for(var m=arguments.length,g=new Array(m>1?m-1:0),h=1;h<m;h++)g[h-1]=arguments[h];e.emit(p,...g)}),this.modules.languageDetector&&(d.languageDetector=s(this.modules.languageDetector),d.languageDetector.init&&d.languageDetector.init(d,this.options.detection,this.options)),this.modules.i18nFormat&&(d.i18nFormat=s(this.modules.i18nFormat),d.i18nFormat.init&&d.i18nFormat.init(this)),this.translator=new tt(this.services,this.options),this.translator.on("*",function(p){for(var m=arguments.length,g=new Array(m>1?m-1:0),h=1;h<m;h++)g[h-1]=arguments[h];e.emit(p,...g)}),this.modules.external.forEach(p=>{p.init&&p.init(this)})}if(this.format=this.options.interpolation.format,r||(r=Ke),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const c=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);c.length>0&&c[0]!=="dev"&&(this.options.lng=c[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(c=>{this[c]=function(){return e.store[c](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(c=>{this[c]=function(){return e.store[c](...arguments),e}});const l=xe(),u=()=>{const c=(f,d)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),l.resolve(d),r(f,d)};if(this.languages&&this.options.compatibilityAPI!=="v1"&&!this.isInitialized)return c(null,this.t.bind(this));this.changeLanguage(this.options.lng,c)};return this.options.resources||!this.options.initImmediate?u():setTimeout(u,0),l}loadResources(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ke;const i=x(e)?e:this.language;if(typeof e=="function"&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if(i&&i.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const s=[],a=o=>{if(!o||o==="cimode")return;this.services.languageUtils.toResolveHierarchy(o).forEach(u=>{u!=="cimode"&&s.indexOf(u)<0&&s.push(u)})};i?a(i):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(l=>a(l)),this.options.preload&&this.options.preload.forEach(o=>a(o)),this.services.backendConnector.load(s,this.options.ns,o=>{!o&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(o)})}else r(null)}reloadResources(e,n,r){const i=xe();return typeof e=="function"&&(r=e,e=void 0),typeof n=="function"&&(r=n,n=void 0),e||(e=this.languages),n||(n=this.options.ns),r||(r=Ke),this.services.backendConnector.reload(e,n,s=>{i.resolve(),r(s)}),i}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&Ai.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1))for(let n=0;n<this.languages.length;n++){const r=this.languages[n];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}}changeLanguage(e,n){var r=this;this.isLanguageChangingTo=e;const i=xe();this.emit("languageChanging",e);const s=l=>{this.language=l,this.languages=this.services.languageUtils.toResolveHierarchy(l),this.resolvedLanguage=void 0,this.setResolvedLanguage(l)},a=(l,u)=>{u?(s(u),this.translator.changeLanguage(u),this.isLanguageChangingTo=void 0,this.emit("languageChanged",u),this.logger.log("languageChanged",u)):this.isLanguageChangingTo=void 0,i.resolve(function(){return r.t(...arguments)}),n&&n(l,function(){return r.t(...arguments)})},o=l=>{!e&&!l&&this.services.languageDetector&&(l=[]);const u=x(l)?l:this.services.languageUtils.getBestMatchFromCodes(l);u&&(this.language||s(u),this.translator.language||this.translator.changeLanguage(u),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(u)),this.loadResources(u,c=>{a(c,u)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?o(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(e),i}getFixedT(e,n,r){var i=this;const s=function(a,o){let l;if(typeof o!="object"){for(var u=arguments.length,c=new Array(u>2?u-2:0),f=2;f<u;f++)c[f-2]=arguments[f];l=i.options.overloadTranslationOptionHandler([a,o].concat(c))}else l={...o};l.lng=l.lng||s.lng,l.lngs=l.lngs||s.lngs,l.ns=l.ns||s.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||r||s.keyPrefix);const d=i.options.keySeparator||".";let p;return l.keyPrefix&&Array.isArray(a)?p=a.map(m=>`${l.keyPrefix}${d}${m}`):p=l.keyPrefix?`${l.keyPrefix}${d}${a}`:a,i.t(p,l)};return x(e)?s.lng=e:s.lngs=e,s.ns=n,s.keyPrefix=r,s}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=n.lng||this.resolvedLanguage||this.languages[0],i=this.options?this.options.fallbackLng:!1,s=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const a=(o,l)=>{const u=this.services.backendConnector.state[`${o}|${l}`];return u===-1||u===0||u===2};if(n.precheck){const o=n.precheck(this,a);if(o!==void 0)return o}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||a(r,e)&&(!i||a(s,e)))}loadNamespaces(e,n){const r=xe();return this.options.ns?(x(e)&&(e=[e]),e.forEach(i=>{this.options.ns.indexOf(i)<0&&this.options.ns.push(i)}),this.loadResources(i=>{r.resolve(),n&&n(i)}),r):(n&&n(),Promise.resolve())}loadLanguages(e,n){const r=xe();x(e)&&(e=[e]);const i=this.options.preload||[],s=e.filter(a=>i.indexOf(a)<0&&this.services.languageUtils.isSupportedCode(a));return s.length?(this.options.preload=i.concat(s),this.loadResources(a=>{r.resolve(),n&&n(a)}),r):(n&&n(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";const n=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=this.services&&this.services.languageUtils||new Qn(tr());return n.indexOf(r.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;return new De(e,n)}cloneInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ke;const r=e.forkResourceStore;r&&delete e.forkResourceStore;const i={...this.options,...e,isClone:!0},s=new De(i);return(e.debug!==void 0||e.prefix!==void 0)&&(s.logger=s.logger.clone(e)),["store","services","language"].forEach(o=>{s[o]=this[o]}),s.services={...this.services},s.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},r&&(s.store=new Yn(this.store.data,i),s.services.resourceStore=s.store),s.translator=new tt(s.services,i),s.translator.on("*",function(o){for(var l=arguments.length,u=new Array(l>1?l-1:0),c=1;c<l;c++)u[c-1]=arguments[c];s.emit(o,...u)}),s.init(i,n),s.translator.options=i,s.translator.backendConnector.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},s}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const N=De.createInstance();N.createInstance=De.createInstance;N.createInstance;N.dir;N.init;N.loadResources;N.reloadResources;N.use;N.changeLanguage;N.getFixedT;N.t;N.exists;N.setDefaultNamespace;N.hasLoadedNamespace;N.loadNamespaces;N.loadLanguages;function Mc(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function $e(t){"@babel/helpers - typeof";return $e=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$e(t)}function Bc(t,e){if($e(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if($e(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function Vc(t){var e=Bc(t,"string");return $e(e)=="symbol"?e:e+""}function Hc(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Vc(r.key),r)}}function Kc(t,e,n){return e&&Hc(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}var Ni=[],Wc=Ni.forEach,qc=Ni.slice;function Jc(t){return Wc.call(qc.call(arguments,1),function(e){if(e)for(var n in e)t[n]===void 0&&(t[n]=e[n])}),t}var rr=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,Gc=function(e,n,r){var i=r||{};i.path=i.path||"/";var s=encodeURIComponent(n),a="".concat(e,"=").concat(s);if(i.maxAge>0){var o=i.maxAge-0;if(Number.isNaN(o))throw new Error("maxAge should be a Number");a+="; Max-Age=".concat(Math.floor(o))}if(i.domain){if(!rr.test(i.domain))throw new TypeError("option domain is invalid");a+="; Domain=".concat(i.domain)}if(i.path){if(!rr.test(i.path))throw new TypeError("option path is invalid");a+="; Path=".concat(i.path)}if(i.expires){if(typeof i.expires.toUTCString!="function")throw new TypeError("option expires is invalid");a+="; Expires=".concat(i.expires.toUTCString())}if(i.httpOnly&&(a+="; HttpOnly"),i.secure&&(a+="; Secure"),i.sameSite){var l=typeof i.sameSite=="string"?i.sameSite.toLowerCase():i.sameSite;switch(l){case!0:a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"strict":a+="; SameSite=Strict";break;case"none":a+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return a},ir={create:function(e,n,r,i){var s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};r&&(s.expires=new Date,s.expires.setTime(s.expires.getTime()+r*60*1e3)),i&&(s.domain=i),document.cookie=Gc(e,encodeURIComponent(n),s)},read:function(e){for(var n="".concat(e,"="),r=document.cookie.split(";"),i=0;i<r.length;i++){for(var s=r[i];s.charAt(0)===" ";)s=s.substring(1,s.length);if(s.indexOf(n)===0)return s.substring(n.length,s.length)}return null},remove:function(e){this.create(e,"",-1)}},Yc={name:"cookie",lookup:function(e){var n;if(e.lookupCookie&&typeof document<"u"){var r=ir.read(e.lookupCookie);r&&(n=r)}return n},cacheUserLanguage:function(e,n){n.lookupCookie&&typeof document<"u"&&ir.create(n.lookupCookie,e,n.cookieMinutes,n.cookieDomain,n.cookieOptions)}},Xc={name:"querystring",lookup:function(e){var n;if(typeof window<"u"){var r=window.location.search;!window.location.search&&window.location.hash&&window.location.hash.indexOf("?")>-1&&(r=window.location.hash.substring(window.location.hash.indexOf("?")));for(var i=r.substring(1),s=i.split("&"),a=0;a<s.length;a++){var o=s[a].indexOf("=");if(o>0){var l=s[a].substring(0,o);l===e.lookupQuerystring&&(n=s[a].substring(o+1))}}}return n}},Ie=null,sr=function(){if(Ie!==null)return Ie;try{Ie=window!=="undefined"&&window.localStorage!==null;var e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch{Ie=!1}return Ie},Qc={name:"localStorage",lookup:function(e){var n;if(e.lookupLocalStorage&&sr()){var r=window.localStorage.getItem(e.lookupLocalStorage);r&&(n=r)}return n},cacheUserLanguage:function(e,n){n.lookupLocalStorage&&sr()&&window.localStorage.setItem(n.lookupLocalStorage,e)}},Ce=null,ar=function(){if(Ce!==null)return Ce;try{Ce=window!=="undefined"&&window.sessionStorage!==null;var e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch{Ce=!1}return Ce},Zc={name:"sessionStorage",lookup:function(e){var n;if(e.lookupSessionStorage&&ar()){var r=window.sessionStorage.getItem(e.lookupSessionStorage);r&&(n=r)}return n},cacheUserLanguage:function(e,n){n.lookupSessionStorage&&ar()&&window.sessionStorage.setItem(n.lookupSessionStorage,e)}},ed={name:"navigator",lookup:function(e){var n=[];if(typeof navigator<"u"){if(navigator.languages)for(var r=0;r<navigator.languages.length;r++)n.push(navigator.languages[r]);navigator.userLanguage&&n.push(navigator.userLanguage),navigator.language&&n.push(navigator.language)}return n.length>0?n:void 0}},td={name:"htmlTag",lookup:function(e){var n,r=e.htmlTag||(typeof document<"u"?document.documentElement:null);return r&&typeof r.getAttribute=="function"&&(n=r.getAttribute("lang")),n}},nd={name:"path",lookup:function(e){var n;if(typeof window<"u"){var r=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(r instanceof Array)if(typeof e.lookupFromPathIndex=="number"){if(typeof r[e.lookupFromPathIndex]!="string")return;n=r[e.lookupFromPathIndex].replace("/","")}else n=r[0].replace("/","")}return n}},rd={name:"subdomain",lookup:function(e){var n=typeof e.lookupFromSubdomainIndex=="number"?e.lookupFromSubdomainIndex+1:1,r=typeof window<"u"&&window.location&&window.location.hostname&&window.location.hostname.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(r)return r[n]}},Oi=!1;try{document.cookie,Oi=!0}catch{}var Ti=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Oi||Ti.splice(1,1);function id(){return{order:Ti,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:function(e){return e}}}var Li=function(){function t(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};Mc(this,t),this.type="languageDetector",this.detectors={},this.init(e,n)}return Kc(t,[{key:"init",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=n||{languageUtils:{}},this.options=Jc(r,this.options||{},id()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=function(s){return s.replace("-","_")}),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=i,this.addDetector(Yc),this.addDetector(Xc),this.addDetector(Qc),this.addDetector(Zc),this.addDetector(ed),this.addDetector(td),this.addDetector(nd),this.addDetector(rd)}},{key:"addDetector",value:function(n){return this.detectors[n.name]=n,this}},{key:"detect",value:function(n){var r=this;n||(n=this.options.order);var i=[];return n.forEach(function(s){if(r.detectors[s]){var a=r.detectors[s].lookup(r.options);a&&typeof a=="string"&&(a=[a]),a&&(i=i.concat(a))}}),i=i.map(function(s){return r.options.convertDetectedLanguage(s)}),this.services.languageUtils.getBestMatchFromCodes?i:i.length>0?i[0]:null}},{key:"cacheUserLanguage",value:function(n,r){var i=this;r||(r=this.options.caches),r&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(n)>-1||r.forEach(function(s){i.detectors[s]&&i.detectors[s].cacheUserLanguage(n,i.options)}))}}])}();Li.type="languageDetector";const sd={name:"Ubuntu Connect",tagline:"Uniting South Africa Through Cultural Collaboration",description:"A culturally sensitive platform designed to unite South Africa through cross-cultural collaboration"},ad={login:"Sign In",register:"Sign Up",logout:"Sign Out",email:"Email Address",password:"Password",confirmPassword:"Confirm Password",name:"Full Name",forgotPassword:"Forgot Password?",resetPassword:"Reset Password",createAccount:"Create Account",alreadyHaveAccount:"Already have an account?",dontHaveAccount:"Don't have an account?",signInWith:"Sign in with {{provider}}",signUpWith:"Sign up with {{provider}}",phoneNumber:"Phone Number",verificationCode:"Verification Code",sendCode:"Send Code",verifyCode:"Verify Code",welcomeBack:"Welcome back!",welcomeToUbuntu:"Welcome to Ubuntu Connect!",culturalIdentity:{title:"Cultural Identity",subtitle:"Share your cultural heritage (optional)",description:"Help us connect you with your cultural community and enable cross-cultural learning. You can select multiple identities or skip this step.",selectCultures:"Select your cultural identities",skip:"Skip for now",continue:"Continue",multiple:"You can select multiple cultures",optional:"This step is completely optional",privacy:"Your cultural identity is private by default"}},od={identities:{zulu:"Zulu",xhosa:"Xhosa",afrikaans:"Afrikaans",english:"English South African",sotho:"Sotho",tswana:"Tswana",tsonga:"Tsonga",venda:"Venda",swazi:"Swazi",ndebele:"Ndebele",indian:"Indian South African",coloured:"Coloured",other:"Other"},connectionTypes:{heritage:"Cultural Heritage",interest:"Cultural Interest",learning:"Learning About",family:"Family Connection",community:"Community Member"}},ld={home:"Home",communities:"Communities",cultural:"Cultural Heritage",knowledge:"Knowledge Exchange",collaboration:"Collaborate",achievements:"Achievements",profile:"Profile",settings:"Settings"},ud={loading:"Loading...",error:"Error",success:"Success",cancel:"Cancel",save:"Save",edit:"Edit",delete:"Delete",confirm:"Confirm",back:"Back",next:"Next",previous:"Previous",close:"Close",search:"Search",filter:"Filter",sort:"Sort",view:"View",share:"Share",like:"Like",comment:"Comment",follow:"Follow",unfollow:"Unfollow",join:"Join",leave:"Leave",optional:"Optional",required:"Required"},cd={required:"This field is required",email:"Please enter a valid email address",password:"Password must be at least 8 characters",passwordMatch:"Passwords do not match",phoneNumber:"Please enter a valid South African phone number",name:"Please enter your full name"},dd={title:"Privacy & Data Protection",description:"We respect your privacy and comply with POPIA (Protection of Personal Information Act)",consent:"I consent to the collection and processing of my personal information",culturalConsent:"I consent to sharing my cultural identity for community matching",dataUsage:"Learn how we use your data",rights:"Your privacy rights",retention:"Data retention policy"},fd={app:sd,auth:ad,cultural:od,navigation:ld,common:ud,validation:cd,privacy:dd},hd={name:"Ubuntu Connect",tagline:"Verenig Suid-Afrika Deur Kulturele Samewerking",description:"ʼn Kultureel sensitiewe platform ontwerp om Suid-Afrika te verenig deur kruis-kulturele samewerking"},pd={login:"Teken In",register:"Registreer",logout:"Teken Uit",email:"E-pos Adres",password:"Wagwoord",confirmPassword:"Bevestig Wagwoord",name:"Volle Naam",forgotPassword:"Wagwoord Vergeet?",resetPassword:"Herstel Wagwoord",createAccount:"Skep Rekening",alreadyHaveAccount:"Het jy reeds ʼn rekening?",dontHaveAccount:"Het jy nie ʼn rekening nie?",signInWith:"Teken in met {{provider}}",signUpWith:"Registreer met {{provider}}",phoneNumber:"Telefoonnommer",verificationCode:"Verifikasiekode",sendCode:"Stuur Kode",verifyCode:"Verifieer Kode",welcomeBack:"Welkom terug!",welcomeToUbuntu:"Welkom by Ubuntu Connect!",culturalIdentity:{title:"Kulturele Identiteit",subtitle:"Deel jou kulturele erfenis (opsioneel)",description:"Help ons om jou met jou kulturele gemeenskap te verbind en kruis-kulturele leer moontlik te maak. Jy kan verskeie identiteite kies of hierdie stap oorslaan.",selectCultures:"Kies jou kulturele identiteite",skip:"Slaan nou oor",continue:"Gaan voort",multiple:"Jy kan verskeie kulture kies",optional:"Hierdie stap is heeltemal opsioneel",privacy:"Jou kulturele identiteit is by verstek privaat"}},gd={identities:{zulu:"Zulu",xhosa:"Xhosa",afrikaans:"Afrikaans",english:"Engels Suid-Afrikaans",sotho:"Sotho",tswana:"Tswana",tsonga:"Tsonga",venda:"Venda",swazi:"Swazi",ndebele:"Ndebele",indian:"Indiër Suid-Afrikaans",coloured:"Kleurling",other:"Ander"},connectionTypes:{heritage:"Kulturele Erfenis",interest:"Kulturele Belangstelling",learning:"Leer Oor",family:"Familie Verbinding",community:"Gemeenskapslid"}},md={home:"Tuis",communities:"Gemeenskappe",cultural:"Kulturele Erfenis",knowledge:"Kennis Uitruiling",collaboration:"Samewerk",achievements:"Prestasies",profile:"Profiel",settings:"Instellings"},yd={loading:"Laai...",error:"Fout",success:"Sukses",cancel:"Kanselleer",save:"Stoor",edit:"Redigeer",delete:"Skrap",confirm:"Bevestig",back:"Terug",next:"Volgende",previous:"Vorige",close:"Sluit",search:"Soek",filter:"Filter",sort:"Sorteer",view:"Bekyk",share:"Deel",like:"Hou van",comment:"Kommentaar",follow:"Volg",unfollow:"Ontvolg",join:"Sluit aan",leave:"Verlaat",optional:"Opsioneel",required:"Vereiste"},vd={required:"Hierdie veld is vereiste",email:"Voer asseblief ʼn geldige e-pos adres in",password:"Wagwoord moet ten minste 8 karakters wees",passwordMatch:"Wagwoorde stem nie ooreen nie",phoneNumber:"Voer asseblief ʼn geldige Suid-Afrikaanse telefoonnommer in",name:"Voer asseblief jou volle naam in"},wd={title:"Privaatheid & Data Beskerming",description:"Ons respekteer jou privaatheid en voldoen aan POPIA (Beskerming van Persoonlike Inligting Wet)",consent:"Ek stem in tot die versameling en verwerking van my persoonlike inligting",culturalConsent:"Ek stem in tot die deel van my kulturele identiteit vir gemeenskapsmatching",dataUsage:"Leer hoe ons jou data gebruik",rights:"Jou privaatheidregte",retention:"Data retensie beleid"},bd={app:hd,auth:pd,cultural:gd,navigation:md,common:yd,validation:vd,privacy:wd},kd={name:"Ubuntu Connect",tagline:"Sihlanganisa iNingizimu Afrika Ngokubambisana Kwamasiko",description:"Inkundla enomusa wamasiko eyenzelwe ukuhlanganisa iNingizimu Afrika ngokubambisana kwamasiko"},Sd={login:"Ngena",register:"Bhalisa",logout:"Phuma",email:"Ikheli le-imeyili",password:"Iphasiwedi",confirmPassword:"Qinisekisa Iphasiwedi",name:"Igama Eliphelele",forgotPassword:"Ukhohlwe iphasiwedi?",resetPassword:"Setha Kabusha Iphasiwedi",createAccount:"Dala i-Akhawunti",alreadyHaveAccount:"Usunayo i-akhawunti?",dontHaveAccount:"Awunayo i-akhawunti?",signInWith:"Ngena nge-{{provider}}",signUpWith:"Bhalisa nge-{{provider}}",phoneNumber:"Inombolo Yefoni",verificationCode:"Ikhodi Yokuqinisekisa",sendCode:"Thumela Ikhodi",verifyCode:"Qinisekisa Ikhodi",welcomeBack:"Sawubona futhi!",welcomeToUbuntu:"Sawubona ku-Ubuntu Connect!",culturalIdentity:{title:"Ubunikazi Bamasiko",subtitle:"Yabelana ngobunikazi bamasiko akho (okukhethekayo)",description:"Sisize sikuxhumanise nomphakathi wamasiko akho futhi sivumele ukufunda kwamasiko. Ungakhetha ubunikazi obuningi noma weqe lesi sinyathelo.",selectCultures:"Khetha ubunikazi bamasiko akho",skip:"Yeqa manje",continue:"Qhubeka",multiple:"Ungakhetha amasiko amaningi",optional:"Lesi sinyathelo sikhethekayo ngokuphelele",privacy:"Ubunikazi bamasiko akho buyimfihlo ngokuzenzakalela"}},xd={identities:{zulu:"IsiZulu",xhosa:"IsiXhosa",afrikaans:"Isi-Afrikaans",english:"IsiNgisi SaseNingizimu Afrika",sotho:"IsiSotho",tswana:"IsiTswana",tsonga:"IsiTsonga",venda:"IsiVenda",swazi:"IsiSwazi",ndebele:"IsiNdebele",indian:"AmaNdiya aseNingizimu Afrika",coloured:"AmaKhaladi",other:"Okunye"},connectionTypes:{heritage:"Ifa Lamasiko",interest:"Intshisekelo Yamasiko",learning:"Ukufunda Ngakho",family:"Ukuxhumana Komndeni",community:"Ilungu Lomphakathi"}},Id={home:"Ikhaya",communities:"Imiphakathi",cultural:"Ifa Lamasiko",knowledge:"Ukushintshana Kolwazi",collaboration:"Ukubambisana",achievements:"Izimpumelelo",profile:"Iphrofayili",settings:"Izilungiselelo"},Cd={loading:"Iyalayisha...",error:"Iphutha",success:"Impumelelo",cancel:"Khansela",save:"Londoloza",edit:"Hlela",delete:"Susa",confirm:"Qinisekisa",back:"Emuva",next:"Okulandelayo",previous:"Okwangaphambilini",close:"Vala",search:"Sesha",filter:"Hlunga",sort:"Hlela",view:"Buka",share:"Yabelana",like:"Uyakuthanda",comment:"Phawula",follow:"Landela",unfollow:"Yeka ukulandela",join:"Joyina",leave:"Suka",optional:"Okukhethekayo",required:"Okudingekayo"},_d={required:"Le nkambu iyadingeka",email:"Sicela ufake ikheli le-imeyili elivumelekile",password:"Iphasiwedi kufanele ibe nobumba obu-8 ubuncane",passwordMatch:"Amaphasiwedi awafani",phoneNumber:"Sicela ufake inombolo yefoni yaseNingizimu Afrika evumelekile",name:"Sicela ufake igama lakho eliphelele"},Pd={title:"Ubumfihlo & Ukuvikelwa Kwedatha",description:"Siyabuhlonipha ubumfihlo bakho futhi siyalandela i-POPIA (Umthetho Wokuvikela Ulwazi Lomuntu Siqu)",consent:"Ngiyavuma ukuqoqwa nokucutshungulwa kolwazi lwami lomuntu siqu",culturalConsent:"Ngiyavuma ukwabelana ngobunikazi bamasiko ami ukuze kufaniswe umphakathi",dataUsage:"Funda ukuthi sisebenzisa kanjani idatha yakho",rights:"Amalungelo akho obumfihlo",retention:"Inqubomgomo yokugcina idatha"},Ed={app:kd,auth:Sd,cultural:xd,navigation:Id,common:Cd,validation:_d,privacy:Pd},Rd={name:"Ubuntu Connect",tagline:"Ukudibanisa uMzantsi Afrika Ngentsebenziswano Yenkcubeko",description:"Iqonga elinovelwano lwenkcubeko eliyilelwe ukudibanisa uMzantsi Afrika ngentsebenziswano yenkcubeko"},Ad={login:"Ngena",register:"Bhalisa",logout:"Phuma",email:"Idilesi ye-imeyili",password:"Iphaswedi",confirmPassword:"Qinisekisa iPhaswedi",name:"Igama Elipheleleyo",forgotPassword:"Ulibale iphaswedi?",resetPassword:"Phinda Usete iPhaswedi",createAccount:"Yenza i-Akhawunti",alreadyHaveAccount:"Unayo i-akhawunti?",dontHaveAccount:"Awunayo i-akhawunti?",signInWith:"Ngena nge-{{provider}}",signUpWith:"Bhalisa nge-{{provider}}",phoneNumber:"Inombolo Yomnxeba",verificationCode:"Ikhowudi Yokuqinisekisa",sendCode:"Thumela iKhowudi",verifyCode:"Qinisekisa iKhowudi",welcomeBack:"Wamkelekile kwakhona!",welcomeToUbuntu:"Wamkelekile ku-Ubuntu Connect!",culturalIdentity:{title:"Isazisi Senkcubeko",subtitle:"Yabelana ngelifa lenkcubeko yakho (okukhethwayo)",description:"Sincede sikudibanise noluntu lwenkcubeko yakho kwaye sivumele ukufunda kwenkcubeko. Ungakhetha izazisi ezininzi okanye utsibe eli nyathelo.",selectCultures:"Khetha izazisi zenkcubeko yakho",skip:"Tsiba ngoku",continue:"Qhubeka",multiple:"Ungakhetha iinkcubeko ezininzi",optional:"Eli nyathelo likhethwa ngokupheleleyo",privacy:"Isazisi senkcubeko yakho siyimfihlo ngokungagqibekanga"}},Nd={identities:{zulu:"IsiZulu",xhosa:"IsiXhosa",afrikaans:"Isi-Afrikaans",english:"IsiNgesi SaseMzantsi Afrika",sotho:"IsiSotho",tswana:"IsiTswana",tsonga:"IsiTsonga",venda:"IsiVenda",swazi:"IsiSwazi",ndebele:"IsiNdebele",indian:"AmaNdiya aseMzantsi Afrika",coloured:"AmaKhaladi",other:"Okunye"},connectionTypes:{heritage:"Ilifa Lenkcubeko",interest:"Umdla Wenkcubeko",learning:"Ukufunda Ngako",family:"Unxibelelwano Losapho",community:"Ilungu Loluntu"}},Od={home:"Ikhaya",communities:"Uluntu",cultural:"Ilifa Lenkcubeko",knowledge:"Ukutshintshiselana Ngolwazi",collaboration:"Intsebenziswano",achievements:"Izinto Eziphunyeziweyo",profile:"Iprofayile",settings:"Iisetingi"},Td={loading:"Iyalayisha...",error:"Impazamo",success:"Impumelelo",cancel:"Rhoxisa",save:"Gcina",edit:"Hlela",delete:"Cima",confirm:"Qinisekisa",back:"Emva",next:"Okulandelayo",previous:"Okwangaphambili",close:"Vala",search:"Khangela",filter:"Hlula",sort:"Hlela",view:"Jonga",share:"Yabelana",like:"Uyayithanda",comment:"Phawula",follow:"Landela",unfollow:"Yeka ukulandela",join:"Ngenela",leave:"Suka",optional:"Okukhethwayo",required:"Okufunekayo"},Ld={required:"Le ntsimi iyafuneka",email:"Nceda ufake idilesi ye-imeyili esebenzayo",password:"Iphaswedi kufuneka ibe neempawu ezi-8 ubuncinci",passwordMatch:"Iiphaswedi azifani",phoneNumber:"Nceda ufake inombolo yomnxeba yaseMzantsi Afrika esebenzayo",name:"Nceda ufake igama lakho elipheleleyo"},Ud={title:"Ubumfihlo & Ukukhuselwa Kwedatha",description:"Siyabuhlonipha ubumfihlo bakho kwaye siyayilandela i-POPIA (Umthetho Wokukhusela Ulwazi Lomntu)",consent:"Ndiyavuma ukuqokelelwa nokucutshungulwa kolwazi lwam lomntu",culturalConsent:"Ndiyavuma ukwabelana ngesazisi senkcubeko yam ukuze kudityaniswe uluntu",dataUsage:"Funda indlela esisebenzisa ngayo idatha yakho",rights:"Amalungelo akho obumfihlo",retention:"Umgaqo-nkqubo wokugcina idatha"},Dd={app:Rd,auth:Ad,cultural:Nd,navigation:Od,common:Td,validation:Ld,privacy:Ud},$d={en:{translation:fd},af:{translation:bd},zu:{translation:Ed},xh:{translation:Dd}};N.use(Li).use(xa).init({resources:$d,fallbackLng:"en",debug:!1,detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"],lookupLocalStorage:"ubuntu-connect-language"},interpolation:{escapeValue:!1},returnObjects:!0,returnEmptyString:!1,defaultNS:"translation",ns:["translation","cultural","auth","community"],react:{useSuspense:!1,bindI18n:"languageChanged loaded",bindI18nStore:"added removed",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transKeepBasicHtmlNodesFor:["br","strong","i","em"]}});const jd=Object.freeze(Object.defineProperty({__proto__:null,default:N},Symbol.toStringTag,{value:"Module"}));_t.createRoot(document.getElementById("root")).render(b.jsx(A.StrictMode,{children:b.jsx(yc,{})}));export{zt as B,Ci as C,xi as a,_i as b,H as c,Pi as d,ac as e,Ei as f,Ks as g,Wd as h,Q as i,b as j,Bd as k,Vd as l,Hd as r,Kd as s,Pa as u};
//# sourceMappingURL=index-nwrMOwxu.js.map
