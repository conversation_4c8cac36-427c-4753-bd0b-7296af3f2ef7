import{i as p,r as Q,s as J,k as W,l as X,a as O,j as e}from"./index-nwrMOwxu.js";import{r as u}from"./vendor-DtOhX2xw.js";import{p as E,q as w,T as v,B,I as q,J as M,K as A,M as S,N as I,O as H,P as $,y as _}from"./firebase-DLuFXYhP.js";import{c as V}from"./culturalValidationService-CA9WZNCm.js";class Z{async createHeritageContent(t){try{const s=`heritage-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,a=await V.validateCulturalContent({id:s,title:t.title,content:t.description,type:t.type,culture:t.culturalGroup[0],media:t.media,author:{userId:t.metadata.creator,culturalCredibility:"community_member"},verification:{status:"pending",reviewedBy:[],reviewNotes:[],approvedAt:null},engagement:{views:0,likes:0,shares:0,comments:0,crossCulturalViews:0},createdAt:t.metadata.dateCreated,lastModified:t.metadata.lastModified}),n={...t,id:s,validation:{status:a.requiresReview?"pending_review":"draft",reviewedBy:[],reviewNotes:a.concerns,communityEndorsements:0,accuracyScore:0},engagement:{views:0,likes:0,shares:0,comments:0,culturalLearningInteractions:0}};return await E(w(p,"cultural-heritage",s),n),a.requiresReview&&await this.notifyCulturalRepresentatives(t.culturalGroup,s),s}catch(s){throw console.error("Error creating heritage content:",s),new Error("Failed to create heritage content")}}async uploadMediaFile(t,s,a){try{const n=`${s}-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,l=Q(J,`cultural-heritage/${a}/${n}`),c=await W(l,t),h=await X(c.ref),y={id:n,url:h,filename:t.name,size:t.size,type:t.type,uploadedAt:v.now(),metadata:{originalName:t.name,uploadedBy:"current-user"}},o=w(p,"cultural-heritage",s);return await B(o,{[`media.${a}`]:q(y),"metadata.lastModified":v.now()}),y}catch(n){throw console.error("Error uploading media file:",n),new Error("Failed to upload media file")}}async createOralTraditionStory(t){try{const s=`story-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,a={...t,id:s};await E(w(p,"oral-traditions",s),a);const n={title:t.title,description:t.story.transcription,type:"story",culturalGroup:[t.culturalGroup],region:"south-africa",media:{images:[],videos:t.story.videoRecording?[t.story.videoRecording]:[],audio:t.story.audioRecording?[t.story.audioRecording]:[],documents:[]},metadata:{creator:t.storyteller.userId,contributors:[],dateCreated:v.now(),lastModified:v.now(),provenance:`Oral tradition from ${t.culturalGroup}`,culturalSignificance:t.culturalContext.appropriateAudience==="all"?"public":"community_only",tags:["oral-tradition","story",t.culturalGroup],language:t.preservation.originalLanguage,region:"south-africa"},validation:{status:"pending_review",reviewedBy:[],reviewNotes:[],communityEndorsements:0,accuracyScore:t.preservation.historicalAccuracy},preservation:{archivalQuality:!0,backupLocations:[],digitalPreservationMetadata:{originalLanguage:t.preservation.originalLanguage,dialectVariations:t.preservation.dialectVariations,culturalAuthenticity:t.preservation.culturalAuthenticity},culturalContextPreserved:!0}};return await this.createHeritageContent(n),s}catch(s){throw console.error("Error creating oral tradition story:",s),new Error("Failed to create oral tradition story")}}async getHeritageContentByCulture(t,s=20){try{const a=M(A(p,"cultural-heritage"),S("culturalGroup","array-contains",t),S("validation.status","==","approved"),I("engagement.views","desc"),H(s));return(await $(a)).docs.map(l=>l.data())}catch(a){throw console.error("Error getting heritage content by culture:",a),new Error("Failed to get heritage content")}}async approveHeritageContent(t,s,a){try{const n=w(p,"cultural-heritage",t);await B(n,{"validation.status":"approved","validation.reviewedBy":q(s),"validation.reviewNotes":a?q(a):[],"metadata.culturalRepresentativeApproval":s,"metadata.lastModified":v.now()})}catch(n){throw console.error("Error approving heritage content:",n),new Error("Failed to approve heritage content")}}async searchHeritageContent(t,s){try{let a=M(A(p,"cultural-heritage"),S("validation.status","==","approved"));return s?.type&&(a=M(a,S("type","==",s.type))),s?.culturalGroup&&(a=M(a,S("culturalGroup","array-contains",s.culturalGroup))),(await $(a)).docs.map(c=>c.data()).filter(c=>c.title.toLowerCase().includes(t.toLowerCase())||c.description.toLowerCase().includes(t.toLowerCase())||c.metadata.tags.some(h=>h.toLowerCase().includes(t.toLowerCase())))}catch(a){throw console.error("Error searching heritage content:",a),new Error("Failed to search heritage content")}}async notifyCulturalRepresentatives(t,s){console.log(`Notifying cultural representatives for groups: ${t.join(", ")} about content: ${s}`)}}const K=new Z,ee=({onContentCreated:f,onCancel:t})=>{const{user:s}=O(),[a,n]=u.useState(1),[l,c]=u.useState(!1),[h,y]=u.useState({}),[o,m]=u.useState({title:"",description:"",type:"story",culturalGroup:[],region:"",provenance:"",culturalSignificance:"public",tags:[],language:"en"}),[g,d]=u.useState({images:[],videos:[],audio:[],documents:[]}),N={images:u.useRef(null),videos:u.useRef(null),audio:u.useRef(null),documents:u.useRef(null)},L=["zulu","xhosa","afrikaans","english","sotho","tswana","tsonga","swati","venda","ndebele","coloured"],D=[{value:"story",label:"Story/Oral Tradition"},{value:"tradition",label:"Cultural Tradition"},{value:"artifact",label:"Cultural Artifact"},{value:"recipe",label:"Traditional Recipe"},{value:"music",label:"Music/Song"},{value:"dance",label:"Dance/Performance"},{value:"language",label:"Language/Dialect"},{value:"history",label:"Historical Account"}],C=(r,x)=>{m(b=>({...b,[r]:x}))},R=(r,x)=>{if(!x)return;const b=Array.from(x);d(k=>({...k,[r]:[...k[r],...b]}))},i=(r,x)=>{d(b=>({...b,[r]:b[r].filter((k,z)=>z!==x)}))},j=async()=>{if(s){c(!0);try{const r={title:o.title,description:o.description,type:o.type,culturalGroup:o.culturalGroup,region:o.region,media:{images:[],videos:[],audio:[],documents:[]},metadata:{creator:s.uid,contributors:[],dateCreated:v.now(),lastModified:v.now(),provenance:o.provenance,culturalSignificance:o.culturalSignificance,tags:o.tags,language:o.language,region:o.region},validation:{status:"draft",reviewedBy:[],reviewNotes:[],communityEndorsements:0,accuracyScore:0},preservation:{archivalQuality:!0,backupLocations:[],digitalPreservationMetadata:{},culturalContextPreserved:!0}},x=await K.createHeritageContent(r),b=Object.keys(g);for(const k of b){const z=g[k];for(let F=0;F<z.length;F++){const Y=z[F];y(T=>({...T,[`${k}-${F}`]:0}));try{await K.uploadMediaFile(Y,x,k),y(T=>({...T,[`${k}-${F}`]:100}))}catch(T){console.error(`Error uploading ${k} file:`,T)}}}f?.(x)}catch(r){console.error("Error creating cultural content:",r),alert("Failed to create cultural content. Please try again.")}finally{c(!1)}}},P=()=>{switch(a){case 1:return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Basic Information"}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),e.jsx("input",{type:"text",value:o.title,onChange:r=>C("title",r.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500",placeholder:"Enter a descriptive title",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Content Type *"}),e.jsx("select",{value:o.type,onChange:r=>C("type",r.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500",required:!0,children:D.map(r=>e.jsx("option",{value:r.value,children:r.label},r.value))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cultural Group(s) *"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:L.map(r=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:o.culturalGroup.includes(r),onChange:x=>{x.target.checked?C("culturalGroup",[...o.culturalGroup,r]):C("culturalGroup",o.culturalGroup.filter(b=>b!==r))},className:"mr-2"}),e.jsx("span",{className:"text-sm capitalize",children:r})]},r))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description *"}),e.jsx("textarea",{value:o.description,onChange:r=>C("description",r.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500",placeholder:"Provide a detailed description of this cultural content",required:!0})]})]});case 2:return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cultural Context"}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cultural Significance"}),e.jsxs("select",{value:o.culturalSignificance,onChange:r=>C("culturalSignificance",r.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500",children:[e.jsx("option",{value:"public",children:"Public - Can be shared openly"}),e.jsx("option",{value:"community_only",children:"Community Only - Restricted to cultural community"}),e.jsx("option",{value:"sacred",children:"Sacred - Requires special permissions"}),e.jsx("option",{value:"restricted",children:"Restricted - Limited access"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Source/Provenance *"}),e.jsx("textarea",{value:o.provenance,onChange:r=>C("provenance",r.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500",placeholder:"Describe the source and historical context of this content",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tags (comma-separated)"}),e.jsx("input",{type:"text",value:o.tags.join(", "),onChange:r=>C("tags",r.target.value.split(",").map(x=>x.trim()).filter(Boolean)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500",placeholder:"tradition, heritage, history, etc."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Primary Language"}),e.jsxs("select",{value:o.language,onChange:r=>C("language",r.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"af",children:"Afrikaans"}),e.jsx("option",{value:"zu",children:"Zulu"}),e.jsx("option",{value:"xh",children:"Xhosa"}),e.jsx("option",{value:"st",children:"Sotho"}),e.jsx("option",{value:"tn",children:"Tswana"}),e.jsx("option",{value:"ts",children:"Tsonga"}),e.jsx("option",{value:"ss",children:"Swati"}),e.jsx("option",{value:"ve",children:"Venda"}),e.jsx("option",{value:"nr",children:"Ndebele"})]})]})]});case 3:return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Media Files"}),Object.entries(N).map(([r,x])=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"font-medium capitalize",children:r}),e.jsxs("button",{type:"button",onClick:()=>x.current?.click(),className:"px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600",children:["Add ",r]})]}),e.jsx("input",{ref:x,type:"file",multiple:!0,accept:r==="images"?"image/*":r==="videos"?"video/*":r==="audio"?"audio/*":".pdf,.doc,.docx,.txt",onChange:b=>R(r,b.target.files),className:"hidden"}),e.jsx("div",{className:"space-y-2",children:g[r].map((b,k)=>e.jsxs("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded",children:[e.jsx("span",{className:"text-sm truncate",children:b.name}),e.jsx("button",{type:"button",onClick:()=>i(r,k),className:"text-red-500 hover:text-red-700 text-sm",children:"Remove"})]},k))})]},r))]});default:return null}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Share Cultural Heritage"}),e.jsx("p",{className:"text-gray-600",children:"Contribute to preserving and sharing South African cultural heritage with respect and authenticity."})]}),e.jsx("div",{className:"flex items-center justify-between mb-8",children:[1,2,3].map(r=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${r<=a?"bg-orange-500 text-white":"bg-gray-200 text-gray-600"}`,children:r}),r<3&&e.jsx("div",{className:`w-16 h-1 mx-2 ${r<a?"bg-orange-500":"bg-gray-200"}`})]},r))}),P(),e.jsxs("div",{className:"flex justify-between mt-8",children:[e.jsx("div",{children:a>1&&e.jsx("button",{type:"button",onClick:()=>n(r=>r-1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",disabled:l,children:"Previous"})}),e.jsxs("div",{className:"space-x-3",children:[e.jsx("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",disabled:l,children:"Cancel"}),a<3?e.jsx("button",{type:"button",onClick:()=>n(r=>r+1),className:"px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600",disabled:!o.title||!o.description||o.culturalGroup.length===0,children:"Next"}):e.jsx("button",{type:"button",onClick:j,disabled:l||!o.title||!o.description||o.culturalGroup.length===0,className:"px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 disabled:opacity-50",children:l?"Creating...":"Create Content"})]})]}),e.jsxs("div",{className:"mt-6 p-4 bg-orange-50 border border-orange-200 rounded-md",children:[e.jsx("h4",{className:"font-medium text-orange-800 mb-2",children:"Cultural Sensitivity Guidelines"}),e.jsxs("ul",{className:"text-sm text-orange-700 space-y-1",children:[e.jsx("li",{children:"• Ensure you have the right to share this cultural content"}),e.jsx("li",{children:"• Respect sacred and sensitive cultural information"}),e.jsx("li",{children:"• Provide accurate historical and cultural context"}),e.jsx("li",{children:"• Content will be reviewed by cultural representatives"}),e.jsx("li",{children:"• Follow Ubuntu principles of mutual respect and community benefit"})]})]})]})};class te{async updateSkillProfile(t){try{await E(w(p,"skill-profiles",t.userId),t)}catch(s){throw console.error("Error updating skill profile:",s),new Error("Failed to update skill profile")}}async findMentorshipMatches(t,s,a){try{const n=await this.getSkillProfile(t);if(!n)throw new Error("User profile not found");const l=M(A(p,"skill-profiles"),S(`skills.${s}`,"!=",null),H(20)),h=(await $(l)).docs.map(o=>o.data()).filter(o=>o.userId!==t),y=[];for(const o of h){const m=this.calculateMentorshipMatchScore(n,o,s,a);if(m>60){const g={id:`match-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,mentorId:a==="mentor"?t:o.userId,menteeId:a==="mentee"?t:o.userId,skillArea:s,culturalContext:this.findCommonCulturalContext(n,o),matchScore:m,status:"pending",goals:{skillGoals:[],culturalLearningGoals:[],timeframe:"3 months",successMetrics:[]},progress:{sessionsCompleted:0,skillProgress:0,culturalUnderstanding:0,mutualLearning:[]},communication:{preferredMethod:"video",frequency:"weekly",language:"en",translationNeeded:!1},createdAt:v.now(),lastActivity:v.now()};y.push(g)}}return y.sort((o,m)=>m.matchScore-o.matchScore)}catch(n){throw console.error("Error finding mentorship matches:",n),new Error("Failed to find mentorship matches")}}async createCollaborativeProject(t){try{const s=`project-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,a={...t,id:s,createdAt:v.now(),lastActivity:v.now()};return await E(w(p,"collaborative-projects",s),a),s}catch(s){throw console.error("Error creating collaborative project:",s),new Error("Failed to create collaborative project")}}async joinCollaborativeProject(t,s,a,n,l){try{const c=w(p,"collaborative-projects",t);await B(c,{[`participants.${s}`]:{role:a,skills:n,culturalBackground:l,joinedAt:v.now(),contributionScore:0},lastActivity:v.now()})}catch(c){throw console.error("Error joining collaborative project:",c),new Error("Failed to join collaborative project")}}async createCulturalLearningPath(t){try{const s=`path-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,a={...t,id:s,engagement:{enrollments:0,completions:0,averageRating:0,culturalFeedback:[]}};return await E(w(p,"cultural-learning-paths",s),a),s}catch(s){throw console.error("Error creating cultural learning path:",s),new Error("Failed to create cultural learning path")}}async getSkillProfile(t){try{const s=w(p,"skill-profiles",t),a=await _(s);return a.exists()?a.data():null}catch(s){throw console.error("Error getting skill profile:",s),new Error("Failed to get skill profile")}}async getUserCollaborativeProjects(t){try{const s=M(A(p,"collaborative-projects"),S(`participants.${t}`,"!=",null),I("lastActivity","desc"));return(await $(s)).docs.map(n=>n.data())}catch(s){throw console.error("Error getting user collaborative projects:",s),new Error("Failed to get user collaborative projects")}}async getCulturalLearningPaths(t){try{let s=M(A(p,"cultural-learning-paths"),I("engagement.enrollments","desc"));return t&&(s=M(s,S("culturalGroup","==",t))),(await $(s)).docs.map(n=>n.data())}catch(s){throw console.error("Error getting cultural learning paths:",s),new Error("Failed to get cultural learning paths")}}calculateMentorshipMatchScore(t,s,a,n){let l=0;const c=t.skills[a],h=s.skills[a];if(c&&h){const g=["beginner","intermediate","advanced","expert"],d=g.indexOf(c.level),N=g.indexOf(h.level);(n==="mentor"&&d>N||n==="mentee"&&N>d)&&(l+=30)}const y=Object.keys(t.culturalKnowledge).filter(g=>s.culturalKnowledge[g]);l+=Math.min(y.length*10,30);const o=Math.abs(t.ubuntuPhilosophy.beliefLevel-s.ubuntuPhilosophy.beliefLevel);l+=Math.max(0,20-o/5);const m=t.availability.languages.filter(g=>s.availability.languages.includes(g));return l+=Math.min(m.length*5,20),Math.min(l,100)}findCommonCulturalContext(t,s){const a=Object.keys(t.culturalKnowledge).filter(n=>s.culturalKnowledge[n]);return a.length>0?a[0]:void 0}}const U=new te,re=({onMentorshipRequest:f})=>{const{user:t}=O(),[s,a]=u.useState("browse"),[n,l]=u.useState([]),[c,h]=u.useState([]),[y,o]=u.useState(""),[m,g]=u.useState(""),[d,N]=u.useState(!1),L=["Technology","Business","Arts & Crafts","Languages","Cooking","Music","Traditional Skills","Agriculture","Healthcare","Education"],D=["Traditional Beadwork","Pottery Making","Traditional Cooking","Storytelling","Traditional Music","Dance","Language Teaching","Cultural History","Traditional Medicine Knowledge","Craft Making"];u.useEffect(()=>{t&&C()},[t]);const C=async()=>{if(t){N(!0);try{const r=await U.getSkillProfile(t.uid);if(r){const x=Object.keys(r.skills);if(x.length>0){const b=await U.findMentorshipMatches(t.uid,x[0],"mentor");h(b)}}}catch(r){console.error("Error loading user data:",r)}finally{N(!1)}}},R=async(r,x)=>{if(!(!t||!r)){N(!0);try{const b=await U.findMentorshipMatches(t.uid,r,x);h(b)}catch(b){console.error("Error searching for mentorship matches:",b)}finally{N(!1)}}},i=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx("input",{type:"text",placeholder:"Search skills, cultural knowledge, or expertise...",value:m,onChange:r=>g(r.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"})}),e.jsxs("select",{value:y,onChange:r=>o(r.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500",children:[e.jsx("option",{value:"",children:"All Skills"}),L.map(r=>e.jsx("option",{value:r,children:r},r))]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"col-span-full",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Featured Cultural Skills"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-3",children:D.map(r=>e.jsx("button",{onClick:()=>R(r,"mentee"),className:"p-3 bg-orange-50 border border-orange-200 rounded-lg text-sm text-orange-800 hover:bg-orange-100 transition-colors",children:r},r))})]}),c.map(r=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900",children:r.skillArea}),r.culturalContext&&e.jsxs("span",{className:"text-sm text-orange-600 bg-orange-50 px-2 py-1 rounded mt-1 inline-block",children:[r.culturalContext," Cultural Context"]})]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-sm font-medium text-green-600",children:[r.matchScore,"% Match"]})})]}),e.jsxs("div",{className:"space-y-2 mb-4",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Goals:"})," ",r.goals.skillGoals.join(", ")||"Not specified"]}),r.goals.culturalLearningGoals.length>0&&e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Cultural Learning:"})," ",r.goals.culturalLearningGoals.join(", ")]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Communication:"})," ",r.communication.preferredMethod," • ",r.communication.frequency]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>f?.(r.id),className:"flex-1 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm",children:"Connect"}),e.jsx("button",{className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:"Learn More"})]})]},r.id))]})]}),j=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-orange-800 mb-2",children:"Share Your Knowledge"}),e.jsx("p",{className:"text-orange-700 mb-4",children:"Embody Ubuntu philosophy by sharing your skills and cultural knowledge with others. Help build bridges across communities through mentorship."}),e.jsx("button",{className:"px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors",children:"Set Up Mentor Profile"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-3",children:"Skills You Can Teach"}),e.jsx("div",{className:"space-y-2",children:L.slice(0,5).map(r=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-700",children:r}),e.jsx("button",{onClick:()=>R(r,"mentor"),className:"text-sm text-orange-600 hover:text-orange-800",children:"Find Learners"})]},r))})]}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-3",children:"Cultural Knowledge"}),e.jsx("div",{className:"space-y-2",children:D.slice(0,5).map(r=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-700",children:r}),e.jsx("button",{onClick:()=>R(r,"mentor"),className:"text-sm text-orange-600 hover:text-orange-800",children:"Share Knowledge"})]},r))})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Your Active Mentorships"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:c.filter(r=>r.status==="active").map(r=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:r.skillArea}),e.jsx("span",{className:"text-sm text-green-600 bg-green-50 px-2 py-1 rounded",children:"Active"})]}),e.jsxs("div",{className:"text-sm text-gray-600 mb-3",children:["Progress: ",r.progress.skillProgress,"% • ",r.progress.sessionsCompleted," sessions"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"flex-1 px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600",children:"Schedule Session"}),e.jsx("button",{className:"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50",children:"View Progress"})]})]},r.id))})]})]}),P=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"Learn & Grow"}),e.jsx("p",{className:"text-blue-700 mb-4",children:"Discover new skills and deepen your cultural understanding through mentorship and collaborative learning experiences."}),e.jsx("button",{className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"Set Learning Goals"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-3",children:"Skills to Learn"}),e.jsx("div",{className:"space-y-2",children:L.map(r=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-700",children:r}),e.jsx("button",{onClick:()=>R(r,"mentee"),className:"text-sm text-blue-600 hover:text-blue-800",children:"Find Mentors"})]},r))})]}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-3",children:"Cultural Learning"}),e.jsx("div",{className:"space-y-2",children:D.map(r=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-700",children:r}),e.jsx("button",{onClick:()=>R(r,"mentee"),className:"text-sm text-blue-600 hover:text-blue-800",children:"Learn More"})]},r))})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Your Learning Journey"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:c.filter(r=>r.status==="active").map(r=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:r.skillArea}),e.jsx("span",{className:"text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded",children:"Learning"})]}),e.jsxs("div",{className:"mb-3",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[e.jsx("span",{children:"Progress"}),e.jsxs("span",{children:[r.progress.skillProgress,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${r.progress.skillProgress}%`}})})]}),e.jsxs("div",{className:"text-sm text-gray-600 mb-3",children:[r.progress.sessionsCompleted," sessions completed"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"flex-1 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600",children:"Continue Learning"}),e.jsx("button",{className:"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50",children:"View Details"})]})]},r.id))})]})]});return e.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Knowledge Sharing Marketplace"}),e.jsx("p",{className:"text-gray-600",children:'Connect, learn, and share knowledge across cultural boundaries. Embrace Ubuntu: "I am because we are."'})]}),e.jsx("div",{className:"border-b border-gray-200 mb-6",children:e.jsx("nav",{className:"flex space-x-8",children:[{id:"browse",label:"Browse Skills",icon:"🔍"},{id:"mentor",label:"Mentor Others",icon:"🎓"},{id:"learn",label:"Learn & Grow",icon:"📚"}].map(r=>e.jsxs("button",{onClick:()=>a(r.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${s===r.id?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx("span",{className:"mr-2",children:r.icon}),r.label]},r.id))})}),d?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"})}):e.jsxs(e.Fragment,{children:[s==="browse"&&i(),s==="mentor"&&j(),s==="learn"&&P()]})]})};class se{async generatePersonalizedRecommendations(t,s=10){try{const a=await this.getPersonalizedDashboard(t);if(!a)throw new Error("User dashboard not found");const n=[];for(const o of a.preferences.culturalInterests){const m=await this.getContentByCulture(o,3);for(const g of m){const d=await this.createContentRecommendation(t,g.id,"heritage","cultural_interest",o);n.push(d)}}const l=await this.generateLearningRecommendations(t,a);n.push(...l);const c=await this.generateTrendingRecommendations(t,3);n.push(...c);const h=await this.generateBridgeBuildingRecommendations(t,2);n.push(...h);const y=n.sort((o,m)=>m.score-o.score).slice(0,s);return await this.saveRecommendationsToUser(t,y),y}catch(a){throw console.error("Error generating personalized recommendations:",a),new Error("Failed to generate personalized recommendations")}}async discoverTrendingTopics(t,s){try{let a=M(A(p,"trending-topics"),S("moderation.verified","==",!0),I("trendMetrics.growthRate","desc"),H(20));return t&&(a=M(a,S("region","==",t))),s&&(a=M(a,S("culturalGroup","==",s))),(await $(a)).docs.map(l=>l.data())}catch(a){throw console.error("Error discovering trending topics:",a),new Error("Failed to discover trending topics")}}async moderateContent(t,s,a){try{const n=await this.performAIModeration(a),l=await this.analyzeCulturalSensitivity(a),c=n.culturalSensitivity.appropriationRisk>30||l.requiresReview||a.metadata?.culturalSignificance==="sacred",h={contentId:t,moderationType:"ai_initial",result:c?"needs_cultural_review":n.isValid?"approved":"needs_revision",concerns:n.concerns,recommendations:n.recommendations,culturalSensitivity:{appropriationRisk:n.culturalSensitivity.appropriationRisk,culturalAccuracy:l.accuracyScore,respectfulPresentation:n.culturalSensitivity.respectfulPresentation,communityBenefit:this.calculateCommunityBenefit(a)},moderatedAt:v.now()};return await E(w(p,"content-moderation",t),h),c&&await this.notifyCulturalRepresentatives(a.culturalGroup||[],t),h}catch(n){throw console.error("Error moderating content:",n),new Error("Failed to moderate content")}}async getPersonalizedDashboard(t){try{const s=w(p,"personalized-dashboards",t),a=await _(s);if(a.exists())return a.data();const n={userId:t,culturalLearningProgress:{},contentInteractions:{viewedContent:[],likedContent:[],sharedContent:[],createdContent:[],bookmarkedContent:[]},bridgeBuildingMetrics:{crossCulturalConnections:0,culturalBridgeScore:0,mentorshipParticipation:0,collaborativeProjects:0,communityContributions:0},recommendations:{dailyRecommendations:[],weeklyGoals:[],culturalChallenges:[],learningOpportunities:[]},preferences:{contentTypes:["heritage","story","tradition"],culturalInterests:[],learningGoals:[],notificationSettings:{}},lastUpdated:v.now()};return await E(s,n),n}catch(s){throw console.error("Error getting personalized dashboard:",s),new Error("Failed to get personalized dashboard")}}async updateContentInteraction(t,s,a){try{const n=w(p,"personalized-dashboards",t);await B(n,{[`contentInteractions.${a==="view"?"viewedContent":a==="like"?"likedContent":a==="share"?"sharedContent":"bookmarkedContent"}`]:q(s),lastUpdated:v.now()}),await this.updateContentEngagement(s,a)}catch(n){throw console.error("Error updating content interaction:",n),new Error("Failed to update content interaction")}}async verifyContentAccuracy(t,s,a,n){try{const l=w(p,"content-verifications",`${t}-${s}`);await E(l,{contentId:t,verifierId:s,accuracyScore:a,notes:n||"",verifiedAt:v.now(),verifierType:"cultural_representative"}),await this.updateContentAccuracyScore(t)}catch(l){throw console.error("Error verifying content accuracy:",l),new Error("Failed to verify content accuracy")}}async getContentByCulture(t,s){try{const a=M(A(p,"cultural-heritage"),S("culturalGroup","array-contains",t),S("validation.status","==","approved"),I("engagement.views","desc"),H(s));return(await $(a)).docs.map(l=>l.data())}catch(a){return console.error("Error getting content by culture:",a),[]}}async createContentRecommendation(t,s,a,n,l){return{id:`rec-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,userId:t,contentId:s,contentType:a,recommendationType:n,score:Math.floor(Math.random()*40)+60,reasoning:{primaryFactors:[`Matches your interest in ${l}`],culturalRelevance:[`Related to ${l} culture`],learningOpportunities:["Cultural understanding","Cross-cultural awareness"],bridgeBuildingPotential:["Connect with community members"]},culturalSensitivity:{appropriateForUser:!0,requiresContext:!1,respectfulApproach:["Approach with openness and respect"]},createdAt:v.now(),expiresAt:v.fromDate(new Date(Date.now()+7*24*60*60*1e3)),status:"pending"}}async performAIModeration(t){return{isValid:!0,concerns:[],recommendations:[],culturalSensitivity:{appropriationRisk:Math.floor(Math.random()*30),respectfulPresentation:Math.floor(Math.random()*20)+80}}}async analyzeCulturalSensitivity(t){return V.validateCulturalContent(t)}calculateCommunityBenefit(t){return Math.floor(Math.random()*20)+80}async generateLearningRecommendations(t,s){return[]}async generateTrendingRecommendations(t,s){return[]}async generateBridgeBuildingRecommendations(t,s){return[]}async saveRecommendationsToUser(t,s){const a=w(p,"personalized-dashboards",t);await B(a,{"recommendations.dailyRecommendations":s,lastUpdated:v.now()})}async updateContentEngagement(t,s){}async updateContentAccuracyScore(t){}async notifyCulturalRepresentatives(t,s){console.log(`Notifying cultural representatives for ${t.join(", ")} about content ${s}`)}}const G=new se,ae=({onContentSelect:f})=>{const{user:t}=O(),[s,a]=u.useState(null),[n,l]=u.useState([]),[c,h]=u.useState([]),[y,o]=u.useState(!0),[m,g]=u.useState("overview");u.useEffect(()=>{t&&d()},[t]);const d=async()=>{if(t){o(!0);try{const[i,j,P]=await Promise.all([G.getPersonalizedDashboard(t.uid),G.generatePersonalizedRecommendations(t.uid,8),G.discoverTrendingTopics("south-africa")]);a(i),l(j),h(P)}catch(i){console.error("Error loading dashboard data:",i)}finally{o(!1)}}},N=async(i,j)=>{if(t)try{if(await G.updateContentInteraction(t.uid,i,j),j==="like"||j==="bookmark"){const P=await G.generatePersonalizedRecommendations(t.uid,8);l(P)}}catch(P){console.error("Error updating content interaction:",P)}},L=()=>e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Cultural Learning Progress"}),e.jsx("div",{className:"space-y-4",children:s&&Object.entries(s.culturalLearningProgress).slice(0,3).map(([i,j])=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"font-medium capitalize",children:i}),e.jsxs("span",{className:"text-gray-600",children:[j.level,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-orange-500 h-2 rounded-full",style:{width:`${j.level}%`}})}),e.jsxs("div",{className:"text-xs text-gray-500",children:[j.completedPaths.length," paths completed • ",j.achievements.length," achievements"]})]},i))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Bridge Building Impact"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Cross-Cultural Connections"}),e.jsx("span",{className:"text-lg font-semibold text-orange-600",children:s?.bridgeBuildingMetrics.crossCulturalConnections||0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Cultural Bridge Score"}),e.jsxs("span",{className:"text-lg font-semibold text-green-600",children:[s?.bridgeBuildingMetrics.culturalBridgeScore||0,"/100"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Community Contributions"}),e.jsx("span",{className:"text-lg font-semibold text-blue-600",children:s?.bridgeBuildingMetrics.communityContributions||0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Active Mentorships"}),e.jsx("span",{className:"text-lg font-semibold text-purple-600",children:s?.bridgeBuildingMetrics.mentorshipParticipation||0})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:()=>g("recommendations"),className:"w-full px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm",children:"View Recommendations"}),e.jsx("button",{onClick:()=>g("trending"),className:"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:"Explore Trending"}),e.jsx("button",{className:"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:"Share Content"}),e.jsx("button",{className:"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:"Find Mentors"})]})]})]}),D=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Personalized Recommendations"}),e.jsx("button",{onClick:d,className:"px-4 py-2 text-sm text-orange-600 border border-orange-300 rounded-lg hover:bg-orange-50",children:"Refresh"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:n.map(i=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"font-semibold text-gray-900 mb-1",children:[i.contentType.charAt(0).toUpperCase()+i.contentType.slice(1)," Content"]}),e.jsx("span",{className:"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded-full",children:i.recommendationType.replace("_"," ")})]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-sm font-medium text-green-600",children:[i.score,"% Match"]})})]}),e.jsxs("div",{className:"space-y-2 mb-4",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Why recommended:"})," ",i.reasoning.primaryFactors.join(", ")]}),i.reasoning.culturalRelevance.length>0&&e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Cultural relevance:"})," ",i.reasoning.culturalRelevance.join(", ")]}),i.reasoning.learningOpportunities.length>0&&e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Learning opportunities:"})," ",i.reasoning.learningOpportunities.join(", ")]})]}),i.culturalSensitivity.requiresContext&&e.jsxs("div",{className:"mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[e.jsx("div",{className:"text-xs font-medium text-yellow-800 mb-1",children:"Cultural Context Required"}),e.jsx("div",{className:"text-xs text-yellow-700",children:i.culturalSensitivity.respectfulApproach.join(", ")})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>{N(i.contentId,"view"),f?.(i.contentId,i.contentType)},className:"flex-1 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm",children:"Explore"}),e.jsx("button",{onClick:()=>N(i.contentId,"bookmark"),className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:"Save"})]})]},i.id))})]}),C=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Trending Cultural Topics"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:c.map(i=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsx("h4",{className:"font-semibold text-gray-900",children:i.topic}),e.jsxs("span",{className:"text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full",children:["+",i.trendMetrics.growthRate,"%"]})]}),i.culturalGroup&&e.jsxs("div",{className:"text-sm text-orange-600 mb-2 capitalize",children:[i.culturalGroup," Culture"]}),e.jsx("div",{className:"text-sm text-gray-600 mb-4",children:i.content.culturalContext}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 mb-4",children:[e.jsxs("span",{children:[i.engagement.views," views"]}),e.jsxs("span",{children:[i.engagement.interactions," interactions"]}),e.jsxs("span",{children:[i.engagement.shares," shares"]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"flex-1 px-3 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm",children:"Explore Topic"}),e.jsx("button",{className:"px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:"Share"})]})]},i.id))})]}),R=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Learning Progress & Goals"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"This Week's Goals"}),e.jsx("div",{className:"space-y-3",children:s?.recommendations.weeklyGoals.map((i,j)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",className:"mr-3"}),e.jsx("span",{className:"text-sm text-gray-700",children:i})]},j))||e.jsx("div",{className:"text-sm text-gray-500",children:"No goals set for this week"})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Cultural Challenges"}),e.jsx("div",{className:"space-y-3",children:s?.recommendations.culturalChallenges.map((i,j)=>e.jsx("div",{className:"p-3 bg-orange-50 border border-orange-200 rounded-lg",children:e.jsx("div",{className:"text-sm text-orange-800",children:i})},j))||e.jsx("div",{className:"text-sm text-gray-500",children:"No active challenges"})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Your Activity Summary"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:s?.contentInteractions.viewedContent.length||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Content Viewed"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:s?.contentInteractions.likedContent.length||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Content Liked"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:s?.contentInteractions.sharedContent.length||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Content Shared"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:s?.contentInteractions.createdContent.length||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Content Created"})]})]})]})]});return y?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"})}):e.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Your Cultural Learning Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Track your progress, discover new content, and build bridges across cultures."})]}),e.jsx("div",{className:"border-b border-gray-200 mb-6",children:e.jsx("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview",icon:"📊"},{id:"recommendations",label:"For You",icon:"🎯"},{id:"trending",label:"Trending",icon:"🔥"},{id:"progress",label:"Progress",icon:"📈"}].map(i=>e.jsxs("button",{onClick:()=>g(i.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${m===i.id?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx("span",{className:"mr-2",children:i.icon}),i.label]},i.id))})}),m==="overview"&&L(),m==="recommendations"&&D(),m==="trending"&&C(),m==="progress"&&R()]})},ce=()=>{const[f,t]=u.useState("dashboard"),[s,a]=u.useState(!1),n=d=>{a(!1),alert(`Cultural content created successfully! ID: ${d}`)},l=d=>{alert(`Mentorship request sent! Match ID: ${d}`)},c=(d,N)=>{console.log(`Selected content: ${d} of type: ${N}`)},h=()=>e.jsx("div",{className:"bg-white shadow-sm border-b border-gray-200",children:e.jsx("div",{className:"max-w-6xl mx-auto px-6",children:e.jsx("nav",{className:"flex space-x-8",children:[{id:"dashboard",label:"Dashboard",icon:"📊",description:"Your personalized learning hub"},{id:"heritage",label:"Heritage",icon:"🏛️",description:"Document & preserve culture"},{id:"knowledge",label:"Knowledge",icon:"🤝",description:"Share & learn skills"},{id:"discovery",label:"Discovery",icon:"🔍",description:"Explore cultural content"}].map(d=>e.jsxs("button",{onClick:()=>t(d.id),className:`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${f===d.id?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,title:d.description,children:[e.jsx("span",{className:"mr-2",children:d.icon}),d.label]},d.id))})})}),y=()=>e.jsx("div",{className:"max-w-6xl mx-auto p-6",children:s?e.jsx(ee,{onContentCreated:n,onCancel:()=>a(!1)}):e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Cultural Heritage Documentation"}),e.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Preserve and share South African cultural heritage for future generations"}),e.jsx("button",{onClick:()=>a(!0),className:"px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-medium",children:"Share Your Heritage"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("div",{className:"text-3xl mb-4",children:"📚"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Oral Traditions"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Record and preserve traditional stories, folklore, and oral histories with audio and video support."}),e.jsx("button",{className:"text-orange-600 hover:text-orange-800 font-medium",children:"Start Recording →"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🏺"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Cultural Artifacts"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Document traditional objects, tools, and artifacts with detailed metadata and provenance."}),e.jsx("button",{className:"text-orange-600 hover:text-orange-800 font-medium",children:"Document Artifact →"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🎭"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Traditions & Customs"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Share traditional practices, ceremonies, and cultural customs with proper context."}),e.jsx("button",{className:"text-orange-600 hover:text-orange-800 font-medium",children:"Share Tradition →"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🍲"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Traditional Recipes"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Preserve family recipes and traditional cooking methods with cultural significance."}),e.jsx("button",{className:"text-orange-600 hover:text-orange-800 font-medium",children:"Share Recipe →"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🎵"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Music & Dance"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Record traditional music, songs, and dance performances with cultural context."}),e.jsx("button",{className:"text-orange-600 hover:text-orange-800 font-medium",children:"Record Performance →"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🗣️"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Languages & Dialects"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Preserve linguistic heritage including dialects, phrases, and language variations."}),e.jsx("button",{className:"text-orange-600 hover:text-orange-800 font-medium",children:"Document Language →"})]})]}),e.jsxs("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-orange-800 mb-3",children:"Cultural Sensitivity & Ubuntu Philosophy"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-orange-700",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Respectful Documentation"}),e.jsxs("ul",{className:"space-y-1",children:[e.jsx("li",{children:"• Ensure you have permission to share cultural content"}),e.jsx("li",{children:"• Respect sacred and sensitive information"}),e.jsx("li",{children:"• Provide accurate cultural context"}),e.jsx("li",{children:"• Honor traditional knowledge holders"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Community Validation"}),e.jsxs("ul",{className:"space-y-1",children:[e.jsx("li",{children:"• Content reviewed by cultural representatives"}),e.jsx("li",{children:"• Community endorsement for accuracy"}),e.jsx("li",{children:"• Protection of sacred content"}),e.jsx("li",{children:"• Collaborative preservation efforts"})]})]})]})]})]})}),o=()=>e.jsx(re,{onMentorshipRequest:l}),m=()=>e.jsx(ae,{onContentSelect:c}),g=()=>e.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Cultural Knowledge & Heritage Hub"}),e.jsx("p",{className:"text-gray-600",children:"Discover, share, and preserve South African cultural heritage through collaborative learning and Ubuntu philosophy."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600 mb-2",children:"1,247"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Heritage Items"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:"856"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Active Mentors"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600 mb-2",children:"2,134"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Learning Paths"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600 mb-2",children:"3,421"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Cultural Bridges"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{onClick:()=>t("heritage"),className:"bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white cursor-pointer hover:from-orange-600 hover:to-orange-700 transition-all",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🏛️"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Cultural Heritage"}),e.jsx("p",{className:"text-orange-100 mb-4",children:"Document and preserve traditional stories, artifacts, and cultural practices for future generations."}),e.jsx("div",{className:"flex items-center text-orange-100",children:e.jsx("span",{children:"Explore Heritage →"})})]}),e.jsxs("div",{onClick:()=>t("knowledge"),className:"bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white cursor-pointer hover:from-blue-600 hover:to-blue-700 transition-all",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🤝"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Knowledge Sharing"}),e.jsx("p",{className:"text-blue-100 mb-4",children:"Connect with mentors and learners across cultures to share skills and build meaningful relationships."}),e.jsx("div",{className:"flex items-center text-blue-100",children:e.jsx("span",{children:"Start Learning →"})})]}),e.jsxs("div",{onClick:()=>t("discovery"),className:"bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white cursor-pointer hover:from-green-600 hover:to-green-700 transition-all",children:[e.jsx("div",{className:"text-3xl mb-4",children:"🔍"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Content Discovery"}),e.jsx("p",{className:"text-green-100 mb-4",children:"Discover personalized cultural content and trending topics that match your interests and learning goals."}),e.jsx("div",{className:"flex items-center text-green-100",children:e.jsx("span",{children:"Discover Content →"})})]})]}),e.jsxs("div",{className:"mt-8 bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"text-2xl mr-3",children:"🤲"}),e.jsx("h3",{className:"text-lg font-semibold text-orange-800",children:'Ubuntu Philosophy: "I am because we are"'})]}),e.jsx("p",{className:"text-orange-700 mb-4",children:"Our platform embodies the Ubuntu philosophy, recognizing that our individual growth and cultural understanding are deeply connected to our community's collective wisdom and shared experiences."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[e.jsxs("div",{className:"bg-white rounded-lg p-4 border border-orange-200",children:[e.jsx("h4",{className:"font-medium text-orange-800 mb-2",children:"Mutual Learning"}),e.jsx("p",{className:"text-orange-700",children:"Every interaction is an opportunity for both teaching and learning."})]}),e.jsxs("div",{className:"bg-white rounded-lg p-4 border border-orange-200",children:[e.jsx("h4",{className:"font-medium text-orange-800 mb-2",children:"Cultural Respect"}),e.jsx("p",{className:"text-orange-700",children:"Honor and celebrate the diversity of South African cultures."})]}),e.jsxs("div",{className:"bg-white rounded-lg p-4 border border-orange-200",children:[e.jsx("h4",{className:"font-medium text-orange-800 mb-2",children:"Community Building"}),e.jsx("p",{className:"text-orange-700",children:"Strengthen bonds across cultural boundaries through shared knowledge."})]})]})]})]});return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[h(),e.jsxs("main",{children:[f==="dashboard"&&g(),f==="heritage"&&y(),f==="knowledge"&&o(),f==="discovery"&&m()]})]})};export{ce as CulturalKnowledgePage,ce as default};
//# sourceMappingURL=CulturalKnowledgePage-BnYvObkg.js.map
