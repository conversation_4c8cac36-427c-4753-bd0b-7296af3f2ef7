{"version": 3, "file": "AuthPage-Chhb6i1V.js", "sources": ["../../node_modules/react-hook-form/dist/index.esm.mjs", "../../node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/LanguageIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/UserIcon.js", "../../src/components/ui/Input.tsx", "../../src/features/auth/components/RegistrationForm.tsx", "../../src/features/auth/components/LoginForm.tsx", "../../src/components/LanguageSelector.tsx", "../../src/pages/AuthPage.tsx"], "sourcesContent": ["import * as React from 'react';\nimport React__default from 'react';\n\nvar isCheckBoxInput = (element) => element.type === 'checkbox';\n\nvar isDateObject = (value) => value instanceof Date;\n\nvar isNullOrUndefined = (value) => value == null;\n\nconst isObjectType = (value) => typeof value === 'object';\nvar isObject = (value) => !isNullOrUndefined(value) &&\n    !Array.isArray(value) &&\n    isObjectType(value) &&\n    !isDateObject(value);\n\nvar getEventValue = (event) => isObject(event) && event.target\n    ? isCheckBoxInput(event.target)\n        ? event.target.checked\n        : event.target.value\n    : event;\n\nvar getNodeParentName = (name) => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\n\nvar isPlainObject = (tempObject) => {\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));\n};\n\nvar isWeb = typeof window !== 'undefined' &&\n    typeof window.HTMLElement !== 'undefined' &&\n    typeof document !== 'undefined';\n\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    }\n    else if (data instanceof Set) {\n        copy = new Set(data);\n    }\n    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&\n        (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        }\n        else {\n            for (const key in data) {\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    }\n    else {\n        return data;\n    }\n    return copy;\n}\n\nvar compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];\n\nvar isUndefined = (val) => val === undefined;\n\nvar get = (object, path, defaultValue) => {\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object\n        ? isUndefined(object[path])\n            ? defaultValue\n            : object[path]\n        : result;\n};\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nvar isKey = (value) => /^\\w*$/.test(value);\n\nvar stringToPath = (input) => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nvar set = (object, path, value) => {\n    let index = -1;\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while (++index < length) {\n        const key = tempPath[index];\n        let newValue = value;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue =\n                isObject(objValue) || Array.isArray(objValue)\n                    ? objValue\n                    : !isNaN(+tempPath[index + 1])\n                        ? []\n                        : {};\n        }\n        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\n\nconst EVENTS = {\n    BLUR: 'blur',\n    FOCUS_OUT: 'focusout',\n    CHANGE: 'change',\n};\nconst VALIDATION_MODE = {\n    onBlur: 'onBlur',\n    onChange: 'onChange',\n    onSubmit: 'onSubmit',\n    onTouched: 'onTouched',\n    all: 'all',\n};\nconst INPUT_VALIDATION_RULES = {\n    max: 'max',\n    min: 'min',\n    maxLength: 'maxLength',\n    minLength: 'minLength',\n    pattern: 'pattern',\n    required: 'required',\n    validate: 'validate',\n};\n\nconst HookFormContext = React__default.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => React__default.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = (props) => {\n    const { children, ...data } = props;\n    return (React__default.createElement(HookFormContext.Provider, { value: data }, children));\n};\n\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n    const result = {\n        defaultValues: control._defaultValues,\n    };\n    for (const key in formState) {\n        Object.defineProperty(result, key, {\n            get: () => {\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            },\n        });\n    }\n    return result;\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = React__default.useState(control._formState);\n    const _localProxyFormState = React__default.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    });\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n            !disabled &&\n                updateFormState({\n                    ...control._formState,\n                    ...formState,\n                });\n        },\n    }), [name, disabled, exact]);\n    React__default.useEffect(() => {\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [control]);\n    return React__default.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\n\nvar isString = (value) => typeof value === 'string';\n\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, } = props || {};\n    const _defaultValue = React__default.useRef(defaultValue);\n    const [value, updateValue] = React__default.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: {\n            values: true,\n        },\n        exact,\n        callback: (formState) => !disabled &&\n            updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current)),\n    }), [name, control, disabled, exact]);\n    React__default.useEffect(() => control._removeUnmounted());\n    return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true,\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true,\n    });\n    const _props = React__default.useRef(props);\n    const _registerProps = React__default.useRef(control.register(name, {\n        ...props.rules,\n        value,\n        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }));\n    const fieldState = React__default.useMemo(() => Object.defineProperties({}, {\n        invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n        },\n    }), [formState, name]);\n    const onChange = React__default.useCallback((event) => _registerProps.current.onChange({\n        target: {\n            value: getEventValue(event),\n            name: name,\n        },\n        type: EVENTS.CHANGE,\n    }), [name]);\n    const onBlur = React__default.useCallback(() => _registerProps.current.onBlur({\n        target: {\n            value: get(control._formValues, name),\n            name: name,\n        },\n        type: EVENTS.BLUR,\n    }), [name, control._formValues]);\n    const ref = React__default.useCallback((elm) => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: () => elm.focus && elm.focus(),\n                select: () => elm.select && elm.select(),\n                setCustomValidity: (message) => elm.setCustomValidity(message),\n                reportValidity: () => elm.reportValidity(),\n            };\n        }\n    }, [control._fields, name]);\n    const field = React__default.useMemo(() => ({\n        name,\n        value,\n        ...(isBoolean(disabled) || formState.disabled\n            ? { disabled: formState.disabled || disabled }\n            : {}),\n        onChange,\n        onBlur,\n        ref,\n    }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n    React__default.useEffect(() => {\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...(isBoolean(_props.current.disabled)\n                ? { disabled: _props.current.disabled }\n                : {}),\n        });\n        const updateMounted = (name, value) => {\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value);\n            }\n        }\n        !isArrayField && control.register(name);\n        return () => {\n            (isArrayField\n                ? _shouldUnregisterField && !control._state.action\n                : _shouldUnregisterField)\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, isArrayField, shouldUnregister]);\n    React__default.useEffect(() => {\n        control._setDisabledField({\n            disabled,\n            name,\n        });\n    }, [disabled, name, control]);\n    return React__default.useMemo(() => ({\n        field,\n        formState,\n        fieldState,\n    }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = (props) => props.render(useController(props));\n\nconst flatten = (obj) => {\n    const output = {};\n    for (const key of Object.keys(obj)) {\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)) {\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        }\n        else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\n\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = React__default.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event) => {\n        let hasError = false;\n        let type = '';\n        await control.handleSubmit(async (data) => {\n            const formData = new FormData();\n            let formDataJson = '';\n            try {\n                formDataJson = JSON.stringify(data);\n            }\n            catch (_a) { }\n            const flattenFormValues = flatten(control._formValues);\n            for (const key in flattenFormValues) {\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson,\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers['Content-Type'],\n                        encType,\n                    ].some((value) => value && value.includes('json'));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...(encType ? { 'Content-Type': encType } : {}),\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData,\n                    });\n                    if (response &&\n                        (validateStatus\n                            ? !validateStatus(response.status)\n                            : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({ response });\n                        type = String(response.status);\n                    }\n                    else {\n                        onSuccess && onSuccess({ response });\n                    }\n                }\n                catch (error) {\n                    hasError = true;\n                    onError && onError({ error });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false,\n            });\n            props.control.setError('root.server', {\n                type,\n            });\n        }\n    };\n    React__default.useEffect(() => {\n        setMounted(true);\n    }, []);\n    return render ? (React__default.createElement(React__default.Fragment, null, render({\n        submit,\n    }))) : (React__default.createElement(\"form\", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n            ...(errors[name] && errors[name].types ? errors[name].types : {}),\n            [type]: message || true,\n        },\n    }\n    : {};\n\nvar convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);\n\nvar createSubject = () => {\n    let _observers = [];\n    const next = (value) => {\n        for (const observer of _observers) {\n            observer.next && observer.next(value);\n        }\n    };\n    const subscribe = (observer) => {\n        _observers.push(observer);\n        return {\n            unsubscribe: () => {\n                _observers = _observers.filter((o) => o !== observer);\n            },\n        };\n    };\n    const unsubscribe = () => {\n        _observers = [];\n    };\n    return {\n        get observers() {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe,\n    };\n};\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1) {\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== 'ref') {\n            const val2 = object2[key];\n            if ((isDateObject(val1) && isDateObject(val2)) ||\n                (isObject(val1) && isObject(val2)) ||\n                (Array.isArray(val1) && Array.isArray(val2))\n                ? !deepEqual(val1, val2)\n                : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isFileInput = (element) => element.type === 'file';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isHTMLElement = (value) => {\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value ? value.ownerDocument : 0;\n    return (value instanceof\n        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));\n};\n\nvar isMultipleSelect = (element) => element.type === `select-multiple`;\n\nvar isRadioInput = (element) => element.type === 'radio';\n\nvar isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);\n\nvar live = (ref) => isHTMLElement(ref) && ref.isConnected;\n\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while (index < length) {\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path)\n        ? path\n        : isKey(path)\n            ? [path]\n            : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 &&\n        ((isObject(childObject) && isEmptyObject(childObject)) ||\n            (Array.isArray(childObject) && isEmptyArray(childObject)))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\n\nvar objectHasFunction = (data) => {\n    for (const key in data) {\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\n\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            }\n            else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                if (isUndefined(formValues) ||\n                    isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key])\n                        ? markFieldsDirty(data[key], [])\n                        : { ...markFieldsDirty(data[key]) };\n                }\n                else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            }\n            else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\n\nconst defaultResult = {\n    value: false,\n    isValid: false,\n};\nconst validResult = { value: true, isValid: true };\nvar getCheckboxValue = (options) => {\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options\n                .filter((option) => option && option.checked && !option.disabled)\n                .map((option) => option.value);\n            return { value: values, isValid: !!values.length };\n        }\n        return options[0].checked && !options[0].disabled\n            ? // @ts-expect-error expected to work in the browser\n                options[0].attributes && !isUndefined(options[0].attributes.value)\n                    ? isUndefined(options[0].value) || options[0].value === ''\n                        ? validResult\n                        : { value: options[0].value, isValid: true }\n                    : validResult\n            : defaultResult;\n    }\n    return defaultResult;\n};\n\nvar getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)\n    ? value\n    : valueAsNumber\n        ? value === ''\n            ? NaN\n            : value\n                ? +value\n                : value\n        : valueAsDate && isString(value)\n            ? new Date(value)\n            : setValueAs\n                ? setValueAs(value)\n                : value;\n\nconst defaultReturn = {\n    isValid: false,\n    value: null,\n};\nvar getRadioValue = (options) => Array.isArray(options)\n    ? options.reduce((previous, option) => option && option.checked && !option.disabled\n        ? {\n            isValid: true,\n            value: option.value,\n        }\n        : previous, defaultReturn)\n    : defaultReturn;\n\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [...ref.selectedOptions].map(({ value }) => value);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n    const fields = {};\n    for (const name of fieldsNames) {\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [...fieldsNames],\n        fields,\n        shouldUseNativeValidation,\n    };\n};\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getRuleValue = (rule) => isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n        ? rule.source\n        : isObject(rule)\n            ? isRegex(rule.value)\n                ? rule.value.source\n                : rule.value\n            : rule;\n\nvar getValidationModes = (mode) => ({\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\n    isOnChange: mode === VALIDATION_MODE.onChange,\n    isOnAll: mode === VALIDATION_MODE.all,\n    isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = (fieldReference) => !!fieldReference &&\n    !!fieldReference.validate &&\n    !!((isFunction(fieldReference.validate) &&\n        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n        (isObject(fieldReference.validate) &&\n            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));\n\nvar hasValidation = (options) => options.mount &&\n    (options.required ||\n        options.min ||\n        options.max ||\n        options.maxLength ||\n        options.minLength ||\n        options.pattern ||\n        options.validate);\n\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&\n    (_names.watchAll ||\n        _names.watch.has(name) ||\n        [..._names.watch].some((watchName) => name.startsWith(watchName) &&\n            /^\\.\\w+/.test(name.slice(watchName.length))));\n\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n    for (const key of fieldsNames || Object.keys(fields)) {\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                }\n                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                }\n                else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            }\n            else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\n\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name,\n        };\n    }\n    const names = name.split('.');\n    while (names.length) {\n        const fieldName = names.join('.');\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return { name };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError,\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root,\n            };\n        }\n        names.pop();\n    }\n    return {\n        name,\n    };\n}\n\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return (isEmptyObject(formState) ||\n        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n        Object.keys(formState).find((key) => _proxyFormState[key] ===\n            (!isRoot || VALIDATION_MODE.all)));\n};\n\nvar shouldSubscribeByName = (name, signalName, exact) => !name ||\n    !signalName ||\n    name === signalName ||\n    convertToArrayPayload(name).some((currentName) => currentName &&\n        (exact\n            ? currentName === signalName\n            : currentName.startsWith(signalName) ||\n                signalName.startsWith(currentName)));\n\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n    if (mode.isOnAll) {\n        return false;\n    }\n    else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    }\n    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    }\n    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\n\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\n\nvar updateFieldArrayRootError = (errors, error, name) => {\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, 'root', error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\n\nvar isMessage = (value) => isString(value);\n\nfunction getValidateError(result, ref, type = 'validate') {\n    if (isMessage(result) ||\n        (Array.isArray(result) && result.every(isMessage)) ||\n        (isBoolean(result) && !result)) {\n        return {\n            type,\n            message: isMessage(result) ? result : '',\n            ref,\n        };\n    }\n}\n\nvar getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n    };\n\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message) => {\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&\n        isUndefined(ref.value) &&\n        isUndefined(inputValue)) ||\n        (isHTMLElement(ref) && ref.value === '') ||\n        inputValue === '' ||\n        (Array.isArray(inputValue) && !inputValue.length);\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n        };\n    };\n    if (isFieldArray\n        ? !Array.isArray(inputValue) || !inputValue.length\n        : required &&\n            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n                (isBoolean(inputValue) && !inputValue) ||\n                (isCheckBox && !getCheckboxValue(refs).isValid) ||\n                (isRadio && !getRadioValue(refs).isValid))) {\n        const { value, message } = isMessage(required)\n            ? { value: !!required, message: required }\n            : getValueAndMessage(required);\n        if (value) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber ||\n                (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        }\n        else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);\n            const isTime = ref.type == 'time';\n            const isWeek = ref.type == 'week';\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime\n                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n                    : isWeek\n                        ? inputValue > maxOutput.value\n                        : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime\n                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n                    : isWeek\n                        ? inputValue < minOutput.value\n                        : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) &&\n        !isEmpty &&\n        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&\n            inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&\n            inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        }\n        else if (isObject(validate)) {\n            let validationResult = {};\n            for (const key in validate) {\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message),\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult,\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\n\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true,\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props,\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false,\n    };\n    const _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)\n        ? cloneObject(_options.defaultValues || _options.values) || {}\n        : {};\n    let _formValues = _options.shouldUnregister\n        ? {}\n        : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false,\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set(),\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState,\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject(),\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback) => (wait) => {\n        clearTimeout(timer);\n        timer = setTimeout(callback, wait);\n    };\n    const _setValid = async (shouldUpdateValid) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValid ||\n                _proxySubscribeFormState.isValid ||\n                shouldUpdateValid)) {\n            const isValid = _options.resolver\n                ? isEmptyObject((await _runSchema()).errors)\n                : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid,\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValidating ||\n                _proxyFormState.validatingFields ||\n                _proxySubscribeFormState.isValidating ||\n                _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name) => {\n                if (name) {\n                    isValidating\n                        ? set(_formState.validatingFields, name, isValidating)\n                        : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields),\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields ||\n                _proxySubscribeFormState.touchedFields) &&\n                shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid,\n            });\n        }\n        else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error) => {\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors,\n        });\n    };\n    const _setErrors = (errors) => {\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false,\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n            isUndefined(defaultValue) ||\n                (ref && ref.defaultChecked) ||\n                shouldSkipSetValueAs\n                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))\n                : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name,\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine\n                    ? unset(_formState.dirtyFields, name)\n                    : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField =\n                    shouldUpdateField ||\n                        ((_proxyFormState.dirtyFields ||\n                            _proxySubscribeFormState.dirtyFields) &&\n                            isPreviousDirty !== !isCurrentFieldPristine);\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField =\n                        shouldUpdateField ||\n                            ((_proxyFormState.touchedFields ||\n                                _proxySubscribeFormState.touchedFields) &&\n                                isPreviousFieldTouched !== isBlurEvent);\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState) => {\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n            isBoolean(isValid) &&\n            _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(() => updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        }\n        else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error\n                ? set(_formState.errors, name, error)\n                : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n            !isEmptyObject(fieldState) ||\n            shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n                errors: _formState.errors,\n                name,\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState,\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name) => {\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names) => {\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names) {\n                const error = get(errors, name);\n                error\n                    ? set(_formState.errors, name, error)\n                    : unset(_formState.errors, name);\n            }\n        }\n        else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true,\n    }) => {\n        for (const name in fields) {\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid &&\n                        (get(fieldError, _f.name)\n                            ? isFieldArrayRoot\n                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)\n                                : set(_formState.errors, _f.name, fieldError[_f.name])\n                            : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) &&\n                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = () => {\n        for (const name of _names.unMount) {\n            const field = get(_fields, name);\n            field &&\n                (field._f.refs\n                    ? field._f.refs.every((ref) => !live(ref))\n                    : !live(field._f.ref)) &&\n                unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data) => !_options.disabled &&\n        (name && data && set(_formValues, name, data),\n            !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n        ...(_state.mount\n            ? _formValues\n            : isUndefined(defaultValue)\n                ? _defaultValues\n                : isString(names)\n                    ? { [names]: defaultValue }\n                    : defaultValue),\n    }, isGlobal, defaultValue);\n    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        let fieldValue = value;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled &&\n                    set(_formValues, name, getFieldValueAs(value, fieldReference));\n                fieldValue =\n                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n                        ? ''\n                        : value;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));\n                }\n                else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef) => {\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data) => data === checkboxRef.value);\n                                }\n                                else {\n                                    checkboxRef.checked =\n                                        fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    }\n                    else {\n                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));\n                    }\n                }\n                else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = '';\n                }\n                else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues),\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) &&\n            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value, options) => {\n        for (const fieldKey in value) {\n            if (!value.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value[fieldKey];\n            const fieldName = name + '.' + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) ||\n                isObject(fieldValue) ||\n                (field && !field._f)) &&\n                !isDateObject(fieldValue)\n                ? setValues(fieldName, fieldValue, options)\n                : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues),\n            });\n            if ((_proxyFormState.isDirty ||\n                _proxyFormState.dirtyFields ||\n                _proxySubscribeFormState.isDirty ||\n                _proxySubscribeFormState.dirtyFields) &&\n                options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue),\n                });\n            }\n        }\n        else {\n            field && !field._f && !isNullOrUndefined(cloneValue)\n                ? setValues(name, cloneValue, options)\n                : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({ ..._formState });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues),\n        });\n    };\n    const onChange = async (event) => {\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue) => {\n            isFieldValueUpdated =\n                Number.isNaN(fieldValue) ||\n                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n                    deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type\n                ? getFieldValue(field._f)\n                : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = (!hasValidation(field._f) &&\n                !_options.resolver &&\n                !get(_formState.errors, name) &&\n                !field._f.deps) ||\n                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            }\n            else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent &&\n                _subjects.state.next({\n                    name,\n                    type: event.type,\n                    values: cloneObject(_formValues),\n                });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === 'onBlur') {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    }\n                    else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return (shouldRender &&\n                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));\n            }\n            !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            }\n            else {\n                _updateIsValidating([name], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    }\n                    else if (_proxyFormState.isValid ||\n                        _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps &&\n                    trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key) => {\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {}) => {\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name\n                ? !fieldNames.some((name) => get(errors, name))\n                : isValid;\n        }\n        else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        }\n        else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...(!isString(name) ||\n                ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n                    isValid !== _formState.isValid)\n                ? {}\n                : { name }),\n            ...(_options.resolver || !name ? { isValid } : {}),\n            errors: _formState.errors,\n        });\n        options.shouldFocus &&\n            !validationResult &&\n            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames) => {\n        const values = {\n            ...(_state.mount ? _formValues : _defaultValues),\n        };\n        return isUndefined(fieldNames)\n            ? values\n            : isString(fieldNames)\n                ? get(values, fieldNames)\n                : fieldNames.map((name) => get(values, name));\n    };\n    const getFieldState = (name, formState) => ({\n        invalid: !!get((formState || _formState).errors, name),\n        isDirty: !!get((formState || _formState).dirtyFields, name),\n        error: get((formState || _formState).errors, name),\n        isValidating: !!get(_formState.validatingFields, name),\n        isTouched: !!get((formState || _formState).touchedFields, name),\n    });\n    const clearErrors = (name) => {\n        name &&\n            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {},\n        });\n    };\n    const setError = (name, error, options) => {\n        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref,\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false,\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue) => isFunction(name)\n        ? _subjects.state.subscribe({\n            next: (payload) => name(_getWatch(undefined, defaultValue), payload),\n        })\n        : _getWatch(name, defaultValue, true);\n    const _subscribe = (props) => _subjects.state.subscribe({\n        next: (formState) => {\n            if (shouldSubscribeByName(props.name, formState.name, props.exact) &&\n                shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                props.callback({\n                    values: { ..._formValues },\n                    ..._formState,\n                    ...formState,\n                });\n            }\n        },\n    }).unsubscribe;\n    const subscribe = (props) => {\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState,\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState,\n        });\n    };\n    const unregister = (name, options = {}) => {\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating &&\n                unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister &&\n                !options.keepDefaultValue &&\n                unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues),\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name, }) => {\n        if ((isBoolean(disabled) && _state.mount) ||\n            !!disabled ||\n            _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {}) => {\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...(field || {}),\n            _f: {\n                ...(field && field._f ? field._f : { ref: { name } }),\n                name,\n                mount: true,\n                ...options,\n            },\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled)\n                    ? options.disabled\n                    : _options.disabled,\n                name,\n            });\n        }\n        else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...(disabledIsDefined\n                ? { disabled: options.disabled || _options.disabled }\n                : {}),\n            ...(_options.progressive\n                ? {\n                    required: !!options.required,\n                    min: getRuleValue(options.min),\n                    max: getRuleValue(options.max),\n                    minLength: getRuleValue(options.minLength),\n                    maxLength: getRuleValue(options.maxLength),\n                    pattern: getRuleValue(options.pattern),\n                }\n                : {}),\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref) => {\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value)\n                        ? ref.querySelectorAll\n                            ? ref.querySelectorAll('input,select,textarea')[0] || ref\n                            : ref\n                        : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox\n                        ? refs.find((option) => option === fieldRef)\n                        : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...(radioOrCheckbox\n                                ? {\n                                    refs: [\n                                        ...refs.filter(live),\n                                        fieldRef,\n                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                                    ],\n                                    ref: { type: fieldRef.type, name },\n                                }\n                                : { ref: fieldRef }),\n                        },\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                }\n                else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) &&\n                        !(isNameInFieldArray(_names.array, name) && _state.action) &&\n                        _names.unMount.add(name);\n                }\n            },\n        };\n    };\n    const _focusError = () => _options.shouldFocusError &&\n        iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled) => {\n        if (isBoolean(disabled)) {\n            _subjects.state.next({ disabled });\n            iterateFieldsByAction(_fields, (ref, name) => {\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef) => {\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid) => async (e) => {\n        let onValidError = undefined;\n        if (e) {\n            e.preventDefault && e.preventDefault();\n            e.persist &&\n                e.persist();\n        }\n        let fieldValues = cloneObject(_formValues);\n        _subjects.state.next({\n            isSubmitting: true,\n        });\n        if (_options.resolver) {\n            const { errors, values } = await _runSchema();\n            _formState.errors = errors;\n            fieldValues = values;\n        }\n        else {\n            await executeBuiltInValidation(_fields);\n        }\n        if (_names.disabled.size) {\n            for (const name of _names.disabled) {\n                set(fieldValues, name, undefined);\n            }\n        }\n        unset(_formState.errors, 'root');\n        if (isEmptyObject(_formState.errors)) {\n            _subjects.state.next({\n                errors: {},\n            });\n            try {\n                await onValid(fieldValues, e);\n            }\n            catch (error) {\n                onValidError = error;\n            }\n        }\n        else {\n            if (onInvalid) {\n                await onInvalid({ ..._formState.errors }, e);\n            }\n            _focusError();\n            setTimeout(_focusError);\n        }\n        _subjects.state.next({\n            isSubmitted: true,\n            isSubmitting: false,\n            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n            submitCount: _formState.submitCount + 1,\n            errors: _formState.errors,\n        });\n        if (onValidError) {\n            throw onValidError;\n        }\n    };\n    const resetField = (name, options = {}) => {\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            }\n            else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue\n                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n                    : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({ ..._formState });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {}) => {\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)) {\n                    get(_formState.dirtyFields, fieldName)\n                        ? set(values, fieldName, get(_formValues, fieldName))\n                        : setValue(fieldName, get(values, fieldName));\n                }\n            }\n            else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount) {\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs)\n                                ? field._f.refs[0]\n                                : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest('form');\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                for (const fieldName of _names.mount) {\n                    setValue(fieldName, get(values, fieldName));\n                }\n            }\n            _formValues = cloneObject(values);\n            _subjects.array.next({\n                values: { ...values },\n            });\n            _subjects.state.next({\n                values: { ...values },\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: '',\n        };\n        _state.mount =\n            !_proxyFormState.isValid ||\n                !!keepStateOptions.keepIsValid ||\n                !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount\n                ? _formState.submitCount\n                : 0,\n            isDirty: isEmptyResetValues\n                ? false\n                : keepStateOptions.keepDirty\n                    ? _formState.isDirty\n                    : !!(keepStateOptions.keepDefaultValues &&\n                        !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted\n                ? _formState.isSubmitted\n                : false,\n            dirtyFields: isEmptyResetValues\n                ? {}\n                : keepStateOptions.keepDirtyValues\n                    ? keepStateOptions.keepDefaultValues && _formValues\n                        ? getDirtyFields(_defaultValues, _formValues)\n                        : _formState.dirtyFields\n                    : keepStateOptions.keepDefaultValues && formValues\n                        ? getDirtyFields(_defaultValues, formValues)\n                        : keepStateOptions.keepDirty\n                            ? _formState.dirtyFields\n                            : {},\n            touchedFields: keepStateOptions.keepTouched\n                ? _formState.touchedFields\n                : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n                ? _formState.isSubmitSuccessful\n                : false,\n            isSubmitting: false,\n        });\n    };\n    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)\n        ? formValues(_formValues)\n        : formValues, keepStateOptions);\n    const setFocus = (name, options = {}) => {\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs\n                ? fieldReference.refs[0]\n                : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect &&\n                    isFunction(fieldRef.select) &&\n                    fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState) => {\n        _formState = {\n            ..._formState,\n            ...updatedFormState,\n        };\n    };\n    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&\n        _options.defaultValues().then((values) => {\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false,\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields() {\n                return _fields;\n            },\n            get _formValues() {\n                return _formValues;\n            },\n            get _state() {\n                return _state;\n            },\n            set _state(value) {\n                _state = value;\n            },\n            get _defaultValues() {\n                return _defaultValues;\n            },\n            get _names() {\n                return _names;\n            },\n            set _names(value) {\n                _names = value;\n            },\n            get _formState() {\n                return _formState;\n            },\n            get _options() {\n                return _options;\n            },\n            set _options(value) {\n                _options = {\n                    ..._options,\n                    ...value,\n                };\n            },\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState,\n    };\n    return {\n        ...methods,\n        formControl: methods,\n    };\n}\n\nvar generateId = () => {\n    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n    });\n};\n\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n\nvar appendAt = (data, value) => [\n    ...data,\n    ...convertToArrayPayload(value),\n];\n\nvar fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;\n\nfunction insert(data, index, value) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value),\n        ...data.slice(index),\n    ];\n}\n\nvar moveArrayAt = (data, from, to) => {\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\n\nvar prependAt = (data, value) => [\n    ...convertToArrayPayload(value),\n    ...convertToArrayPayload(data),\n];\n\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [...data];\n    for (const index of indexes) {\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index)\n    ? []\n    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\n\nvar swapArrayAt = (data, indexA, indexB) => {\n    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n\nvar updateAt = (fieldValues, index, value) => {\n    fieldValues[index] = value;\n    return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;\n    const [fields, setFields] = React__default.useState(control._getFieldArray(name));\n    const ids = React__default.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = React__default.useRef(fields);\n    const _name = React__default.useRef(name);\n    const _actioned = React__default.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules &&\n        control.register(name, rules);\n    React__default.useEffect(() => control._subjects.array.subscribe({\n        next: ({ values, name: fieldArrayName, }) => {\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n    }).unsubscribe, [control]);\n    const updateValues = React__default.useCallback((updatedFieldArrayValues) => {\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [control, name]);\n    const append = (value, options) => {\n        const appendValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const prepend = (value, options) => {\n        const prependValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const remove = (index) => {\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) &&\n            set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index,\n        });\n    };\n    const insert$1 = (index, value, options) => {\n        const insertValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value),\n        });\n    };\n    const swap = (indexA, indexB) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB,\n        }, false);\n    };\n    const move = (from, to) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to,\n        }, false);\n    };\n    const update = (index, value) => {\n        const updateValue = cloneObject(value);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue,\n        }, true, false);\n    };\n    const replace = (value) => {\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([...updatedFieldArrayValues]);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);\n    };\n    React__default.useEffect(() => {\n        control._state.action = false;\n        isWatched(name, control._names) &&\n            control._subjects.state.next({\n                ...control._formState,\n            });\n        if (_actioned.current &&\n            (!getValidationModes(control._options.mode).isOnSubmit ||\n                control._formState.isSubmitted) &&\n            !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([name]).then((result) => {\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError\n                        ? (!error && existingError.type) ||\n                            (error &&\n                                (existingError.type !== error.type ||\n                                    existingError.message !== error.message))\n                        : error && error.type) {\n                        error\n                            ? set(control._formState.errors, name, error)\n                            : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors,\n                        });\n                    }\n                });\n            }\n            else {\n                const field = get(control._fields, name);\n                if (field &&\n                    field._f &&\n                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&\n                        getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&\n                        control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name),\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues),\n        });\n        control._names.focus &&\n            iterateFieldsByAction(control._fields, (ref, key) => {\n                if (control._names.focus &&\n                    key.startsWith(control._names.focus) &&\n                    ref.focus) {\n                    ref.focus();\n                    return 1;\n                }\n                return;\n            });\n        control._names.focus = '';\n        control._setValid();\n        _actioned.current = false;\n    }, [fields, name, control]);\n    React__default.useEffect(() => {\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return () => {\n            const updateMounted = (name, value) => {\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, keyName, shouldUnregister]);\n    return {\n        swap: React__default.useCallback(swap, [updateValues, name, control]),\n        move: React__default.useCallback(move, [updateValues, name, control]),\n        prepend: React__default.useCallback(prepend, [updateValues, name, control]),\n        append: React__default.useCallback(append, [updateValues, name, control]),\n        remove: React__default.useCallback(remove, [updateValues, name, control]),\n        insert: React__default.useCallback(insert$1, [updateValues, name, control]),\n        update: React__default.useCallback(update, [updateValues, name, control]),\n        replace: React__default.useCallback(replace, [updateValues, name, control]),\n        fields: React__default.useMemo(() => fields.map((field, index) => ({\n            ...field,\n            [keyName]: ids.current[index] || generateId(),\n        })), [fields, keyName]),\n    };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n    const _formControl = React__default.useRef(undefined);\n    const _values = React__default.useRef(undefined);\n    const [formState, updateFormState] = React__default.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues)\n            ? undefined\n            : props.defaultValues,\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...(props.formControl ? props.formControl : createFormControl(props)),\n            formState,\n        };\n        if (props.formControl &&\n            props.defaultValues &&\n            !isFunction(props.defaultValues)) {\n            props.formControl.reset(props.defaultValues, props.resetOptions);\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(() => {\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: () => updateFormState({ ...control._formState }),\n            reRenderRoot: true,\n        });\n        updateFormState((data) => ({\n            ...data,\n            isReady: true,\n        }));\n        control._formState.isReady = true;\n        return sub;\n    }, [control]);\n    React__default.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n    React__default.useEffect(() => {\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [control, props.mode, props.reValidateMode]);\n    React__default.useEffect(() => {\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [control, props.errors]);\n    React__default.useEffect(() => {\n        props.shouldUnregister &&\n            control._subjects.state.next({\n                values: control._getWatch(),\n            });\n    }, [control, props.shouldUnregister]);\n    React__default.useEffect(() => {\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty,\n                });\n            }\n        }\n    }, [control, formState.isDirty]);\n    React__default.useEffect(() => {\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state) => ({ ...state }));\n        }\n        else {\n            control._resetDefaultValues();\n        }\n    }, [control, props.values]);\n    React__default.useEffect(() => {\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({ ...control._formState });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n\nexport { Controller, Form, FormProvider, appendErrors, createFormControl, get, set, useController, useFieldArray, useForm, useFormContext, useFormState, useWatch };\n//# sourceMappingURL=index.esm.mjs.map\n", "import * as React from \"react\";\nfunction ChevronDownIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m19.5 8.25-7.5 7.5-7.5-7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronDownIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeSlashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeSlashIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction LanguageIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(LanguageIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PhoneIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhoneIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction UserIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserIcon);\nexport default ForwardRef;", "import React from 'react'\nimport { clsx } from 'clsx'\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  helperText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  fullWidth?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  (\n    {\n      className,\n      label,\n      error,\n      helperText,\n      leftIcon,\n      rightIcon,\n      fullWidth = false,\n      id,\n      ...props\n    },\n    ref\n  ) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n\n    const inputClasses = clsx(\n      'form-input',\n      'block px-3 py-2 border rounded-md shadow-sm',\n      'placeholder-gray-400 focus:outline-none transition-colors duration-200',\n      leftIcon && 'pl-10',\n      rightIcon && 'pr-10',\n      error\n        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'\n        : 'border-gray-300 focus:ring-cultural-500 focus:border-cultural-500',\n      fullWidth ? 'w-full' : 'w-auto',\n      className\n    )\n\n    return (\n      <div className={clsx('space-y-1', fullWidth && 'w-full')}>\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700\"\n          >\n            {label}\n            {props.required && <span className=\"text-red-500 ml-1\">*</span>}\n          </label>\n        )}\n        \n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-400\">{leftIcon}</span>\n            </div>\n          )}\n          \n          <input\n            ref={ref}\n            id={inputId}\n            className={inputClasses}\n            {...props}\n          />\n          \n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-400\">{rightIcon}</span>\n            </div>\n          )}\n        </div>\n        \n        {error && (\n          <p className=\"text-sm text-red-600\" role=\"alert\">\n            {error}\n          </p>\n        )}\n        \n        {helperText && !error && (\n          <p className=\"text-sm text-gray-500\">\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport default Input\n", "import React, { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { useTranslation } from 'react-i18next'\nimport { EyeIcon, EyeSlashIcon, EnvelopeIcon, UserIcon, PhoneIcon } from '@heroicons/react/24/outline'\nimport Button from '@/components/ui/Button'\nimport Input from '@/components/ui/Input'\nimport Card, { CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card'\nimport { RegistrationData } from '@/types/user'\nimport { useAuth } from '@/features/auth/hooks/useAuth'\n\ninterface RegistrationFormProps {\n  onSuccess?: () => void\n  onSwitchToLogin?: () => void\n}\n\ninterface FormData {\n  name: string\n  email: string\n  password: string\n  confirmPassword: string\n  phoneNumber?: string\n  privacyConsent: boolean\n  culturalConsent: boolean\n}\n\nconst RegistrationForm: React.FC<RegistrationFormProps> = ({\n  onSuccess,\n  onSwitchToLogin,\n}) => {\n  const { t } = useTranslation()\n  const { register: registerUser, isLoading, error } = useAuth()\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    formState: { errors, isValid },\n  } = useForm<FormData>({\n    mode: 'onChange',\n    defaultValues: {\n      privacyConsent: false,\n      culturalConsent: false,\n    },\n  })\n\n  const password = watch('password')\n\n  const onSubmit = async (data: FormData) => {\n    try {\n      const registrationData: RegistrationData = {\n        name: data.name,\n        email: data.email,\n        password: data.password,\n        phoneNumber: data.phoneNumber,\n        preferredLanguage: 'en', // Will be updated based on i18n\n        privacyConsent: data.privacyConsent,\n        culturalConsent: data.culturalConsent,\n      }\n\n      await registerUser(registrationData)\n      onSuccess?.()\n    } catch (error) {\n      // Error is handled by the auth store\n      console.error('Registration failed:', error)\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto\" variant=\"elevated\">\n      <CardHeader>\n        <CardTitle className=\"text-center text-cultural-gradient\">\n          {t('auth.welcomeToUbuntu')}\n        </CardTitle>\n        <CardDescription className=\"text-center\">\n          {t('app.description')}\n        </CardDescription>\n      </CardHeader>\n\n      <CardContent>\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          {/* Name Field */}\n          <Input\n            label={t('auth.name')}\n            type=\"text\"\n            leftIcon={<UserIcon className=\"w-5 h-5\" />}\n            fullWidth\n            error={errors.name?.message}\n            {...register('name', {\n              required: t('validation.required'),\n              minLength: {\n                value: 2,\n                message: t('validation.name'),\n              },\n            })}\n          />\n\n          {/* Email Field */}\n          <Input\n            label={t('auth.email')}\n            type=\"email\"\n            leftIcon={<EnvelopeIcon className=\"w-5 h-5\" />}\n            fullWidth\n            error={errors.email?.message}\n            {...register('email', {\n              required: t('validation.required'),\n              pattern: {\n                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\n                message: t('validation.email'),\n              },\n            })}\n          />\n\n          {/* Phone Number Field (Optional) */}\n          <Input\n            label={`${t('auth.phoneNumber')} (${t('common.optional')})`}\n            type=\"tel\"\n            leftIcon={<PhoneIcon className=\"w-5 h-5\" />}\n            fullWidth\n            helperText=\"South African format: +27 XX XXX XXXX\"\n            error={errors.phoneNumber?.message}\n            {...register('phoneNumber', {\n              pattern: {\n                value: /^\\+27[0-9]{9}$/,\n                message: t('validation.phoneNumber'),\n              },\n            })}\n          />\n\n          {/* Password Field */}\n          <Input\n            label={t('auth.password')}\n            type={showPassword ? 'text' : 'password'}\n            fullWidth\n            error={errors.password?.message}\n            rightIcon={\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                {showPassword ? (\n                  <EyeSlashIcon className=\"w-5 h-5\" />\n                ) : (\n                  <EyeIcon className=\"w-5 h-5\" />\n                )}\n              </button>\n            }\n            {...register('password', {\n              required: t('validation.required'),\n              minLength: {\n                value: 8,\n                message: t('validation.password'),\n              },\n            })}\n          />\n\n          {/* Confirm Password Field */}\n          <Input\n            label={t('auth.confirmPassword')}\n            type={showConfirmPassword ? 'text' : 'password'}\n            fullWidth\n            error={errors.confirmPassword?.message}\n            rightIcon={\n              <button\n                type=\"button\"\n                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                {showConfirmPassword ? (\n                  <EyeSlashIcon className=\"w-5 h-5\" />\n                ) : (\n                  <EyeIcon className=\"w-5 h-5\" />\n                )}\n              </button>\n            }\n            {...register('confirmPassword', {\n              required: t('validation.required'),\n              validate: (value) =>\n                value === password || t('validation.passwordMatch'),\n            })}\n          />\n\n          {/* Privacy Consent */}\n          <div className=\"space-y-3\">\n            <label className=\"flex items-start space-x-3\">\n              <input\n                type=\"checkbox\"\n                className=\"mt-1 h-4 w-4 text-cultural-600 focus:ring-cultural-500 border-gray-300 rounded\"\n                {...register('privacyConsent', {\n                  required: 'Privacy consent is required',\n                })}\n              />\n              <span className=\"text-sm text-gray-700\">\n                {t('privacy.consent')}\n                <a\n                  href=\"/privacy\"\n                  className=\"text-cultural-600 hover:text-cultural-700 ml-1\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  {t('privacy.dataUsage')}\n                </a>\n              </span>\n            </label>\n            {errors.privacyConsent && (\n              <p className=\"text-sm text-red-600\">{errors.privacyConsent.message}</p>\n            )}\n\n            <label className=\"flex items-start space-x-3\">\n              <input\n                type=\"checkbox\"\n                className=\"mt-1 h-4 w-4 text-cultural-600 focus:ring-cultural-500 border-gray-300 rounded\"\n                {...register('culturalConsent')}\n              />\n              <span className=\"text-sm text-gray-700\">\n                {t('privacy.culturalConsent')} ({t('common.optional')})\n              </span>\n            </label>\n          </div>\n\n          {/* Error Display */}\n          {error && (\n            <div className=\"p-3 bg-red-50 border border-red-200 rounded-md\">\n              <p className=\"text-sm text-red-600\">{error}</p>\n            </div>\n          )}\n\n          {/* Submit Button */}\n          <Button\n            type=\"submit\"\n            variant=\"cultural\"\n            size=\"lg\"\n            fullWidth\n            isLoading={isLoading}\n            disabled={!isValid}\n          >\n            {t('auth.createAccount')}\n          </Button>\n\n          {/* Switch to Login */}\n          <div className=\"text-center\">\n            <span className=\"text-sm text-gray-600\">\n              {t('auth.alreadyHaveAccount')}{' '}\n              <button\n                type=\"button\"\n                onClick={onSwitchToLogin}\n                className=\"text-cultural-600 hover:text-cultural-700 font-medium\"\n              >\n                {t('auth.login')}\n              </button>\n            </span>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n\nexport default RegistrationForm\n", "import React, { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { useTranslation } from 'react-i18next'\nimport { EyeIcon, EyeSlashIcon, EnvelopeIcon } from '@heroicons/react/24/outline'\nimport Button from '@/components/ui/Button'\nimport Input from '@/components/ui/Input'\nimport Card, { CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card'\nimport { useAuth } from '@/features/auth/hooks/useAuth'\n\ninterface LoginFormProps {\n  onSuccess?: () => void\n  onSwitchToRegister?: () => void\n}\n\ninterface FormData {\n  email: string\n  password: string\n}\n\nconst LoginForm: React.FC<LoginFormProps> = ({\n  onSuccess,\n  onSwitchToRegister,\n}) => {\n  const { t } = useTranslation()\n  const { login, loginWithProvider, isLoading, error } = useAuth()\n  const [showPassword, setShowPassword] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isValid },\n  } = useForm<FormData>({\n    mode: 'onChange',\n  })\n\n  const onSubmit = async (data: FormData) => {\n    try {\n      await login(data.email, data.password)\n      onSuccess?.()\n    } catch (error) {\n      // Error is handled by the auth store\n      console.error('Login failed:', error)\n    }\n  }\n\n  const handleProviderLogin = async (provider: 'google' | 'facebook') => {\n    try {\n      await loginWithProvider(provider)\n      onSuccess?.()\n    } catch (error) {\n      console.error('Provider login failed:', error)\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto\" variant=\"elevated\">\n      <CardHeader>\n        <CardTitle className=\"text-center text-cultural-gradient\">\n          {t('auth.welcomeBack')}\n        </CardTitle>\n        <CardDescription className=\"text-center\">\n          Sign in to continue your cultural journey\n        </CardDescription>\n      </CardHeader>\n\n      <CardContent>\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          {/* Email Field */}\n          <Input\n            label={t('auth.email')}\n            type=\"email\"\n            leftIcon={<EnvelopeIcon className=\"w-5 h-5\" />}\n            fullWidth\n            error={errors.email?.message}\n            {...register('email', {\n              required: t('validation.required'),\n              pattern: {\n                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\n                message: t('validation.email'),\n              },\n            })}\n          />\n\n          {/* Password Field */}\n          <Input\n            label={t('auth.password')}\n            type={showPassword ? 'text' : 'password'}\n            fullWidth\n            error={errors.password?.message}\n            rightIcon={\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                {showPassword ? (\n                  <EyeSlashIcon className=\"w-5 h-5\" />\n                ) : (\n                  <EyeIcon className=\"w-5 h-5\" />\n                )}\n              </button>\n            }\n            {...register('password', {\n              required: t('validation.required'),\n            })}\n          />\n\n          {/* Forgot Password Link */}\n          <div className=\"text-right\">\n            <button\n              type=\"button\"\n              className=\"text-sm text-cultural-600 hover:text-cultural-700\"\n            >\n              {t('auth.forgotPassword')}\n            </button>\n          </div>\n\n          {/* Error Display */}\n          {error && (\n            <div className=\"p-3 bg-red-50 border border-red-200 rounded-md\">\n              <p className=\"text-sm text-red-600\">{error}</p>\n            </div>\n          )}\n\n          {/* Submit Button */}\n          <Button\n            type=\"submit\"\n            variant=\"cultural\"\n            size=\"lg\"\n            fullWidth\n            isLoading={isLoading}\n            disabled={!isValid}\n          >\n            {t('auth.login')}\n          </Button>\n\n          {/* Divider */}\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n            </div>\n          </div>\n\n          {/* Social Login Buttons */}\n          <div className=\"space-y-2\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              size=\"lg\"\n              fullWidth\n              onClick={() => handleProviderLogin('google')}\n              leftIcon={\n                <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  />\n                </svg>\n              }\n            >\n              {t('auth.signInWith', { provider: 'Google' })}\n            </Button>\n\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              size=\"lg\"\n              fullWidth\n              onClick={() => handleProviderLogin('facebook')}\n              leftIcon={\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\" />\n                </svg>\n              }\n            >\n              {t('auth.signInWith', { provider: 'Facebook' })}\n            </Button>\n          </div>\n\n          {/* Switch to Register */}\n          <div className=\"text-center\">\n            <span className=\"text-sm text-gray-600\">\n              {t('auth.dontHaveAccount')}{' '}\n              <button\n                type=\"button\"\n                onClick={onSwitchToRegister}\n                className=\"text-cultural-600 hover:text-cultural-700 font-medium\"\n              >\n                {t('auth.register')}\n              </button>\n            </span>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n\nexport default LoginForm\n", "import React, { useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { ChevronDownIcon, LanguageIcon } from '@heroicons/react/24/outline'\nimport { clsx } from 'clsx'\n\ninterface Language {\n  code: string\n  name: string\n  nativeName: string\n  flag: string\n}\n\nconst languages: Language[] = [\n  { code: 'en', name: 'English', nativeName: 'English', flag: '🇿🇦' },\n  { code: 'af', name: 'Afrikaans', nativeName: 'Afrikaans', flag: '🇿🇦' },\n  { code: 'zu', name: '<PERSON><PERSON>', nativeName: 'IsiZulu', flag: '🇿🇦' },\n  { code: 'xh', name: 'Xhosa', nativeName: 'IsiXhosa', flag: '🇿🇦' },\n]\n\nconst LanguageSelector: React.FC = () => {\n  const { i18n } = useTranslation()\n  const [isOpen, setIsOpen] = useState(false)\n\n  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0]\n\n  const handleLanguageChange = (languageCode: string) => {\n    i18n.changeLanguage(languageCode)\n    setIsOpen(false)\n  }\n\n  return (\n    <div className=\"relative\">\n      <button\n        type=\"button\"\n        className={clsx(\n          'flex items-center space-x-2 px-3 py-2 text-sm font-medium',\n          'text-gray-700 bg-white border border-gray-300 rounded-md',\n          'hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cultural-500',\n          'transition-colors duration-200'\n        )}\n        onClick={() => setIsOpen(!isOpen)}\n        aria-expanded={isOpen}\n        aria-haspopup=\"listbox\"\n      >\n        <LanguageIcon className=\"w-4 h-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.flag}</span>\n        <span className=\"hidden md:inline\">{currentLanguage.nativeName}</span>\n        <ChevronDownIcon\n          className={clsx(\n            'w-4 h-4 transition-transform duration-200',\n            isOpen && 'rotate-180'\n          )}\n        />\n      </button>\n\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          \n          {/* Dropdown */}\n          <div className=\"absolute right-0 z-20 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg\">\n            <div className=\"py-1\" role=\"listbox\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  type=\"button\"\n                  className={clsx(\n                    'w-full px-4 py-2 text-left text-sm hover:bg-gray-100',\n                    'focus:outline-none focus:bg-gray-100',\n                    'flex items-center space-x-3',\n                    language.code === i18n.language && 'bg-cultural-50 text-cultural-700'\n                  )}\n                  onClick={() => handleLanguageChange(language.code)}\n                  role=\"option\"\n                  aria-selected={language.code === i18n.language}\n                >\n                  <span className=\"text-lg\">{language.flag}</span>\n                  <div className=\"flex-1\">\n                    <div className=\"font-medium\">{language.nativeName}</div>\n                    <div className=\"text-xs text-gray-500\">{language.name}</div>\n                  </div>\n                  {language.code === i18n.language && (\n                    <div className=\"w-2 h-2 bg-cultural-500 rounded-full\" />\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  )\n}\n\nexport default LanguageSelector\n", "import React, { useState } from 'react'\nimport { useNavigate } from 'react-router-dom'\nimport { useTranslation } from 'react-i18next'\nimport RegistrationForm from '@/features/auth/components/RegistrationForm'\nimport LoginForm from '@/features/auth/components/LoginForm'\nimport LanguageSelector from '@/components/LanguageSelector'\n\ntype AuthMode = 'login' | 'register'\n\nconst AuthPage: React.FC = () => {\n  const [mode, setMode] = useState<AuthMode>('register')\n  const navigate = useNavigate()\n  const { t } = useTranslation()\n\n  const handleAuthSuccess = () => {\n    // Navigate to cultural onboarding for new users, or home for returning users\n    if (mode === 'register') {\n      navigate('/onboarding/cultural')\n    } else {\n      navigate('/')\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-cultural-50 via-white to-ubuntu-50 flex flex-col\">\n      {/* Header */}\n      <header className=\"p-4 flex justify-between items-center\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-gradient-to-r from-cultural-500 to-ubuntu-500 rounded-full\" />\n          <h1 className=\"text-xl font-bold text-cultural-gradient\">\n            {t('app.name')}\n          </h1>\n        </div>\n        <LanguageSelector />\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1 flex items-center justify-center p-4\">\n        <div className=\"w-full max-w-md\">\n          {mode === 'register' ? (\n            <RegistrationForm\n              onSuccess={handleAuthSuccess}\n              onSwitchToLogin={() => setMode('login')}\n            />\n          ) : (\n            <LoginForm\n              onSuccess={handleAuthSuccess}\n              onSwitchToRegister={() => setMode('register')}\n            />\n          )}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"p-4 text-center text-sm text-gray-600\">\n        <p>{t('app.tagline')}</p>\n        <p className=\"mt-1\">\n          Building bridges across cultures • Celebrating diversity • Ubuntu philosophy\n        </p>\n      </footer>\n\n      {/* reCAPTCHA container for phone authentication */}\n      <div id=\"recaptcha-container\" />\n    </div>\n  )\n}\n\nexport default AuthPage\n"], "names": ["isCheckBoxInput", "element", "isDateObject", "value", "isNullOrUndefined", "isObjectType", "isObject", "getEventValue", "event", "getNodeParentName", "name", "isNameInFieldArray", "names", "isPlainObject", "tempObject", "prototypeCopy", "isWeb", "cloneObject", "data", "copy", "isArray", "isFileListInstance", "key", "compact", "isUndefined", "val", "get", "object", "path", "defaultValue", "result", "isBoolean", "is<PERSON>ey", "stringToPath", "input", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "React__default", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "_key", "useIsomorphicLayoutEffect", "React.useLayoutEffect", "React.useEffect", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "fieldName", "appendErrors", "validateAllFieldCriteria", "errors", "type", "message", "convertToArrayPayload", "createSubject", "_observers", "observer", "o", "isPrimitive", "deepEqual", "object1", "object2", "keys1", "keys2", "val1", "val2", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "isMultipleSelect", "isRadioInput", "isRadioOrCheckbox", "ref", "live", "baseGet", "updatePath", "isEmptyArray", "obj", "unset", "paths", "childObject", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultValues", "defaultResult", "validResult", "getCheckboxValue", "options", "values", "option", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "_f", "getResolverOptions", "fieldsNames", "_fields", "criteriaMode", "shouldUseNativeValidation", "field", "isRegex", "getRuleValue", "rule", "getValidationModes", "mode", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "validateFunction", "hasValidation", "isWatched", "isBlurEvent", "watchName", "iterateFieldsByAction", "action", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "error", "found<PERSON><PERSON>r", "shouldRenderFormState", "formStateData", "_proxyFormState", "updateFormState", "shouldSubscribeByName", "signalName", "exact", "currentName", "skipValidation", "isTouched", "isSubmitted", "reValidateMode", "unsetEmptyArray", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "getValueAndMessage", "validationData", "validateField", "disabled<PERSON>ieldN<PERSON>s", "isFieldArray", "refs", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "validate", "mount", "inputValue", "inputRef", "setCustomValidity", "isRadio", "isCheckBox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueNumber", "valueDate", "convertTimeToDate", "time", "isTime", "isWeek", "maxLengthOutput", "minLengthOutput", "patternValue", "validateError", "validationResult", "defaultOptions", "createFormControl", "props", "_options", "_formState", "_defaultValues", "_formValues", "_state", "delayError<PERSON><PERSON><PERSON>", "timer", "_proxySubscribeFormState", "_subjects", "shouldDisplayAllAssociatedErrors", "debounce", "callback", "wait", "_setValid", "shouldUpdateValid", "<PERSON><PERSON><PERSON><PERSON>", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "isValidating", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "field<PERSON><PERSON><PERSON>", "touchedFields", "_getDirty", "updateErrors", "_setErrors", "updateValidAndValue", "shouldSkipSetValueAs", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "output", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "fieldState", "previousFieldError", "updatedFormState", "executeSchemaAndUpdateState", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "context", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "_removeUnmounted", "unregister", "getV<PERSON>ues", "_getWatch", "_getFieldArray", "optionRef", "checkboxRef", "radioRef", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "onChange", "target", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "getFieldState", "clearErrors", "inputName", "setError", "currentError", "currentRef", "restOfErrorTree", "watch", "payload", "_subscribe", "_setFormState", "subscribe", "_setDisabledField", "disabled", "register", "disabledIsDefined", "fieldRef", "radioOrCheckbox", "_focusError", "_disableForm", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "reset<PERSON>ield", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "fieldsToCheck", "form", "reset", "setFocus", "methods", "useForm", "_formControl", "_values", "sub", "isDirty", "state", "ChevronDownIcon", "title", "titleId", "svgRef", "React.createElement", "ForwardRef", "React.forwardRef", "EnvelopeIcon", "EyeSlashIcon", "EyeIcon", "LanguageIcon", "PhoneIcon", "UserIcon", "Input", "React", "className", "label", "helperText", "leftIcon", "rightIcon", "fullWidth", "id", "inputId", "inputClasses", "clsx", "jsxs", "jsx", "RegistrationForm", "onSuccess", "onSwitchToLogin", "useTranslation", "registerUser", "isLoading", "useAuth", "showPassword", "setShowPassword", "useState", "showConfirmPassword", "setShowConfirmPassword", "password", "onSubmit", "registrationData", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "LoginForm", "onSwitchToRegister", "login", "loginWithProvider", "handleProviderLogin", "provider", "languages", "LanguageSelector", "i18n", "isOpen", "setIsOpen", "currentLanguage", "lang", "handleLanguageChange", "languageCode", "Fragment", "language", "AuthPage", "setMode", "navigate", "useNavigate", "t", "handleAuthSuccess"], "mappings": "uMAGA,IAAIA,GAAmBC,GAAYA,EAAQ,OAAS,WAEhDC,GAAgBC,GAAUA,aAAiB,KAE3CC,EAAqBD,GAAUA,GAAS,KAE5C,MAAME,GAAgBF,GAAU,OAAOA,GAAU,SACjD,IAAIG,EAAYH,GAAU,CAACC,EAAkBD,CAAK,GAC9C,CAAC,MAAM,QAAQA,CAAK,GACpBE,GAAaF,CAAK,GAClB,CAACD,GAAaC,CAAK,EAEnBI,GAAiBC,GAAUF,EAASE,CAAK,GAAKA,EAAM,OAClDR,GAAgBQ,EAAM,MAAM,EACxBA,EAAM,OAAO,QACbA,EAAM,OAAO,MACjBA,EAEFC,GAAqBC,GAASA,EAAK,UAAU,EAAGA,EAAK,OAAO,aAAa,CAAC,GAAKA,EAE/EC,GAAqB,CAACC,EAAOF,IAASE,EAAM,IAAIH,GAAkBC,CAAI,CAAC,EAEvEG,GAAiBC,GAAe,CAChC,MAAMC,EAAgBD,EAAW,aAAeA,EAAW,YAAY,UACvE,OAAQR,EAASS,CAAa,GAAKA,EAAc,eAAe,eAAe,CACnF,EAEIC,GAAQ,OAAO,OAAW,KAC1B,OAAO,OAAO,YAAgB,KAC9B,OAAO,SAAa,IAExB,SAASC,EAAYC,EAAM,CACvB,IAAIC,EACJ,MAAMC,EAAU,MAAM,QAAQF,CAAI,EAC5BG,EAAqB,OAAO,SAAa,IAAcH,aAAgB,SAAW,GACxF,GAAIA,aAAgB,KAChBC,EAAO,IAAI,KAAKD,CAAI,UAEfA,aAAgB,IACrBC,EAAO,IAAI,IAAID,CAAI,UAEd,EAAEF,KAAUE,aAAgB,MAAQG,MACxCD,GAAWd,EAASY,CAAI,GAEzB,GADAC,EAAOC,EAAU,CAAA,EAAK,CAAE,EACpB,CAACA,GAAW,CAACP,GAAcK,CAAI,EAC/BC,EAAOD,MAGP,WAAWI,KAAOJ,EACVA,EAAK,eAAeI,CAAG,IACvBH,EAAKG,CAAG,EAAIL,EAAYC,EAAKI,CAAG,CAAC,OAM7C,QAAOJ,EAEX,OAAOC,CACX,CAEA,IAAII,GAAWpB,GAAU,MAAM,QAAQA,CAAK,EAAIA,EAAM,OAAO,OAAO,EAAI,CAAE,EAEtEqB,EAAeC,GAAQA,IAAQ,OAE/BC,EAAM,CAACC,EAAQC,EAAMC,IAAiB,CACtC,GAAI,CAACD,GAAQ,CAACtB,EAASqB,CAAM,EACzB,OAAOE,EAEX,MAAMC,EAASP,GAAQK,EAAK,MAAM,WAAW,CAAC,EAAE,OAAO,CAACE,EAAQR,IAAQlB,EAAkB0B,CAAM,EAAIA,EAASA,EAAOR,CAAG,EAAGK,CAAM,EAChI,OAAOH,EAAYM,CAAM,GAAKA,IAAWH,EACnCH,EAAYG,EAAOC,CAAI,CAAC,EACpBC,EACAF,EAAOC,CAAI,EACfE,CACV,EAEIC,EAAa5B,GAAU,OAAOA,GAAU,UAExC6B,GAAS7B,GAAU,QAAQ,KAAKA,CAAK,EAErC8B,GAAgBC,GAAUX,GAAQW,EAAM,QAAQ,YAAa,EAAE,EAAE,MAAM,OAAO,CAAC,EAE/EC,EAAM,CAACR,EAAQC,EAAMzB,IAAU,CAC/B,IAAIiC,EAAQ,GACZ,MAAMC,EAAWL,GAAMJ,CAAI,EAAI,CAACA,CAAI,EAAIK,GAAaL,CAAI,EACnDU,EAASD,EAAS,OAClBE,EAAYD,EAAS,EAC3B,KAAO,EAAEF,EAAQE,GAAQ,CACrB,MAAMhB,EAAMe,EAASD,CAAK,EAC1B,IAAII,EAAWrC,EACf,GAAIiC,IAAUG,EAAW,CACrB,MAAME,EAAWd,EAAOL,CAAG,EAC3BkB,EACIlC,EAASmC,CAAQ,GAAK,MAAM,QAAQA,CAAQ,EACtCA,EACC,MAAM,CAACJ,EAASD,EAAQ,CAAC,CAAC,EAEvB,CAAE,EADF,CAAA,CAE1B,CACQ,GAAId,IAAQ,aAAeA,IAAQ,eAAiBA,IAAQ,YACxD,OAEJK,EAAOL,CAAG,EAAIkB,EACdb,EAASA,EAAOL,CAAG,CAC3B,CACA,EAEA,MAAMoB,GAAS,CACX,KAAM,OACN,UAAW,UAEf,EACMC,EAAkB,CACpB,OAAQ,SACR,SAAU,WACV,SAAU,WACV,UAAW,YACX,IAAK,KACT,EACMC,GAAyB,CAC3B,IAAK,MACL,IAAK,MACL,UAAW,YACX,UAAW,YACX,QAAS,UACT,SAAU,WACV,SAAU,UACd,EAEwBC,EAAe,cAAc,IAAI,EAmEzD,IAAIC,GAAoB,CAACC,EAAWC,EAASC,EAAqBC,EAAS,KAAS,CAChF,MAAMpB,EAAS,CACX,cAAekB,EAAQ,cAC1B,EACD,UAAW1B,KAAOyB,EACd,OAAO,eAAejB,EAAQR,EAAK,CAC/B,IAAK,IAAM,CACP,MAAM6B,EAAO7B,EACb,OAAI0B,EAAQ,gBAAgBG,CAAI,IAAMR,EAAgB,MAClDK,EAAQ,gBAAgBG,CAAI,EAAI,CAACD,GAAUP,EAAgB,KAGxDI,EAAUI,CAAI,CACxB,CACb,CAAS,EAEL,OAAOrB,CACX,EAEA,MAAMsB,GAA4B,OAAO,OAAW,IAAcC,EAAqB,gBAAGC,EAAe,UAgEzG,IAAIC,EAAYpD,GAAU,OAAOA,GAAU,SAEvCqD,GAAsB,CAAC5C,EAAO6C,EAAQC,EAAYC,EAAU9B,IACxD0B,EAAS3C,CAAK,GACd+C,GAAYF,EAAO,MAAM,IAAI7C,CAAK,EAC3Bc,EAAIgC,EAAY9C,EAAOiB,CAAY,GAE1C,MAAM,QAAQjB,CAAK,EACZA,EAAM,IAAKgD,IAAeD,GAAYF,EAAO,MAAM,IAAIG,CAAS,EAAGlC,EAAIgC,EAAYE,CAAS,EAAE,GAEzGD,IAAaF,EAAO,SAAW,IACxBC,GAsVPG,GAAe,CAACnD,EAAMoD,EAA0BC,EAAQC,EAAMC,IAAYH,EACxE,CACE,GAAGC,EAAOrD,CAAI,EACd,MAAO,CACH,GAAIqD,EAAOrD,CAAI,GAAKqD,EAAOrD,CAAI,EAAE,MAAQqD,EAAOrD,CAAI,EAAE,MAAQ,CAAA,EAC9D,CAACsD,CAAI,EAAGC,GAAW,EACtB,CACT,EACM,CAAE,EAEJC,GAAyB/D,GAAW,MAAM,QAAQA,CAAK,EAAIA,EAAQ,CAACA,CAAK,EAEzEgE,GAAgB,IAAM,CACtB,IAAIC,EAAa,CAAE,EAiBnB,MAAO,CACH,IAAI,WAAY,CACZ,OAAOA,CACV,EACD,KApBUjE,GAAU,CACpB,UAAWkE,KAAYD,EACnBC,EAAS,MAAQA,EAAS,KAAKlE,CAAK,CAE3C,EAiBG,UAhBekE,IACfD,EAAW,KAAKC,CAAQ,EACjB,CACH,YAAa,IAAM,CACfD,EAAaA,EAAW,OAAQE,GAAMA,IAAMD,CAAQ,CACvD,CACJ,GAWD,YATgB,IAAM,CACtBD,EAAa,CAAE,CAClB,CAQA,CACL,EAEIG,GAAepE,GAAUC,EAAkBD,CAAK,GAAK,CAACE,GAAaF,CAAK,EAE5E,SAASqE,GAAUC,EAASC,EAAS,CACjC,GAAIH,GAAYE,CAAO,GAAKF,GAAYG,CAAO,EAC3C,OAAOD,IAAYC,EAEvB,GAAIxE,GAAauE,CAAO,GAAKvE,GAAawE,CAAO,EAC7C,OAAOD,EAAQ,YAAcC,EAAQ,QAAS,EAElD,MAAMC,EAAQ,OAAO,KAAKF,CAAO,EAC3BG,EAAQ,OAAO,KAAKF,CAAO,EACjC,GAAIC,EAAM,SAAWC,EAAM,OACvB,MAAO,GAEX,UAAWtD,KAAOqD,EAAO,CACrB,MAAME,EAAOJ,EAAQnD,CAAG,EACxB,GAAI,CAACsD,EAAM,SAAStD,CAAG,EACnB,MAAO,GAEX,GAAIA,IAAQ,MAAO,CACf,MAAMwD,EAAOJ,EAAQpD,CAAG,EACxB,GAAKpB,GAAa2E,CAAI,GAAK3E,GAAa4E,CAAI,GACvCxE,EAASuE,CAAI,GAAKvE,EAASwE,CAAI,GAC/B,MAAM,QAAQD,CAAI,GAAK,MAAM,QAAQC,CAAI,EACxC,CAACN,GAAUK,EAAMC,CAAI,EACrBD,IAASC,EACX,MAAO,EAEvB,CACA,CACI,MAAO,EACX,CAEA,IAAIC,EAAiB5E,GAAUG,EAASH,CAAK,GAAK,CAAC,OAAO,KAAKA,CAAK,EAAE,OAElE6E,GAAe/E,GAAYA,EAAQ,OAAS,OAE5CgF,EAAc9E,GAAU,OAAOA,GAAU,WAEzC+E,GAAiB/E,GAAU,CAC3B,GAAI,CAACa,GACD,MAAO,GAEX,MAAMmE,EAAQhF,EAAQA,EAAM,cAAgB,EAC5C,OAAQA,aACHgF,GAASA,EAAM,YAAcA,EAAM,YAAY,YAAc,YACtE,EAEIC,GAAoBnF,GAAYA,EAAQ,OAAS,kBAEjDoF,GAAgBpF,GAAYA,EAAQ,OAAS,QAE7CqF,GAAqBC,GAAQF,GAAaE,CAAG,GAAKvF,GAAgBuF,CAAG,EAErEC,GAAQD,GAAQL,GAAcK,CAAG,GAAKA,EAAI,YAE9C,SAASE,GAAQ9D,EAAQ+D,EAAY,CACjC,MAAMpD,EAASoD,EAAW,MAAM,EAAG,EAAE,EAAE,OACvC,IAAItD,EAAQ,EACZ,KAAOA,EAAQE,GACXX,EAASH,EAAYG,CAAM,EAAIS,IAAUT,EAAO+D,EAAWtD,GAAO,CAAC,EAEvE,OAAOT,CACX,CACA,SAASgE,GAAaC,EAAK,CACvB,UAAWtE,KAAOsE,EACd,GAAIA,EAAI,eAAetE,CAAG,GAAK,CAACE,EAAYoE,EAAItE,CAAG,CAAC,EAChD,MAAO,GAGf,MAAO,EACX,CACA,SAASuE,EAAMlE,EAAQC,EAAM,CACzB,MAAMkE,EAAQ,MAAM,QAAQlE,CAAI,EAC1BA,EACAI,GAAMJ,CAAI,EACN,CAACA,CAAI,EACLK,GAAaL,CAAI,EACrBmE,EAAcD,EAAM,SAAW,EAAInE,EAAS8D,GAAQ9D,EAAQmE,CAAK,EACjE1D,EAAQ0D,EAAM,OAAS,EACvBxE,EAAMwE,EAAM1D,CAAK,EACvB,OAAI2D,GACA,OAAOA,EAAYzE,CAAG,EAEtBc,IAAU,IACR9B,EAASyF,CAAW,GAAKhB,EAAcgB,CAAW,GAC/C,MAAM,QAAQA,CAAW,GAAKJ,GAAaI,CAAW,IAC3DF,EAAMlE,EAAQmE,EAAM,MAAM,EAAG,EAAE,CAAC,EAE7BnE,CACX,CAEA,IAAIqE,GAAqB9E,GAAS,CAC9B,UAAWI,KAAOJ,EACd,GAAI+D,EAAW/D,EAAKI,CAAG,CAAC,EACpB,MAAO,GAGf,MAAO,EACX,EAEA,SAAS2E,GAAgB/E,EAAMgF,EAAS,GAAI,CACxC,MAAMC,EAAoB,MAAM,QAAQjF,CAAI,EAC5C,GAAIZ,EAASY,CAAI,GAAKiF,EAClB,UAAW7E,KAAOJ,EACV,MAAM,QAAQA,EAAKI,CAAG,CAAC,GACtBhB,EAASY,EAAKI,CAAG,CAAC,GAAK,CAAC0E,GAAkB9E,EAAKI,CAAG,CAAC,GACpD4E,EAAO5E,CAAG,EAAI,MAAM,QAAQJ,EAAKI,CAAG,CAAC,EAAI,CAAA,EAAK,CAAE,EAChD2E,GAAgB/E,EAAKI,CAAG,EAAG4E,EAAO5E,CAAG,CAAC,GAEhClB,EAAkBc,EAAKI,CAAG,CAAC,IACjC4E,EAAO5E,CAAG,EAAI,IAI1B,OAAO4E,CACX,CACA,SAASE,GAAgClF,EAAMwC,EAAY2C,EAAuB,CAC9E,MAAMF,EAAoB,MAAM,QAAQjF,CAAI,EAC5C,GAAIZ,EAASY,CAAI,GAAKiF,EAClB,UAAW7E,KAAOJ,EACV,MAAM,QAAQA,EAAKI,CAAG,CAAC,GACtBhB,EAASY,EAAKI,CAAG,CAAC,GAAK,CAAC0E,GAAkB9E,EAAKI,CAAG,CAAC,EAChDE,EAAYkC,CAAU,GACtBa,GAAY8B,EAAsB/E,CAAG,CAAC,EACtC+E,EAAsB/E,CAAG,EAAI,MAAM,QAAQJ,EAAKI,CAAG,CAAC,EAC9C2E,GAAgB/E,EAAKI,CAAG,EAAG,CAAE,CAAA,EAC7B,CAAE,GAAG2E,GAAgB/E,EAAKI,CAAG,CAAC,CAAG,EAGvC8E,GAAgClF,EAAKI,CAAG,EAAGlB,EAAkBsD,CAAU,EAAI,CAAE,EAAGA,EAAWpC,CAAG,EAAG+E,EAAsB/E,CAAG,CAAC,EAI/H+E,EAAsB/E,CAAG,EAAI,CAACkD,GAAUtD,EAAKI,CAAG,EAAGoC,EAAWpC,CAAG,CAAC,EAI9E,OAAO+E,CACX,CACA,IAAIC,GAAiB,CAACC,EAAe7C,IAAe0C,GAAgCG,EAAe7C,EAAYuC,GAAgBvC,CAAU,CAAC,EAE1I,MAAM8C,GAAgB,CAClB,MAAO,GACP,QAAS,EACb,EACMC,GAAc,CAAE,MAAO,GAAM,QAAS,EAAM,EAClD,IAAIC,GAAoBC,GAAY,CAChC,GAAI,MAAM,QAAQA,CAAO,EAAG,CACxB,GAAIA,EAAQ,OAAS,EAAG,CACpB,MAAMC,EAASD,EACV,OAAQE,GAAWA,GAAUA,EAAO,SAAW,CAACA,EAAO,QAAQ,EAC/D,IAAKA,GAAWA,EAAO,KAAK,EACjC,MAAO,CAAE,MAAOD,EAAQ,QAAS,CAAC,CAACA,EAAO,MAAQ,CAC9D,CACQ,OAAOD,EAAQ,CAAC,EAAE,SAAW,CAACA,EAAQ,CAAC,EAAE,SAEjCA,EAAQ,CAAC,EAAE,YAAc,CAACnF,EAAYmF,EAAQ,CAAC,EAAE,WAAW,KAAK,EAC3DnF,EAAYmF,EAAQ,CAAC,EAAE,KAAK,GAAKA,EAAQ,CAAC,EAAE,QAAU,GAClDF,GACA,CAAE,MAAOE,EAAQ,CAAC,EAAE,MAAO,QAAS,EAAI,EAC5CF,GACRD,EACd,CACI,OAAOA,EACX,EAEIM,GAAkB,CAAC3G,EAAO,CAAE,cAAA4G,EAAe,YAAAC,EAAa,WAAAC,CAAU,IAAOzF,EAAYrB,CAAK,EACxFA,EACA4G,EACI5G,IAAU,GACN,IACAA,GACI,CAACA,EAET6G,GAAezD,EAASpD,CAAK,EACzB,IAAI,KAAKA,CAAK,EACd8G,EACIA,EAAW9G,CAAK,EAChBA,EAElB,MAAM+G,GAAgB,CAClB,QAAS,GACT,MAAO,IACX,EACA,IAAIC,GAAiBR,GAAY,MAAM,QAAQA,CAAO,EAChDA,EAAQ,OAAO,CAACS,EAAUP,IAAWA,GAAUA,EAAO,SAAW,CAACA,EAAO,SACrE,CACE,QAAS,GACT,MAAOA,EAAO,KAC1B,EACUO,EAAUF,EAAa,EAC3BA,GAEN,SAASG,GAAcC,EAAI,CACvB,MAAM/B,EAAM+B,EAAG,IACf,OAAItC,GAAYO,CAAG,EACRA,EAAI,MAEXF,GAAaE,CAAG,EACT4B,GAAcG,EAAG,IAAI,EAAE,MAE9BlC,GAAiBG,CAAG,EACb,CAAC,GAAGA,EAAI,eAAe,EAAE,IAAI,CAAC,CAAE,MAAApF,CAAO,IAAKA,CAAK,EAExDH,GAAgBuF,CAAG,EACZmB,GAAiBY,EAAG,IAAI,EAAE,MAE9BR,GAAgBtF,EAAY+D,EAAI,KAAK,EAAI+B,EAAG,IAAI,MAAQ/B,EAAI,MAAO+B,CAAE,CAChF,CAEA,IAAIC,GAAqB,CAACC,EAAaC,EAASC,EAAcC,IAA8B,CACxF,MAAMzB,EAAS,CAAE,EACjB,UAAWxF,KAAQ8G,EAAa,CAC5B,MAAMI,EAAQlG,EAAI+F,EAAS/G,CAAI,EAC/BkH,GAASzF,EAAI+D,EAAQxF,EAAMkH,EAAM,EAAE,CAC3C,CACI,MAAO,CACH,aAAAF,EACA,MAAO,CAAC,GAAGF,CAAW,EACtB,OAAAtB,EACA,0BAAAyB,CACH,CACL,EAEIE,GAAW1H,GAAUA,aAAiB,OAEtC2H,GAAgBC,GAASvG,EAAYuG,CAAI,EACvCA,EACAF,GAAQE,CAAI,EACRA,EAAK,OACLzH,EAASyH,CAAI,EACTF,GAAQE,EAAK,KAAK,EACdA,EAAK,MAAM,OACXA,EAAK,MACTA,EAEVC,GAAsBC,IAAU,CAChC,WAAY,CAACA,GAAQA,IAAStF,EAAgB,SAC9C,SAAUsF,IAAStF,EAAgB,OACnC,WAAYsF,IAAStF,EAAgB,SACrC,QAASsF,IAAStF,EAAgB,IAClC,UAAWsF,IAAStF,EAAgB,SACxC,GAEA,MAAMuF,GAAiB,gBACvB,IAAIC,GAAwBC,GAAmB,CAAC,CAACA,GAC7C,CAAC,CAACA,EAAe,UACjB,CAAC,EAAGnD,EAAWmD,EAAe,QAAQ,GAClCA,EAAe,SAAS,YAAY,OAASF,IAC5C5H,EAAS8H,EAAe,QAAQ,GAC7B,OAAO,OAAOA,EAAe,QAAQ,EAAE,KAAMC,GAAqBA,EAAiB,YAAY,OAASH,EAAc,GAE9HI,GAAiB3B,GAAYA,EAAQ,QACpCA,EAAQ,UACLA,EAAQ,KACRA,EAAQ,KACRA,EAAQ,WACRA,EAAQ,WACRA,EAAQ,SACRA,EAAQ,UAEZ4B,GAAY,CAAC7H,EAAM+C,EAAQ+E,IAAgB,CAACA,IAC3C/E,EAAO,UACJA,EAAO,MAAM,IAAI/C,CAAI,GACrB,CAAC,GAAG+C,EAAO,KAAK,EAAE,KAAMgF,GAAc/H,EAAK,WAAW+H,CAAS,GAC3D,SAAS,KAAK/H,EAAK,MAAM+H,EAAU,MAAM,CAAC,CAAC,GAEvD,MAAMC,GAAwB,CAACxC,EAAQyC,EAAQnB,EAAaoB,IAAe,CACvE,UAAWtH,KAAOkG,GAAe,OAAO,KAAKtB,CAAM,EAAG,CAClD,MAAM0B,EAAQlG,EAAIwE,EAAQ5E,CAAG,EAC7B,GAAIsG,EAAO,CACP,KAAM,CAAE,GAAAN,EAAI,GAAGuB,CAAY,EAAKjB,EAChC,GAAIN,EAAI,CACJ,GAAIA,EAAG,MAAQA,EAAG,KAAK,CAAC,GAAKqB,EAAOrB,EAAG,KAAK,CAAC,EAAGhG,CAAG,GAAK,CAACsH,EACrD,MAAO,GAEN,GAAItB,EAAG,KAAOqB,EAAOrB,EAAG,IAAKA,EAAG,IAAI,GAAK,CAACsB,EAC3C,MAAO,GAGP,GAAIF,GAAsBG,EAAcF,CAAM,EAC1C,KAGxB,SACqBrI,EAASuI,CAAY,GACtBH,GAAsBG,EAAcF,CAAM,EAC1C,KAGpB,CACA,CAEA,EAEA,SAASG,GAAkB/E,EAAQ0D,EAAS/G,EAAM,CAC9C,MAAMqI,EAAQrH,EAAIqC,EAAQrD,CAAI,EAC9B,GAAIqI,GAAS/G,GAAMtB,CAAI,EACnB,MAAO,CACH,MAAAqI,EACA,KAAArI,CACH,EAEL,MAAME,EAAQF,EAAK,MAAM,GAAG,EAC5B,KAAOE,EAAM,QAAQ,CACjB,MAAMgD,EAAYhD,EAAM,KAAK,GAAG,EAC1BgH,EAAQlG,EAAI+F,EAAS7D,CAAS,EAC9BoF,EAAatH,EAAIqC,EAAQH,CAAS,EACxC,GAAIgE,GAAS,CAAC,MAAM,QAAQA,CAAK,GAAKlH,IAASkD,EAC3C,MAAO,CAAE,KAAAlD,CAAM,EAEnB,GAAIsI,GAAcA,EAAW,KACzB,MAAO,CACH,KAAMpF,EACN,MAAOoF,CACV,EAEL,GAAIA,GAAcA,EAAW,MAAQA,EAAW,KAAK,KACjD,MAAO,CACH,KAAM,GAAGpF,CAAS,QAClB,MAAOoF,EAAW,IACrB,EAELpI,EAAM,IAAK,CACnB,CACI,MAAO,CACH,KAAAF,CACH,CACL,CAEA,IAAIuI,GAAwB,CAACC,EAAeC,EAAiBC,EAAiBlG,IAAW,CACrFkG,EAAgBF,CAAa,EAC7B,KAAM,CAAE,KAAAxI,EAAM,GAAGqC,CAAS,EAAKmG,EAC/B,OAAQnE,EAAchC,CAAS,GAC3B,OAAO,KAAKA,CAAS,EAAE,QAAU,OAAO,KAAKoG,CAAe,EAAE,QAC9D,OAAO,KAAKpG,CAAS,EAAE,KAAMzB,GAAQ6H,EAAgB7H,CAAG,KACnD,CAAC4B,GAAUP,EAAgB,IAAI,CAC5C,EAEI0G,GAAwB,CAAC3I,EAAM4I,EAAYC,IAAU,CAAC7I,GACtD,CAAC4I,GACD5I,IAAS4I,GACTpF,GAAsBxD,CAAI,EAAE,KAAM8I,GAAgBA,IAC7CD,EACKC,IAAgBF,EAChBE,EAAY,WAAWF,CAAU,GAC/BA,EAAW,WAAWE,CAAW,EAAE,EAE/CC,GAAiB,CAACjB,EAAakB,EAAWC,EAAaC,EAAgB3B,IACnEA,EAAK,QACE,GAEF,CAAC0B,GAAe1B,EAAK,UACnB,EAAEyB,GAAalB,IAEjBmB,EAAcC,EAAe,SAAW3B,EAAK,UAC3C,CAACO,GAEHmB,EAAcC,EAAe,WAAa3B,EAAK,YAC7CO,EAEJ,GAGPqB,GAAkB,CAACtE,EAAK7E,IAAS,CAACa,GAAQG,EAAI6D,EAAK7E,CAAI,CAAC,EAAE,QAAUmF,EAAMN,EAAK7E,CAAI,EAEnFoJ,GAA4B,CAAC/F,EAAQgF,EAAOrI,IAAS,CACrD,MAAMqJ,EAAmB7F,GAAsBxC,EAAIqC,EAAQrD,CAAI,CAAC,EAChE,OAAAyB,EAAI4H,EAAkB,OAAQhB,EAAMrI,CAAI,CAAC,EACzCyB,EAAI4B,EAAQrD,EAAMqJ,CAAgB,EAC3BhG,CACX,EAEIiG,GAAa7J,GAAUoD,EAASpD,CAAK,EAEzC,SAAS8J,GAAiBnI,EAAQyD,EAAKvB,EAAO,WAAY,CACtD,GAAIgG,GAAUlI,CAAM,GACf,MAAM,QAAQA,CAAM,GAAKA,EAAO,MAAMkI,EAAS,GAC/CjI,EAAUD,CAAM,GAAK,CAACA,EACvB,MAAO,CACH,KAAAkC,EACA,QAASgG,GAAUlI,CAAM,EAAIA,EAAS,GACtC,IAAAyD,CACH,CAET,CAEA,IAAI2E,GAAsBC,GAAmB7J,EAAS6J,CAAc,GAAK,CAACtC,GAAQsC,CAAc,EAC1FA,EACA,CACE,MAAOA,EACP,QAAS,EACZ,EAEDC,GAAgB,MAAOxC,EAAOyC,EAAoB3G,EAAYI,EAA0B6D,EAA2B2C,IAAiB,CACpI,KAAM,CAAE,IAAA/E,EAAK,KAAAgF,EAAM,SAAAC,EAAU,UAAAC,EAAW,UAAAC,EAAW,IAAAC,EAAK,IAAAC,EAAK,QAAAC,EAAS,SAAAC,EAAU,KAAApK,EAAM,cAAAqG,EAAe,MAAAgE,CAAK,EAAMnD,EAAM,GAChHoD,EAAatJ,EAAIgC,EAAYhD,CAAI,EACvC,GAAI,CAACqK,GAASV,EAAmB,IAAI3J,CAAI,EACrC,MAAO,CAAE,EAEb,MAAMuK,GAAWV,EAAOA,EAAK,CAAC,EAAIhF,EAC5B2F,EAAqBjH,GAAY,CAC/B0D,GAA6BsD,GAAS,iBACtCA,GAAS,kBAAkBlJ,EAAUkC,CAAO,EAAI,GAAKA,GAAW,EAAE,EAClEgH,GAAS,eAAgB,EAEhC,EACKlC,EAAQ,CAAE,EACVoC,GAAU9F,GAAaE,CAAG,EAC1B6F,GAAapL,GAAgBuF,CAAG,EAChCD,GAAoB6F,IAAWC,GAC/BC,GAAYtE,GAAiB/B,GAAYO,CAAG,IAC9C/D,EAAY+D,EAAI,KAAK,GACrB/D,EAAYwJ,CAAU,GACrB9F,GAAcK,CAAG,GAAKA,EAAI,QAAU,IACrCyF,IAAe,IACd,MAAM,QAAQA,CAAU,GAAK,CAACA,EAAW,OACxCM,GAAoBzH,GAAa,KAAK,KAAMnD,EAAMoD,EAA0BiF,CAAK,EACjFwC,EAAmB,CAACC,EAAWC,EAAkBC,EAAkBC,EAAU/I,GAAuB,UAAWgJ,EAAUhJ,GAAuB,YAAc,CAChK,MAAMqB,EAAUuH,EAAYC,EAAmBC,EAC/C3C,EAAMrI,CAAI,EAAI,CACV,KAAM8K,EAAYG,EAAUC,EAC5B,QAAA3H,EACA,IAAAsB,EACA,GAAG+F,GAAkBE,EAAYG,EAAUC,EAAS3H,CAAO,CAC9D,CACJ,EACD,GAAIqG,EACE,CAAC,MAAM,QAAQU,CAAU,GAAK,CAACA,EAAW,OAC1CR,IACI,CAAClF,KAAsB+F,GAAWjL,EAAkB4K,CAAU,IAC3DjJ,EAAUiJ,CAAU,GAAK,CAACA,GAC1BI,IAAc,CAAC1E,GAAiB6D,CAAI,EAAE,SACtCY,IAAW,CAAChE,GAAcoD,CAAI,EAAE,SAAW,CACpD,KAAM,CAAE,MAAApK,EAAO,QAAA8D,CAAS,EAAG+F,GAAUQ,CAAQ,EACvC,CAAE,MAAO,CAAC,CAACA,EAAU,QAASA,CAAQ,EACtCN,GAAmBM,CAAQ,EACjC,GAAIrK,IACA4I,EAAMrI,CAAI,EAAI,CACV,KAAMkC,GAAuB,SAC7B,QAAAqB,EACA,IAAKgH,GACL,GAAGK,GAAkB1I,GAAuB,SAAUqB,CAAO,CAChE,EACG,CAACH,GACD,OAAAoH,EAAkBjH,CAAO,EAClB8E,CAGvB,CACI,GAAI,CAACsC,IAAY,CAACjL,EAAkBuK,CAAG,GAAK,CAACvK,EAAkBwK,CAAG,GAAI,CAClE,IAAIY,EACAK,EACJ,MAAMC,EAAY5B,GAAmBU,CAAG,EAClCmB,EAAY7B,GAAmBS,CAAG,EACxC,GAAI,CAACvK,EAAkB4K,CAAU,GAAK,CAAC,MAAMA,CAAU,EAAG,CACtD,MAAMgB,EAAczG,EAAI,eACnByF,GAAa,CAACA,EACd5K,EAAkB0L,EAAU,KAAK,IAClCN,EAAYQ,EAAcF,EAAU,OAEnC1L,EAAkB2L,EAAU,KAAK,IAClCF,EAAYG,EAAcD,EAAU,MAEpD,KACa,CACD,MAAME,EAAY1G,EAAI,aAAe,IAAI,KAAKyF,CAAU,EAClDkB,EAAqBC,IAAS,IAAI,KAAK,IAAI,KAAI,EAAG,aAAY,EAAK,IAAMA,EAAI,EAC7EC,GAAS7G,EAAI,MAAQ,OACrB8G,GAAS9G,EAAI,MAAQ,OACvBhC,EAASuI,EAAU,KAAK,GAAKd,IAC7BQ,EAAYY,GACNF,EAAkBlB,CAAU,EAAIkB,EAAkBJ,EAAU,KAAK,EACjEO,GACIrB,EAAac,EAAU,MACvBG,EAAY,IAAI,KAAKH,EAAU,KAAK,GAE9CvI,EAASwI,EAAU,KAAK,GAAKf,IAC7Ba,EAAYO,GACNF,EAAkBlB,CAAU,EAAIkB,EAAkBH,EAAU,KAAK,EACjEM,GACIrB,EAAae,EAAU,MACvBE,EAAY,IAAI,KAAKF,EAAU,KAAK,EAE9D,CACQ,IAAIP,GAAaK,KACbN,EAAiB,CAAC,CAACC,EAAWM,EAAU,QAASC,EAAU,QAASnJ,GAAuB,IAAKA,GAAuB,GAAG,EACtH,CAACkB,GACD,OAAAoH,EAAkBnC,EAAMrI,CAAI,EAAE,OAAO,EAC9BqI,CAGvB,CACI,IAAK0B,GAAaC,IACd,CAACW,IACA9H,EAASyH,CAAU,GAAMV,GAAgB,MAAM,QAAQU,CAAU,GAAK,CACvE,MAAMsB,EAAkBpC,GAAmBO,CAAS,EAC9C8B,EAAkBrC,GAAmBQ,CAAS,EAC9Cc,EAAY,CAACpL,EAAkBkM,EAAgB,KAAK,GACtDtB,EAAW,OAAS,CAACsB,EAAgB,MACnCT,EAAY,CAACzL,EAAkBmM,EAAgB,KAAK,GACtDvB,EAAW,OAAS,CAACuB,EAAgB,MACzC,IAAIf,GAAaK,KACbN,EAAiBC,EAAWc,EAAgB,QAASC,EAAgB,OAAO,EACxE,CAACzI,GACD,OAAAoH,EAAkBnC,EAAMrI,CAAI,EAAE,OAAO,EAC9BqI,CAGvB,CACI,GAAI8B,GAAW,CAACQ,GAAW9H,EAASyH,CAAU,EAAG,CAC7C,KAAM,CAAE,MAAOwB,EAAc,QAAAvI,CAAO,EAAKiG,GAAmBW,CAAO,EACnE,GAAIhD,GAAQ2E,CAAY,GAAK,CAACxB,EAAW,MAAMwB,CAAY,IACvDzD,EAAMrI,CAAI,EAAI,CACV,KAAMkC,GAAuB,QAC7B,QAAAqB,EACA,IAAAsB,EACA,GAAG+F,GAAkB1I,GAAuB,QAASqB,CAAO,CAC/D,EACG,CAACH,GACD,OAAAoH,EAAkBjH,CAAO,EAClB8E,CAGvB,CACI,GAAI+B,GACA,GAAI7F,EAAW6F,CAAQ,EAAG,CACtB,MAAMhJ,EAAS,MAAMgJ,EAASE,EAAYtH,CAAU,EAC9C+I,EAAgBxC,GAAiBnI,EAAQmJ,EAAQ,EACvD,GAAIwB,IACA1D,EAAMrI,CAAI,EAAI,CACV,GAAG+L,EACH,GAAGnB,GAAkB1I,GAAuB,SAAU6J,EAAc,OAAO,CAC9E,EACG,CAAC3I,GACD,OAAAoH,EAAkBuB,EAAc,OAAO,EAChC1D,CAG3B,SACiBzI,EAASwK,CAAQ,EAAG,CACzB,IAAI4B,EAAmB,CAAE,EACzB,UAAWpL,KAAOwJ,EAAU,CACxB,GAAI,CAAC/F,EAAc2H,CAAgB,GAAK,CAAC5I,EACrC,MAEJ,MAAM2I,EAAgBxC,GAAiB,MAAMa,EAASxJ,CAAG,EAAE0J,EAAYtH,CAAU,EAAGuH,GAAU3J,CAAG,EAC7FmL,IACAC,EAAmB,CACf,GAAGD,EACH,GAAGnB,GAAkBhK,EAAKmL,EAAc,OAAO,CAClD,EACDvB,EAAkBuB,EAAc,OAAO,EACnC3I,IACAiF,EAAMrI,CAAI,EAAIgM,GAGtC,CACY,GAAI,CAAC3H,EAAc2H,CAAgB,IAC/B3D,EAAMrI,CAAI,EAAI,CACV,IAAKuK,GACL,GAAGyB,CACN,EACG,CAAC5I,GACD,OAAOiF,CAG3B,EAEI,OAAAmC,EAAkB,EAAI,EACfnC,CACX,EAEA,MAAM4D,GAAiB,CACnB,KAAMhK,EAAgB,SACtB,eAAgBA,EAAgB,SAChC,iBAAkB,EACtB,EACA,SAASiK,GAAkBC,EAAQ,GAAI,CACnC,IAAIC,EAAW,CACX,GAAGH,GACH,GAAGE,CACN,EACGE,EAAa,CACb,YAAa,EACb,QAAS,GACT,QAAS,GACT,UAAW9H,EAAW6H,EAAS,aAAa,EAC5C,aAAc,GACd,YAAa,GACb,aAAc,GACd,mBAAoB,GACpB,QAAS,GACT,cAAe,CAAE,EACjB,YAAa,CAAE,EACf,iBAAkB,CAAE,EACpB,OAAQA,EAAS,QAAU,CAAE,EAC7B,SAAUA,EAAS,UAAY,EAClC,EACD,MAAMrF,EAAU,CAAE,EAClB,IAAIuF,EAAiB1M,EAASwM,EAAS,aAAa,GAAKxM,EAASwM,EAAS,MAAM,EAC3E7L,EAAY6L,EAAS,eAAiBA,EAAS,MAAM,GAAK,CAAA,EAC1D,CAAE,EACJG,EAAcH,EAAS,iBACrB,CAAA,EACA7L,EAAY+L,CAAc,EAC5BE,EAAS,CACT,OAAQ,GACR,MAAO,GACP,MAAO,EACV,EACGzJ,EAAS,CACT,MAAO,IAAI,IACX,SAAU,IAAI,IACd,QAAS,IAAI,IACb,MAAO,IAAI,IACX,MAAO,IAAI,GACd,EACG0J,EACAC,EAAQ,EACZ,MAAMjE,EAAkB,CACpB,QAAS,GACT,YAAa,GACb,iBAAkB,GAClB,cAAe,GACf,aAAc,GACd,QAAS,GACT,OAAQ,EACX,EACD,IAAIkE,EAA2B,CAC3B,GAAGlE,CACN,EACD,MAAMmE,EAAY,CACd,MAAOnJ,GAAe,EACtB,MAAOA,GAAe,CACzB,EACKoJ,EAAmCT,EAAS,eAAiBnK,EAAgB,IAC7E6K,EAAYC,GAAcC,GAAS,CACrC,aAAaN,CAAK,EAClBA,EAAQ,WAAWK,EAAUC,CAAI,CACpC,EACKC,EAAY,MAAOC,GAAsB,CAC3C,GAAI,CAACd,EAAS,WACT3D,EAAgB,SACbkE,EAAyB,SACzBO,GAAoB,CACxB,MAAMC,EAAUf,EAAS,SACnB/H,GAAe,MAAM+I,GAAU,GAAI,MAAM,EACzC,MAAMC,EAAyBtG,EAAS,EAAI,EAC9CoG,IAAYd,EAAW,SACvBO,EAAU,MAAM,KAAK,CACjB,QAAAO,CACpB,CAAiB,CAEjB,CACK,EACKG,EAAsB,CAACpN,EAAOqN,IAAiB,CAC7C,CAACnB,EAAS,WACT3D,EAAgB,cACbA,EAAgB,kBAChBkE,EAAyB,cACzBA,EAAyB,qBAC5BzM,GAAS,MAAM,KAAK6C,EAAO,KAAK,GAAG,QAAS/C,GAAS,CAC9CA,IACAuN,EACM9L,EAAI4K,EAAW,iBAAkBrM,EAAMuN,CAAY,EACnDpI,EAAMkH,EAAW,iBAAkBrM,CAAI,EAEjE,CAAa,EACD4M,EAAU,MAAM,KAAK,CACjB,iBAAkBP,EAAW,iBAC7B,aAAc,CAAChI,EAAcgI,EAAW,gBAAgB,CACxE,CAAa,EAER,EACKmB,EAAiB,CAACxN,EAAMkG,EAAS,CAAE,EAAEuH,EAAQC,EAAMC,EAAkB,GAAMC,EAA6B,KAAS,CACnH,GAAIF,GAAQD,GAAU,CAACrB,EAAS,SAAU,CAEtC,GADAI,EAAO,OAAS,GACZoB,GAA8B,MAAM,QAAQ5M,EAAI+F,EAAS/G,CAAI,CAAC,EAAG,CACjE,MAAM6N,EAAcJ,EAAOzM,EAAI+F,EAAS/G,CAAI,EAAG0N,EAAK,KAAMA,EAAK,IAAI,EACnEC,GAAmBlM,EAAIsF,EAAS/G,EAAM6N,CAAW,CACjE,CACY,GAAID,GACA,MAAM,QAAQ5M,EAAIqL,EAAW,OAAQrM,CAAI,CAAC,EAAG,CAC7C,MAAMqD,EAASoK,EAAOzM,EAAIqL,EAAW,OAAQrM,CAAI,EAAG0N,EAAK,KAAMA,EAAK,IAAI,EACxEC,GAAmBlM,EAAI4K,EAAW,OAAQrM,EAAMqD,CAAM,EACtD8F,GAAgBkD,EAAW,OAAQrM,CAAI,CACvD,CACY,IAAKyI,EAAgB,eACjBkE,EAAyB,gBACzBiB,GACA,MAAM,QAAQ5M,EAAIqL,EAAW,cAAerM,CAAI,CAAC,EAAG,CACpD,MAAM8N,EAAgBL,EAAOzM,EAAIqL,EAAW,cAAerM,CAAI,EAAG0N,EAAK,KAAMA,EAAK,IAAI,EACtFC,GAAmBlM,EAAI4K,EAAW,cAAerM,EAAM8N,CAAa,CACpF,EACgBrF,EAAgB,aAAekE,EAAyB,eACxDN,EAAW,YAAczG,GAAe0G,EAAgBC,CAAW,GAEvEK,EAAU,MAAM,KAAK,CACjB,KAAA5M,EACA,QAAS+N,EAAU/N,EAAMkG,CAAM,EAC/B,YAAamG,EAAW,YACxB,OAAQA,EAAW,OACnB,QAASA,EAAW,OACpC,CAAa,CACb,MAEY5K,EAAI8K,EAAavM,EAAMkG,CAAM,CAEpC,EACK8H,EAAe,CAAChO,EAAMqI,IAAU,CAClC5G,EAAI4K,EAAW,OAAQrM,EAAMqI,CAAK,EAClCuE,EAAU,MAAM,KAAK,CACjB,OAAQP,EAAW,MAC/B,CAAS,CACJ,EACK4B,GAAc5K,GAAW,CAC3BgJ,EAAW,OAAShJ,EACpBuJ,EAAU,MAAM,KAAK,CACjB,OAAQP,EAAW,OACnB,QAAS,EACrB,CAAS,CACJ,EACK6B,EAAsB,CAAClO,EAAMmO,EAAsB1O,EAAOoF,IAAQ,CACpE,MAAMqC,EAAQlG,EAAI+F,EAAS/G,CAAI,EAC/B,GAAIkH,EAAO,CACP,MAAM/F,EAAeH,EAAIuL,EAAavM,EAAMc,EAAYrB,CAAK,EAAIuB,EAAIsL,EAAgBtM,CAAI,EAAIP,CAAK,EAClGqB,EAAYK,CAAY,GACnB0D,GAAOA,EAAI,gBACZsJ,EACE1M,EAAI8K,EAAavM,EAAMmO,EAAuBhN,EAAewF,GAAcO,EAAM,EAAE,CAAC,EACpFkH,EAAcpO,EAAMmB,CAAY,EACtCqL,EAAO,OAASS,EAAW,CACvC,CACK,EACKoB,EAAsB,CAACrO,EAAMsO,EAAYxG,EAAayG,EAAaC,IAAiB,CACtF,IAAIC,EAAoB,GACpBC,EAAkB,GACtB,MAAMC,EAAS,CACX,KAAA3O,CACH,EACD,GAAI,CAACoM,EAAS,SAAU,CACpB,GAAI,CAACtE,GAAeyG,EAAa,EACzB9F,EAAgB,SAAWkE,EAAyB,WACpD+B,EAAkBrC,EAAW,QAC7BA,EAAW,QAAUsC,EAAO,QAAUZ,EAAW,EACjDU,EAAoBC,IAAoBC,EAAO,SAEnD,MAAMC,EAAyB9K,GAAU9C,EAAIsL,EAAgBtM,CAAI,EAAGsO,CAAU,EAC9EI,EAAkB,CAAC,CAAC1N,EAAIqL,EAAW,YAAarM,CAAI,EACpD4O,EACMzJ,EAAMkH,EAAW,YAAarM,CAAI,EAClCyB,EAAI4K,EAAW,YAAarM,EAAM,EAAI,EAC5C2O,EAAO,YAActC,EAAW,YAChCoC,EACIA,IACMhG,EAAgB,aACdkE,EAAyB,cACzB+B,IAAoB,CAACE,CACjD,CACY,GAAI9G,EAAa,CACb,MAAM+G,EAAyB7N,EAAIqL,EAAW,cAAerM,CAAI,EAC5D6O,IACDpN,EAAI4K,EAAW,cAAerM,EAAM8H,CAAW,EAC/C6G,EAAO,cAAgBtC,EAAW,cAClCoC,EACIA,IACMhG,EAAgB,eACdkE,EAAyB,gBACzBkC,IAA2B/G,EAE3D,CACY2G,GAAqBD,GAAgB5B,EAAU,MAAM,KAAK+B,CAAM,CAC5E,CACQ,OAAOF,EAAoBE,EAAS,CAAE,CACzC,EACKG,GAAsB,CAAC9O,EAAMmN,EAAS9E,EAAO0G,IAAe,CAC9D,MAAMC,EAAqBhO,EAAIqL,EAAW,OAAQrM,CAAI,EAChDkN,GAAqBzE,EAAgB,SAAWkE,EAAyB,UAC3EtL,EAAU8L,CAAO,GACjBd,EAAW,UAAYc,EAY3B,GAXIf,EAAS,YAAc/D,GACvBoE,EAAqBK,EAAS,IAAMkB,EAAahO,EAAMqI,CAAK,CAAC,EAC7DoE,EAAmBL,EAAS,UAAU,IAGtC,aAAaM,CAAK,EAClBD,EAAqB,KACrBpE,EACM5G,EAAI4K,EAAW,OAAQrM,EAAMqI,CAAK,EAClClD,EAAMkH,EAAW,OAAQrM,CAAI,IAElCqI,EAAQ,CAACvE,GAAUkL,EAAoB3G,CAAK,EAAI2G,IACjD,CAAC3K,EAAc0K,CAAU,GACzB7B,EAAmB,CACnB,MAAM+B,EAAmB,CACrB,GAAGF,EACH,GAAI7B,GAAqB7L,EAAU8L,CAAO,EAAI,CAAE,QAAAA,CAAS,EAAG,GAC5D,OAAQd,EAAW,OACnB,KAAArM,CACH,EACDqM,EAAa,CACT,GAAGA,EACH,GAAG4C,CACN,EACDrC,EAAU,MAAM,KAAKqC,CAAgB,CACjD,CACK,EACK7B,GAAa,MAAOpN,GAAS,CAC/BsN,EAAoBtN,EAAM,EAAI,EAC9B,MAAMoB,EAAS,MAAMgL,EAAS,SAASG,EAAaH,EAAS,QAASvF,GAAmB7G,GAAQ+C,EAAO,MAAOgE,EAASqF,EAAS,aAAcA,EAAS,yBAAyB,CAAC,EAClL,OAAAkB,EAAoBtN,CAAI,EACjBoB,CACV,EACK8N,GAA8B,MAAOhP,GAAU,CACjD,KAAM,CAAE,OAAAmD,CAAM,EAAK,MAAM+J,GAAWlN,CAAK,EACzC,GAAIA,EACA,UAAWF,KAAQE,EAAO,CACtB,MAAMmI,EAAQrH,EAAIqC,EAAQrD,CAAI,EAC9BqI,EACM5G,EAAI4K,EAAW,OAAQrM,EAAMqI,CAAK,EAClClD,EAAMkH,EAAW,OAAQrM,CAAI,CACnD,MAGYqM,EAAW,OAAShJ,EAExB,OAAOA,CACV,EACKgK,EAA2B,MAAO7H,EAAQ2J,EAAsBC,EAAU,CAC5E,MAAO,EACf,IAAU,CACF,UAAWpP,KAAQwF,EAAQ,CACvB,MAAM0B,EAAQ1B,EAAOxF,CAAI,EACzB,GAAIkH,EAAO,CACP,KAAM,CAAE,GAAAN,EAAI,GAAG0H,CAAU,EAAKpH,EAC9B,GAAIN,EAAI,CACJ,MAAMyI,EAAmBtM,EAAO,MAAM,IAAI6D,EAAG,IAAI,EAC3C0I,EAAoBpI,EAAM,IAAMO,GAAqBP,EAAM,EAAE,EAC/DoI,GAAqB7G,EAAgB,kBACrC6E,EAAoB,CAACtN,CAAI,EAAG,EAAI,EAEpC,MAAMuP,EAAa,MAAM7F,GAAcxC,EAAOnE,EAAO,SAAUwJ,EAAaM,EAAkCT,EAAS,2BAA6B,CAAC+C,EAAsBE,CAAgB,EAI3L,GAHIC,GAAqB7G,EAAgB,kBACrC6E,EAAoB,CAACtN,CAAI,CAAC,EAE1BuP,EAAW3I,EAAG,IAAI,IAClBwI,EAAQ,MAAQ,GACZD,GACA,MAGR,CAACA,IACInO,EAAIuO,EAAY3I,EAAG,IAAI,EAClByI,EACIjG,GAA0BiD,EAAW,OAAQkD,EAAY3I,EAAG,IAAI,EAChEnF,EAAI4K,EAAW,OAAQzF,EAAG,KAAM2I,EAAW3I,EAAG,IAAI,CAAC,EACvDzB,EAAMkH,EAAW,OAAQzF,EAAG,IAAI,EAC9D,CACgB,CAACvC,EAAciK,CAAU,GACpB,MAAMjB,EAAyBiB,EAAYa,EAAsBC,CAAO,CAC7F,CACA,CACQ,OAAOA,EAAQ,KAClB,EACKI,GAAmB,IAAM,CAC3B,UAAWxP,KAAQ+C,EAAO,QAAS,CAC/B,MAAMmE,EAAQlG,EAAI+F,EAAS/G,CAAI,EAC/BkH,IACKA,EAAM,GAAG,KACJA,EAAM,GAAG,KAAK,MAAOrC,GAAQ,CAACC,GAAKD,CAAG,CAAC,EACvC,CAACC,GAAKoC,EAAM,GAAG,GAAG,IACxBuI,GAAWzP,CAAI,CAC/B,CACQ+C,EAAO,QAAU,IAAI,GACxB,EACKgL,EAAY,CAAC/N,EAAMQ,IAAS,CAAC4L,EAAS,WACvCpM,GAAQQ,GAAQiB,EAAI8K,EAAavM,EAAMQ,CAAI,EACxC,CAACsD,GAAU4L,KAAapD,CAAc,GACxCqD,EAAY,CAACzP,EAAOiB,EAAc8B,IAAaH,GAAoB5C,EAAO6C,EAAQ,CACpF,GAAIyJ,EAAO,MACLD,EACAzL,EAAYK,CAAY,EACpBmL,EACAzJ,EAAS3C,CAAK,EACV,CAAE,CAACA,CAAK,EAAGiB,CAAY,EACvBA,CACtB,EAAO8B,EAAU9B,CAAY,EACnByO,EAAkB5P,GAASa,GAAQG,EAAIwL,EAAO,MAAQD,EAAcD,EAAgBtM,EAAMoM,EAAS,iBAAmBpL,EAAIsL,EAAgBtM,EAAM,CAAE,CAAA,EAAI,CAAA,CAAE,CAAC,EACzJoO,EAAgB,CAACpO,EAAMP,EAAOwG,EAAU,CAAA,IAAO,CACjD,MAAMiB,EAAQlG,EAAI+F,EAAS/G,CAAI,EAC/B,IAAIsO,EAAa7O,EACjB,GAAIyH,EAAO,CACP,MAAMQ,EAAiBR,EAAM,GACzBQ,IACA,CAACA,EAAe,UACZjG,EAAI8K,EAAavM,EAAMoG,GAAgB3G,EAAOiI,CAAc,CAAC,EACjE4G,EACI9J,GAAckD,EAAe,GAAG,GAAKhI,EAAkBD,CAAK,EACtD,GACAA,EACNiF,GAAiBgD,EAAe,GAAG,EACnC,CAAC,GAAGA,EAAe,IAAI,OAAO,EAAE,QAASmI,GAAeA,EAAU,SAAWvB,EAAW,SAASuB,EAAU,KAAK,CAAE,EAE7GnI,EAAe,KAChBpI,GAAgBoI,EAAe,GAAG,EAClCA,EAAe,KAAK,QAASoI,GAAgB,EACrC,CAACA,EAAY,gBAAkB,CAACA,EAAY,YACxC,MAAM,QAAQxB,CAAU,EACxBwB,EAAY,QAAU,CAAC,CAACxB,EAAW,KAAM9N,GAASA,IAASsP,EAAY,KAAK,EAG5EA,EAAY,QACRxB,IAAewB,EAAY,OAAS,CAAC,CAACxB,EAG9E,CAAyB,EAGD5G,EAAe,KAAK,QAASqI,GAAcA,EAAS,QAAUA,EAAS,QAAUzB,CAAW,EAG3FhK,GAAYoD,EAAe,GAAG,EACnCA,EAAe,IAAI,MAAQ,IAG3BA,EAAe,IAAI,MAAQ4G,EACtB5G,EAAe,IAAI,MACpBkF,EAAU,MAAM,KAAK,CACjB,KAAA5M,EACA,OAAQO,EAAYgM,CAAW,CAC3D,CAAyB,GAIzB,EACStG,EAAQ,aAAeA,EAAQ,cAC5BoI,EAAoBrO,EAAMsO,EAAYrI,EAAQ,YAAaA,EAAQ,YAAa,EAAI,EACxFA,EAAQ,gBAAkB+J,GAAQhQ,CAAI,CACzC,EACKiQ,EAAY,CAACjQ,EAAMP,EAAOwG,IAAY,CACxC,UAAWiK,KAAYzQ,EAAO,CAC1B,GAAI,CAACA,EAAM,eAAeyQ,CAAQ,EAC9B,OAEJ,MAAM5B,EAAa7O,EAAMyQ,CAAQ,EAC3BhN,EAAYlD,EAAO,IAAMkQ,EACzBhJ,EAAQlG,EAAI+F,EAAS7D,CAAS,GACnCH,EAAO,MAAM,IAAI/C,CAAI,GAClBJ,EAAS0O,CAAU,GAClBpH,GAAS,CAACA,EAAM,KACjB,CAAC1H,GAAa8O,CAAU,EACtB2B,EAAU/M,EAAWoL,EAAYrI,CAAO,EACxCmI,EAAclL,EAAWoL,EAAYrI,CAAO,CAC9D,CACK,EACKkK,EAAW,CAACnQ,EAAMP,EAAOwG,EAAU,CAAA,IAAO,CAC5C,MAAMiB,EAAQlG,EAAI+F,EAAS/G,CAAI,EACzB4J,EAAe7G,EAAO,MAAM,IAAI/C,CAAI,EACpCoQ,EAAa7P,EAAYd,CAAK,EACpCgC,EAAI8K,EAAavM,EAAMoQ,CAAU,EAC7BxG,GACAgD,EAAU,MAAM,KAAK,CACjB,KAAA5M,EACA,OAAQO,EAAYgM,CAAW,CAC/C,CAAa,GACI9D,EAAgB,SACjBA,EAAgB,aAChBkE,EAAyB,SACzBA,EAAyB,cACzB1G,EAAQ,aACR2G,EAAU,MAAM,KAAK,CACjB,KAAA5M,EACA,YAAa4F,GAAe0G,EAAgBC,CAAW,EACvD,QAASwB,EAAU/N,EAAMoQ,CAAU,CACvD,CAAiB,GAILlJ,GAAS,CAACA,EAAM,IAAM,CAACxH,EAAkB0Q,CAAU,EAC7CH,EAAUjQ,EAAMoQ,EAAYnK,CAAO,EACnCmI,EAAcpO,EAAMoQ,EAAYnK,CAAO,EAEjD4B,GAAU7H,EAAM+C,CAAM,GAAK6J,EAAU,MAAM,KAAK,CAAE,GAAGP,EAAY,EACjEO,EAAU,MAAM,KAAK,CACjB,KAAMJ,EAAO,MAAQxM,EAAO,OAC5B,OAAQO,EAAYgM,CAAW,CAC3C,CAAS,CACJ,EACK8D,EAAW,MAAOvQ,GAAU,CAC9B0M,EAAO,MAAQ,GACf,MAAM8D,EAASxQ,EAAM,OACrB,IAAIE,EAAOsQ,EAAO,KACdC,EAAsB,GAC1B,MAAMrJ,EAAQlG,EAAI+F,EAAS/G,CAAI,EACzBwQ,EAA8BlC,GAAe,CAC/CiC,EACI,OAAO,MAAMjC,CAAU,GAClB9O,GAAa8O,CAAU,GAAK,MAAMA,EAAW,QAAS,CAAA,GACvDxK,GAAUwK,EAAYtN,EAAIuL,EAAavM,EAAMsO,CAAU,CAAC,CACnE,EACKmC,EAA6BnJ,GAAmB8E,EAAS,IAAI,EAC7DsE,EAA4BpJ,GAAmB8E,EAAS,cAAc,EAC5E,GAAIlF,EAAO,CACP,IAAImB,EACA8E,EACJ,MAAMmB,GAAagC,EAAO,KACpB3J,GAAcO,EAAM,EAAE,EACtBrH,GAAcC,CAAK,EACnBgI,GAAchI,EAAM,OAASkC,GAAO,MAAQlC,EAAM,OAASkC,GAAO,UAClE2O,GAAwB,CAAC/I,GAAcV,EAAM,EAAE,GACjD,CAACkF,EAAS,UACV,CAACpL,EAAIqL,EAAW,OAAQrM,CAAI,GAC5B,CAACkH,EAAM,GAAG,MACV6B,GAAejB,GAAa9G,EAAIqL,EAAW,cAAerM,CAAI,EAAGqM,EAAW,YAAaqE,EAA2BD,CAA0B,EAC5IG,GAAU/I,GAAU7H,EAAM+C,EAAQ+E,EAAW,EACnDrG,EAAI8K,EAAavM,EAAMsO,EAAU,EAC7BxG,IACAZ,EAAM,GAAG,QAAUA,EAAM,GAAG,OAAOpH,CAAK,EACxC2M,GAAsBA,EAAmB,CAAC,GAErCvF,EAAM,GAAG,UACdA,EAAM,GAAG,SAASpH,CAAK,EAE3B,MAAMiP,GAAaV,EAAoBrO,EAAMsO,GAAYxG,EAAW,EAC9D0G,GAAe,CAACnK,EAAc0K,EAAU,GAAK6B,GAOnD,GANA,CAAC9I,IACG8E,EAAU,MAAM,KAAK,CACjB,KAAA5M,EACA,KAAMF,EAAM,KACZ,OAAQS,EAAYgM,CAAW,CACnD,CAAiB,EACDoE,GACA,OAAIlI,EAAgB,SAAWkE,EAAyB,WAChDP,EAAS,OAAS,SACdtE,IACAmF,EAAW,EAGTnF,IACNmF,EAAW,GAGXuB,IACJ5B,EAAU,MAAM,KAAK,CAAE,KAAA5M,EAAM,GAAI4Q,GAAU,CAAA,EAAK7B,GAAa,EAGrE,GADA,CAACjH,IAAe8I,IAAWhE,EAAU,MAAM,KAAK,CAAE,GAAGP,EAAY,EAC7DD,EAAS,SAAU,CACnB,KAAM,CAAE,OAAA/I,EAAQ,EAAG,MAAM+J,GAAW,CAACpN,CAAI,CAAC,EAE1C,GADAwQ,EAA2BlC,EAAU,EACjCiC,EAAqB,CACrB,MAAMM,GAA4BzI,GAAkBiE,EAAW,OAAQtF,EAAS/G,CAAI,EAC9E8Q,GAAoB1I,GAAkB/E,GAAQ0D,EAAS8J,GAA0B,MAAQ7Q,CAAI,EACnGqI,EAAQyI,GAAkB,MAC1B9Q,EAAO8Q,GAAkB,KACzB3D,EAAU9I,EAAchB,EAAM,CAClD,CACA,MAEgBiK,EAAoB,CAACtN,CAAI,EAAG,EAAI,EAChCqI,GAAS,MAAMqB,GAAcxC,EAAOnE,EAAO,SAAUwJ,EAAaM,EAAkCT,EAAS,yBAAyB,GAAGpM,CAAI,EAC7IsN,EAAoB,CAACtN,CAAI,CAAC,EAC1BwQ,EAA2BlC,EAAU,EACjCiC,IACIlI,EACA8E,EAAU,IAEL1E,EAAgB,SACrBkE,EAAyB,WACzBQ,EAAU,MAAME,EAAyBtG,EAAS,EAAI,IAI9DwJ,IACArJ,EAAM,GAAG,MACL8I,GAAQ9I,EAAM,GAAG,IAAI,EACzB4H,GAAoB9O,EAAMmN,EAAS9E,EAAO0G,EAAU,EAEpE,CACK,EACKgC,GAAc,CAAClM,EAAKjE,IAAQ,CAC9B,GAAII,EAAIqL,EAAW,OAAQzL,CAAG,GAAKiE,EAAI,MACnC,OAAAA,EAAI,MAAO,EACJ,CAGd,EACKmL,GAAU,MAAOhQ,EAAMiG,EAAU,CAAA,IAAO,CAC1C,IAAIkH,EACAnB,EACJ,MAAMgF,EAAaxN,GAAsBxD,CAAI,EAC7C,GAAIoM,EAAS,SAAU,CACnB,MAAM/I,EAAS,MAAM6L,GAA4BpO,EAAYd,CAAI,EAAIA,EAAOgR,CAAU,EACtF7D,EAAU9I,EAAchB,CAAM,EAC9B2I,EAAmBhM,EACb,CAACgR,EAAW,KAAMhR,GAASgB,EAAIqC,EAAQrD,CAAI,CAAC,EAC5CmN,CAClB,MACiBnN,GACLgM,GAAoB,MAAM,QAAQ,IAAIgF,EAAW,IAAI,MAAO9N,GAAc,CACtE,MAAMgE,EAAQlG,EAAI+F,EAAS7D,CAAS,EACpC,OAAO,MAAMmK,EAAyBnG,GAASA,EAAM,GAAK,CAAE,CAAChE,CAAS,EAAGgE,CAAO,EAAGA,CAAK,CACxG,CAAa,CAAC,GAAG,MAAM,OAAO,EAClB,EAAE,CAAC8E,GAAoB,CAACK,EAAW,UAAYY,EAAW,GAG1DjB,EAAmBmB,EAAU,MAAME,EAAyBtG,CAAO,EAEvE,OAAA6F,EAAU,MAAM,KAAK,CACjB,GAAI,CAAC/J,EAAS7C,CAAI,IACZyI,EAAgB,SAAWkE,EAAyB,UAClDQ,IAAYd,EAAW,QACzB,CAAA,EACA,CAAE,KAAArM,CAAI,EACZ,GAAIoM,EAAS,UAAY,CAACpM,EAAO,CAAE,QAAAmN,CAAS,EAAG,GAC/C,OAAQd,EAAW,MAC/B,CAAS,EACDpG,EAAQ,aACJ,CAAC+F,GACDhE,GAAsBjB,EAASgK,GAAa/Q,EAAOgR,EAAajO,EAAO,KAAK,EACzEiJ,CACV,EACK0D,GAAasB,GAAe,CAC9B,MAAM9K,EAAS,CACX,GAAIsG,EAAO,MAAQD,EAAcD,CACpC,EACD,OAAOxL,EAAYkQ,CAAU,EACvB9K,EACArD,EAASmO,CAAU,EACfhQ,EAAIkF,EAAQ8K,CAAU,EACtBA,EAAW,IAAKhR,GAASgB,EAAIkF,EAAQlG,CAAI,CAAC,CACvD,EACKiR,GAAgB,CAACjR,EAAMqC,KAAe,CACxC,QAAS,CAAC,CAACrB,GAAKqB,GAAagK,GAAY,OAAQrM,CAAI,EACrD,QAAS,CAAC,CAACgB,GAAKqB,GAAagK,GAAY,YAAarM,CAAI,EAC1D,MAAOgB,GAAKqB,GAAagK,GAAY,OAAQrM,CAAI,EACjD,aAAc,CAAC,CAACgB,EAAIqL,EAAW,iBAAkBrM,CAAI,EACrD,UAAW,CAAC,CAACgB,GAAKqB,GAAagK,GAAY,cAAerM,CAAI,CACtE,GACUkR,GAAelR,GAAS,CAC1BA,GACIwD,GAAsBxD,CAAI,EAAE,QAASmR,GAAchM,EAAMkH,EAAW,OAAQ8E,CAAS,CAAC,EAC1FvE,EAAU,MAAM,KAAK,CACjB,OAAQ5M,EAAOqM,EAAW,OAAS,CAAE,CACjD,CAAS,CACJ,EACK+E,GAAW,CAACpR,EAAMqI,EAAOpC,IAAY,CACvC,MAAMpB,GAAO7D,EAAI+F,EAAS/G,EAAM,CAAE,GAAI,EAAI,CAAA,EAAE,IAAM,CAAE,GAAE,IAChDqR,EAAerQ,EAAIqL,EAAW,OAAQrM,CAAI,GAAK,CAAE,EAEjD,CAAE,IAAKsR,EAAY,QAAA/N,EAAS,KAAAD,EAAM,GAAGiO,CAAe,EAAKF,EAC/D5P,EAAI4K,EAAW,OAAQrM,EAAM,CACzB,GAAGuR,EACH,GAAGlJ,EACH,IAAAxD,CACZ,CAAS,EACD+H,EAAU,MAAM,KAAK,CACjB,KAAA5M,EACA,OAAQqM,EAAW,OACnB,QAAS,EACrB,CAAS,EACDpG,GAAWA,EAAQ,aAAepB,GAAOA,EAAI,OAASA,EAAI,MAAO,CACpE,EACK2M,GAAQ,CAACxR,EAAMmB,IAAiBoD,EAAWvE,CAAI,EAC/C4M,EAAU,MAAM,UAAU,CACxB,KAAO6E,GAAYzR,EAAK2P,EAAU,OAAWxO,CAAY,EAAGsQ,CAAO,CACtE,CAAA,EACC9B,EAAU3P,EAAMmB,EAAc,EAAI,EAClCuQ,GAAcvF,GAAUS,EAAU,MAAM,UAAU,CACpD,KAAOvK,GAAc,CACbsG,GAAsBwD,EAAM,KAAM9J,EAAU,KAAM8J,EAAM,KAAK,GAC7D5D,GAAsBlG,EAAW8J,EAAM,WAAa1D,EAAiBkJ,GAAexF,EAAM,YAAY,GACtGA,EAAM,SAAS,CACX,OAAQ,CAAE,GAAGI,CAAa,EAC1B,GAAGF,EACH,GAAGhK,CACvB,CAAiB,CAER,CACJ,CAAA,EAAE,YACGuP,GAAazF,IACfK,EAAO,MAAQ,GACfG,EAA2B,CACvB,GAAGA,EACH,GAAGR,EAAM,SACZ,EACMuF,GAAW,CACd,GAAGvF,EACH,UAAWQ,CACvB,CAAS,GAEC8C,GAAa,CAACzP,EAAMiG,EAAU,CAAA,IAAO,CACvC,UAAW/C,KAAalD,EAAOwD,GAAsBxD,CAAI,EAAI+C,EAAO,MAChEA,EAAO,MAAM,OAAOG,CAAS,EAC7BH,EAAO,MAAM,OAAOG,CAAS,EACxB+C,EAAQ,YACTd,EAAM4B,EAAS7D,CAAS,EACxBiC,EAAMoH,EAAarJ,CAAS,GAEhC,CAAC+C,EAAQ,WAAad,EAAMkH,EAAW,OAAQnJ,CAAS,EACxD,CAAC+C,EAAQ,WAAad,EAAMkH,EAAW,YAAanJ,CAAS,EAC7D,CAAC+C,EAAQ,aAAed,EAAMkH,EAAW,cAAenJ,CAAS,EACjE,CAAC+C,EAAQ,kBACLd,EAAMkH,EAAW,iBAAkBnJ,CAAS,EAChD,CAACkJ,EAAS,kBACN,CAACnG,EAAQ,kBACTd,EAAMmH,EAAgBpJ,CAAS,EAEvC0J,EAAU,MAAM,KAAK,CACjB,OAAQrM,EAAYgM,CAAW,CAC3C,CAAS,EACDK,EAAU,MAAM,KAAK,CACjB,GAAGP,EACH,GAAKpG,EAAQ,UAAiB,CAAE,QAAS8H,EAAS,GAAzB,CAAA,CACrC,CAAS,EACD,CAAC9H,EAAQ,aAAegH,EAAW,CACtC,EACK4E,GAAoB,CAAC,CAAE,SAAAC,EAAU,KAAA9R,CAAI,IAAQ,EAC1CqB,EAAUyQ,CAAQ,GAAKtF,EAAO,OAC7BsF,GACF/O,EAAO,SAAS,IAAI/C,CAAI,KACxB8R,EAAW/O,EAAO,SAAS,IAAI/C,CAAI,EAAI+C,EAAO,SAAS,OAAO/C,CAAI,EAEzE,EACK+R,GAAW,CAAC/R,EAAMiG,EAAU,CAAA,IAAO,CACrC,IAAIiB,EAAQlG,EAAI+F,EAAS/G,CAAI,EAC7B,MAAMgS,EAAoB3Q,EAAU4E,EAAQ,QAAQ,GAAK5E,EAAU+K,EAAS,QAAQ,EACpF,OAAA3K,EAAIsF,EAAS/G,EAAM,CACf,GAAIkH,GAAS,CAAA,EACb,GAAI,CACA,GAAIA,GAASA,EAAM,GAAKA,EAAM,GAAK,CAAE,IAAK,CAAE,KAAAlH,CAAI,GAChD,KAAAA,EACA,MAAO,GACP,GAAGiG,CACN,CACb,CAAS,EACDlD,EAAO,MAAM,IAAI/C,CAAI,EACjBkH,EACA2K,GAAkB,CACd,SAAUxQ,EAAU4E,EAAQ,QAAQ,EAC9BA,EAAQ,SACRmG,EAAS,SACf,KAAApM,CAChB,CAAa,EAGDkO,EAAoBlO,EAAM,GAAMiG,EAAQ,KAAK,EAE1C,CACH,GAAI+L,EACE,CAAE,SAAU/L,EAAQ,UAAYmG,EAAS,QAAQ,EACjD,GACN,GAAIA,EAAS,YACP,CACE,SAAU,CAAC,CAACnG,EAAQ,SACpB,IAAKmB,GAAanB,EAAQ,GAAG,EAC7B,IAAKmB,GAAanB,EAAQ,GAAG,EAC7B,UAAWmB,GAAanB,EAAQ,SAAS,EACzC,UAAWmB,GAAanB,EAAQ,SAAS,EACzC,QAASmB,GAAanB,EAAQ,OAAO,CACzD,EACkB,GACN,KAAAjG,EACA,SAAAqQ,EACA,OAAQA,EACR,IAAMxL,GAAQ,CACV,GAAIA,EAAK,CACLkN,GAAS/R,EAAMiG,CAAO,EACtBiB,EAAQlG,EAAI+F,EAAS/G,CAAI,EACzB,MAAMiS,EAAWnR,EAAY+D,EAAI,KAAK,GAChCA,EAAI,kBACAA,EAAI,iBAAiB,uBAAuB,EAAE,CAAC,GAAKA,EAGxDqN,EAAkBtN,GAAkBqN,CAAQ,EAC5CpI,EAAO3C,EAAM,GAAG,MAAQ,CAAE,EAChC,GAAIgL,EACErI,EAAK,KAAM1D,GAAWA,IAAW8L,CAAQ,EACzCA,IAAa/K,EAAM,GAAG,IACxB,OAEJzF,EAAIsF,EAAS/G,EAAM,CACf,GAAI,CACA,GAAGkH,EAAM,GACT,GAAIgL,EACE,CACE,KAAM,CACF,GAAGrI,EAAK,OAAO/E,EAAI,EACnBmN,EACA,GAAI,MAAM,QAAQjR,EAAIsL,EAAgBtM,CAAI,CAAC,EAAI,CAAC,EAAE,EAAI,EACzD,EACD,IAAK,CAAE,KAAMiS,EAAS,KAAM,KAAAjS,CAAM,CACtE,EACkC,CAAE,IAAKiS,EAChB,CACzB,CAAqB,EACD/D,EAAoBlO,EAAM,GAAO,OAAWiS,CAAQ,CACxE,MAEoB/K,EAAQlG,EAAI+F,EAAS/G,EAAM,CAAA,CAAE,EACzBkH,EAAM,KACNA,EAAM,GAAG,MAAQ,KAEpBkF,EAAS,kBAAoBnG,EAAQ,mBAClC,EAAEhG,GAAmB8C,EAAO,MAAO/C,CAAI,GAAKwM,EAAO,SACnDzJ,EAAO,QAAQ,IAAI/C,CAAI,CAElC,CACJ,CACJ,EACKmS,GAAc,IAAM/F,EAAS,kBAC/BpE,GAAsBjB,EAASgK,GAAahO,EAAO,KAAK,EACtDqP,GAAgBN,GAAa,CAC3BzQ,EAAUyQ,CAAQ,IAClBlF,EAAU,MAAM,KAAK,CAAE,SAAAkF,CAAQ,CAAE,EACjC9J,GAAsBjB,EAAS,CAAClC,EAAK7E,IAAS,CAC1C,MAAMmI,EAAenH,EAAI+F,EAAS/G,CAAI,EAClCmI,IACAtD,EAAI,SAAWsD,EAAa,GAAG,UAAY2J,EACvC,MAAM,QAAQ3J,EAAa,GAAG,IAAI,GAClCA,EAAa,GAAG,KAAK,QAASoC,GAAa,CACvCA,EAAS,SAAWpC,EAAa,GAAG,UAAY2J,CAC5E,CAAyB,EAGzB,EAAe,EAAG,EAAK,EAElB,EACKO,GAAe,CAACC,EAASC,IAAc,MAAOC,GAAM,CACtD,IAAIC,EACAD,IACAA,EAAE,gBAAkBA,EAAE,eAAgB,EACtCA,EAAE,SACEA,EAAE,QAAS,GAEnB,IAAI3E,EAActN,EAAYgM,CAAW,EAIzC,GAHAK,EAAU,MAAM,KAAK,CACjB,aAAc,EAC1B,CAAS,EACGR,EAAS,SAAU,CACnB,KAAM,CAAE,OAAA/I,EAAQ,OAAA6C,CAAQ,EAAG,MAAMkH,GAAY,EAC7Cf,EAAW,OAAShJ,EACpBwK,EAAc3H,CAC1B,MAEY,MAAMmH,EAAyBtG,CAAO,EAE1C,GAAIhE,EAAO,SAAS,KAChB,UAAW/C,KAAQ+C,EAAO,SACtBtB,EAAIoM,EAAa7N,EAAM,MAAS,EAIxC,GADAmF,EAAMkH,EAAW,OAAQ,MAAM,EAC3BhI,EAAcgI,EAAW,MAAM,EAAG,CAClCO,EAAU,MAAM,KAAK,CACjB,OAAQ,CAAE,CAC1B,CAAa,EACD,GAAI,CACA,MAAM0F,EAAQzE,EAAa2E,CAAC,CAC5C,OACmBnK,EAAO,CACVoK,EAAepK,CAC/B,CACA,MAEgBkK,GACA,MAAMA,EAAU,CAAE,GAAGlG,EAAW,MAAM,EAAImG,CAAC,EAE/CL,GAAa,EACb,WAAWA,EAAW,EAS1B,GAPAvF,EAAU,MAAM,KAAK,CACjB,YAAa,GACb,aAAc,GACd,mBAAoBvI,EAAcgI,EAAW,MAAM,GAAK,CAACoG,EACzD,YAAapG,EAAW,YAAc,EACtC,OAAQA,EAAW,MAC/B,CAAS,EACGoG,EACA,MAAMA,CAEb,EACKC,GAAa,CAAC1S,EAAMiG,EAAU,CAAA,IAAO,CACnCjF,EAAI+F,EAAS/G,CAAI,IACbc,EAAYmF,EAAQ,YAAY,EAChCkK,EAASnQ,EAAMO,EAAYS,EAAIsL,EAAgBtM,CAAI,CAAC,CAAC,GAGrDmQ,EAASnQ,EAAMiG,EAAQ,YAAY,EACnCxE,EAAI6K,EAAgBtM,EAAMO,EAAY0F,EAAQ,YAAY,CAAC,GAE1DA,EAAQ,aACTd,EAAMkH,EAAW,cAAerM,CAAI,EAEnCiG,EAAQ,YACTd,EAAMkH,EAAW,YAAarM,CAAI,EAClCqM,EAAW,QAAUpG,EAAQ,aACvB8H,EAAU/N,EAAMO,EAAYS,EAAIsL,EAAgBtM,CAAI,CAAC,CAAC,EACtD+N,EAAW,GAEhB9H,EAAQ,YACTd,EAAMkH,EAAW,OAAQrM,CAAI,EAC7ByI,EAAgB,SAAWwE,EAAW,GAE1CL,EAAU,MAAM,KAAK,CAAE,GAAGP,CAAU,CAAE,EAE7C,EACKsG,GAAS,CAAC3P,EAAY4P,EAAmB,CAAA,IAAO,CAClD,MAAMC,EAAgB7P,EAAazC,EAAYyC,CAAU,EAAIsJ,EACvDwG,EAAqBvS,EAAYsS,CAAa,EAC9CE,EAAqB1O,EAAcrB,CAAU,EAC7CkD,EAAS6M,EAAqBzG,EAAiBwG,EAIrD,GAHKF,EAAiB,oBAClBtG,EAAiBuG,GAEjB,CAACD,EAAiB,WAAY,CAC9B,GAAIA,EAAiB,gBAAiB,CAClC,MAAMI,EAAgB,IAAI,IAAI,CAC1B,GAAGjQ,EAAO,MACV,GAAG,OAAO,KAAK6C,GAAe0G,EAAgBC,CAAW,CAAC,CAC9E,CAAiB,EACD,UAAWrJ,KAAa,MAAM,KAAK8P,CAAa,EAC5ChS,EAAIqL,EAAW,YAAanJ,CAAS,EAC/BzB,EAAIyE,EAAQhD,EAAWlC,EAAIuL,EAAarJ,CAAS,CAAC,EAClDiN,EAASjN,EAAWlC,EAAIkF,EAAQhD,CAAS,CAAC,CAEpE,KACiB,CACD,GAAI5C,IAASQ,EAAYkC,CAAU,EAC/B,UAAWhD,KAAQ+C,EAAO,MAAO,CAC7B,MAAMmE,EAAQlG,EAAI+F,EAAS/G,CAAI,EAC/B,GAAIkH,GAASA,EAAM,GAAI,CACnB,MAAMQ,EAAiB,MAAM,QAAQR,EAAM,GAAG,IAAI,EAC5CA,EAAM,GAAG,KAAK,CAAC,EACfA,EAAM,GAAG,IACf,GAAI1C,GAAckD,CAAc,EAAG,CAC/B,MAAMuL,EAAOvL,EAAe,QAAQ,MAAM,EAC1C,GAAIuL,EAAM,CACNA,EAAK,MAAO,EACZ,KACpC,CACA,CACA,CACA,CAEgB,UAAW/P,KAAaH,EAAO,MAC3BoN,EAASjN,EAAWlC,EAAIkF,EAAQhD,CAAS,CAAC,CAE9D,CACYqJ,EAAchM,EAAY2F,CAAM,EAChC0G,EAAU,MAAM,KAAK,CACjB,OAAQ,CAAE,GAAG1G,CAAQ,CACrC,CAAa,EACD0G,EAAU,MAAM,KAAK,CACjB,OAAQ,CAAE,GAAG1G,CAAQ,CACrC,CAAa,CACb,CACQnD,EAAS,CACL,MAAO6P,EAAiB,gBAAkB7P,EAAO,MAAQ,IAAI,IAC7D,QAAS,IAAI,IACb,MAAO,IAAI,IACX,SAAU,IAAI,IACd,MAAO,IAAI,IACX,SAAU,GACV,MAAO,EACV,EACDyJ,EAAO,MACH,CAAC/D,EAAgB,SACb,CAAC,CAACmK,EAAiB,aACnB,CAAC,CAACA,EAAiB,gBAC3BpG,EAAO,MAAQ,CAAC,CAACJ,EAAS,iBAC1BQ,EAAU,MAAM,KAAK,CACjB,YAAagG,EAAiB,gBACxBvG,EAAW,YACX,EACN,QAAS0G,EACH,GACAH,EAAiB,UACbvG,EAAW,QACX,CAAC,EAAEuG,EAAiB,mBAClB,CAAC9O,GAAUd,EAAYsJ,CAAc,GACjD,YAAasG,EAAiB,gBACxBvG,EAAW,YACX,GACN,YAAa0G,EACP,CAAA,EACAH,EAAiB,gBACbA,EAAiB,mBAAqBrG,EAClC3G,GAAe0G,EAAgBC,CAAW,EAC1CF,EAAW,YACfuG,EAAiB,mBAAqB5P,EAClC4C,GAAe0G,EAAgBtJ,CAAU,EACzC4P,EAAiB,UACbvG,EAAW,YACX,CAAE,EACpB,cAAeuG,EAAiB,YAC1BvG,EAAW,cACX,CAAE,EACR,OAAQuG,EAAiB,WAAavG,EAAW,OAAS,CAAE,EAC5D,mBAAoBuG,EAAiB,uBAC/BvG,EAAW,mBACX,GACN,aAAc,EAC1B,CAAS,CACJ,EACK6G,GAAQ,CAAClQ,EAAY4P,IAAqBD,GAAOpO,EAAWvB,CAAU,EACtEA,EAAWuJ,CAAW,EACtBvJ,EAAY4P,CAAgB,EAC5BO,GAAW,CAACnT,EAAMiG,EAAU,CAAA,IAAO,CACrC,MAAMiB,EAAQlG,EAAI+F,EAAS/G,CAAI,EACzB0H,EAAiBR,GAASA,EAAM,GACtC,GAAIQ,EAAgB,CAChB,MAAMuK,EAAWvK,EAAe,KAC1BA,EAAe,KAAK,CAAC,EACrBA,EAAe,IACjBuK,EAAS,QACTA,EAAS,MAAO,EAChBhM,EAAQ,cACJ1B,EAAW0N,EAAS,MAAM,GAC1BA,EAAS,OAAQ,EAErC,CACK,EACKN,GAAiB1C,GAAqB,CACxC5C,EAAa,CACT,GAAGA,EACH,GAAG4C,CACN,CACJ,EAQKmE,GAAU,CACZ,QAAS,CACL,SAAArB,GACA,WAAAtC,GACA,cAAAwB,GACA,aAAAoB,GACA,SAAAjB,GACA,WAAAM,GACA,WAAAtE,GACA,YAAA+E,GACA,UAAAxC,EACA,UAAA5B,EACA,UAAAd,EACA,eAAAO,EACA,kBAAAqE,GACA,WAAA5D,GACA,eAAA2B,EACA,OAAA+C,GACA,oBAzBoB,IAAMpO,EAAW6H,EAAS,aAAa,GAC/DA,EAAS,cAAa,EAAG,KAAMlG,GAAW,CACtCgN,GAAMhN,EAAQkG,EAAS,YAAY,EACnCQ,EAAU,MAAM,KAAK,CACjB,UAAW,EAC3B,CAAa,CACb,CAAS,EAoBG,iBAAA4C,GACA,aAAA4C,GACA,UAAAxF,EACA,gBAAAnE,EACA,IAAI,SAAU,CACV,OAAO1B,CACV,EACD,IAAI,aAAc,CACd,OAAOwF,CACV,EACD,IAAI,QAAS,CACT,OAAOC,CACV,EACD,IAAI,OAAO/M,EAAO,CACd+M,EAAS/M,CACZ,EACD,IAAI,gBAAiB,CACjB,OAAO6M,CACV,EACD,IAAI,QAAS,CACT,OAAOvJ,CACV,EACD,IAAI,OAAOtD,EAAO,CACdsD,EAAStD,CACZ,EACD,IAAI,YAAa,CACb,OAAO4M,CACV,EACD,IAAI,UAAW,CACX,OAAOD,CACV,EACD,IAAI,SAAS3M,EAAO,CAChB2M,EAAW,CACP,GAAGA,EACH,GAAG3M,CACN,CACJ,CACJ,EACD,UAAAmS,GACA,QAAA5B,GACA,SAAA+B,GACA,aAAAM,GACA,MAAAb,GACA,SAAArB,EACA,UAAAT,GACA,MAAAwD,GACA,WAAAR,GACA,YAAAxB,GACA,WAAAzB,GACA,SAAA2B,GACA,SAAA+B,GACA,cAAAlC,EACH,EACD,MAAO,CACH,GAAGmC,GACH,YAAaA,EAChB,CACL,CAiVA,SAASC,GAAQlH,EAAQ,GAAI,CACzB,MAAMmH,EAAenR,EAAe,OAAO,MAAS,EAC9CoR,EAAUpR,EAAe,OAAO,MAAS,EACzC,CAACE,EAAWqG,CAAe,EAAIvG,EAAe,SAAS,CACzD,QAAS,GACT,aAAc,GACd,UAAWoC,EAAW4H,EAAM,aAAa,EACzC,YAAa,GACb,aAAc,GACd,mBAAoB,GACpB,QAAS,GACT,YAAa,EACb,YAAa,CAAE,EACf,cAAe,CAAE,EACjB,iBAAkB,CAAE,EACpB,OAAQA,EAAM,QAAU,CAAE,EAC1B,SAAUA,EAAM,UAAY,GAC5B,QAAS,GACT,cAAe5H,EAAW4H,EAAM,aAAa,EACvC,OACAA,EAAM,aACpB,CAAK,EACImH,EAAa,UACdA,EAAa,QAAU,CACnB,GAAInH,EAAM,YAAcA,EAAM,YAAcD,GAAkBC,CAAK,EACnE,UAAA9J,CACH,EACG8J,EAAM,aACNA,EAAM,eACN,CAAC5H,EAAW4H,EAAM,aAAa,GAC/BA,EAAM,YAAY,MAAMA,EAAM,cAAeA,EAAM,YAAY,GAGvE,MAAM7J,EAAUgR,EAAa,QAAQ,QACrC,OAAAhR,EAAQ,SAAW6J,EACnBzJ,GAA0B,IAAM,CAC5B,MAAM8Q,EAAMlR,EAAQ,WAAW,CAC3B,UAAWA,EAAQ,gBACnB,SAAU,IAAMoG,EAAgB,CAAE,GAAGpG,EAAQ,UAAU,CAAE,EACzD,aAAc,EAC1B,CAAS,EACD,OAAAoG,EAAiBlI,IAAU,CACvB,GAAGA,EACH,QAAS,EACrB,EAAU,EACF8B,EAAQ,WAAW,QAAU,GACtBkR,CACf,EAAO,CAAClR,CAAO,CAAC,EACZH,EAAe,UAAU,IAAMG,EAAQ,aAAa6J,EAAM,QAAQ,EAAG,CAAC7J,EAAS6J,EAAM,QAAQ,CAAC,EAC9FhK,EAAe,UAAU,IAAM,CACvBgK,EAAM,OACN7J,EAAQ,SAAS,KAAO6J,EAAM,MAE9BA,EAAM,iBACN7J,EAAQ,SAAS,eAAiB6J,EAAM,eAEpD,EAAO,CAAC7J,EAAS6J,EAAM,KAAMA,EAAM,cAAc,CAAC,EAC9ChK,EAAe,UAAU,IAAM,CACvBgK,EAAM,SACN7J,EAAQ,WAAW6J,EAAM,MAAM,EAC/B7J,EAAQ,YAAa,EAE5B,EAAE,CAACA,EAAS6J,EAAM,MAAM,CAAC,EAC1BhK,EAAe,UAAU,IAAM,CAC3BgK,EAAM,kBACF7J,EAAQ,UAAU,MAAM,KAAK,CACzB,OAAQA,EAAQ,UAAW,CAC3C,CAAa,CACR,EAAE,CAACA,EAAS6J,EAAM,gBAAgB,CAAC,EACpChK,EAAe,UAAU,IAAM,CAC3B,GAAIG,EAAQ,gBAAgB,QAAS,CACjC,MAAMmR,EAAUnR,EAAQ,UAAW,EAC/BmR,IAAYpR,EAAU,SACtBC,EAAQ,UAAU,MAAM,KAAK,CACzB,QAAAmR,CACpB,CAAiB,CAEjB,CACK,EAAE,CAACnR,EAASD,EAAU,OAAO,CAAC,EAC/BF,EAAe,UAAU,IAAM,CACvBgK,EAAM,QAAU,CAACrI,GAAUqI,EAAM,OAAQoH,EAAQ,OAAO,GACxDjR,EAAQ,OAAO6J,EAAM,OAAQ7J,EAAQ,SAAS,YAAY,EAC1DiR,EAAQ,QAAUpH,EAAM,OACxBzD,EAAiBgL,IAAW,CAAE,GAAGA,CAAO,EAAC,GAGzCpR,EAAQ,oBAAqB,CAEpC,EAAE,CAACA,EAAS6J,EAAM,MAAM,CAAC,EAC1BhK,EAAe,UAAU,IAAM,CACtBG,EAAQ,OAAO,QAChBA,EAAQ,UAAW,EACnBA,EAAQ,OAAO,MAAQ,IAEvBA,EAAQ,OAAO,QACfA,EAAQ,OAAO,MAAQ,GACvBA,EAAQ,UAAU,MAAM,KAAK,CAAE,GAAGA,EAAQ,WAAY,GAE1DA,EAAQ,iBAAkB,CAClC,CAAK,EACDgR,EAAa,QAAQ,UAAYlR,GAAkBC,EAAWC,CAAO,EAC9DgR,EAAa,OACxB,CCvqFA,SAASK,GAAgB,CACvB,MAAAC,EACA,QAAAC,EACA,GAAG1H,CACL,EAAG2H,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBD,CACpB,EAAE1H,CAAK,EAAGyH,EAAqBG,EAAAA,cAAoB,QAAS,CAC3D,GAAIF,CACL,EAAED,CAAK,EAAI,KAAmBG,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,4BACP,CAAG,CAAC,CACJ,CACA,MAAMC,GAA2BC,EAAgB,WAACN,EAAe,ECvBjE,SAASO,GAAa,CACpB,MAAAN,EACA,QAAAC,EACA,GAAG1H,CACL,EAAG2H,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBD,CACpB,EAAE1H,CAAK,EAAGyH,EAAqBG,EAAAA,cAAoB,QAAS,CAC3D,GAAIF,CACL,EAAED,CAAK,EAAI,KAAmBG,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,gQACP,CAAG,CAAC,CACJ,CACA,MAAMC,GAA2BC,EAAgB,WAACC,EAAY,ECvB9D,SAASC,GAAa,CACpB,MAAAP,EACA,QAAAC,EACA,GAAG1H,CACL,EAAG2H,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBD,CACpB,EAAE1H,CAAK,EAAGyH,EAAqBG,EAAAA,cAAoB,QAAS,CAC3D,GAAIF,CACL,EAAED,CAAK,EAAI,KAAmBG,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,8UACP,CAAG,CAAC,CACJ,CACA,MAAMC,GAA2BC,EAAgB,WAACE,EAAY,ECvB9D,SAASC,GAAQ,CACf,MAAAR,EACA,QAAAC,EACA,GAAG1H,CACL,EAAG2H,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBD,CACpB,EAAE1H,CAAK,EAAGyH,EAAqBG,EAAAA,cAAoB,QAAS,CAC3D,GAAIF,CACL,EAAED,CAAK,EAAI,KAAmBG,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,0LACP,CAAG,EAAgBA,EAAmB,cAAC,OAAQ,CAC3C,cAAe,QACf,eAAgB,QAChB,EAAG,qCACP,CAAG,CAAC,CACJ,CACA,MAAMC,GAA2BC,EAAgB,WAACG,EAAO,EC3BzD,SAASC,GAAa,CACpB,MAAAT,EACA,QAAAC,EACA,GAAG1H,CACL,EAAG2H,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBD,CACpB,EAAE1H,CAAK,EAAGyH,EAAqBG,EAAAA,cAAoB,QAAS,CAC3D,GAAIF,CACL,EAAED,CAAK,EAAI,KAAmBG,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,oPACP,CAAG,CAAC,CACJ,CACA,MAAMC,GAA2BC,EAAgB,WAACI,EAAY,ECvB9D,SAASC,GAAU,CACjB,MAAAV,EACA,QAAAC,EACA,GAAG1H,CACL,EAAG2H,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBD,CACpB,EAAE1H,CAAK,EAAGyH,EAAqBG,EAAAA,cAAoB,QAAS,CAC3D,GAAIF,CACL,EAAED,CAAK,EAAI,KAAmBG,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,mWACP,CAAG,CAAC,CACJ,CACA,MAAMC,GAA2BC,EAAgB,WAACK,EAAS,ECvB3D,SAASC,GAAS,CAChB,MAAAX,EACA,QAAAC,EACA,GAAG1H,CACL,EAAG2H,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBD,CACpB,EAAE1H,CAAK,EAAGyH,EAAqBG,EAAAA,cAAoB,QAAS,CAC3D,GAAIF,CACL,EAAED,CAAK,EAAI,KAAmBG,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,yJACP,CAAG,CAAC,CACJ,CACA,MAAMC,GAA2BC,EAAgB,WAACM,EAAQ,ECZpDC,GAAQC,EAAM,WAClB,CACE,CACE,UAAAC,EACA,MAAAC,EACA,MAAAtM,EACA,WAAAuM,EACA,SAAAC,EACA,UAAAC,EACA,UAAAC,EAAY,GACZ,GAAAC,EACA,GAAG7I,GAELtH,IACG,CACH,MAAMoQ,EAAUD,GAAM,SAAS,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAEhEE,EAAeC,GACnB,aACA,8CACA,yEACAN,GAAY,QACZC,GAAa,QACbzM,EACI,yDACA,oEACJ0M,EAAY,SAAW,SACvBL,CACF,EAEA,cACG,MAAI,CAAA,UAAWS,GAAK,YAAaJ,GAAa,QAAQ,EACpD,SAAA,CACCJ,GAAAS,EAAA,KAAC,QAAA,CACC,QAASH,EACT,UAAU,0CAET,SAAA,CAAAN,EACAxI,EAAM,UAAYkJ,EAAAA,IAAC,OAAK,CAAA,UAAU,oBAAoB,SAAC,GAAA,CAAA,CAAA,CAAA,CAC1D,EAGFD,EAAAA,KAAC,MAAI,CAAA,UAAU,WACZ,SAAA,CACCP,GAAAQ,EAAA,IAAC,OAAI,UAAU,uEACb,eAAC,OAAK,CAAA,UAAU,gBAAiB,SAAAR,CAAA,CAAS,CAC5C,CAAA,EAGFQ,EAAA,IAAC,QAAA,CACC,IAAAxQ,EACA,GAAIoQ,EACJ,UAAWC,EACV,GAAG/I,CAAA,CACN,EAEC2I,GACEO,EAAAA,IAAA,MAAA,CAAI,UAAU,wEACb,eAAC,OAAK,CAAA,UAAU,gBAAiB,SAAAP,CAAU,CAAA,CAC7C,CAAA,CAAA,EAEJ,EAECzM,GACEgN,EAAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,KAAK,QACtC,SACHhN,EAAA,EAGDuM,GAAc,CAACvM,SACb,IAAE,CAAA,UAAU,wBACV,SACHuM,CAAA,CAAA,CAAA,EAEJ,CAAA,CAGN,EAEAJ,GAAM,YAAc,QClEpB,MAAMc,GAAoD,CAAC,CACzD,UAAAC,EACA,gBAAAC,CACF,IAAM,CACE,KAAA,CAAE,CAAE,EAAIC,GAAe,EACvB,CAAE,SAAUC,EAAc,UAAAC,EAAW,MAAAtN,CAAA,EAAUuN,GAAQ,EACvD,CAACC,EAAcC,CAAe,EAAIC,EAAAA,SAAS,EAAK,EAChD,CAACC,EAAqBC,CAAsB,EAAIF,EAAAA,SAAS,EAAK,EAE9D,CACJ,SAAAhE,EACA,aAAAM,EACA,MAAAb,EACA,UAAW,CAAE,OAAAnO,EAAQ,QAAA8J,CAAQ,GAC3BkG,GAAkB,CACpB,KAAM,WACN,cAAe,CACb,eAAgB,GAChB,gBAAiB,EAAA,CACnB,CACD,EAEK6C,EAAW1E,EAAM,UAAU,EAE3B2E,EAAW,MAAO3V,GAAmB,CACrC,GAAA,CACF,MAAM4V,EAAqC,CACzC,KAAM5V,EAAK,KACX,MAAOA,EAAK,MACZ,SAAUA,EAAK,SACf,YAAaA,EAAK,YAClB,kBAAmB,KACnB,eAAgBA,EAAK,eACrB,gBAAiBA,EAAK,eACxB,EAEA,MAAMkV,EAAaU,CAAgB,EACvBb,IAAA,QACLlN,EAAO,CAEN,QAAA,MAAM,uBAAwBA,CAAK,CAAA,CAE/C,EAEA,OACG+M,EAAAA,KAAAiB,GAAA,CAAK,UAAU,0BAA0B,QAAQ,WAChD,SAAA,CAAAjB,OAACkB,GACC,CAAA,SAAA,CAAAjB,MAACkB,GAAU,CAAA,UAAU,qCAClB,SAAA,EAAE,sBAAsB,EAC3B,QACCC,GAAgB,CAAA,UAAU,cACxB,SAAA,EAAE,iBAAiB,CACtB,CAAA,CAAA,EACF,EAEAnB,EAAAA,IAACoB,IACC,SAACrB,EAAAA,KAAA,OAAA,CAAK,SAAU/C,EAAa8D,CAAQ,EAAG,UAAU,YAEhD,SAAA,CAAAd,EAAA,IAACb,GAAA,CACC,MAAO,EAAE,WAAW,EACpB,KAAK,OACL,SAAUa,EAAAA,IAACd,GAAS,CAAA,UAAU,SAAU,CAAA,EACxC,UAAS,GACT,MAAOlR,EAAO,MAAM,QACnB,GAAG0O,EAAS,OAAQ,CACnB,SAAU,EAAE,qBAAqB,EACjC,UAAW,CACT,MAAO,EACP,QAAS,EAAE,iBAAiB,CAAA,CAE/B,CAAA,CAAA,CACH,EAGAsD,EAAA,IAACb,GAAA,CACC,MAAO,EAAE,YAAY,EACrB,KAAK,QACL,SAAUa,EAAAA,IAACnB,GAAa,CAAA,UAAU,SAAU,CAAA,EAC5C,UAAS,GACT,MAAO7Q,EAAO,OAAO,QACpB,GAAG0O,EAAS,QAAS,CACpB,SAAU,EAAE,qBAAqB,EACjC,QAAS,CACP,MAAO,2CACP,QAAS,EAAE,kBAAkB,CAAA,CAEhC,CAAA,CAAA,CACH,EAGAsD,EAAA,IAACb,GAAA,CACC,MAAO,GAAG,EAAE,kBAAkB,CAAC,KAAK,EAAE,iBAAiB,CAAC,IACxD,KAAK,MACL,SAAUa,EAAAA,IAACf,GAAU,CAAA,UAAU,SAAU,CAAA,EACzC,UAAS,GACT,WAAW,wCACX,MAAOjR,EAAO,aAAa,QAC1B,GAAG0O,EAAS,cAAe,CAC1B,QAAS,CACP,MAAO,iBACP,QAAS,EAAE,wBAAwB,CAAA,CAEtC,CAAA,CAAA,CACH,EAGAsD,EAAA,IAACb,GAAA,CACC,MAAO,EAAE,eAAe,EACxB,KAAMqB,EAAe,OAAS,WAC9B,UAAS,GACT,MAAOxS,EAAO,UAAU,QACxB,UACEgS,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMS,EAAgB,CAACD,CAAY,EAC5C,UAAU,oCAET,SAAAA,QACE1B,GAAa,CAAA,UAAU,SAAU,CAAA,EAElCkB,EAAAA,IAACjB,GAAQ,CAAA,UAAU,SAAU,CAAA,CAAA,CAEjC,EAED,GAAGrC,EAAS,WAAY,CACvB,SAAU,EAAE,qBAAqB,EACjC,UAAW,CACT,MAAO,EACP,QAAS,EAAE,qBAAqB,CAAA,CAEnC,CAAA,CAAA,CACH,EAGAsD,EAAA,IAACb,GAAA,CACC,MAAO,EAAE,sBAAsB,EAC/B,KAAMwB,EAAsB,OAAS,WACrC,UAAS,GACT,MAAO3S,EAAO,iBAAiB,QAC/B,UACEgS,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMY,EAAuB,CAACD,CAAmB,EAC1D,UAAU,oCAET,SAAAA,QACE7B,GAAa,CAAA,UAAU,SAAU,CAAA,EAElCkB,EAAAA,IAACjB,GAAQ,CAAA,UAAU,SAAU,CAAA,CAAA,CAEjC,EAED,GAAGrC,EAAS,kBAAmB,CAC9B,SAAU,EAAE,qBAAqB,EACjC,SAAWtS,GACTA,IAAUyW,GAAY,EAAE,0BAA0B,CACrD,CAAA,CAAA,CACH,EAGAd,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,6BACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,UAAU,iFACT,GAAGtD,EAAS,iBAAkB,CAC7B,SAAU,6BACX,CAAA,CAAA,CACH,EACAqD,EAAAA,KAAC,OAAK,CAAA,UAAU,wBACb,SAAA,CAAA,EAAE,iBAAiB,EACpBC,EAAA,IAAC,IAAA,CACC,KAAK,WACL,UAAU,iDACV,OAAO,SACP,IAAI,sBAEH,WAAE,mBAAmB,CAAA,CAAA,CACxB,CACF,CAAA,CAAA,EACF,EACChS,EAAO,gBACLgS,MAAA,IAAA,CAAE,UAAU,uBAAwB,SAAAhS,EAAO,eAAe,OAAQ,CAAA,EAGrE+R,EAAAA,KAAC,QAAM,CAAA,UAAU,6BACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,UAAU,iFACT,GAAGtD,EAAS,iBAAiB,CAAA,CAChC,EACAqD,EAAAA,KAAC,OAAK,CAAA,UAAU,wBACb,SAAA,CAAA,EAAE,yBAAyB,EAAE,KAAG,EAAE,iBAAiB,EAAE,GAAA,CACxD,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGC/M,GACEgN,EAAA,IAAA,MAAA,CAAI,UAAU,iDACb,eAAC,IAAE,CAAA,UAAU,uBAAwB,SAAAhN,CAAA,CAAM,CAC7C,CAAA,EAIFgN,EAAA,IAACqB,GAAA,CACC,KAAK,SACL,QAAQ,WACR,KAAK,KACL,UAAS,GACT,UAAAf,EACA,SAAU,CAACxI,EAEV,WAAE,oBAAoB,CAAA,CACzB,QAGC,MAAI,CAAA,UAAU,cACb,SAACiI,EAAA,KAAA,OAAA,CAAK,UAAU,wBACb,SAAA,CAAA,EAAE,yBAAyB,EAAG,IAC/BC,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAASG,EACT,UAAU,wDAET,WAAE,YAAY,CAAA,CAAA,CACjB,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,EC/OMmB,GAAsC,CAAC,CAC3C,UAAApB,EACA,mBAAAqB,CACF,IAAM,CACE,KAAA,CAAE,CAAE,EAAInB,GAAe,EACvB,CAAE,MAAAoB,EAAO,kBAAAC,EAAmB,UAAAnB,EAAW,MAAAtN,CAAA,EAAUuN,GAAQ,EACzD,CAACC,EAAcC,CAAe,EAAIC,EAAAA,SAAS,EAAK,EAEhD,CACJ,SAAAhE,EACA,aAAAM,EACA,UAAW,CAAE,OAAAhP,EAAQ,QAAA8J,CAAQ,GAC3BkG,GAAkB,CACpB,KAAM,UAAA,CACP,EAEK8C,EAAW,MAAO3V,GAAmB,CACrC,GAAA,CACF,MAAMqW,EAAMrW,EAAK,MAAOA,EAAK,QAAQ,EACzB+U,IAAA,QACLlN,EAAO,CAEN,QAAA,MAAM,gBAAiBA,CAAK,CAAA,CAExC,EAEM0O,EAAsB,MAAOC,GAAoC,CACjE,GAAA,CACF,MAAMF,EAAkBE,CAAQ,EACpBzB,IAAA,QACLlN,EAAO,CACN,QAAA,MAAM,yBAA0BA,CAAK,CAAA,CAEjD,EAEA,OACG+M,EAAAA,KAAAiB,GAAA,CAAK,UAAU,0BAA0B,QAAQ,WAChD,SAAA,CAAAjB,OAACkB,GACC,CAAA,SAAA,CAAAjB,MAACkB,GAAU,CAAA,UAAU,qCAClB,SAAA,EAAE,kBAAkB,EACvB,EACClB,EAAA,IAAAmB,GAAA,CAAgB,UAAU,cAAc,SAEzC,2CAAA,CAAA,CAAA,EACF,EAEAnB,EAAAA,IAACoB,IACC,SAACrB,EAAAA,KAAA,OAAA,CAAK,SAAU/C,EAAa8D,CAAQ,EAAG,UAAU,YAEhD,SAAA,CAAAd,EAAA,IAACb,GAAA,CACC,MAAO,EAAE,YAAY,EACrB,KAAK,QACL,SAAUa,EAAAA,IAACnB,GAAa,CAAA,UAAU,SAAU,CAAA,EAC5C,UAAS,GACT,MAAO7Q,EAAO,OAAO,QACpB,GAAG0O,EAAS,QAAS,CACpB,SAAU,EAAE,qBAAqB,EACjC,QAAS,CACP,MAAO,2CACP,QAAS,EAAE,kBAAkB,CAAA,CAEhC,CAAA,CAAA,CACH,EAGAsD,EAAA,IAACb,GAAA,CACC,MAAO,EAAE,eAAe,EACxB,KAAMqB,EAAe,OAAS,WAC9B,UAAS,GACT,MAAOxS,EAAO,UAAU,QACxB,UACEgS,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMS,EAAgB,CAACD,CAAY,EAC5C,UAAU,oCAET,SAAAA,QACE1B,GAAa,CAAA,UAAU,SAAU,CAAA,EAElCkB,EAAAA,IAACjB,GAAQ,CAAA,UAAU,SAAU,CAAA,CAAA,CAEjC,EAED,GAAGrC,EAAS,WAAY,CACvB,SAAU,EAAE,qBAAqB,CAClC,CAAA,CAAA,CACH,EAGAsD,EAAAA,IAAC,MAAI,CAAA,UAAU,aACb,SAAAA,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,oDAET,WAAE,qBAAqB,CAAA,CAAA,EAE5B,EAGChN,GACEgN,EAAA,IAAA,MAAA,CAAI,UAAU,iDACb,eAAC,IAAE,CAAA,UAAU,uBAAwB,SAAAhN,CAAA,CAAM,CAC7C,CAAA,EAIFgN,EAAA,IAACqB,GAAA,CACC,KAAK,SACL,QAAQ,WACR,KAAK,KACL,UAAS,GACT,UAAAf,EACA,SAAU,CAACxI,EAEV,WAAE,YAAY,CAAA,CACjB,EAGAiI,EAAAA,KAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qCACb,eAAC,MAAI,CAAA,UAAU,kCAAkC,CACnD,CAAA,EACAA,EAAAA,IAAC,OAAI,UAAU,uCACb,eAAC,OAAK,CAAA,UAAU,8BAA8B,SAAA,kBAAgB,CAAA,CAChE,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAA,IAACqB,GAAA,CACC,KAAK,SACL,QAAQ,UACR,KAAK,KACL,UAAS,GACT,QAAS,IAAMK,EAAoB,QAAQ,EAC3C,SACG3B,EAAAA,KAAA,MAAA,CAAI,UAAU,UAAU,QAAQ,YAC/B,SAAA,CAAAC,EAAA,IAAC,OAAA,CACC,KAAK,eACL,EAAE,yHAAA,CACJ,EACAA,EAAA,IAAC,OAAA,CACC,KAAK,eACL,EAAE,uIAAA,CACJ,EACAA,EAAA,IAAC,OAAA,CACC,KAAK,eACL,EAAE,+HAAA,CACJ,EACAA,EAAA,IAAC,OAAA,CACC,KAAK,eACL,EAAE,qIAAA,CAAA,CACJ,EACF,EAGD,SAAE,EAAA,kBAAmB,CAAE,SAAU,QAAU,CAAA,CAAA,CAC9C,EAEAA,EAAA,IAACqB,GAAA,CACC,KAAK,SACL,QAAQ,UACR,KAAK,KACL,UAAS,GACT,QAAS,IAAMK,EAAoB,UAAU,EAC7C,SACE1B,EAAA,IAAC,MAAI,CAAA,UAAU,UAAU,KAAK,eAAe,QAAQ,YACnD,SAAAA,EAAAA,IAAC,OAAK,CAAA,EAAE,gSAAiS,CAAA,EAC3S,EAGD,SAAE,EAAA,kBAAmB,CAAE,SAAU,UAAY,CAAA,CAAA,CAAA,CAChD,EACF,QAGC,MAAI,CAAA,UAAU,cACb,SAACD,EAAA,KAAA,OAAA,CAAK,UAAU,wBACb,SAAA,CAAA,EAAE,sBAAsB,EAAG,IAC5BC,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAASuB,EACT,UAAU,wDAET,WAAE,eAAe,CAAA,CAAA,CACpB,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,ECvMMK,GAAwB,CAC5B,CAAE,KAAM,KAAM,KAAM,UAAW,WAAY,UAAW,KAAM,MAAO,EACnE,CAAE,KAAM,KAAM,KAAM,YAAa,WAAY,YAAa,KAAM,MAAO,EACvE,CAAE,KAAM,KAAM,KAAM,OAAQ,WAAY,UAAW,KAAM,MAAO,EAChE,CAAE,KAAM,KAAM,KAAM,QAAS,WAAY,WAAY,KAAM,MAAO,CACpE,EAEMC,GAA6B,IAAM,CACjC,KAAA,CAAE,KAAAC,CAAK,EAAI1B,GAAe,EAC1B,CAAC2B,EAAQC,CAAS,EAAItB,EAAAA,SAAS,EAAK,EAEpCuB,EAAkBL,GAAU,KAAaM,GAAAA,EAAK,OAASJ,EAAK,QAAQ,GAAKF,GAAU,CAAC,EAEpFO,EAAwBC,GAAyB,CACrDN,EAAK,eAAeM,CAAY,EAChCJ,EAAU,EAAK,CACjB,EAGE,OAAAjC,EAAA,KAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,KAAK,SACL,UAAWD,GACT,4DACA,2DACA,2EACA,gCACF,EACA,QAAS,IAAMkC,EAAU,CAACD,CAAM,EAChC,gBAAeA,EACf,gBAAc,UAEd,SAAA,CAAC/B,EAAAA,IAAAhB,GAAA,CAAa,UAAU,SAAU,CAAA,EACjCgB,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAoB,WAAgB,KAAK,EACxDA,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAoB,WAAgB,WAAW,EAC/DA,EAAA,IAAC1B,GAAA,CACC,UAAWwB,GACT,4CACAiC,GAAU,YAAA,CACZ,CAAA,CACF,CAAA,CACF,EAECA,GAGGhC,EAAA,KAAAsC,WAAA,CAAA,SAAA,CAAArC,EAAA,IAAC,MAAA,CACC,UAAU,qBACV,QAAS,IAAMgC,EAAU,EAAK,CAAA,CAChC,EAGChC,EAAA,IAAA,MAAA,CAAI,UAAU,uFACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,OAAO,KAAK,UACxB,SAAU4B,GAAA,IAAKU,GACdvC,EAAA,KAAC,SAAA,CAEC,KAAK,SACL,UAAWD,GACT,uDACA,uCACA,8BACAwC,EAAS,OAASR,EAAK,UAAY,kCACrC,EACA,QAAS,IAAMK,EAAqBG,EAAS,IAAI,EACjD,KAAK,SACL,gBAAeA,EAAS,OAASR,EAAK,SAEtC,SAAA,CAAA9B,EAAA,IAAC,OAAK,CAAA,UAAU,UAAW,SAAAsC,EAAS,KAAK,EACzCvC,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAAAC,EAAA,IAAC,MAAI,CAAA,UAAU,cAAe,SAAAsC,EAAS,WAAW,EACjDtC,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAyB,WAAS,IAAK,CAAA,CAAA,EACxD,EACCsC,EAAS,OAASR,EAAK,UACrB9B,EAAA,IAAA,MAAA,CAAI,UAAU,sCAAuC,CAAA,CAAA,CAAA,EAlBnDsC,EAAS,IAqBjB,CAAA,CACH,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EAEJ,CAEJ,ECvFMC,GAAqB,IAAM,CAC/B,KAAM,CAACrQ,EAAMsQ,CAAO,EAAI9B,EAAAA,SAAmB,UAAU,EAC/C+B,EAAWC,GAAY,EACvB,CAAE,EAAAC,CAAE,EAAIvC,GAAe,EAEvBwC,EAAoB,IAAM,CAG5BH,EADEvQ,IAAS,WACF,uBAEA,GAFsB,CAInC,EAGE,OAAA6N,EAAA,KAAC,MAAI,CAAA,UAAU,uFAEb,SAAA,CAACA,EAAAA,KAAA,SAAA,CAAO,UAAU,wCAChB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,uEAAwE,CAAA,QACtF,KAAG,CAAA,UAAU,2CACX,SAAA2C,EAAE,UAAU,CACf,CAAA,CAAA,EACF,QACCd,GAAiB,CAAA,CAAA,CAAA,EACpB,EAGA7B,EAAAA,IAAC,QAAK,UAAU,8CACd,eAAC,MAAI,CAAA,UAAU,kBACZ,SAAA9N,IAAS,WACR8N,EAAA,IAACC,GAAA,CACC,UAAW2C,EACX,gBAAiB,IAAMJ,EAAQ,OAAO,CAAA,CAAA,EAGxCxC,EAAA,IAACsB,GAAA,CACC,UAAWsB,EACX,mBAAoB,IAAMJ,EAAQ,UAAU,CAAA,GAGlD,CACF,CAAA,EAGAzC,EAAAA,KAAC,SAAO,CAAA,UAAU,wCAChB,SAAA,CAACC,EAAA,IAAA,IAAA,CAAG,SAAE2C,EAAA,aAAa,CAAE,CAAA,EACpB3C,EAAA,IAAA,IAAA,CAAE,UAAU,OAAO,SAEpB,8EAAA,CAAA,CAAA,EACF,EAGAA,EAAAA,IAAC,MAAI,CAAA,GAAG,qBAAsB,CAAA,CAAA,EAChC,CAEJ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}