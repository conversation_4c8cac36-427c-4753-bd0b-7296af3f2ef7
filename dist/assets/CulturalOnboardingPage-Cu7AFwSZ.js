import{u as g,j as e,C as b,b as v,d as j,e as N,f as y,c as f,B as p,g as k,h as C,a as S}from"./index-nwrMOwxu.js";import{r as a}from"./vendor-DtOhX2xw.js";import"./firebase-DLuFXYhP.js";function E({title:t,titleId:r,...s},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":r},s),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))}const L=a.forwardRef(E);function M({title:t,titleId:r,...s},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":r},s),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))}const W=a.forwardRef(M);function A({title:t,titleId:r,...s},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":r},s),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))}const R=a.forwardRef(A);function T({title:t,titleId:r,...s},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":r},s),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}const I=a.forwardRef(T);function $({title:t,titleId:r,...s},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":r},s),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.05 4.575a1.575 1.575 0 1 0-3.15 0v3m3.15-3v-1.5a1.575 1.575 0 0 1 3.15 0v1.5m-3.15 0 .075 5.925m3.075.75V4.575m0 0a1.575 1.575 0 0 1 3.15 0V15M6.9 7.575a1.575 1.575 0 1 0-3.15 0v8.175a6.75 6.75 0 0 0 6.75 6.75h2.018a5.25 5.25 0 0 0 3.712-1.538l1.732-1.732a5.25 5.25 0 0 0 1.538-3.712l.003-2.024a.668.668 0 0 1 .198-.471 1.575 1.575 0 1 0-2.228-2.228 3.818 3.818 0 0 0-1.12 2.687M6.9 7.575V12m6.27 4.318A4.49 4.49 0 0 1 16.35 15m.002 0h-.002"}))}const B=a.forwardRef($);function F({title:t,titleId:r,...s},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":r},s),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))}const w=a.forwardRef(F);function O({title:t,titleId:r,...s},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":r},s),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 6.75V15m6-6v8.25m.503 3.498 4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 0 0-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0Z"}))}const Z=a.forwardRef(O);function z({title:t,titleId:r,...s},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":r},s),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}const V=a.forwardRef(z),K=({selectedIdentities:t,onSelectionChange:r,onContinue:s,onSkip:i})=>{const{t:n}=g(),[d,c]=a.useState(null),o=[{id:"zulu",name:n("cultural.identities.zulu"),description:"The largest ethnic group in South Africa, primarily in KwaZulu-Natal",region:"KwaZulu-Natal"},{id:"xhosa",name:n("cultural.identities.xhosa"),description:"Primarily in Eastern Cape and Western Cape provinces",region:"Eastern Cape, Western Cape"},{id:"afrikaans",name:n("cultural.identities.afrikaans"),description:"Afrikaans-speaking community across South Africa",region:"Western Cape, Northern Cape"},{id:"english",name:n("cultural.identities.english"),description:"English-speaking South African community",region:"Gauteng, KwaZulu-Natal"},{id:"sotho",name:n("cultural.identities.sotho"),description:"Sesotho-speaking community, primarily in Free State",region:"Free State, Gauteng"},{id:"tswana",name:n("cultural.identities.tswana"),description:"Setswana-speaking community in North West province",region:"North West, Gauteng"},{id:"tsonga",name:n("cultural.identities.tsonga"),description:"Xitsonga-speaking community in Limpopo and Mpumalanga",region:"Limpopo, Mpumalanga"},{id:"venda",name:n("cultural.identities.venda"),description:"Tshivenda-speaking community in Limpopo",region:"Limpopo"},{id:"swazi",name:n("cultural.identities.swazi"),description:"SiSwati-speaking community in Mpumalanga",region:"Mpumalanga"},{id:"ndebele",name:n("cultural.identities.ndebele"),description:"IsiNdebele-speaking community in Mpumalanga and Limpopo",region:"Mpumalanga, Limpopo"},{id:"indian",name:n("cultural.identities.indian"),description:"South African Indian community",region:"KwaZulu-Natal, Gauteng"},{id:"coloured",name:n("cultural.identities.coloured"),description:"Mixed-race South African community",region:"Western Cape, Northern Cape"},{id:"other",name:n("cultural.identities.other"),description:"Other cultural backgrounds and mixed heritage",region:"All provinces"}],m=l=>{const h=t.includes(l)?t.filter(u=>u!==l):[...t,l];r(h)};return e.jsxs(b,{className:"w-full max-w-2xl mx-auto",variant:"cultural",children:[e.jsxs(v,{children:[e.jsx(j,{className:"text-center text-cultural-gradient",children:n("auth.culturalIdentity.title")}),e.jsx(N,{className:"text-center",children:n("auth.culturalIdentity.description")}),e.jsxs("div",{className:"flex items-center justify-center space-x-4 text-sm text-gray-600",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(w,{className:"w-4 h-4 mr-1"}),n("auth.culturalIdentity.multiple")]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(w,{className:"w-4 h-4 mr-1"}),n("auth.culturalIdentity.optional")]})]})]}),e.jsx(y,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:o.map(l=>e.jsxs("div",{className:f("relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200","hover:shadow-md focus-within:ring-2 focus-within:ring-cultural-500",t.includes(l.id)?"border-cultural-500 bg-cultural-50":"border-gray-200 hover:border-cultural-300"),onClick:()=>m(l.id),onMouseEnter:()=>c(l.id),onMouseLeave:()=>c(null),children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-medium text-gray-900",children:l.name}),l.region&&e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:l.region})]}),e.jsx("div",{className:f("w-5 h-5 rounded border-2 flex items-center justify-center",t.includes(l.id)?"border-cultural-500 bg-cultural-500":"border-gray-300"),children:t.includes(l.id)&&e.jsx(I,{className:"w-3 h-3 text-white"})})]}),d===l.id&&e.jsx("div",{className:"absolute z-10 bottom-full left-0 right-0 mb-2 p-2 bg-gray-900 text-white text-xs rounded shadow-lg",children:l.description})]},l.id))}),t.length>0&&e.jsxs("div",{className:"p-4 bg-ubuntu-50 border border-ubuntu-200 rounded-lg",children:[e.jsxs("h4",{className:"font-medium text-ubuntu-800 mb-2",children:["Selected Cultural Identities (",t.length,")"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:t.map(l=>{const h=o.find(u=>u.id===l);return e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-ubuntu-100 text-ubuntu-800",children:h?.name},l)})})]}),e.jsx("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:e.jsxs("p",{className:"text-sm text-blue-800",children:["🔒 ",n("auth.culturalIdentity.privacy")]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsx(p,{variant:"outline",size:"lg",fullWidth:!0,onClick:i,children:n("auth.culturalIdentity.skip")}),e.jsx(p,{variant:"cultural",size:"lg",fullWidth:!0,onClick:s,children:n("auth.culturalIdentity.continue")})]})]})})]})},U=({onComplete:t,onSkip:r,userCulturalIdentities:s})=>{const{t:i}=g(),[n,d]=a.useState(0),c=[{id:"platform-overview",title:"Welcome to Ubuntu Connect",description:"A platform that celebrates South African cultural diversity while building bridges between communities.",icon:e.jsx(B,{className:"w-12 h-12 text-cultural-500"}),features:["Connect with people from all 11 official language groups","Share and learn about different cultural traditions","Collaborate on projects that unite communities","Respect and celebrate our Rainbow Nation heritage"],culturalContext:s.length>0?`We see you've selected ${s.length} cultural ${s.length===1?"identity":"identities"}. This will help us connect you with relevant communities and content.`:"You can always add your cultural identities later to get personalized recommendations."},{id:"cultural-discovery",title:"Discover Cultural Heritage",description:"Explore the rich tapestry of South African cultures through interactive maps, stories, and traditions.",icon:e.jsx(Z,{className:"w-12 h-12 text-ubuntu-500"}),features:["Interactive cultural map of South Africa","Traditional stories and historical content","Language learning opportunities","Cultural events and celebrations"],culturalContext:"Every culture has unique wisdom to share. Ubuntu Connect helps preserve and share this knowledge respectfully."},{id:"community-features",title:"Join Diverse Communities",description:"Find communities based on location, interests, or cultural background. Build meaningful cross-cultural connections.",icon:e.jsx(V,{className:"w-12 h-12 text-cultural-500"}),features:["Location-based community discovery","Cross-cultural collaboration projects","Skill sharing and mentorship","Cultural exchange programs"],culturalContext:'Ubuntu philosophy teaches us "I am because we are." Together, we are stronger and more creative.'},{id:"knowledge-exchange",title:"Share Knowledge & Skills",description:"Exchange skills, mentor others, and learn from diverse perspectives in our knowledge marketplace.",icon:e.jsx(L,{className:"w-12 h-12 text-ubuntu-500"}),features:["Skill-based matching system","Time banking for fair exchange","Cultural context in learning","Recognition and achievements"],culturalContext:"Every person has valuable knowledge. Ubuntu Connect helps you share your gifts and learn from others."}],o=c[n],m=n===c.length-1,l=()=>{m?t():d(n+1)},h=()=>{n>0&&d(n-1)};return e.jsxs(b,{className:"w-full max-w-3xl mx-auto",variant:"cultural",children:[e.jsxs(v,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[o.icon,e.jsxs("div",{children:[e.jsx(j,{className:"text-2xl text-cultural-gradient",children:o.title}),e.jsx(N,{className:"text-lg",children:o.description})]})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[n+1," of ",c.length]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-cultural-500 to-ubuntu-500 h-2 rounded-full transition-all duration-300",style:{width:`${(n+1)/c.length*100}%`}})})]}),e.jsx(y,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"font-semibold text-gray-800",children:"Key Features:"}),e.jsx("ul",{className:"space-y-2",children:o.features.map((u,x)=>e.jsxs("li",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-cultural-500 rounded-full mt-2 flex-shrink-0"}),e.jsx("span",{className:"text-gray-700",children:u})]},x))})]}),o.culturalContext&&e.jsx("div",{className:"p-4 bg-ubuntu-50 border border-ubuntu-200 rounded-lg",children:e.jsxs("p",{className:"text-ubuntu-800 text-sm",children:[e.jsx("span",{className:"font-medium",children:"Cultural Insight:"})," ",o.culturalContext]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 pt-4",children:[e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(p,{variant:"ghost",onClick:r,children:"Skip Tour"}),n>0&&e.jsx(p,{variant:"outline",onClick:h,leftIcon:e.jsx(W,{className:"w-4 h-4"}),children:"Previous"})]}),e.jsx(p,{variant:"cultural",size:"lg",onClick:l,rightIcon:m?void 0:e.jsx(R,{className:"w-4 h-4"}),children:m?"Start Your Journey":"Next"})]}),e.jsx("div",{className:"flex justify-center space-x-2 pt-4",children:c.map((u,x)=>e.jsx("button",{className:`w-3 h-3 rounded-full transition-colors duration-200 ${x===n?"bg-cultural-500":x<n?"bg-ubuntu-400":"bg-gray-300"}`,onClick:()=>d(x),"aria-label":`Go to step ${x+1}`},x))})]})})]})},D=()=>{const[t,r]=a.useState("cultural-identity"),[s,i]=a.useState([]),n=k(),{t:d}=g(),{setCulturalIdentities:c}=C(),{user:o}=S(),m=()=>{c(s),r("welcome-tour")},l=()=>{r("welcome-tour")},h=()=>{r("complete"),n("/")},u=()=>{n("/")};return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-cultural-50 via-white to-ubuntu-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b border-gray-200",children:e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-cultural-500 to-ubuntu-500 rounded-full"}),e.jsx("h1",{className:"text-xl font-bold text-cultural-gradient",children:d("app.name")})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${t==="cultural-identity"?"bg-cultural-500":"bg-gray-300"}`}),e.jsx("div",{className:`w-3 h-3 rounded-full ${t==="welcome-tour"?"bg-cultural-500":"bg-gray-300"}`}),e.jsx("span",{className:"text-sm text-gray-600 ml-2",children:"Welcome Setup"})]})]})})})}),e.jsxs("main",{className:"flex-1 flex items-center justify-center p-4 py-8",children:[t==="cultural-identity"&&e.jsx(K,{selectedIdentities:s,onSelectionChange:i,onContinue:m,onSkip:l}),t==="welcome-tour"&&e.jsx(U,{onComplete:h,onSkip:u,userCulturalIdentities:s})]}),e.jsxs("footer",{className:"p-4 text-center text-sm text-gray-600",children:[e.jsxs("p",{children:["Welcome to Ubuntu Connect, ",o?.displayName||"friend","!"]}),e.jsx("p",{className:"mt-1",children:"Let's set up your cultural journey • Building bridges across cultures"})]})]})};export{D as default};
//# sourceMappingURL=CulturalOnboardingPage-Cu7AFwSZ.js.map
