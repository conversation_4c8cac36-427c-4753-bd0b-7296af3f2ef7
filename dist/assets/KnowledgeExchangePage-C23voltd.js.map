{"version": 3, "file": "KnowledgeExchangePage-C23voltd.js", "sources": ["../../src/services/skillSharingService.ts", "../../src/services/timeBankingService.ts", "../../src/features/skill-sharing/components/SkillSharingDashboard.tsx", "../../src/services/marketplaceService.ts", "../../src/features/marketplace/components/KnowledgeMarketplace.tsx", "../../src/features/time-banking/components/TimeBankingDashboard.tsx", "../../src/pages/KnowledgeExchangePage.tsx"], "sourcesContent": ["import { \n  collection, \n  doc, \n  setDoc, \n  getDoc, \n  getDocs, \n  query, \n  where, \n  orderBy, \n  limit, \n  updateDoc,\n  arrayUnion,\n  arrayRemove,\n  Timestamp,\n  writeBatch\n} from 'firebase/firestore'\nimport { db } from './firebase'\nimport { culturalValidationService } from './culturalValidationService'\n\nexport interface SkillProfile {\n  id: string\n  userId: string\n  skillsToTeach: TeachingSkill[]\n  skillsToLearn: LearningGoal[]\n  culturalTeachingStyle: CulturalTeachingStyle\n  mentorshipPreferences: MentorshipPreferences\n  availability: AvailabilitySchedule\n  reputation: ReputationMetrics\n  timeCredits: TimeCredits\n  createdAt: Timestamp\n  updatedAt: Timestamp\n}\n\nexport interface TeachingSkill {\n  skillId: string\n  skillName: string\n  category: SkillCategory\n  proficiencyLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert' | 'master'\n  culturalContext: CulturalContext[]\n  yearsOfExperience: number\n  certifications: Certification[]\n  teachingMethods: TeachingMethod[]\n  culturalApproaches: string[]\n  maximumStudents: number\n  preferredSessionDuration: number // minutes\n  timeCreditsPerHour: number\n  portfolioItems: PortfolioItem[]\n}\n\nexport interface LearningGoal {\n  skillId: string\n  skillName: string\n  currentLevel: 'none' | 'beginner' | 'intermediate' | 'advanced'\n  targetLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert'\n  learningStyle: LearningStyle\n  culturalPreferences: CulturalLearningPreference[]\n  timeCommitment: number // hours per week\n  deadline?: Timestamp\n  motivation: string\n  priorExperience?: string\n}\n\nexport interface CulturalTeachingStyle {\n  primaryCulture: string\n  teachingPhilosophy: string\n  culturalApproaches: CulturalApproach[]\n  languageCapabilities: LanguageCapability[]\n  culturalSensitivities: string[]\n  crossCulturalExperience: number // years\n  culturalAdaptability: number // 1-10 scale\n}\n\nexport interface MentorshipMatch {\n  id: string\n  mentorId: string\n  menteeId: string\n  skillId: string\n  matchScore: number // 0-100\n  culturalCompatibility: number // 0-100\n  matchingFactors: MatchingFactor[]\n  status: 'pending' | 'active' | 'completed' | 'paused' | 'terminated'\n  learningPlan: LearningPlan\n  culturalExchangePlan: CulturalExchangePlan\n  createdAt: Timestamp\n  startDate?: Timestamp\n  expectedDuration: number // weeks\n  actualProgress: ProgressTracking\n}\n\nexport interface LearningSession {\n  id: string\n  mentorshipMatchId: string\n  scheduledAt: Timestamp\n  duration: number // minutes\n  sessionType: 'individual' | 'group' | 'workshop' | 'practice'\n  location: 'online' | 'in_person' | 'hybrid'\n  culturalElements: CulturalElement[]\n  learningObjectives: string[]\n  preparationMaterials: Material[]\n  sessionNotes: SessionNote[]\n  skillProgress: SkillProgressMetric[]\n  culturalInsights: CulturalInsight[]\n  timeCreditsAwarded: number\n  feedback: SessionFeedback\n  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'\n}\n\nexport interface TimeCredits {\n  balance: number\n  earned: TimeTransaction[]\n  spent: TimeTransaction[]\n  reserved: TimeReservation[]\n  lifetimeEarned: number\n  lifetimeSpent: number\n  qualityMultiplier: number // 0.5-2.0 based on teaching quality\n  culturalBonusRate: number // additional credits for cross-cultural exchanges\n}\n\n// Supporting interfaces\nexport interface SkillCategory {\n  id: string\n  name: string\n  culturalOrigin?: string\n  description: string\n}\n\nexport interface CulturalContext {\n  culture: string\n  significance: string\n  traditionalMethods: string[]\n  modernAdaptations: string[]\n}\n\nexport interface Certification {\n  id: string\n  name: string\n  issuer: string\n  dateEarned: Timestamp\n  verificationUrl?: string\n  culturalRecognition?: boolean\n}\n\nexport interface TeachingMethod {\n  method: string\n  culturalApproach: string\n  effectiveness: number\n  preferredGroupSize: number\n}\n\nexport interface PortfolioItem {\n  id: string\n  title: string\n  description: string\n  mediaUrl: string\n  culturalContext?: string\n  achievements: string[]\n}\n\nexport interface LearningStyle {\n  visual: number // 0-100\n  auditory: number // 0-100\n  kinesthetic: number // 0-100\n  reading: number // 0-100\n  culturalPreferences: string[]\n}\n\nexport interface CulturalLearningPreference {\n  culture: string\n  interest: number // 0-100\n  currentKnowledge: number // 0-100\n  learningGoals: string[]\n}\n\nexport interface CulturalApproach {\n  approach: string\n  culturalOrigin: string\n  description: string\n  effectiveness: number\n}\n\nexport interface LanguageCapability {\n  language: string\n  proficiency: 'basic' | 'conversational' | 'fluent' | 'native'\n  canTeachIn: boolean\n  culturalNuances: boolean\n}\n\nexport interface MentorshipPreferences {\n  preferredMenteeLevel: string[]\n  maxActiveMentees: number\n  sessionFrequency: 'weekly' | 'biweekly' | 'monthly' | 'flexible'\n  communicationStyle: 'formal' | 'casual' | 'cultural_traditional'\n  culturalExchangeInterest: number // 0-100\n  groupSessionWillingness: boolean\n}\n\nexport interface AvailabilitySchedule {\n  timeZone: string\n  weeklyHours: WeeklyHours\n  culturalHolidays: string[]\n  preferredSessionTimes: TimeSlot[]\n  blackoutDates: DateRange[]\n}\n\nexport interface ReputationMetrics {\n  overallRating: number // 0-5\n  totalSessions: number\n  completionRate: number // 0-100\n  culturalSensitivityRating: number // 0-5\n  teachingEffectiveness: number // 0-5\n  crossCulturalSuccess: number // 0-5\n  endorsements: Endorsement[]\n}\n\nexport interface MatchingFactor {\n  factor: string\n  weight: number\n  score: number\n  culturalRelevance: boolean\n}\n\nexport interface LearningPlan {\n  objectives: string[]\n  milestones: Milestone[]\n  culturalLearningGoals: string[]\n  estimatedDuration: number // weeks\n  sessionFrequency: string\n  assessmentMethods: string[]\n}\n\nexport interface CulturalExchangePlan {\n  culturalTopics: string[]\n  languagePractice: boolean\n  culturalActivities: string[]\n  traditionalKnowledgeSharing: boolean\n  crossCulturalProjects: string[]\n}\n\nexport interface ProgressTracking {\n  skillProgress: number // 0-100\n  culturalUnderstanding: number // 0-100\n  sessionsCompleted: number\n  milestonesAchieved: number\n  culturalInsightsGained: string[]\n  mutualLearningAchievements: string[]\n}\n\nclass SkillSharingService {\n  // Create or update skill profile\n  async createSkillProfile(profile: Omit<SkillProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    try {\n      const profileId = `skill-profile-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      // Validate cultural teaching approaches\n      const validation = await culturalValidationService.validateCulturalContent({\n        id: profileId,\n        title: `Skill Profile for ${profile.userId}`,\n        content: JSON.stringify(profile.culturalTeachingStyle),\n        type: 'skill_profile',\n        culture: profile.culturalTeachingStyle.primaryCulture,\n        author: {\n          userId: profile.userId,\n          culturalCredibility: 'community_member',\n        },\n        verification: {\n          status: 'pending',\n          reviewedBy: [],\n          reviewNotes: [],\n          approvedAt: null,\n        },\n        engagement: {\n          views: 0,\n          likes: 0,\n          shares: 0,\n          comments: 0,\n          crossCulturalViews: 0,\n        },\n        createdAt: Timestamp.now(),\n        lastModified: Timestamp.now(),\n      })\n\n      const skillProfile: SkillProfile = {\n        ...profile,\n        id: profileId,\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now(),\n      }\n\n      await setDoc(doc(db, 'skill-profiles', profileId), skillProfile)\n\n      return profileId\n    } catch (error) {\n      console.error('Error creating skill profile:', error)\n      throw new Error('Failed to create skill profile')\n    }\n  }\n\n  // Update existing skill profile\n  async updateSkillProfile(profileId: string, updates: Partial<SkillProfile>): Promise<void> {\n    try {\n      const profileRef = doc(db, 'skill-profiles', profileId)\n      \n      await updateDoc(profileRef, {\n        ...updates,\n        updatedAt: Timestamp.now(),\n      })\n    } catch (error) {\n      console.error('Error updating skill profile:', error)\n      throw new Error('Failed to update skill profile')\n    }\n  }\n\n  // Get skill profile by user ID\n  async getSkillProfileByUserId(userId: string): Promise<SkillProfile | null> {\n    try {\n      const q = query(\n        collection(db, 'skill-profiles'),\n        where('userId', '==', userId),\n        limit(1)\n      )\n\n      const snapshot = await getDocs(q)\n      \n      if (snapshot.empty) {\n        return null\n      }\n\n      return snapshot.docs[0].data() as SkillProfile\n    } catch (error) {\n      console.error('Error getting skill profile:', error)\n      throw new Error('Failed to get skill profile')\n    }\n  }\n\n  // Search for mentors by skill and cultural preferences\n  async searchMentors(\n    skillId: string, \n    culturalPreferences?: string[], \n    learningStyle?: LearningStyle,\n    limit_count: number = 20\n  ): Promise<SkillProfile[]> {\n    try {\n      let q = query(\n        collection(db, 'skill-profiles'),\n        where('skillsToTeach', 'array-contains-any', [skillId]),\n        orderBy('reputation.overallRating', 'desc'),\n        limit(limit_count)\n      )\n\n      const snapshot = await getDocs(q)\n      let mentors = snapshot.docs.map(doc => doc.data() as SkillProfile)\n\n      // Filter by cultural preferences if provided\n      if (culturalPreferences && culturalPreferences.length > 0) {\n        mentors = mentors.filter(mentor => \n          culturalPreferences.some(pref => \n            mentor.culturalTeachingStyle.primaryCulture === pref ||\n            mentor.culturalTeachingStyle.culturalApproaches.some(approach => \n              approach.culturalOrigin === pref\n            )\n          )\n        )\n      }\n\n      return mentors\n    } catch (error) {\n      console.error('Error searching mentors:', error)\n      throw new Error('Failed to search mentors')\n    }\n  }\n\n  // Create mentorship match\n  async createMentorshipMatch(match: Omit<MentorshipMatch, 'id' | 'createdAt'>): Promise<string> {\n    try {\n      const matchId = `match-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      const mentorshipMatch: MentorshipMatch = {\n        ...match,\n        id: matchId,\n        createdAt: Timestamp.now(),\n      }\n\n      await setDoc(doc(db, 'mentorship-matches', matchId), mentorshipMatch)\n\n      return matchId\n    } catch (error) {\n      console.error('Error creating mentorship match:', error)\n      throw new Error('Failed to create mentorship match')\n    }\n  }\n\n  // Schedule learning session\n  async scheduleLearningSession(session: Omit<LearningSession, 'id'>): Promise<string> {\n    try {\n      const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      const learningSession: LearningSession = {\n        ...session,\n        id: sessionId,\n      }\n\n      await setDoc(doc(db, 'learning-sessions', sessionId), learningSession)\n\n      return sessionId\n    } catch (error) {\n      console.error('Error scheduling learning session:', error)\n      throw new Error('Failed to schedule learning session')\n    }\n  }\n\n  // Complete learning session and award credits\n  async completeLearningSession(\n    sessionId: string, \n    feedback: SessionFeedback,\n    skillProgress: SkillProgressMetric[],\n    culturalInsights: CulturalInsight[]\n  ): Promise<void> {\n    try {\n      const sessionRef = doc(db, 'learning-sessions', sessionId)\n      \n      // Calculate time credits based on session duration and quality\n      const session = await getDoc(sessionRef)\n      if (!session.exists()) {\n        throw new Error('Session not found')\n      }\n\n      const sessionData = session.data() as LearningSession\n      const baseCredits = (sessionData.duration / 60) * 10 // 10 credits per hour base rate\n      const qualityMultiplier = feedback.overallRating / 5 // 0-1 multiplier based on rating\n      const culturalBonus = culturalInsights.length > 0 ? 1.2 : 1.0 // 20% bonus for cultural exchange\n      \n      const timeCreditsAwarded = Math.round(baseCredits * qualityMultiplier * culturalBonus)\n\n      await updateDoc(sessionRef, {\n        feedback,\n        skillProgress,\n        culturalInsights,\n        timeCreditsAwarded,\n        status: 'completed',\n      })\n\n      // Update mentor's time credits (this would integrate with TimeBankingService)\n      // await this.awardTimeCredits(mentorId, timeCreditsAwarded, sessionId)\n\n    } catch (error) {\n      console.error('Error completing learning session:', error)\n      throw new Error('Failed to complete learning session')\n    }\n  }\n\n  // Get user's mentorship matches\n  async getUserMentorshipMatches(userId: string, role: 'mentor' | 'mentee'): Promise<MentorshipMatch[]> {\n    try {\n      const field = role === 'mentor' ? 'mentorId' : 'menteeId'\n      \n      const q = query(\n        collection(db, 'mentorship-matches'),\n        where(field, '==', userId),\n        orderBy('createdAt', 'desc')\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as MentorshipMatch)\n    } catch (error) {\n      console.error('Error getting user mentorship matches:', error)\n      throw new Error('Failed to get user mentorship matches')\n    }\n  }\n\n  // Get learning sessions for a mentorship match\n  async getLearningSessionsForMatch(matchId: string): Promise<LearningSession[]> {\n    try {\n      const q = query(\n        collection(db, 'learning-sessions'),\n        where('mentorshipMatchId', '==', matchId),\n        orderBy('scheduledAt', 'desc')\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as LearningSession)\n    } catch (error) {\n      console.error('Error getting learning sessions:', error)\n      throw new Error('Failed to get learning sessions')\n    }\n  }\n\n  // Update mentorship match status\n  async updateMentorshipMatchStatus(\n    matchId: string, \n    status: MentorshipMatch['status'],\n    notes?: string\n  ): Promise<void> {\n    try {\n      const matchRef = doc(db, 'mentorship-matches', matchId)\n      \n      await updateDoc(matchRef, {\n        status,\n        ...(notes && { statusNotes: notes }),\n        updatedAt: Timestamp.now(),\n      })\n    } catch (error) {\n      console.error('Error updating mentorship match status:', error)\n      throw new Error('Failed to update mentorship match status')\n    }\n  }\n}\n\nexport const skillSharingService = new SkillSharingService()\n", "import { \n  collection, \n  doc, \n  setDoc, \n  getDoc, \n  getDocs, \n  query, \n  where, \n  orderBy, \n  limit, \n  updateDoc,\n  arrayUnion,\n  Timestamp,\n  writeBatch,\n  runTransaction\n} from 'firebase/firestore'\nimport { db } from './firebase'\n\nexport interface TimeBankAccount {\n  id: string\n  userId: string\n  balance: number\n  totalEarned: number\n  totalSpent: number\n  totalReserved: number\n  qualityMultiplier: number // 0.5-2.0 based on service quality\n  culturalBonusRate: number // additional rate for cross-cultural exchanges\n  reputationScore: number // 0-100\n  accountStatus: 'active' | 'suspended' | 'restricted' | 'closed'\n  createdAt: Timestamp\n  lastActivity: Timestamp\n}\n\nexport interface TimeTransaction {\n  id: string\n  type: 'earn' | 'spend' | 'reserve' | 'release' | 'bonus' | 'penalty' | 'adjustment'\n  fromUserId?: string\n  toUserId: string\n  amount: number\n  baseAmount: number\n  multipliers: TransactionMultiplier[]\n  description: string\n  category: TransactionCategory\n  relatedExchangeId?: string\n  relatedSessionId?: string\n  culturalContext?: CulturalTransactionContext\n  status: 'pending' | 'completed' | 'failed' | 'disputed' | 'reversed'\n  timestamp: Timestamp\n  expiresAt?: Timestamp\n  metadata: TransactionMetadata\n}\n\nexport interface CreditCalculation {\n  baseCredits: number\n  qualityMultiplier: number\n  culturalBonus: number\n  skillComplexityBonus: number\n  demandMultiplier: number\n  communityContributionBonus: number\n  finalCredits: number\n  breakdown: CalculationBreakdown[]\n}\n\nexport interface DisputeCase {\n  id: string\n  transactionId: string\n  disputantUserId: string\n  respondentUserId: string\n  disputeType: 'quality' | 'cultural_insensitivity' | 'non_completion' | 'overcharge' | 'other'\n  description: string\n  evidence: DisputeEvidence[]\n  culturalContext?: CulturalDisputeContext\n  status: 'open' | 'investigating' | 'mediation' | 'resolved' | 'escalated' | 'closed'\n  resolution?: DisputeResolution\n  mediatorId?: string\n  culturalMediatorId?: string\n  createdAt: Timestamp\n  resolvedAt?: Timestamp\n}\n\nexport interface CulturalValueAssessment {\n  skillId: string\n  culturalOrigin: string\n  traditionalValue: number // base cultural significance score\n  rarityMultiplier: number // how rare this knowledge is\n  preservationImportance: number // importance for cultural preservation\n  crossCulturalAppeal: number // appeal to other cultures\n  modernRelevance: number // relevance in modern context\n  communityEndorsement: number // community validation score\n  finalCulturalValue: number\n  assessmentDate: Timestamp\n  assessedBy: string[] // cultural representatives\n}\n\nexport interface FairExchangeMetrics {\n  userId: string\n  exchangeEquity: number // how fair their exchanges have been\n  culturalContribution: number // contribution to cultural preservation\n  communityImpact: number // positive impact on community\n  crossCulturalBridging: number // success in cross-cultural exchanges\n  knowledgeSharing: number // willingness to share knowledge\n  learningEngagement: number // engagement as a learner\n  overallFairnessScore: number\n  lastCalculated: Timestamp\n}\n\nexport interface TransactionMultiplier {\n  type: 'quality' | 'cultural' | 'complexity' | 'demand' | 'community' | 'rarity'\n  value: number\n  reason: string\n  appliedBy: string\n}\n\nexport interface TransactionCategory {\n  primary: 'skill_teaching' | 'cultural_sharing' | 'mentorship' | 'consultation' | 'workshop' | 'other'\n  secondary?: string\n  culturalCategory?: string\n}\n\nexport interface CulturalTransactionContext {\n  primaryCulture: string\n  crossCulturalExchange: boolean\n  traditionalKnowledgeInvolved: boolean\n  culturalSensitivityLevel: 'low' | 'medium' | 'high'\n  communityBenefit: boolean\n}\n\nexport interface TransactionMetadata {\n  sessionDuration?: number\n  participantCount?: number\n  skillLevel: string\n  culturalElements: string[]\n  qualityRating?: number\n  culturalRespectRating?: number\n  learningOutcomes: string[]\n}\n\nexport interface CalculationBreakdown {\n  component: string\n  value: number\n  explanation: string\n}\n\nexport interface DisputeEvidence {\n  type: 'text' | 'image' | 'video' | 'audio' | 'document'\n  content: string\n  description: string\n  timestamp: Timestamp\n}\n\nexport interface CulturalDisputeContext {\n  culturalIssues: string[]\n  culturalMisunderstanding: boolean\n  traditionalKnowledgeConcerns: boolean\n  communityImpact: string\n  culturalMediationNeeded: boolean\n}\n\nexport interface DisputeResolution {\n  outcome: 'refund_full' | 'refund_partial' | 'no_refund' | 'additional_compensation' | 'mediated_agreement'\n  creditAdjustment: number\n  explanation: string\n  culturalGuidance?: string\n  preventionMeasures: string[]\n  satisfactionRating: number\n}\n\nclass TimeBankingService {\n  // Create time bank account\n  async createTimeBankAccount(userId: string): Promise<string> {\n    try {\n      const accountId = `timebank-${userId}`\n      \n      const account: TimeBankAccount = {\n        id: accountId,\n        userId,\n        balance: 100, // Starting bonus credits\n        totalEarned: 100,\n        totalSpent: 0,\n        totalReserved: 0,\n        qualityMultiplier: 1.0,\n        culturalBonusRate: 0.1, // 10% bonus for cultural exchanges\n        reputationScore: 50, // Starting neutral reputation\n        accountStatus: 'active',\n        createdAt: Timestamp.now(),\n        lastActivity: Timestamp.now(),\n      }\n\n      await setDoc(doc(db, 'timebank-accounts', accountId), account)\n\n      // Create welcome transaction\n      await this.recordTransaction({\n        type: 'bonus',\n        toUserId: userId,\n        amount: 100,\n        baseAmount: 100,\n        multipliers: [],\n        description: 'Welcome bonus for joining Ubuntu Connect',\n        category: {\n          primary: 'other',\n          secondary: 'welcome_bonus',\n        },\n        status: 'completed',\n        timestamp: Timestamp.now(),\n        metadata: {\n          skillLevel: 'beginner',\n          culturalElements: ['ubuntu_philosophy'],\n          learningOutcomes: ['Platform onboarding'],\n        },\n      })\n\n      return accountId\n    } catch (error) {\n      console.error('Error creating time bank account:', error)\n      throw new Error('Failed to create time bank account')\n    }\n  }\n\n  // Calculate credits for a service/exchange\n  async calculateCredits(\n    serviceDetails: {\n      skillId: string\n      duration: number // minutes\n      skillLevel: string\n      culturalContext?: CulturalTransactionContext\n      qualityRating?: number\n      participantCount?: number\n    },\n    providerId: string\n  ): Promise<CreditCalculation> {\n    try {\n      // Base calculation: 10 credits per hour\n      const baseCredits = (serviceDetails.duration / 60) * 10\n\n      // Get provider's quality multiplier\n      const providerAccount = await this.getTimeBankAccount(providerId)\n      const qualityMultiplier = providerAccount?.qualityMultiplier || 1.0\n\n      // Cultural bonus calculation\n      let culturalBonus = 0\n      if (serviceDetails.culturalContext?.crossCulturalExchange) {\n        culturalBonus = baseCredits * (providerAccount?.culturalBonusRate || 0.1)\n      }\n\n      // Skill complexity bonus\n      const complexityMultipliers = {\n        'beginner': 1.0,\n        'intermediate': 1.2,\n        'advanced': 1.5,\n        'expert': 2.0,\n        'master': 2.5,\n      }\n      const skillComplexityBonus = baseCredits * (complexityMultipliers[serviceDetails.skillLevel as keyof typeof complexityMultipliers] - 1)\n\n      // Demand multiplier (simplified - could be based on marketplace data)\n      const demandMultiplier = 1.0 // TODO: Implement demand-based pricing\n\n      // Community contribution bonus\n      let communityContributionBonus = 0\n      if (serviceDetails.culturalContext?.communityBenefit) {\n        communityContributionBonus = baseCredits * 0.15 // 15% bonus\n      }\n\n      // Group session bonus\n      let groupBonus = 0\n      if (serviceDetails.participantCount && serviceDetails.participantCount > 1) {\n        groupBonus = baseCredits * 0.1 * (serviceDetails.participantCount - 1) // 10% per additional participant\n      }\n\n      const finalCredits = Math.round(\n        baseCredits * qualityMultiplier * demandMultiplier + \n        culturalBonus + \n        skillComplexityBonus + \n        communityContributionBonus + \n        groupBonus\n      )\n\n      const breakdown: CalculationBreakdown[] = [\n        { component: 'Base Credits', value: baseCredits, explanation: `${serviceDetails.duration} minutes at 10 credits/hour` },\n        { component: 'Quality Multiplier', value: qualityMultiplier, explanation: `Based on provider reputation (${providerAccount?.reputationScore || 50}/100)` },\n        { component: 'Cultural Bonus', value: culturalBonus, explanation: 'Cross-cultural exchange bonus' },\n        { component: 'Skill Complexity', value: skillComplexityBonus, explanation: `${serviceDetails.skillLevel} level bonus` },\n        { component: 'Community Contribution', value: communityContributionBonus, explanation: 'Community benefit bonus' },\n        { component: 'Group Session', value: groupBonus, explanation: `${serviceDetails.participantCount || 1} participants` },\n      ]\n\n      return {\n        baseCredits,\n        qualityMultiplier,\n        culturalBonus,\n        skillComplexityBonus,\n        demandMultiplier,\n        communityContributionBonus,\n        finalCredits,\n        breakdown,\n      }\n    } catch (error) {\n      console.error('Error calculating credits:', error)\n      throw new Error('Failed to calculate credits')\n    }\n  }\n\n  // Record a transaction\n  async recordTransaction(transaction: Omit<TimeTransaction, 'id'>): Promise<string> {\n    try {\n      const transactionId = `tx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      const timeTransaction: TimeTransaction = {\n        ...transaction,\n        id: transactionId,\n      }\n\n      // Use transaction to ensure consistency\n      await runTransaction(db, async (firestoreTransaction) => {\n        // Record the transaction\n        const transactionRef = doc(db, 'time-transactions', transactionId)\n        firestoreTransaction.set(transactionRef, timeTransaction)\n\n        // Update account balances\n        if (transaction.type === 'earn' || transaction.type === 'bonus') {\n          await this.updateAccountBalance(transaction.toUserId, transaction.amount, 'add', firestoreTransaction)\n        } else if (transaction.type === 'spend') {\n          await this.updateAccountBalance(transaction.toUserId, -transaction.amount, 'subtract', firestoreTransaction)\n          if (transaction.fromUserId) {\n            await this.updateAccountBalance(transaction.fromUserId, transaction.amount, 'add', firestoreTransaction)\n          }\n        } else if (transaction.type === 'reserve') {\n          await this.updateAccountReserved(transaction.toUserId, transaction.amount, 'add', firestoreTransaction)\n        } else if (transaction.type === 'release') {\n          await this.updateAccountReserved(transaction.toUserId, -transaction.amount, 'subtract', firestoreTransaction)\n        }\n      })\n\n      return transactionId\n    } catch (error) {\n      console.error('Error recording transaction:', error)\n      throw new Error('Failed to record transaction')\n    }\n  }\n\n  // Get time bank account\n  async getTimeBankAccount(userId: string): Promise<TimeBankAccount | null> {\n    try {\n      const accountRef = doc(db, 'timebank-accounts', `timebank-${userId}`)\n      const accountDoc = await getDoc(accountRef)\n      \n      if (accountDoc.exists()) {\n        return accountDoc.data() as TimeBankAccount\n      }\n      \n      return null\n    } catch (error) {\n      console.error('Error getting time bank account:', error)\n      throw new Error('Failed to get time bank account')\n    }\n  }\n\n  // Get user's transaction history\n  async getUserTransactionHistory(userId: string, limit_count: number = 50): Promise<TimeTransaction[]> {\n    try {\n      const q = query(\n        collection(db, 'time-transactions'),\n        where('toUserId', '==', userId),\n        orderBy('timestamp', 'desc'),\n        limit(limit_count)\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as TimeTransaction)\n    } catch (error) {\n      console.error('Error getting user transaction history:', error)\n      throw new Error('Failed to get user transaction history')\n    }\n  }\n\n  // Process payment for knowledge exchange\n  async processExchangePayment(\n    exchangeId: string,\n    payerId: string,\n    payeeId: string,\n    amount: number,\n    serviceDetails: any\n  ): Promise<string> {\n    try {\n      // Check if payer has sufficient balance\n      const payerAccount = await this.getTimeBankAccount(payerId)\n      if (!payerAccount || payerAccount.balance < amount) {\n        throw new Error('Insufficient credits')\n      }\n\n      // Calculate final credits with bonuses\n      const creditCalculation = await this.calculateCredits(serviceDetails, payeeId)\n\n      // Record the payment transaction\n      const transactionId = await this.recordTransaction({\n        type: 'spend',\n        fromUserId: payeeId,\n        toUserId: payerId,\n        amount: creditCalculation.finalCredits,\n        baseAmount: creditCalculation.baseCredits,\n        multipliers: [\n          { type: 'quality', value: creditCalculation.qualityMultiplier, reason: 'Provider quality rating', appliedBy: 'system' },\n          { type: 'cultural', value: creditCalculation.culturalBonus, reason: 'Cross-cultural exchange', appliedBy: 'system' },\n          { type: 'complexity', value: creditCalculation.skillComplexityBonus, reason: 'Skill complexity', appliedBy: 'system' },\n        ],\n        description: `Payment for knowledge exchange: ${exchangeId}`,\n        category: {\n          primary: 'skill_teaching',\n          culturalCategory: serviceDetails.culturalContext?.primaryCulture,\n        },\n        relatedExchangeId: exchangeId,\n        culturalContext: serviceDetails.culturalContext,\n        status: 'completed',\n        timestamp: Timestamp.now(),\n        metadata: {\n          sessionDuration: serviceDetails.duration,\n          participantCount: serviceDetails.participantCount || 1,\n          skillLevel: serviceDetails.skillLevel,\n          culturalElements: serviceDetails.culturalContext?.culturalElements || [],\n          learningOutcomes: serviceDetails.learningOutcomes || [],\n        },\n      })\n\n      return transactionId\n    } catch (error) {\n      console.error('Error processing exchange payment:', error)\n      throw new Error('Failed to process exchange payment')\n    }\n  }\n\n  // Create dispute case\n  async createDisputeCase(dispute: Omit<DisputeCase, 'id' | 'createdAt'>): Promise<string> {\n    try {\n      const disputeId = `dispute-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      const disputeCase: DisputeCase = {\n        ...dispute,\n        id: disputeId,\n        createdAt: Timestamp.now(),\n      }\n\n      await setDoc(doc(db, 'dispute-cases', disputeId), disputeCase)\n\n      // Freeze related transaction if applicable\n      if (dispute.transactionId) {\n        await this.freezeTransaction(dispute.transactionId)\n      }\n\n      return disputeId\n    } catch (error) {\n      console.error('Error creating dispute case:', error)\n      throw new Error('Failed to create dispute case')\n    }\n  }\n\n  // Resolve dispute case\n  async resolveDisputeCase(\n    disputeId: string,\n    resolution: DisputeResolution,\n    mediatorId: string\n  ): Promise<void> {\n    try {\n      const disputeRef = doc(db, 'dispute-cases', disputeId)\n      \n      await updateDoc(disputeRef, {\n        status: 'resolved',\n        resolution,\n        mediatorId,\n        resolvedAt: Timestamp.now(),\n      })\n\n      // Apply credit adjustments if needed\n      if (resolution.creditAdjustment !== 0) {\n        const disputeDoc = await getDoc(disputeRef)\n        if (disputeDoc.exists()) {\n          const dispute = disputeDoc.data() as DisputeCase\n          \n          await this.recordTransaction({\n            type: resolution.creditAdjustment > 0 ? 'bonus' : 'penalty',\n            toUserId: dispute.disputantUserId,\n            amount: Math.abs(resolution.creditAdjustment),\n            baseAmount: Math.abs(resolution.creditAdjustment),\n            multipliers: [],\n            description: `Dispute resolution adjustment: ${resolution.explanation}`,\n            category: {\n              primary: 'other',\n              secondary: 'dispute_resolution',\n            },\n            relatedExchangeId: dispute.transactionId,\n            status: 'completed',\n            timestamp: Timestamp.now(),\n            metadata: {\n              skillLevel: 'n/a',\n              culturalElements: [],\n              learningOutcomes: ['Dispute resolved'],\n            },\n          })\n        }\n      }\n    } catch (error) {\n      console.error('Error resolving dispute case:', error)\n      throw new Error('Failed to resolve dispute case')\n    }\n  }\n\n  // Calculate fair exchange metrics\n  async calculateFairExchangeMetrics(userId: string): Promise<FairExchangeMetrics> {\n    try {\n      // Get user's transaction history\n      const transactions = await this.getUserTransactionHistory(userId, 100)\n      \n      // Calculate various fairness metrics\n      const exchangeEquity = this.calculateExchangeEquity(transactions)\n      const culturalContribution = this.calculateCulturalContribution(transactions)\n      const communityImpact = this.calculateCommunityImpact(transactions)\n      const crossCulturalBridging = this.calculateCrossCulturalBridging(transactions)\n      const knowledgeSharing = this.calculateKnowledgeSharing(transactions)\n      const learningEngagement = this.calculateLearningEngagement(transactions)\n\n      const overallFairnessScore = Math.round(\n        (exchangeEquity + culturalContribution + communityImpact + \n         crossCulturalBridging + knowledgeSharing + learningEngagement) / 6\n      )\n\n      const metrics: FairExchangeMetrics = {\n        userId,\n        exchangeEquity,\n        culturalContribution,\n        communityImpact,\n        crossCulturalBridging,\n        knowledgeSharing,\n        learningEngagement,\n        overallFairnessScore,\n        lastCalculated: Timestamp.now(),\n      }\n\n      // Save metrics\n      await setDoc(doc(db, 'fair-exchange-metrics', userId), metrics)\n\n      return metrics\n    } catch (error) {\n      console.error('Error calculating fair exchange metrics:', error)\n      throw new Error('Failed to calculate fair exchange metrics')\n    }\n  }\n\n  // Private helper methods\n  private async updateAccountBalance(\n    userId: string, \n    amount: number, \n    operation: 'add' | 'subtract',\n    firestoreTransaction?: any\n  ): Promise<void> {\n    const accountRef = doc(db, 'timebank-accounts', `timebank-${userId}`)\n    \n    if (firestoreTransaction) {\n      const accountDoc = await firestoreTransaction.get(accountRef)\n      if (accountDoc.exists()) {\n        const currentBalance = accountDoc.data().balance\n        const newBalance = operation === 'add' ? currentBalance + amount : currentBalance - amount\n        \n        firestoreTransaction.update(accountRef, {\n          balance: Math.max(0, newBalance),\n          lastActivity: Timestamp.now(),\n          ...(operation === 'add' && { totalEarned: accountDoc.data().totalEarned + amount }),\n          ...(operation === 'subtract' && { totalSpent: accountDoc.data().totalSpent + amount }),\n        })\n      }\n    } else {\n      const accountDoc = await getDoc(accountRef)\n      if (accountDoc.exists()) {\n        const currentBalance = accountDoc.data().balance\n        const newBalance = operation === 'add' ? currentBalance + amount : currentBalance - amount\n        \n        await updateDoc(accountRef, {\n          balance: Math.max(0, newBalance),\n          lastActivity: Timestamp.now(),\n          ...(operation === 'add' && { totalEarned: accountDoc.data().totalEarned + amount }),\n          ...(operation === 'subtract' && { totalSpent: accountDoc.data().totalSpent + amount }),\n        })\n      }\n    }\n  }\n\n  private async updateAccountReserved(\n    userId: string, \n    amount: number, \n    operation: 'add' | 'subtract',\n    firestoreTransaction?: any\n  ): Promise<void> {\n    const accountRef = doc(db, 'timebank-accounts', `timebank-${userId}`)\n    \n    if (firestoreTransaction) {\n      const accountDoc = await firestoreTransaction.get(accountRef)\n      if (accountDoc.exists()) {\n        const currentReserved = accountDoc.data().totalReserved\n        const newReserved = operation === 'add' ? currentReserved + amount : currentReserved - amount\n        \n        firestoreTransaction.update(accountRef, {\n          totalReserved: Math.max(0, newReserved),\n          lastActivity: Timestamp.now(),\n        })\n      }\n    }\n  }\n\n  private async freezeTransaction(transactionId: string): Promise<void> {\n    const transactionRef = doc(db, 'time-transactions', transactionId)\n    await updateDoc(transactionRef, {\n      status: 'disputed',\n    })\n  }\n\n  private calculateExchangeEquity(transactions: TimeTransaction[]): number {\n    // Calculate how fair the user's exchanges have been\n    // This is a simplified calculation\n    const earnTransactions = transactions.filter(tx => tx.type === 'earn')\n    const spendTransactions = transactions.filter(tx => tx.type === 'spend')\n    \n    if (earnTransactions.length === 0 && spendTransactions.length === 0) return 50\n    \n    const earnTotal = earnTransactions.reduce((sum, tx) => sum + tx.amount, 0)\n    const spendTotal = spendTransactions.reduce((sum, tx) => sum + tx.amount, 0)\n    \n    // Balanced earning and spending indicates good exchange equity\n    const ratio = earnTotal > 0 ? spendTotal / earnTotal : 0\n    return Math.min(100, Math.max(0, 100 - Math.abs(ratio - 1) * 100))\n  }\n\n  private calculateCulturalContribution(transactions: TimeTransaction[]): number {\n    const culturalTransactions = transactions.filter(tx => \n      tx.culturalContext?.crossCulturalExchange || \n      tx.culturalContext?.traditionalKnowledgeInvolved\n    )\n    \n    return Math.min(100, (culturalTransactions.length / Math.max(transactions.length, 1)) * 100)\n  }\n\n  private calculateCommunityImpact(transactions: TimeTransaction[]): number {\n    const communityTransactions = transactions.filter(tx => \n      tx.culturalContext?.communityBenefit ||\n      tx.category.secondary === 'community_service'\n    )\n    \n    return Math.min(100, (communityTransactions.length / Math.max(transactions.length, 1)) * 100)\n  }\n\n  private calculateCrossCulturalBridging(transactions: TimeTransaction[]): number {\n    const crossCulturalTransactions = transactions.filter(tx => \n      tx.culturalContext?.crossCulturalExchange\n    )\n    \n    return Math.min(100, (crossCulturalTransactions.length / Math.max(transactions.length, 1)) * 100)\n  }\n\n  private calculateKnowledgeSharing(transactions: TimeTransaction[]): number {\n    const teachingTransactions = transactions.filter(tx => \n      tx.type === 'earn' && tx.category.primary === 'skill_teaching'\n    )\n    \n    return Math.min(100, (teachingTransactions.length / Math.max(transactions.length, 1)) * 100)\n  }\n\n  private calculateLearningEngagement(transactions: TimeTransaction[]): number {\n    const learningTransactions = transactions.filter(tx => \n      tx.type === 'spend' && tx.category.primary === 'skill_teaching'\n    )\n    \n    return Math.min(100, (learningTransactions.length / Math.max(transactions.length, 1)) * 100)\n  }\n}\n\nexport const timeBankingService = new TimeBankingService()\n", "import React, { useState, useEffect } from 'react'\nimport { skillSharingService, SkillProfile, MentorshipMatch, LearningSession } from '../../../services/skillSharingService'\nimport { mentorshipMatchingEngine } from '../../../services/mentorshipMatchingEngine'\nimport { timeBankingService, TimeBankAccount } from '../../../services/timeBankingService'\nimport { useAuth } from '../../auth/hooks/useAuth'\n\ninterface SkillSharingDashboardProps {\n  onNavigateToMarketplace?: () => void\n  onNavigateToTimeBank?: () => void\n}\n\nexport const SkillSharingDashboard: React.FC<SkillSharingDashboardProps> = ({\n  onNavigateToMarketplace,\n  onNavigateToTimeBank,\n}) => {\n  const { user } = useAuth()\n  const [activeTab, setActiveTab] = useState<'overview' | 'mentoring' | 'learning' | 'sessions'>('overview')\n  const [skillProfile, setSkillProfile] = useState<SkillProfile | null>(null)\n  const [mentorshipMatches, setMentorshipMatches] = useState<MentorshipMatch[]>([])\n  const [learningSessions, setLearningSessions] = useState<LearningSession[]>([])\n  const [timeBankAccount, setTimeBankAccount] = useState<TimeBankAccount | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    if (user) {\n      loadDashboardData()\n    }\n  }, [user])\n\n  const loadDashboardData = async () => {\n    if (!user) return\n\n    setLoading(true)\n    try {\n      const [profile, mentorMatches, menteeMatches, timeAccount] = await Promise.all([\n        skillSharingService.getSkillProfileByUserId(user.uid),\n        skillSharingService.getUserMentorshipMatches(user.uid, 'mentor'),\n        skillSharingService.getUserMentorshipMatches(user.uid, 'mentee'),\n        timeBankingService.getTimeBankAccount(user.uid),\n      ])\n\n      setSkillProfile(profile)\n      setMentorshipMatches([...mentorMatches, ...menteeMatches])\n      setTimeBankAccount(timeAccount)\n\n      // Load recent sessions\n      if (mentorMatches.length > 0 || menteeMatches.length > 0) {\n        const allMatches = [...mentorMatches, ...menteeMatches]\n        const sessionPromises = allMatches.slice(0, 3).map(match =>\n          skillSharingService.getLearningSessionsForMatch(match.id)\n        )\n        const sessionResults = await Promise.all(sessionPromises)\n        const allSessions = sessionResults.flat().sort((a, b) => \n          b.scheduledAt.seconds - a.scheduledAt.seconds\n        )\n        setLearningSessions(allSessions.slice(0, 10))\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const renderOverview = () => (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">🎓</div>\n            <div>\n              <div className=\"text-2xl font-bold text-blue-600\">\n                {skillProfile?.skillsToTeach.length || 0}\n              </div>\n              <div className=\"text-sm text-gray-600\">Skills Teaching</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">📚</div>\n            <div>\n              <div className=\"text-2xl font-bold text-green-600\">\n                {skillProfile?.skillsToLearn.length || 0}\n              </div>\n              <div className=\"text-sm text-gray-600\">Skills Learning</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">🤝</div>\n            <div>\n              <div className=\"text-2xl font-bold text-purple-600\">\n                {mentorshipMatches.filter(m => m.status === 'active').length}\n              </div>\n              <div className=\"text-sm text-gray-600\">Active Mentorships</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">⏰</div>\n            <div>\n              <div className=\"text-2xl font-bold text-orange-600\">\n                {timeBankAccount?.balance || 0}\n              </div>\n              <div className=\"text-sm text-gray-600\">Time Credits</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white\">\n          <h3 className=\"text-lg font-semibold mb-2\">Find a Mentor</h3>\n          <p className=\"text-blue-100 mb-4\">\n            Connect with experienced mentors to accelerate your learning journey.\n          </p>\n          <button className=\"bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors\">\n            Browse Mentors\n          </button>\n        </div>\n\n        <div className=\"bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white\">\n          <h3 className=\"text-lg font-semibold mb-2\">Become a Mentor</h3>\n          <p className=\"text-green-100 mb-4\">\n            Share your expertise and help others while earning time credits.\n          </p>\n          <button className=\"bg-white text-green-600 px-4 py-2 rounded-lg hover:bg-green-50 transition-colors\">\n            Start Mentoring\n          </button>\n        </div>\n\n        <div className=\"bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white\">\n          <h3 className=\"text-lg font-semibold mb-2\">Explore Marketplace</h3>\n          <p className=\"text-purple-100 mb-4\">\n            Discover knowledge exchange opportunities in the marketplace.\n          </p>\n          <button \n            onClick={onNavigateToMarketplace}\n            className=\"bg-white text-purple-600 px-4 py-2 rounded-lg hover:bg-purple-50 transition-colors\"\n          >\n            Visit Marketplace\n          </button>\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Learning Sessions</h3>\n        <div className=\"space-y-4\">\n          {learningSessions.slice(0, 5).map(session => (\n            <div key={session.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-gray-900\">\n                  {session.sessionType.charAt(0).toUpperCase() + session.sessionType.slice(1)} Session\n                </h4>\n                <p className=\"text-sm text-gray-600\">\n                  {session.duration} minutes • {session.location}\n                </p>\n                <div className=\"flex items-center mt-1\">\n                  {session.culturalElements.map(element => (\n                    <span key={element.culture} className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded mr-2\">\n                      {element.culture}\n                    </span>\n                  ))}\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className={`text-sm font-medium ${\n                  session.status === 'completed' ? 'text-green-600' :\n                  session.status === 'scheduled' ? 'text-blue-600' :\n                  session.status === 'in_progress' ? 'text-yellow-600' :\n                  'text-gray-600'\n                }`}>\n                  {session.status.replace('_', ' ').toUpperCase()}\n                </div>\n                <div className=\"text-xs text-gray-500\">\n                  {new Date(session.scheduledAt.seconds * 1000).toLocaleDateString()}\n                </div>\n              </div>\n            </div>\n          ))}\n          \n          {learningSessions.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">📅</div>\n              <p>No learning sessions yet</p>\n              <p className=\"text-sm\">Start a mentorship to schedule your first session!</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderMentoring = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Your Mentoring Activities</h3>\n        <button className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\">\n          Update Teaching Profile\n        </button>\n      </div>\n\n      {/* Teaching Skills */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h4 className=\"font-semibold text-gray-900 mb-4\">Skills You Teach</h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {skillProfile?.skillsToTeach.map(skill => (\n            <div key={skill.skillId} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h5 className=\"font-medium text-gray-900\">{skill.skillName}</h5>\n                <span className={`text-xs px-2 py-1 rounded-full ${\n                  skill.proficiencyLevel === 'expert' || skill.proficiencyLevel === 'master' \n                    ? 'bg-green-100 text-green-800'\n                    : 'bg-blue-100 text-blue-800'\n                }`}>\n                  {skill.proficiencyLevel}\n                </span>\n              </div>\n              <p className=\"text-sm text-gray-600 mb-2\">\n                {skill.yearsOfExperience} years experience\n              </p>\n              {skill.culturalContext && skill.culturalContext.length > 0 && (\n                <div className=\"flex flex-wrap gap-1\">\n                  {skill.culturalContext.map(context => (\n                    <span key={context.culture} className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n                      {context.culture}\n                    </span>\n                  ))}\n                </div>\n              )}\n              <div className=\"mt-3 flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">\n                  {skill.timeCreditsPerHour} credits/hour\n                </span>\n                <button className=\"text-sm text-blue-600 hover:text-blue-800\">\n                  Edit\n                </button>\n              </div>\n            </div>\n          )) || (\n            <div className=\"col-span-2 text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">🎓</div>\n              <p>No teaching skills added yet</p>\n              <button className=\"mt-2 text-blue-600 hover:text-blue-800\">\n                Add your first teaching skill\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Active Mentees */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h4 className=\"font-semibold text-gray-900 mb-4\">Your Mentees</h4>\n        <div className=\"space-y-4\">\n          {mentorshipMatches\n            .filter(match => match.mentorId === user?.uid && match.status === 'active')\n            .map(match => (\n              <div key={match.id} className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h5 className=\"font-medium text-gray-900\">\n                      {match.skillId} Mentorship\n                    </h5>\n                    <p className=\"text-sm text-gray-600\">\n                      Progress: {match.actualProgress.skillProgress}% • \n                      {match.actualProgress.sessionsCompleted} sessions completed\n                    </p>\n                    {match.culturalContext && (\n                      <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded mt-1 inline-block\">\n                        {match.culturalContext} Cultural Context\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"flex gap-2\">\n                    <button className=\"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\">\n                      Schedule Session\n                    </button>\n                    <button className=\"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50\">\n                      View Progress\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderLearning = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Your Learning Journey</h3>\n        <button className=\"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\">\n          Add Learning Goal\n        </button>\n      </div>\n\n      {/* Learning Goals */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h4 className=\"font-semibold text-gray-900 mb-4\">Learning Goals</h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {skillProfile?.skillsToLearn.map(goal => (\n            <div key={goal.skillId} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h5 className=\"font-medium text-gray-900\">{goal.skillName}</h5>\n                <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                  {goal.currentLevel} → {goal.targetLevel}\n                </span>\n              </div>\n              <p className=\"text-sm text-gray-600 mb-2\">{goal.motivation}</p>\n              <div className=\"text-sm text-gray-600 mb-3\">\n                Time commitment: {goal.timeCommitment} hours/week\n              </div>\n              {goal.culturalPreferences && goal.culturalPreferences.length > 0 && (\n                <div className=\"flex flex-wrap gap-1 mb-3\">\n                  {goal.culturalPreferences.map(pref => (\n                    <span key={pref.culture} className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n                      {pref.culture} ({pref.interest}% interest)\n                    </span>\n                  ))}\n                </div>\n              )}\n              <button className=\"w-full px-3 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600\">\n                Find Mentor\n              </button>\n            </div>\n          )) || (\n            <div className=\"col-span-2 text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">📚</div>\n              <p>No learning goals set yet</p>\n              <button className=\"mt-2 text-green-600 hover:text-green-800\">\n                Set your first learning goal\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Active Mentorships */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h4 className=\"font-semibold text-gray-900 mb-4\">Your Mentors</h4>\n        <div className=\"space-y-4\">\n          {mentorshipMatches\n            .filter(match => match.menteeId === user?.uid && match.status === 'active')\n            .map(match => (\n              <div key={match.id} className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h5 className=\"font-medium text-gray-900\">\n                      {match.skillId} Learning\n                    </h5>\n                    <p className=\"text-sm text-gray-600\">\n                      Progress: {match.actualProgress.skillProgress}% • \n                      {match.actualProgress.sessionsCompleted} sessions completed\n                    </p>\n                    <div className=\"mt-1\">\n                      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                        <div \n                          className=\"bg-green-500 h-2 rounded-full\" \n                          style={{ width: `${match.actualProgress.skillProgress}%` }}\n                        />\n                      </div>\n                    </div>\n                    {match.culturalContext && (\n                      <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded mt-2 inline-block\">\n                        {match.culturalContext} Cultural Learning\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"flex gap-2\">\n                    <button className=\"px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600\">\n                      Continue Learning\n                    </button>\n                    <button className=\"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50\">\n                      View Details\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderSessions = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Learning Sessions</h3>\n        <button className=\"px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors\">\n          Schedule New Session\n        </button>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"space-y-4\">\n          {learningSessions.map(session => (\n            <div key={session.id} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">\n                    {session.sessionType.charAt(0).toUpperCase() + session.sessionType.slice(1)} Session\n                  </h4>\n                  <p className=\"text-sm text-gray-600\">\n                    {new Date(session.scheduledAt.seconds * 1000).toLocaleString()}\n                  </p>\n                </div>\n                <span className={`px-3 py-1 rounded-full text-sm ${\n                  session.status === 'completed' ? 'bg-green-100 text-green-800' :\n                  session.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :\n                  session.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :\n                  'bg-gray-100 text-gray-800'\n                }`}>\n                  {session.status.replace('_', ' ')}\n                </span>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-3\">\n                <div>\n                  <span className=\"text-sm font-medium text-gray-700\">Duration:</span>\n                  <span className=\"text-sm text-gray-600 ml-1\">{session.duration} minutes</span>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-gray-700\">Location:</span>\n                  <span className=\"text-sm text-gray-600 ml-1\">{session.location}</span>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-gray-700\">Credits:</span>\n                  <span className=\"text-sm text-gray-600 ml-1\">{session.timeCreditsAwarded || 0}</span>\n                </div>\n              </div>\n\n              {session.learningObjectives.length > 0 && (\n                <div className=\"mb-3\">\n                  <span className=\"text-sm font-medium text-gray-700\">Objectives:</span>\n                  <ul className=\"text-sm text-gray-600 ml-4 mt-1\">\n                    {session.learningObjectives.map((objective, index) => (\n                      <li key={index} className=\"list-disc\">{objective}</li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n\n              {session.culturalElements.length > 0 && (\n                <div className=\"flex flex-wrap gap-1 mb-3\">\n                  {session.culturalElements.map(element => (\n                    <span key={element.culture} className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n                      {element.culture} Culture\n                    </span>\n                  ))}\n                </div>\n              )}\n\n              {session.status === 'completed' && session.feedback && (\n                <div className=\"bg-gray-50 rounded-lg p-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium text-gray-700\">Session Rating:</span>\n                    <div className=\"flex items-center\">\n                      {[1, 2, 3, 4, 5].map(star => (\n                        <span key={star} className={`text-lg ${\n                          star <= session.feedback.overallRating ? 'text-yellow-400' : 'text-gray-300'\n                        }`}>\n                          ⭐\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          ))}\n\n          {learningSessions.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">📅</div>\n              <p>No sessions scheduled yet</p>\n              <p className=\"text-sm\">Start a mentorship to schedule your first session!</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Skill Sharing & Mentorship</h1>\n        <p className=\"text-gray-600\">\n          Connect, learn, and share knowledge across cultures with Ubuntu philosophy: \"I am because we are.\"\n        </p>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"flex space-x-8\">\n          {[\n            { id: 'overview', label: 'Overview', icon: '📊' },\n            { id: 'mentoring', label: 'Mentoring', icon: '🎓' },\n            { id: 'learning', label: 'Learning', icon: '📚' },\n            { id: 'sessions', label: 'Sessions', icon: '📅' },\n          ].map(tab => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'overview' && renderOverview()}\n      {activeTab === 'mentoring' && renderMentoring()}\n      {activeTab === 'learning' && renderLearning()}\n      {activeTab === 'sessions' && renderSessions()}\n    </div>\n  )\n}\n\nexport default SkillSharingDashboard\n", "import { \n  collection, \n  doc, \n  setDoc, \n  getDoc, \n  getDocs, \n  query, \n  where, \n  orderBy, \n  limit, \n  updateDoc,\n  arrayUnion,\n  Timestamp,\n  writeBatch\n} from 'firebase/firestore'\nimport { db } from './firebase'\nimport { culturalValidationService } from './culturalValidationService'\n\nexport interface MarketplaceListing {\n  id: string\n  listingType: 'skill_request' | 'skill_offering'\n  userId: string\n  skillId: string\n  title: string\n  description: string\n  culturalContext: CulturalContext[]\n  requirements: ListingRequirements\n  offering: ListingOffering\n  pricing: PricingStructure\n  availability: AvailabilityWindow[]\n  status: 'active' | 'paused' | 'fulfilled' | 'expired' | 'cancelled'\n  views: number\n  responses: number\n  createdAt: Timestamp\n  updatedAt: Timestamp\n  expiresAt: Timestamp\n  featuredUntil?: Timestamp\n}\n\nexport interface ListingRequirements {\n  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert'\n  learningFormat: LearningFormat[]\n  culturalPreferences: CulturalPreference[]\n  languageRequirements: string[]\n  timeCommitment: TimeCommitment\n  sessionPreferences: SessionPreference\n  additionalRequirements?: string\n}\n\nexport interface ListingOffering {\n  expertiseLevel: 'intermediate' | 'advanced' | 'expert' | 'master'\n  teachingMethods: TeachingMethod[]\n  culturalApproaches: CulturalApproach[]\n  portfolioItems: PortfolioItem[]\n  certifications: Certification[]\n  packages: LearningPackage[]\n  uniqueValue: string\n  successStories: SuccessStory[]\n}\n\nexport interface PricingStructure {\n  baseRate: number // time credits per hour\n  packagePricing: PackagePricing[]\n  culturalExchangeRate?: number // discount for cultural knowledge sharing\n  alternativeCompensation: AlternativeCompensation[]\n  flexibilityOptions: PricingFlexibility\n}\n\nexport interface KnowledgeExchange {\n  id: string\n  marketplaceListingId: string\n  requesterId: string\n  providerId: string\n  skillId: string\n  exchangeType: 'direct_hire' | 'negotiated' | 'cultural_exchange' | 'barter'\n  agreement: ExchangeAgreement\n  milestones: ExchangeMilestone[]\n  communications: ExchangeMessage[]\n  payments: PaymentTransaction[]\n  culturalElements: CulturalExchangeElement[]\n  status: 'proposed' | 'negotiating' | 'agreed' | 'in_progress' | 'completed' | 'disputed' | 'cancelled'\n  startDate: Timestamp\n  expectedEndDate: Timestamp\n  actualEndDate?: Timestamp\n  satisfaction: MutualSatisfaction\n}\n\nexport interface ExchangeAgreement {\n  learningObjectives: string[]\n  deliverables: Deliverable[]\n  timeline: TimelineItem[]\n  compensationTerms: CompensationTerms\n  culturalGuidelines: CulturalGuideline[]\n  cancellationPolicy: CancellationPolicy\n  intellectualPropertyTerms: IPTerms\n  disputeResolutionProcess: DisputeResolution\n}\n\nexport interface CulturalKnowledgeVerification {\n  knowledgeId: string\n  culturalOrigin: string\n  verificationStatus: 'pending' | 'verified' | 'questioned' | 'rejected'\n  verifiers: CulturalVerifier[]\n  verificationDate: Timestamp\n  culturalContext: string\n  traditionalKnowledgeRights: boolean\n  communityApproval: boolean\n  culturalSensitivityGuidelines: string[]\n  appropriatenessRating: number // 1-10\n}\n\nexport interface MarketplaceAnalytics {\n  listingPerformance: ListingMetrics\n  exchangeSuccess: ExchangeMetrics\n  culturalDiversity: CulturalMetrics\n  userEngagement: EngagementMetrics\n  qualityIndicators: QualityMetrics\n  economicImpact: EconomicMetrics\n}\n\n// Supporting interfaces\nexport interface CulturalContext {\n  culture: string\n  significance: string\n  traditionalMethods: string[]\n  modernAdaptations: string[]\n}\n\nexport interface LearningFormat {\n  format: 'online' | 'in_person' | 'hybrid' | 'self_paced' | 'group' | 'workshop'\n  preference: number // 1-10\n}\n\nexport interface CulturalPreference {\n  culture: string\n  interest: number // 1-10\n  currentKnowledge: number // 1-10\n  learningGoals: string[]\n}\n\nexport interface TimeCommitment {\n  hoursPerWeek: number\n  totalDuration: number // weeks\n  flexibility: 'rigid' | 'flexible' | 'very_flexible'\n}\n\nexport interface SessionPreference {\n  duration: number // minutes\n  frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly'\n  groupSize: 'individual' | 'small_group' | 'large_group' | 'any'\n}\n\nexport interface LearningPackage {\n  id: string\n  name: string\n  description: string\n  duration: number // hours\n  price: number // time credits\n  culturalElements: string[]\n  outcomes: string[]\n}\n\nexport interface PackagePricing {\n  packageId: string\n  originalPrice: number\n  discountedPrice: number\n  culturalBonus: boolean\n}\n\nexport interface AlternativeCompensation {\n  type: 'skill_exchange' | 'cultural_sharing' | 'community_service' | 'mentorship'\n  description: string\n  value: number // equivalent time credits\n}\n\nexport interface PricingFlexibility {\n  negotiable: boolean\n  scholarshipAvailable: boolean\n  culturalExchangeDiscount: number // percentage\n  communityMemberDiscount: number // percentage\n}\n\nexport interface AvailabilityWindow {\n  startDate: Timestamp\n  endDate: Timestamp\n  timeSlots: TimeSlot[]\n  culturalConsiderations: string[]\n}\n\nexport interface TimeSlot {\n  dayOfWeek: number // 0-6\n  startTime: string // HH:MM\n  endTime: string // HH:MM\n  timeZone: string\n}\n\nclass MarketplaceService {\n  // Create marketplace listing\n  async createMarketplaceListing(listing: Omit<MarketplaceListing, 'id' | 'views' | 'responses' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    try {\n      const listingId = `listing-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      // Validate cultural content if applicable\n      if (listing.culturalContext && listing.culturalContext.length > 0) {\n        const validation = await culturalValidationService.validateCulturalContent({\n          id: listingId,\n          title: listing.title,\n          content: listing.description,\n          type: 'marketplace_listing',\n          culture: listing.culturalContext[0].culture,\n          author: {\n            userId: listing.userId,\n            culturalCredibility: 'community_member',\n          },\n          verification: {\n            status: 'pending',\n            reviewedBy: [],\n            reviewNotes: [],\n            approvedAt: null,\n          },\n          engagement: {\n            views: 0,\n            likes: 0,\n            shares: 0,\n            comments: 0,\n            crossCulturalViews: 0,\n          },\n          createdAt: Timestamp.now(),\n          lastModified: Timestamp.now(),\n        })\n\n        if (!validation.isValid && validation.requiresReview) {\n          // Notify cultural representatives for review\n          await this.notifyCulturalRepresentatives(\n            listing.culturalContext.map(ctx => ctx.culture),\n            listingId\n          )\n        }\n      }\n\n      const marketplaceListing: MarketplaceListing = {\n        ...listing,\n        id: listingId,\n        views: 0,\n        responses: 0,\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now(),\n      }\n\n      await setDoc(doc(db, 'marketplace-listings', listingId), marketplaceListing)\n\n      return listingId\n    } catch (error) {\n      console.error('Error creating marketplace listing:', error)\n      throw new Error('Failed to create marketplace listing')\n    }\n  }\n\n  // Search marketplace listings\n  async searchMarketplaceListings(\n    searchQuery: {\n      query?: string\n      category?: string\n      culturalContext?: string\n      priceRange?: { min: number; max: number }\n      location?: string\n      availability?: string\n      ratingMin?: number\n      listingType?: 'skill_request' | 'skill_offering'\n    },\n    limit_count: number = 20\n  ): Promise<MarketplaceListing[]> {\n    try {\n      let q = query(\n        collection(db, 'marketplace-listings'),\n        where('status', '==', 'active'),\n        orderBy('createdAt', 'desc'),\n        limit(limit_count)\n      )\n\n      // Apply filters\n      if (searchQuery.listingType) {\n        q = query(q, where('listingType', '==', searchQuery.listingType))\n      }\n\n      const snapshot = await getDocs(q)\n      let listings = snapshot.docs.map(doc => doc.data() as MarketplaceListing)\n\n      // Apply additional filters\n      if (searchQuery.query) {\n        const queryLower = searchQuery.query.toLowerCase()\n        listings = listings.filter(listing =>\n          listing.title.toLowerCase().includes(queryLower) ||\n          listing.description.toLowerCase().includes(queryLower)\n        )\n      }\n\n      if (searchQuery.culturalContext) {\n        listings = listings.filter(listing =>\n          listing.culturalContext.some(ctx => ctx.culture === searchQuery.culturalContext)\n        )\n      }\n\n      if (searchQuery.priceRange) {\n        listings = listings.filter(listing =>\n          listing.pricing.baseRate >= searchQuery.priceRange!.min &&\n          listing.pricing.baseRate <= searchQuery.priceRange!.max\n        )\n      }\n\n      return listings\n    } catch (error) {\n      console.error('Error searching marketplace listings:', error)\n      throw new Error('Failed to search marketplace listings')\n    }\n  }\n\n  // Get personalized recommendations\n  async getPersonalizedRecommendations(userId: string, limit_count: number = 10): Promise<MarketplaceListing[]> {\n    try {\n      // Get user's skill profile to understand interests\n      const userProfile = await this.getUserSkillProfile(userId)\n      if (!userProfile) {\n        // Return general popular listings if no profile\n        return this.getPopularListings(limit_count)\n      }\n\n      // Find listings that match user's learning goals\n      const recommendations: MarketplaceListing[] = []\n\n      for (const learningGoal of userProfile.skillsToLearn) {\n        const skillListings = await this.searchMarketplaceListings({\n          query: learningGoal.skillName,\n          listingType: 'skill_offering',\n        }, 5)\n\n        recommendations.push(...skillListings)\n      }\n\n      // Find listings that match user's cultural interests\n      const culturalInterests = userProfile.skillsToLearn.flatMap(goal => \n        goal.culturalPreferences?.map(pref => pref.culture) || []\n      )\n\n      for (const culture of culturalInterests) {\n        const culturalListings = await this.searchMarketplaceListings({\n          culturalContext: culture,\n        }, 3)\n\n        recommendations.push(...culturalListings)\n      }\n\n      // Remove duplicates and sort by relevance\n      const uniqueRecommendations = recommendations.filter((listing, index, self) =>\n        index === self.findIndex(l => l.id === listing.id)\n      )\n\n      return uniqueRecommendations.slice(0, limit_count)\n    } catch (error) {\n      console.error('Error getting personalized recommendations:', error)\n      throw new Error('Failed to get personalized recommendations')\n    }\n  }\n\n  // Initiate knowledge exchange\n  async initiateKnowledgeExchange(\n    listingId: string,\n    requesterId: string,\n    proposalMessage: string,\n    proposedTerms: Partial<ExchangeAgreement>\n  ): Promise<string> {\n    try {\n      const exchangeId = `exchange-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n      \n      // Get listing details\n      const listingDoc = await getDoc(doc(db, 'marketplace-listings', listingId))\n      if (!listingDoc.exists()) {\n        throw new Error('Listing not found')\n      }\n\n      const listing = listingDoc.data() as MarketplaceListing\n      \n      const knowledgeExchange: KnowledgeExchange = {\n        id: exchangeId,\n        marketplaceListingId: listingId,\n        requesterId,\n        providerId: listing.userId,\n        skillId: listing.skillId,\n        exchangeType: 'negotiated',\n        agreement: {\n          learningObjectives: proposedTerms.learningObjectives || [],\n          deliverables: proposedTerms.deliverables || [],\n          timeline: proposedTerms.timeline || [],\n          compensationTerms: proposedTerms.compensationTerms || {\n            type: 'time_credits',\n            amount: listing.pricing.baseRate,\n            schedule: 'per_session',\n          },\n          culturalGuidelines: proposedTerms.culturalGuidelines || [],\n          cancellationPolicy: proposedTerms.cancellationPolicy || {\n            noticePeriod: 24, // hours\n            refundPolicy: 'partial',\n          },\n          intellectualPropertyTerms: proposedTerms.intellectualPropertyTerms || {\n            ownershipRights: 'shared',\n            attributionRequired: true,\n          },\n          disputeResolutionProcess: proposedTerms.disputeResolutionProcess || {\n            steps: ['direct_negotiation', 'mediation', 'arbitration'],\n            culturalMediationAvailable: true,\n          },\n        },\n        milestones: [],\n        communications: [{\n          id: `msg-${Date.now()}`,\n          senderId: requesterId,\n          message: proposalMessage,\n          timestamp: Timestamp.now(),\n          type: 'proposal',\n        }],\n        payments: [],\n        culturalElements: listing.culturalContext.map(ctx => ({\n          culture: ctx.culture,\n          significance: ctx.significance,\n          learningObjectives: [`Understand ${ctx.culture} cultural context`],\n          respectGuidelines: ['Approach with cultural humility', 'Ask questions respectfully'],\n        })),\n        status: 'proposed',\n        startDate: Timestamp.now(),\n        expectedEndDate: Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 30 days\n        satisfaction: {\n          requesterRating: 0,\n          providerRating: 0,\n          culturalRespectRating: 0,\n          learningOutcomeRating: 0,\n          overallSatisfaction: 0,\n        },\n      }\n\n      await setDoc(doc(db, 'knowledge-exchanges', exchangeId), knowledgeExchange)\n\n      // Update listing response count\n      await updateDoc(doc(db, 'marketplace-listings', listingId), {\n        responses: listing.responses + 1,\n        updatedAt: Timestamp.now(),\n      })\n\n      return exchangeId\n    } catch (error) {\n      console.error('Error initiating knowledge exchange:', error)\n      throw new Error('Failed to initiate knowledge exchange')\n    }\n  }\n\n  // Update knowledge exchange status\n  async updateKnowledgeExchangeStatus(\n    exchangeId: string,\n    status: KnowledgeExchange['status'],\n    notes?: string\n  ): Promise<void> {\n    try {\n      const exchangeRef = doc(db, 'knowledge-exchanges', exchangeId)\n      \n      const updateData: any = {\n        status,\n        updatedAt: Timestamp.now(),\n      }\n\n      if (status === 'agreed') {\n        updateData.startDate = Timestamp.now()\n      } else if (status === 'completed') {\n        updateData.actualEndDate = Timestamp.now()\n      }\n\n      if (notes) {\n        updateData.statusNotes = notes\n      }\n\n      await updateDoc(exchangeRef, updateData)\n    } catch (error) {\n      console.error('Error updating knowledge exchange status:', error)\n      throw new Error('Failed to update knowledge exchange status')\n    }\n  }\n\n  // Get user's marketplace listings\n  async getUserMarketplaceListings(userId: string): Promise<MarketplaceListing[]> {\n    try {\n      const q = query(\n        collection(db, 'marketplace-listings'),\n        where('userId', '==', userId),\n        orderBy('createdAt', 'desc')\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as MarketplaceListing)\n    } catch (error) {\n      console.error('Error getting user marketplace listings:', error)\n      throw new Error('Failed to get user marketplace listings')\n    }\n  }\n\n  // Get user's knowledge exchanges\n  async getUserKnowledgeExchanges(userId: string, role?: 'requester' | 'provider'): Promise<KnowledgeExchange[]> {\n    try {\n      let q\n      \n      if (role === 'requester') {\n        q = query(\n          collection(db, 'knowledge-exchanges'),\n          where('requesterId', '==', userId),\n          orderBy('createdAt', 'desc')\n        )\n      } else if (role === 'provider') {\n        q = query(\n          collection(db, 'knowledge-exchanges'),\n          where('providerId', '==', userId),\n          orderBy('createdAt', 'desc')\n        )\n      } else {\n        // Get both requester and provider exchanges\n        const requesterQuery = query(\n          collection(db, 'knowledge-exchanges'),\n          where('requesterId', '==', userId),\n          orderBy('createdAt', 'desc')\n        )\n        \n        const providerQuery = query(\n          collection(db, 'knowledge-exchanges'),\n          where('providerId', '==', userId),\n          orderBy('createdAt', 'desc')\n        )\n\n        const [requesterSnapshot, providerSnapshot] = await Promise.all([\n          getDocs(requesterQuery),\n          getDocs(providerQuery)\n        ])\n\n        const requesterExchanges = requesterSnapshot.docs.map(doc => doc.data() as KnowledgeExchange)\n        const providerExchanges = providerSnapshot.docs.map(doc => doc.data() as KnowledgeExchange)\n\n        return [...requesterExchanges, ...providerExchanges]\n          .sort((a, b) => b.createdAt.seconds - a.createdAt.seconds)\n      }\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as KnowledgeExchange)\n    } catch (error) {\n      console.error('Error getting user knowledge exchanges:', error)\n      throw new Error('Failed to get user knowledge exchanges')\n    }\n  }\n\n  // Get featured listings\n  async getFeaturedListings(limit_count: number = 10): Promise<MarketplaceListing[]> {\n    try {\n      const q = query(\n        collection(db, 'marketplace-listings'),\n        where('status', '==', 'active'),\n        where('featuredUntil', '>', Timestamp.now()),\n        orderBy('featuredUntil', 'desc'),\n        limit(limit_count)\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as MarketplaceListing)\n    } catch (error) {\n      console.error('Error getting featured listings:', error)\n      throw new Error('Failed to get featured listings')\n    }\n  }\n\n  // Get popular listings\n  async getPopularListings(limit_count: number = 10): Promise<MarketplaceListing[]> {\n    try {\n      const q = query(\n        collection(db, 'marketplace-listings'),\n        where('status', '==', 'active'),\n        orderBy('views', 'desc'),\n        limit(limit_count)\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.docs.map(doc => doc.data() as MarketplaceListing)\n    } catch (error) {\n      console.error('Error getting popular listings:', error)\n      throw new Error('Failed to get popular listings')\n    }\n  }\n\n  // Increment listing views\n  async incrementListingViews(listingId: string): Promise<void> {\n    try {\n      const listingRef = doc(db, 'marketplace-listings', listingId)\n      const listingDoc = await getDoc(listingRef)\n      \n      if (listingDoc.exists()) {\n        const currentViews = listingDoc.data().views || 0\n        await updateDoc(listingRef, {\n          views: currentViews + 1,\n          updatedAt: Timestamp.now(),\n        })\n      }\n    } catch (error) {\n      console.error('Error incrementing listing views:', error)\n      // Don't throw error for view tracking\n    }\n  }\n\n  // Private helper methods\n  private async getUserSkillProfile(userId: string): Promise<any> {\n    try {\n      const q = query(\n        collection(db, 'skill-profiles'),\n        where('userId', '==', userId),\n        limit(1)\n      )\n\n      const snapshot = await getDocs(q)\n      return snapshot.empty ? null : snapshot.docs[0].data()\n    } catch (error) {\n      console.error('Error getting user skill profile:', error)\n      return null\n    }\n  }\n\n  private async notifyCulturalRepresentatives(cultures: string[], listingId: string): Promise<void> {\n    // TODO: Implement notification system for cultural representatives\n    console.log(`Notifying cultural representatives for cultures: ${cultures.join(', ')} about listing: ${listingId}`)\n  }\n}\n\nexport const marketplaceService = new MarketplaceService()\n", "import React, { useState, useEffect } from 'react'\nimport { marketplaceService, MarketplaceListing, KnowledgeExchange } from '../../../services/marketplaceService'\nimport { timeBankingService, TimeBankAccount } from '../../../services/timeBankingService'\nimport { useAuth } from '../../auth/hooks/useAuth'\n\ninterface KnowledgeMarketplaceProps {\n  onNavigateToTimeBank?: () => void\n  onNavigateToSkillSharing?: () => void\n}\n\nexport const KnowledgeMarketplace: React.FC<KnowledgeMarketplaceProps> = ({\n  onNavigateToTimeBank,\n  onNavigateToSkillSharing,\n}) => {\n  const { user } = useAuth()\n  const [activeTab, setActiveTab] = useState<'browse' | 'my_listings' | 'exchanges' | 'create'>('browse')\n  const [listings, setListings] = useState<MarketplaceListing[]>([])\n  const [myListings, setMyListings] = useState<MarketplaceListing[]>([])\n  const [exchanges, setExchanges] = useState<KnowledgeExchange[]>([])\n  const [timeBankAccount, setTimeBankAccount] = useState<TimeBankAccount | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n  const [selectedCulture, setSelectedCulture] = useState('')\n\n  const categories = [\n    'Technology', 'Business', 'Arts & Crafts', 'Languages', 'Cooking',\n    'Music', 'Traditional Skills', 'Agriculture', 'Healthcare', 'Education'\n  ]\n\n  const cultures = [\n    'zulu', 'xhosa', 'afrikaans', 'english', 'sotho', 'tswana',\n    'tsonga', 'swati', 'venda', 'ndebele', 'coloured'\n  ]\n\n  useEffect(() => {\n    if (user) {\n      loadMarketplaceData()\n    }\n  }, [user])\n\n  const loadMarketplaceData = async () => {\n    if (!user) return\n\n    setLoading(true)\n    try {\n      const [allListings, userListings, userExchanges, timeAccount] = await Promise.all([\n        marketplaceService.searchMarketplaceListings({}, 20),\n        marketplaceService.getUserMarketplaceListings(user.uid),\n        marketplaceService.getUserKnowledgeExchanges(user.uid),\n        timeBankingService.getTimeBankAccount(user.uid),\n      ])\n\n      setListings(allListings)\n      setMyListings(userListings)\n      setExchanges(userExchanges)\n      setTimeBankAccount(timeAccount)\n    } catch (error) {\n      console.error('Error loading marketplace data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSearch = async () => {\n    setLoading(true)\n    try {\n      const searchResults = await marketplaceService.searchMarketplaceListings({\n        query: searchQuery,\n        category: selectedCategory,\n        culturalContext: selectedCulture,\n      })\n      setListings(searchResults)\n    } catch (error) {\n      console.error('Error searching listings:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleInitiateExchange = async (listingId: string) => {\n    if (!user) return\n\n    try {\n      const proposalMessage = \"I'm interested in this knowledge exchange opportunity. Let's discuss the details!\"\n      \n      const exchangeId = await marketplaceService.initiateKnowledgeExchange(\n        listingId,\n        user.uid,\n        proposalMessage,\n        {\n          learningObjectives: ['Gain practical skills', 'Cultural understanding'],\n          compensationTerms: {\n            type: 'time_credits',\n            amount: 0, // Will be negotiated\n            schedule: 'per_session',\n          },\n        }\n      )\n\n      alert(`Exchange request sent! Exchange ID: ${exchangeId}`)\n      loadMarketplaceData() // Refresh data\n    } catch (error) {\n      console.error('Error initiating exchange:', error)\n      alert('Failed to initiate exchange. Please try again.')\n    }\n  }\n\n  const renderBrowse = () => (\n    <div className=\"space-y-6\">\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div className=\"md:col-span-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search skills, knowledge, or expertise...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"\">All Categories</option>\n            {categories.map(category => (\n              <option key={category} value={category}>{category}</option>\n            ))}\n          </select>\n          <select\n            value={selectedCulture}\n            onChange={(e) => setSelectedCulture(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"\">All Cultures</option>\n            {cultures.map(culture => (\n              <option key={culture} value={culture} className=\"capitalize\">{culture}</option>\n            ))}\n          </select>\n        </div>\n        <div className=\"mt-4\">\n          <button\n            onClick={handleSearch}\n            className=\"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n          >\n            Search\n          </button>\n        </div>\n      </div>\n\n      {/* Featured Listings */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Featured Knowledge Exchanges</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {listings.slice(0, 6).map(listing => (\n            <div key={listing.id} className=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n              <div className=\"flex items-start justify-between mb-3\">\n                <div>\n                  <h4 className=\"font-semibold text-gray-900\">{listing.title}</h4>\n                  <span className={`text-xs px-2 py-1 rounded-full ${\n                    listing.listingType === 'skill_offering' \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-blue-100 text-blue-800'\n                  }`}>\n                    {listing.listingType === 'skill_offering' ? 'Offering' : 'Seeking'}\n                  </span>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-sm font-medium text-orange-600\">\n                    {listing.pricing.baseRate} credits/hour\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {listing.views} views\n                  </div>\n                </div>\n              </div>\n\n              <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                {listing.description}\n              </p>\n\n              {listing.culturalContext.length > 0 && (\n                <div className=\"flex flex-wrap gap-1 mb-3\">\n                  {listing.culturalContext.map(context => (\n                    <span key={context.culture} className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n                      {context.culture}\n                    </span>\n                  ))}\n                </div>\n              )}\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-xs text-gray-500\">\n                  {listing.responses} responses\n                </div>\n                <button\n                  onClick={() => handleInitiateExchange(listing.id)}\n                  className=\"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors\"\n                >\n                  Connect\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-blue-600\">{listings.length}</div>\n          <div className=\"text-sm text-gray-600\">Active Listings</div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-green-600\">\n            {listings.filter(l => l.listingType === 'skill_offering').length}\n          </div>\n          <div className=\"text-sm text-gray-600\">Skills Available</div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-purple-600\">\n            {new Set(listings.flatMap(l => l.culturalContext.map(c => c.culture))).size}\n          </div>\n          <div className=\"text-sm text-gray-600\">Cultures Represented</div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-orange-600\">\n            {timeBankAccount?.balance || 0}\n          </div>\n          <div className=\"text-sm text-gray-600\">Your Credits</div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderMyListings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Your Marketplace Listings</h3>\n        <button\n          onClick={() => setActiveTab('create')}\n          className=\"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n        >\n          Create New Listing\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {myListings.map(listing => (\n          <div key={listing.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-start justify-between mb-3\">\n              <div>\n                <h4 className=\"font-semibold text-gray-900\">{listing.title}</h4>\n                <span className={`text-xs px-2 py-1 rounded-full ${\n                  listing.status === 'active' ? 'bg-green-100 text-green-800' :\n                  listing.status === 'paused' ? 'bg-yellow-100 text-yellow-800' :\n                  'bg-gray-100 text-gray-800'\n                }`}>\n                  {listing.status}\n                </span>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-sm font-medium text-orange-600\">\n                  {listing.pricing.baseRate} credits/hour\n                </div>\n              </div>\n            </div>\n\n            <p className=\"text-sm text-gray-600 mb-3\">{listing.description}</p>\n\n            <div className=\"grid grid-cols-2 gap-4 mb-4\">\n              <div>\n                <span className=\"text-xs text-gray-500\">Views:</span>\n                <span className=\"text-sm font-medium text-gray-900 ml-1\">{listing.views}</span>\n              </div>\n              <div>\n                <span className=\"text-xs text-gray-500\">Responses:</span>\n                <span className=\"text-sm font-medium text-gray-900 ml-1\">{listing.responses}</span>\n              </div>\n            </div>\n\n            {listing.culturalContext.length > 0 && (\n              <div className=\"flex flex-wrap gap-1 mb-4\">\n                {listing.culturalContext.map(context => (\n                  <span key={context.culture} className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n                    {context.culture}\n                  </span>\n                ))}\n              </div>\n            )}\n\n            <div className=\"flex gap-2\">\n              <button className=\"flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50\">\n                Edit\n              </button>\n              <button className=\"flex-1 px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\">\n                View Responses\n              </button>\n            </div>\n          </div>\n        ))}\n\n        {myListings.length === 0 && (\n          <div className=\"col-span-2 text-center py-12 text-gray-500\">\n            <div className=\"text-4xl mb-4\">📝</div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No listings yet</h3>\n            <p className=\"text-gray-600 mb-4\">\n              Create your first marketplace listing to start sharing your knowledge or find what you need to learn.\n            </p>\n            <button\n              onClick={() => setActiveTab('create')}\n              className=\"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n            >\n              Create Your First Listing\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n\n  const renderExchanges = () => (\n    <div className=\"space-y-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900\">Your Knowledge Exchanges</h3>\n\n      <div className=\"space-y-4\">\n        {exchanges.map(exchange => (\n          <div key={exchange.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-start justify-between mb-4\">\n              <div>\n                <h4 className=\"font-semibold text-gray-900\">\n                  {exchange.skillId} Exchange\n                </h4>\n                <p className=\"text-sm text-gray-600\">\n                  {exchange.requesterId === user?.uid ? 'You are learning' : 'You are teaching'}\n                </p>\n              </div>\n              <span className={`px-3 py-1 rounded-full text-sm ${\n                exchange.status === 'active' || exchange.status === 'in_progress' ? 'bg-green-100 text-green-800' :\n                exchange.status === 'completed' ? 'bg-blue-100 text-blue-800' :\n                exchange.status === 'proposed' || exchange.status === 'negotiating' ? 'bg-yellow-100 text-yellow-800' :\n                'bg-gray-100 text-gray-800'\n              }`}>\n                {exchange.status.replace('_', ' ')}\n              </span>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n              <div>\n                <span className=\"text-sm font-medium text-gray-700\">Type:</span>\n                <span className=\"text-sm text-gray-600 ml-1 capitalize\">\n                  {exchange.exchangeType.replace('_', ' ')}\n                </span>\n              </div>\n              <div>\n                <span className=\"text-sm font-medium text-gray-700\">Start Date:</span>\n                <span className=\"text-sm text-gray-600 ml-1\">\n                  {new Date(exchange.startDate.seconds * 1000).toLocaleDateString()}\n                </span>\n              </div>\n              <div>\n                <span className=\"text-sm font-medium text-gray-700\">Expected End:</span>\n                <span className=\"text-sm text-gray-600 ml-1\">\n                  {new Date(exchange.expectedEndDate.seconds * 1000).toLocaleDateString()}\n                </span>\n              </div>\n            </div>\n\n            {exchange.culturalElements.length > 0 && (\n              <div className=\"mb-4\">\n                <span className=\"text-sm font-medium text-gray-700\">Cultural Elements:</span>\n                <div className=\"flex flex-wrap gap-1 mt-1\">\n                  {exchange.culturalElements.map(element => (\n                    <span key={element.culture} className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n                      {element.culture}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex gap-2\">\n              <button className=\"px-4 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\">\n                View Details\n              </button>\n              <button className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50\">\n                Messages\n              </button>\n              {exchange.status === 'proposed' && exchange.providerId === user?.uid && (\n                <button className=\"px-4 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600\">\n                  Accept\n                </button>\n              )}\n            </div>\n          </div>\n        ))}\n\n        {exchanges.length === 0 && (\n          <div className=\"text-center py-12 text-gray-500\">\n            <div className=\"text-4xl mb-4\">🤝</div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No exchanges yet</h3>\n            <p className=\"text-gray-600 mb-4\">\n              Start connecting with others to begin your knowledge exchange journey.\n            </p>\n            <button\n              onClick={() => setActiveTab('browse')}\n              className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n            >\n              Browse Marketplace\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n\n  const renderCreate = () => (\n    <div className=\"max-w-2xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">Create Marketplace Listing</h3>\n        \n        <div className=\"space-y-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Listing Type\n            </label>\n            <div className=\"grid grid-cols-2 gap-4\">\n              <label className=\"flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50\">\n                <input type=\"radio\" name=\"listingType\" value=\"skill_offering\" className=\"mr-3\" />\n                <div>\n                  <div className=\"font-medium text-gray-900\">Offering Skills</div>\n                  <div className=\"text-sm text-gray-600\">Share your expertise</div>\n                </div>\n              </label>\n              <label className=\"flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50\">\n                <input type=\"radio\" name=\"listingType\" value=\"skill_request\" className=\"mr-3\" />\n                <div>\n                  <div className=\"font-medium text-gray-900\">Seeking Skills</div>\n                  <div className=\"text-sm text-gray-600\">Find what you need to learn</div>\n                </div>\n              </label>\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Title *\n            </label>\n            <input\n              type=\"text\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"e.g., Expert Zulu Language Tutoring with Cultural Context\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Description *\n            </label>\n            <textarea\n              rows={4}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Describe what you're offering or seeking, including cultural context and learning approach...\"\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Category\n              </label>\n              <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                <option value=\"\">Select category</option>\n                {categories.map(category => (\n                  <option key={category} value={category}>{category}</option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Cultural Context\n              </label>\n              <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                <option value=\"\">Select culture (optional)</option>\n                {cultures.map(culture => (\n                  <option key={culture} value={culture} className=\"capitalize\">{culture}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Rate (Time Credits per Hour)\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"e.g., 15\"\n            />\n          </div>\n\n          <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-4\">\n            <h4 className=\"font-medium text-orange-800 mb-2\">Cultural Sensitivity Guidelines</h4>\n            <ul className=\"text-sm text-orange-700 space-y-1\">\n              <li>• Ensure respectful representation of cultural knowledge</li>\n              <li>• Provide proper cultural context and significance</li>\n              <li>• Respect traditional knowledge and intellectual property</li>\n              <li>• Follow Ubuntu principles of mutual respect and benefit</li>\n            </ul>\n          </div>\n\n          <div className=\"flex gap-4\">\n            <button\n              onClick={() => setActiveTab('browse')}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button className=\"flex-1 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600\">\n              Create Listing\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Knowledge Exchange Marketplace</h1>\n        <p className=\"text-gray-600\">\n          Discover, share, and exchange knowledge across cultures with fair time-based compensation.\n        </p>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"flex space-x-8\">\n          {[\n            { id: 'browse', label: 'Browse', icon: '🔍' },\n            { id: 'my_listings', label: 'My Listings', icon: '📝' },\n            { id: 'exchanges', label: 'Exchanges', icon: '🤝' },\n            { id: 'create', label: 'Create', icon: '➕' },\n          ].map(tab => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'browse' && renderBrowse()}\n      {activeTab === 'my_listings' && renderMyListings()}\n      {activeTab === 'exchanges' && renderExchanges()}\n      {activeTab === 'create' && renderCreate()}\n    </div>\n  )\n}\n\nexport default KnowledgeMarketplace\n", "import React, { useState, useEffect } from 'react'\nimport { timeBankingService, TimeBankAccount, TimeTransaction, FairExchangeMetrics } from '../../../services/timeBankingService'\nimport { useAuth } from '../../auth/hooks/useAuth'\n\ninterface TimeBankingDashboardProps {\n  onNavigateToMarketplace?: () => void\n  onNavigateToSkillSharing?: () => void\n}\n\nexport const TimeBankingDashboard: React.FC<TimeBankingDashboardProps> = ({\n  onNavigateToMarketplace,\n  onNavigateToSkillSharing,\n}) => {\n  const { user } = useAuth()\n  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'metrics' | 'disputes'>('overview')\n  const [timeBankAccount, setTimeBankAccount] = useState<TimeBankAccount | null>(null)\n  const [transactions, setTransactions] = useState<TimeTransaction[]>([])\n  const [fairExchangeMetrics, setFairExchangeMetrics] = useState<FairExchangeMetrics | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    if (user) {\n      loadTimeBankingData()\n    }\n  }, [user])\n\n  const loadTimeBankingData = async () => {\n    if (!user) return\n\n    setLoading(true)\n    try {\n      const [account, transactionHistory, metrics] = await Promise.all([\n        timeBankingService.getTimeBankAccount(user.uid),\n        timeBankingService.getUserTransactionHistory(user.uid, 50),\n        timeBankingService.calculateFairExchangeMetrics(user.uid),\n      ])\n\n      setTimeBankAccount(account)\n      setTransactions(transactionHistory)\n      setFairExchangeMetrics(metrics)\n    } catch (error) {\n      console.error('Error loading time banking data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const renderOverview = () => (\n    <div className=\"space-y-6\">\n      {/* Account Balance Card */}\n      <div className=\"bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-lg font-semibold mb-2\">Time Credit Balance</h3>\n            <div className=\"text-3xl font-bold\">{timeBankAccount?.balance || 0}</div>\n            <p className=\"text-orange-100 mt-1\">Credits available for exchanges</p>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-orange-100 text-sm\">Quality Multiplier</div>\n            <div className=\"text-xl font-semibold\">{timeBankAccount?.qualityMultiplier.toFixed(1) || '1.0'}x</div>\n            <div className=\"text-orange-100 text-sm\">Cultural Bonus</div>\n            <div className=\"text-lg font-medium\">{((timeBankAccount?.culturalBonusRate || 0) * 100).toFixed(0)}%</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">💰</div>\n            <div>\n              <div className=\"text-2xl font-bold text-green-600\">\n                {timeBankAccount?.totalEarned || 0}\n              </div>\n              <div className=\"text-sm text-gray-600\">Total Earned</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">💸</div>\n            <div>\n              <div className=\"text-2xl font-bold text-blue-600\">\n                {timeBankAccount?.totalSpent || 0}\n              </div>\n              <div className=\"text-sm text-gray-600\">Total Spent</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">⭐</div>\n            <div>\n              <div className=\"text-2xl font-bold text-purple-600\">\n                {timeBankAccount?.reputationScore || 0}\n              </div>\n              <div className=\"text-sm text-gray-600\">Reputation Score</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">🔒</div>\n            <div>\n              <div className=\"text-2xl font-bold text-yellow-600\">\n                {timeBankAccount?.totalReserved || 0}\n              </div>\n              <div className=\"text-sm text-gray-600\">Reserved Credits</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Fair Exchange Metrics */}\n      {fairExchangeMetrics && (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Fair Exchange Metrics</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-green-600 mb-1\">\n                {fairExchangeMetrics.overallFairnessScore}/100\n              </div>\n              <div className=\"text-sm text-gray-600\">Overall Fairness</div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n                <div \n                  className=\"bg-green-500 h-2 rounded-full\" \n                  style={{ width: `${fairExchangeMetrics.overallFairnessScore}%` }}\n                />\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-orange-600 mb-1\">\n                {fairExchangeMetrics.culturalContribution}/100\n              </div>\n              <div className=\"text-sm text-gray-600\">Cultural Contribution</div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n                <div \n                  className=\"bg-orange-500 h-2 rounded-full\" \n                  style={{ width: `${fairExchangeMetrics.culturalContribution}%` }}\n                />\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-blue-600 mb-1\">\n                {fairExchangeMetrics.crossCulturalBridging}/100\n              </div>\n              <div className=\"text-sm text-gray-600\">Cross-Cultural Bridging</div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n                <div \n                  className=\"bg-blue-500 h-2 rounded-full\" \n                  style={{ width: `${fairExchangeMetrics.crossCulturalBridging}%` }}\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Recent Transactions */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Transactions</h3>\n        <div className=\"space-y-3\">\n          {transactions.slice(0, 5).map(transaction => (\n            <div key={transaction.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div className=\"flex items-center\">\n                <div className={`text-2xl mr-3 ${\n                  transaction.type === 'earn' || transaction.type === 'bonus' ? 'text-green-600' :\n                  transaction.type === 'spend' ? 'text-blue-600' :\n                  'text-gray-600'\n                }`}>\n                  {transaction.type === 'earn' || transaction.type === 'bonus' ? '💰' :\n                   transaction.type === 'spend' ? '💸' : '⏰'}\n                </div>\n                <div>\n                  <div className=\"font-medium text-gray-900\">{transaction.description}</div>\n                  <div className=\"text-sm text-gray-600\">\n                    {new Date(transaction.timestamp.seconds * 1000).toLocaleDateString()}\n                  </div>\n                  {transaction.culturalContext && (\n                    <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded mt-1 inline-block\">\n                      {transaction.culturalContext.primaryCulture}\n                    </span>\n                  )}\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className={`font-semibold ${\n                  transaction.type === 'earn' || transaction.type === 'bonus' ? 'text-green-600' :\n                  transaction.type === 'spend' ? 'text-red-600' :\n                  'text-gray-600'\n                }`}>\n                  {transaction.type === 'earn' || transaction.type === 'bonus' ? '+' : \n                   transaction.type === 'spend' ? '-' : ''}\n                  {transaction.amount}\n                </div>\n                <div className=\"text-xs text-gray-500\">\n                  {transaction.status}\n                </div>\n              </div>\n            </div>\n          ))}\n          \n          {transactions.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">📊</div>\n              <p>No transactions yet</p>\n              <p className=\"text-sm\">Start participating in knowledge exchanges to see your transaction history!</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-3xl mb-3\">🎓</div>\n          <h4 className=\"font-semibold text-gray-900 mb-2\">Earn Credits</h4>\n          <p className=\"text-sm text-gray-600 mb-4\">\n            Share your skills and knowledge to earn time credits\n          </p>\n          <button \n            onClick={onNavigateToSkillSharing}\n            className=\"w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n          >\n            Start Teaching\n          </button>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-3xl mb-3\">📚</div>\n          <h4 className=\"font-semibold text-gray-900 mb-2\">Spend Credits</h4>\n          <p className=\"text-sm text-gray-600 mb-4\">\n            Use your credits to learn new skills and knowledge\n          </p>\n          <button \n            onClick={onNavigateToMarketplace}\n            className=\"w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n          >\n            Find Learning\n          </button>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-3xl mb-3\">🤝</div>\n          <h4 className=\"font-semibold text-gray-900 mb-2\">Cultural Exchange</h4>\n          <p className=\"text-sm text-gray-600 mb-4\">\n            Participate in cross-cultural exchanges for bonus credits\n          </p>\n          <button className=\"w-full px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors\">\n            Explore Cultures\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderTransactions = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Transaction History</h3>\n        <div className=\"flex gap-2\">\n          <select className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm\">\n            <option value=\"\">All Types</option>\n            <option value=\"earn\">Earned</option>\n            <option value=\"spend\">Spent</option>\n            <option value=\"bonus\">Bonus</option>\n          </select>\n          <select className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm\">\n            <option value=\"\">All Time</option>\n            <option value=\"week\">This Week</option>\n            <option value=\"month\">This Month</option>\n            <option value=\"year\">This Year</option>\n          </select>\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Date\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Description\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Type\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Amount\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Cultural Context\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {transactions.map(transaction => (\n                <tr key={transaction.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {new Date(transaction.timestamp.seconds * 1000).toLocaleDateString()}\n                  </td>\n                  <td className=\"px-6 py-4 text-sm text-gray-900\">\n                    <div className=\"max-w-xs truncate\">{transaction.description}</div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                      transaction.type === 'earn' || transaction.type === 'bonus' ? 'bg-green-100 text-green-800' :\n                      transaction.type === 'spend' ? 'bg-blue-100 text-blue-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      {transaction.type}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <span className={\n                      transaction.type === 'earn' || transaction.type === 'bonus' ? 'text-green-600' :\n                      transaction.type === 'spend' ? 'text-red-600' :\n                      'text-gray-600'\n                    }>\n                      {transaction.type === 'earn' || transaction.type === 'bonus' ? '+' : \n                       transaction.type === 'spend' ? '-' : ''}\n                      {transaction.amount}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                      transaction.status === 'completed' ? 'bg-green-100 text-green-800' :\n                      transaction.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                      transaction.status === 'failed' ? 'bg-red-100 text-red-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      {transaction.status}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {transaction.culturalContext ? (\n                      <span className=\"bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs\">\n                        {transaction.culturalContext.primaryCulture}\n                      </span>\n                    ) : (\n                      '-'\n                    )}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {transactions.length === 0 && (\n          <div className=\"text-center py-12 text-gray-500\">\n            <div className=\"text-4xl mb-4\">📊</div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No transactions yet</h3>\n            <p className=\"text-gray-600\">\n              Your transaction history will appear here as you participate in knowledge exchanges.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n\n  const renderMetrics = () => (\n    <div className=\"space-y-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900\">Fair Exchange Metrics</h3>\n\n      {fairExchangeMetrics ? (\n        <>\n          {/* Overall Score */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-green-600 mb-2\">\n                {fairExchangeMetrics.overallFairnessScore}/100\n              </div>\n              <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Overall Fairness Score</h4>\n              <p className=\"text-gray-600\">\n                Your overall fairness in knowledge exchanges based on Ubuntu principles\n              </p>\n              <div className=\"w-full bg-gray-200 rounded-full h-4 mt-4\">\n                <div \n                  className=\"bg-green-500 h-4 rounded-full transition-all duration-500\" \n                  style={{ width: `${fairExchangeMetrics.overallFairnessScore}%` }}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Detailed Metrics */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h4 className=\"font-semibold text-gray-900 mb-4\">Exchange Patterns</h4>\n              <div className=\"space-y-4\">\n                <div>\n                  <div className=\"flex justify-between text-sm mb-1\">\n                    <span>Exchange Equity</span>\n                    <span>{fairExchangeMetrics.exchangeEquity}/100</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className=\"bg-blue-500 h-2 rounded-full\" \n                      style={{ width: `${fairExchangeMetrics.exchangeEquity}%` }}\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <div className=\"flex justify-between text-sm mb-1\">\n                    <span>Knowledge Sharing</span>\n                    <span>{fairExchangeMetrics.knowledgeSharing}/100</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className=\"bg-green-500 h-2 rounded-full\" \n                      style={{ width: `${fairExchangeMetrics.knowledgeSharing}%` }}\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <div className=\"flex justify-between text-sm mb-1\">\n                    <span>Learning Engagement</span>\n                    <span>{fairExchangeMetrics.learningEngagement}/100</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className=\"bg-purple-500 h-2 rounded-full\" \n                      style={{ width: `${fairExchangeMetrics.learningEngagement}%` }}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h4 className=\"font-semibold text-gray-900 mb-4\">Cultural Impact</h4>\n              <div className=\"space-y-4\">\n                <div>\n                  <div className=\"flex justify-between text-sm mb-1\">\n                    <span>Cultural Contribution</span>\n                    <span>{fairExchangeMetrics.culturalContribution}/100</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className=\"bg-orange-500 h-2 rounded-full\" \n                      style={{ width: `${fairExchangeMetrics.culturalContribution}%` }}\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <div className=\"flex justify-between text-sm mb-1\">\n                    <span>Cross-Cultural Bridging</span>\n                    <span>{fairExchangeMetrics.crossCulturalBridging}/100</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className=\"bg-blue-500 h-2 rounded-full\" \n                      style={{ width: `${fairExchangeMetrics.crossCulturalBridging}%` }}\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <div className=\"flex justify-between text-sm mb-1\">\n                    <span>Community Impact</span>\n                    <span>{fairExchangeMetrics.communityImpact}/100</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className=\"bg-green-500 h-2 rounded-full\" \n                      style={{ width: `${fairExchangeMetrics.communityImpact}%` }}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Improvement Suggestions */}\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n            <h4 className=\"font-semibold text-blue-800 mb-3\">Improve Your Fairness Score</h4>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700\">\n              <div>\n                <h5 className=\"font-medium mb-2\">Increase Cultural Contribution:</h5>\n                <ul className=\"space-y-1\">\n                  <li>• Participate in cross-cultural exchanges</li>\n                  <li>• Share traditional knowledge respectfully</li>\n                  <li>• Engage with diverse cultural communities</li>\n                </ul>\n              </div>\n              <div>\n                <h5 className=\"font-medium mb-2\">Enhance Exchange Equity:</h5>\n                <ul className=\"space-y-1\">\n                  <li>• Balance teaching and learning activities</li>\n                  <li>• Provide fair value in all exchanges</li>\n                  <li>• Complete commitments reliably</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </>\n      ) : (\n        <div className=\"text-center py-12 text-gray-500\">\n          <div className=\"text-4xl mb-4\">📊</div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No metrics available yet</h3>\n          <p className=\"text-gray-600\">\n            Participate in knowledge exchanges to see your fairness metrics.\n          </p>\n        </div>\n      )}\n    </div>\n  )\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Time Banking Dashboard</h1>\n        <p className=\"text-gray-600\">\n          Manage your time credits and track fair exchange metrics based on Ubuntu principles.\n        </p>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"flex space-x-8\">\n          {[\n            { id: 'overview', label: 'Overview', icon: '📊' },\n            { id: 'transactions', label: 'Transactions', icon: '💰' },\n            { id: 'metrics', label: 'Fairness Metrics', icon: '⚖️' },\n            { id: 'disputes', label: 'Disputes', icon: '⚠️' },\n          ].map(tab => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === tab.id\n                  ? 'border-orange-500 text-orange-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'overview' && renderOverview()}\n      {activeTab === 'transactions' && renderTransactions()}\n      {activeTab === 'metrics' && renderMetrics()}\n      {activeTab === 'disputes' && (\n        <div className=\"text-center py-12 text-gray-500\">\n          <div className=\"text-4xl mb-4\">⚠️</div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No disputes</h3>\n          <p className=\"text-gray-600\">\n            Great! You have no active disputes. Keep up the fair exchanges!\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default TimeBankingDashboard\n", "import React, { useState } from 'react'\nimport SkillSharingDashboard from '../features/skill-sharing/components/SkillSharingDashboard'\nimport KnowledgeMarketplace from '../features/marketplace/components/KnowledgeMarketplace'\nimport TimeBankingDashboard from '../features/time-banking/components/TimeBankingDashboard'\n\ntype ActiveView = 'overview' | 'skill_sharing' | 'marketplace' | 'time_banking'\n\nexport const KnowledgeExchangePage: React.FC = () => {\n  const [activeView, setActiveView] = useState<ActiveView>('overview')\n\n  const renderNavigation = () => (\n    <div className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"max-w-6xl mx-auto px-6\">\n        <nav className=\"flex space-x-8\">\n          {[\n            { id: 'overview', label: 'Overview', icon: '🏠', description: 'Knowledge exchange hub overview' },\n            { id: 'skill_sharing', label: 'Skill Sharing', icon: '🎓', description: 'Mentorship and skill development' },\n            { id: 'marketplace', label: 'Marketplace', icon: '🛒', description: 'Knowledge exchange marketplace' },\n            { id: 'time_banking', label: 'Time Banking', icon: '⏰', description: 'Fair exchange credit system' },\n          ].map(tab => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveView(tab.id as ActiveView)}\n              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${\n                activeView === tab.id\n                  ? 'border-orange-500 text-orange-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n              title={tab.description}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n    </div>\n  )\n\n  const renderOverview = () => (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Knowledge Exchange & Mentorship Platform</h1>\n        <p className=\"text-lg text-gray-600 mb-8\">\n          Connect, learn, and share knowledge across cultures with fair time-based compensation and Ubuntu philosophy.\n        </p>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-blue-600 mb-2\">2,847</div>\n          <div className=\"text-sm text-gray-600\">Active Mentorships</div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-green-600 mb-2\">1,523</div>\n          <div className=\"text-sm text-gray-600\">Marketplace Listings</div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-purple-600 mb-2\">45,892</div>\n          <div className=\"text-sm text-gray-600\">Credits Exchanged</div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center\">\n          <div className=\"text-2xl font-bold text-orange-600 mb-2\">11</div>\n          <div className=\"text-sm text-gray-600\">Cultures Connected</div>\n        </div>\n      </div>\n\n      {/* Feature Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n        <div \n          onClick={() => setActiveView('skill_sharing')}\n          className=\"bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white cursor-pointer hover:from-blue-600 hover:to-blue-700 transition-all\"\n        >\n          <div className=\"text-3xl mb-4\">🎓</div>\n          <h3 className=\"text-xl font-semibold mb-2\">Cross-Cultural Skill Sharing</h3>\n          <p className=\"text-blue-100 mb-4\">\n            Connect with mentors and mentees across cultures. Share expertise while learning about different traditions and approaches.\n          </p>\n          <div className=\"flex items-center text-blue-100\">\n            <span>Start Sharing →</span>\n          </div>\n        </div>\n\n        <div \n          onClick={() => setActiveView('marketplace')}\n          className=\"bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white cursor-pointer hover:from-green-600 hover:to-green-700 transition-all\"\n        >\n          <div className=\"text-3xl mb-4\">🛒</div>\n          <h3 className=\"text-xl font-semibold mb-2\">Knowledge Exchange Marketplace</h3>\n          <p className=\"text-green-100 mb-4\">\n            Discover knowledge exchange opportunities. Post what you can teach or find what you want to learn with fair compensation.\n          </p>\n          <div className=\"flex items-center text-green-100\">\n            <span>Explore Marketplace →</span>\n          </div>\n        </div>\n\n        <div \n          onClick={() => setActiveView('time_banking')}\n          className=\"bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white cursor-pointer hover:from-orange-600 hover:to-orange-700 transition-all\"\n        >\n          <div className=\"text-3xl mb-4\">⏰</div>\n          <h3 className=\"text-xl font-semibold mb-2\">Time Banking & Fair Exchange</h3>\n          <p className=\"text-orange-100 mb-4\">\n            Fair time-based credit system that values all knowledge equally. Earn credits by teaching, spend them learning.\n          </p>\n          <div className=\"flex items-center text-orange-100\">\n            <span>Manage Credits →</span>\n          </div>\n        </div>\n      </div>\n\n      {/* How It Works */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6 text-center\">How Knowledge Exchange Works</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">👤</span>\n            </div>\n            <h3 className=\"font-semibold text-gray-900 mb-2\">1. Create Profile</h3>\n            <p className=\"text-sm text-gray-600\">\n              Set up your skill profile with what you can teach and want to learn, including cultural context.\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">🔍</span>\n            </div>\n            <h3 className=\"font-semibold text-gray-900 mb-2\">2. Find Matches</h3>\n            <p className=\"text-sm text-gray-600\">\n              Our algorithm finds optimal mentor-mentee matches based on skills, culture, and learning styles.\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">🤝</span>\n            </div>\n            <h3 className=\"font-semibold text-gray-900 mb-2\">3. Exchange Knowledge</h3>\n            <p className=\"text-sm text-gray-600\">\n              Engage in meaningful learning sessions with fair time credit compensation for all participants.\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">🌟</span>\n            </div>\n            <h3 className=\"font-semibold text-gray-900 mb-2\">4. Build Bridges</h3>\n            <p className=\"text-sm text-gray-600\">\n              Create lasting cross-cultural connections while contributing to community knowledge preservation.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Ubuntu Philosophy Section */}\n      <div className=\"bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-8\">\n        <div className=\"flex items-center mb-6\">\n          <div className=\"text-3xl mr-4\">🤲</div>\n          <h2 className=\"text-2xl font-bold text-orange-800\">Ubuntu Philosophy in Knowledge Exchange</h2>\n        </div>\n        <p className=\"text-orange-700 mb-6\">\n          Our knowledge exchange platform embodies Ubuntu: \"I am because we are.\" Every exchange strengthens our collective wisdom \n          and creates bridges across cultural boundaries.\n        </p>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-white rounded-lg p-6 border border-orange-200\">\n            <h3 className=\"font-semibold text-orange-800 mb-3\">🔄 Reciprocal Learning</h3>\n            <p className=\"text-orange-700 text-sm\">\n              Every teaching moment is also a learning opportunity. Mentors gain cultural insights while sharing their expertise.\n            </p>\n          </div>\n          <div className=\"bg-white rounded-lg p-6 border border-orange-200\">\n            <h3 className=\"font-semibold text-orange-800 mb-3\">⚖️ Fair Value Exchange</h3>\n            <p className=\"text-orange-700 text-sm\">\n              Time banking ensures all knowledge is valued equally, with cultural bonuses recognizing traditional wisdom.\n            </p>\n          </div>\n          <div className=\"bg-white rounded-lg p-6 border border-orange-200\">\n            <h3 className=\"font-semibold text-orange-800 mb-3\">🌍 Community Strengthening</h3>\n            <p className=\"text-orange-700 text-sm\">\n              Each exchange contributes to community resilience and cross-cultural understanding in South Africa.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Success Stories */}\n      <div className=\"mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-8\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6 text-center\">Success Stories</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"border border-gray-200 rounded-lg p-6\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4\">\n                <span className=\"text-xl\">👩‍💻</span>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-gray-900\">Nomsa & James</h4>\n                <p className=\"text-sm text-gray-600\">Zulu Traditional Beadwork ↔ Web Development</p>\n              </div>\n            </div>\n            <p className=\"text-gray-700 text-sm\">\n              \"Through our exchange, I learned not just web development skills, but also the deep cultural significance \n              behind traditional Zulu patterns. James now incorporates these patterns into his digital designs!\"\n            </p>\n            <div className=\"mt-3 flex items-center text-sm text-gray-500\">\n              <span className=\"bg-orange-100 text-orange-800 px-2 py-1 rounded mr-2\">Cultural Bridge</span>\n              <span>150 credits exchanged</span>\n            </div>\n          </div>\n\n          <div className=\"border border-gray-200 rounded-lg p-6\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4\">\n                <span className=\"text-xl\">👨‍🍳</span>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-gray-900\">Pieter & Thandiwe</h4>\n                <p className=\"text-sm text-gray-600\">Afrikaans Cooking ↔ Business Strategy</p>\n              </div>\n            </div>\n            <p className=\"text-gray-700 text-sm\">\n              \"Learning traditional Afrikaans recipes opened my eyes to the history and culture behind the food. \n              Thandiwe's business insights helped me start my own catering company celebrating diverse cuisines.\"\n            </p>\n            <div className=\"mt-3 flex items-center text-sm text-gray-500\">\n              <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded mr-2\">Business Success</span>\n              <span>200 credits exchanged</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderSkillSharing = () => (\n    <SkillSharingDashboard \n      onNavigateToMarketplace={() => setActiveView('marketplace')}\n      onNavigateToTimeBank={() => setActiveView('time_banking')}\n    />\n  )\n\n  const renderMarketplace = () => (\n    <KnowledgeMarketplace \n      onNavigateToTimeBank={() => setActiveView('time_banking')}\n      onNavigateToSkillSharing={() => setActiveView('skill_sharing')}\n    />\n  )\n\n  const renderTimeBanking = () => (\n    <TimeBankingDashboard \n      onNavigateToMarketplace={() => setActiveView('marketplace')}\n      onNavigateToSkillSharing={() => setActiveView('skill_sharing')}\n    />\n  )\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {renderNavigation()}\n      \n      <main>\n        {activeView === 'overview' && renderOverview()}\n        {activeView === 'skill_sharing' && renderSkillSharing()}\n        {activeView === 'marketplace' && renderMarketplace()}\n        {activeView === 'time_banking' && renderTimeBanking()}\n      </main>\n    </div>\n  )\n}\n\nexport default KnowledgeExchangePage\n"], "names": ["SkillSharingService", "profile", "profileId", "validation", "culturalValidationService", "Timestamp", "skillProfile", "setDoc", "doc", "db", "error", "updates", "profileRef", "updateDoc", "userId", "q", "query", "collection", "where", "limit", "snapshot", "getDocs", "skillId", "culturalPreferences", "learningStyle", "limit_count", "orderBy", "mentors", "mentor", "pref", "approach", "match", "matchId", "mentorship<PERSON><PERSON>", "session", "sessionId", "learningSession", "feedback", "skillProgress", "culturalInsights", "sessionRef", "getDoc", "baseCredits", "qualityMultiplier", "culturalBonus", "timeCreditsAwarded", "role", "field", "status", "notes", "matchRef", "skillSharingService", "TimeBankingService", "accountId", "account", "serviceDetails", "providerId", "providerAccount", "skillComplexityBonus", "demandMultiplier", "communityContributionBonus", "groupBonus", "finalCredits", "breakdown", "transaction", "transactionId", "timeTransaction", "runTransaction", "firestoreTransaction", "transactionRef", "accountRef", "accountDoc", "exchangeId", "payerId", "payeeId", "amount", "payerAccount", "creditCalculation", "dispute", "disputeId", "disputeCase", "resolution", "mediatorId", "disputeRef", "disputeDoc", "transactions", "exchangeEquity", "culturalContribution", "communityImpact", "crossCulturalBridging", "knowledgeSharing", "learningEngagement", "overallFairnessScore", "metrics", "operation", "currentBalance", "newBalance", "current<PERSON><PERSON><PERSON>d", "new<PERSON><PERSON><PERSON>d", "earnTransactions", "tx", "spendTransactions", "earnTotal", "sum", "spendTotal", "ratio", "culturalTransactions", "communityTransactions", "crossCulturalTransactions", "teachingTransactions", "learningTransactions", "timeBankingService", "SkillSharingDashboard", "onNavigateToMarketplace", "onNavigateToTimeBank", "user", "useAuth", "activeTab", "setActiveTab", "useState", "setSkillProfile", "mentorship<PERSON><PERSON><PERSON>", "setMentorshipMatches", "learningSessions", "setLearningSessions", "timeBankAccount", "setTimeBankAccount", "loading", "setLoading", "useEffect", "loadDashboardData", "<PERSON><PERSON><PERSON><PERSON>", "menteeMatches", "timeAccount", "sessionPromises", "allSessions", "a", "b", "renderOverview", "jsxs", "jsx", "m", "element", "renderMentoring", "skill", "context", "renderLearning", "goal", "renderSessions", "objective", "index", "star", "tab", "MarketplaceService", "listing", "listingId", "ctx", "marketplaceListing", "searchQuery", "listings", "query<PERSON><PERSON>er", "userProfile", "recommendations", "learningGoal", "skillListings", "culturalInterests", "culture", "culturalListings", "self", "l", "requesterId", "proposalMessage", "proposedTerms", "listingDoc", "knowledgeExchange", "exchangeRef", "updateData", "requester<PERSON><PERSON><PERSON>", "providerQuery", "requesterSnapshot", "providerSnapshot", "requesterExchanges", "providerExchanges", "listingRef", "currentViews", "cultures", "marketplaceService", "KnowledgeMarketplace", "onNavigateToSkillSharing", "setListings", "myListings", "setMyListings", "exchanges", "setExchanges", "setSearch<PERSON>uery", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedCulture", "setSelectedCulture", "categories", "loadMarketplaceData", "allListings", "userListings", "userExchanges", "handleSearch", "searchResults", "handleInitiateExchange", "renderBrowse", "e", "category", "c", "renderMyListings", "renderExchanges", "exchange", "renderCreate", "TimeBankingDashboard", "setTransactions", "fairExchangeMetrics", "setFairExchangeMetrics", "loadTimeBankingData", "transactionHistory", "renderTransactions", "renderMetrics", "Fragment", "KnowledgeExchangePage", "activeView", "setActiveView", "renderNavigation", "renderSkillSharing", "renderMarketplace", "renderTimeBanking"], "mappings": "8QAuPA,MAAMA,EAAoB,CAExB,MAAM,mBAAmBC,EAAgF,CACnG,GAAA,CACF,MAAMC,EAAY,iBAAiB,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAGlFC,EAAa,MAAMC,EAA0B,wBAAwB,CACzE,GAAIF,EACJ,MAAO,qBAAqBD,EAAQ,MAAM,GAC1C,QAAS,KAAK,UAAUA,EAAQ,qBAAqB,EACrD,KAAM,gBACN,QAASA,EAAQ,sBAAsB,eACvC,OAAQ,CACN,OAAQA,EAAQ,OAChB,oBAAqB,kBACvB,EACA,aAAc,CACZ,OAAQ,UACR,WAAY,CAAC,EACb,YAAa,CAAC,EACd,WAAY,IACd,EACA,WAAY,CACV,MAAO,EACP,MAAO,EACP,OAAQ,EACR,SAAU,EACV,mBAAoB,CACtB,EACA,UAAWI,EAAU,IAAI,EACzB,aAAcA,EAAU,IAAI,CAAA,CAC7B,EAEKC,EAA6B,CACjC,GAAGL,EACH,GAAIC,EACJ,UAAWG,EAAU,IAAI,EACzB,UAAWA,EAAU,IAAI,CAC3B,EAEA,aAAME,EAAOC,EAAIC,EAAI,iBAAkBP,CAAS,EAAGI,CAAY,EAExDJ,QACAQ,EAAO,CACN,cAAA,MAAM,gCAAiCA,CAAK,EAC9C,IAAI,MAAM,gCAAgC,CAAA,CAClD,CAIF,MAAM,mBAAmBR,EAAmBS,EAA+C,CACrF,GAAA,CACF,MAAMC,EAAaJ,EAAIC,EAAI,iBAAkBP,CAAS,EAEtD,MAAMW,EAAUD,EAAY,CAC1B,GAAGD,EACH,UAAWN,EAAU,IAAI,CAAA,CAC1B,QACMK,EAAO,CACN,cAAA,MAAM,gCAAiCA,CAAK,EAC9C,IAAI,MAAM,gCAAgC,CAAA,CAClD,CAIF,MAAM,wBAAwBI,EAA8C,CACtE,GAAA,CACF,MAAMC,EAAIC,EACRC,EAAWR,EAAI,gBAAgB,EAC/BS,EAAM,SAAU,KAAMJ,CAAM,EAC5BK,EAAM,CAAC,CACT,EAEMC,EAAW,MAAMC,EAAQN,CAAC,EAEhC,OAAIK,EAAS,MACJ,KAGFA,EAAS,KAAK,CAAC,EAAE,KAAK,QACtBV,EAAO,CACN,cAAA,MAAM,+BAAgCA,CAAK,EAC7C,IAAI,MAAM,6BAA6B,CAAA,CAC/C,CAIF,MAAM,cACJY,EACAC,EACAC,EACAC,EAAsB,GACG,CACrB,GAAA,CACF,IAAIV,EAAIC,EACNC,EAAWR,EAAI,gBAAgB,EAC/BS,EAAM,gBAAiB,qBAAsB,CAACI,CAAO,CAAC,EACtDI,EAAQ,2BAA4B,MAAM,EAC1CP,EAAMM,CAAW,CACnB,EAGIE,GADa,MAAMN,EAAQN,CAAC,GACT,KAAK,IAAIP,GAAOA,EAAI,MAAsB,EAG7D,OAAAe,GAAuBA,EAAoB,OAAS,IACtDI,EAAUA,EAAQ,UAChBJ,EAAoB,QAClBK,EAAO,sBAAsB,iBAAmBC,GAChDD,EAAO,sBAAsB,mBAAmB,KAAKE,GACnDA,EAAS,iBAAmBD,CAAA,CAC9B,CAEJ,GAGKF,QACAjB,EAAO,CACN,cAAA,MAAM,2BAA4BA,CAAK,EACzC,IAAI,MAAM,0BAA0B,CAAA,CAC5C,CAIF,MAAM,sBAAsBqB,EAAmE,CACzF,GAAA,CACF,MAAMC,EAAU,SAAS,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAExEC,EAAmC,CACvC,GAAGF,EACH,GAAIC,EACJ,UAAW3B,EAAU,IAAI,CAC3B,EAEA,aAAME,EAAOC,EAAIC,EAAI,qBAAsBuB,CAAO,EAAGC,CAAe,EAE7DD,QACAtB,EAAO,CACN,cAAA,MAAM,mCAAoCA,CAAK,EACjD,IAAI,MAAM,mCAAmC,CAAA,CACrD,CAIF,MAAM,wBAAwBwB,EAAuD,CAC/E,GAAA,CACF,MAAMC,EAAY,WAAW,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAE5EC,EAAmC,CACvC,GAAGF,EACH,GAAIC,CACN,EAEA,aAAM5B,EAAOC,EAAIC,EAAI,oBAAqB0B,CAAS,EAAGC,CAAe,EAE9DD,QACAzB,EAAO,CACN,cAAA,MAAM,qCAAsCA,CAAK,EACnD,IAAI,MAAM,qCAAqC,CAAA,CACvD,CAIF,MAAM,wBACJyB,EACAE,EACAC,EACAC,EACe,CACX,GAAA,CACF,MAAMC,EAAahC,EAAIC,EAAI,oBAAqB0B,CAAS,EAGnDD,EAAU,MAAMO,EAAOD,CAAU,EACnC,GAAA,CAACN,EAAQ,SACL,MAAA,IAAI,MAAM,mBAAmB,EAI/B,MAAAQ,EADcR,EAAQ,KAAK,EACA,SAAW,GAAM,GAC5CS,EAAoBN,EAAS,cAAgB,EAC7CO,EAAgBL,EAAiB,OAAS,EAAI,IAAM,EAEpDM,EAAqB,KAAK,MAAMH,EAAcC,EAAoBC,CAAa,EAErF,MAAM/B,EAAU2B,EAAY,CAC1B,SAAAH,EACA,cAAAC,EACA,iBAAAC,EACA,mBAAAM,EACA,OAAQ,WAAA,CACT,QAKMnC,EAAO,CACN,cAAA,MAAM,qCAAsCA,CAAK,EACnD,IAAI,MAAM,qCAAqC,CAAA,CACvD,CAIF,MAAM,yBAAyBI,EAAgBgC,EAAuD,CAChG,GAAA,CACI,MAAAC,EAAQD,IAAS,SAAW,WAAa,WAEzC/B,EAAIC,EACRC,EAAWR,EAAI,oBAAoB,EACnCS,EAAM6B,EAAO,KAAMjC,CAAM,EACzBY,EAAQ,YAAa,MAAM,CAC7B,EAGA,OADiB,MAAML,EAAQN,CAAC,GAChB,KAAK,IAAIP,GAAOA,EAAI,MAAyB,QACtDE,EAAO,CACN,cAAA,MAAM,yCAA0CA,CAAK,EACvD,IAAI,MAAM,uCAAuC,CAAA,CACzD,CAIF,MAAM,4BAA4BsB,EAA6C,CACzE,GAAA,CACF,MAAMjB,EAAIC,EACRC,EAAWR,EAAI,mBAAmB,EAClCS,EAAM,oBAAqB,KAAMc,CAAO,EACxCN,EAAQ,cAAe,MAAM,CAC/B,EAGA,OADiB,MAAML,EAAQN,CAAC,GAChB,KAAK,IAAIP,GAAOA,EAAI,MAAyB,QACtDE,EAAO,CACN,cAAA,MAAM,mCAAoCA,CAAK,EACjD,IAAI,MAAM,iCAAiC,CAAA,CACnD,CAIF,MAAM,4BACJsB,EACAgB,EACAC,EACe,CACX,GAAA,CACF,MAAMC,EAAW1C,EAAIC,EAAI,qBAAsBuB,CAAO,EAEtD,MAAMnB,EAAUqC,EAAU,CACxB,OAAAF,EACA,GAAIC,GAAS,CAAE,YAAaA,CAAM,EAClC,UAAW5C,EAAU,IAAI,CAAA,CAC1B,QACMK,EAAO,CACN,cAAA,MAAM,0CAA2CA,CAAK,EACxD,IAAI,MAAM,0CAA0C,CAAA,CAC5D,CAEJ,CAEa,MAAAyC,EAAsB,IAAInD,GCpVvC,MAAMoD,EAAmB,CAEvB,MAAM,sBAAsBtC,EAAiC,CACvD,GAAA,CACI,MAAAuC,EAAY,YAAYvC,CAAM,GAE9BwC,EAA2B,CAC/B,GAAID,EACJ,OAAAvC,EACA,QAAS,IACT,YAAa,IACb,WAAY,EACZ,cAAe,EACf,kBAAmB,EACnB,kBAAmB,GACnB,gBAAiB,GACjB,cAAe,SACf,UAAWT,EAAU,IAAI,EACzB,aAAcA,EAAU,IAAI,CAC9B,EAEA,aAAME,EAAOC,EAAIC,EAAI,oBAAqB4C,CAAS,EAAGC,CAAO,EAG7D,MAAM,KAAK,kBAAkB,CAC3B,KAAM,QACN,SAAUxC,EACV,OAAQ,IACR,WAAY,IACZ,YAAa,CAAC,EACd,YAAa,2CACb,SAAU,CACR,QAAS,QACT,UAAW,eACb,EACA,OAAQ,YACR,UAAWT,EAAU,IAAI,EACzB,SAAU,CACR,WAAY,WACZ,iBAAkB,CAAC,mBAAmB,EACtC,iBAAkB,CAAC,qBAAqB,CAAA,CAC1C,CACD,EAEMgD,QACA3C,EAAO,CACN,cAAA,MAAM,oCAAqCA,CAAK,EAClD,IAAI,MAAM,oCAAoC,CAAA,CACtD,CAIF,MAAM,iBACJ6C,EAQAC,EAC4B,CACxB,GAAA,CAEI,MAAAd,EAAea,EAAe,SAAW,GAAM,GAG/CE,EAAkB,MAAM,KAAK,mBAAmBD,CAAU,EAC1Db,EAAoBc,GAAiB,mBAAqB,EAGhE,IAAIb,EAAgB,EAChBW,EAAe,iBAAiB,wBAClBX,EAAAF,GAAee,GAAiB,mBAAqB,KAWvE,MAAMC,EAAuBhB,GAPC,CAC5B,SAAY,EACZ,aAAgB,IAChB,SAAY,IACZ,OAAU,EACV,OAAU,GACZ,EACkEa,EAAe,UAAgD,EAAI,GAG/HI,EAAmB,EAGzB,IAAIC,EAA6B,EAC7BL,EAAe,iBAAiB,mBAClCK,EAA6BlB,EAAc,KAI7C,IAAImB,EAAa,EACbN,EAAe,kBAAoBA,EAAe,iBAAmB,IAC1DM,EAAAnB,EAAc,IAAOa,EAAe,iBAAmB,IAGtE,MAAMO,EAAe,KAAK,MACxBpB,EAAcC,EAAoBgB,EAClCf,EACAc,EACAE,EACAC,CACF,EAEME,EAAoC,CACxC,CAAE,UAAW,eAAgB,MAAOrB,EAAa,YAAa,GAAGa,EAAe,QAAQ,6BAA8B,EACtH,CAAE,UAAW,qBAAsB,MAAOZ,EAAmB,YAAa,iCAAiCc,GAAiB,iBAAmB,EAAE,OAAQ,EACzJ,CAAE,UAAW,iBAAkB,MAAOb,EAAe,YAAa,+BAAgC,EAClG,CAAE,UAAW,mBAAoB,MAAOc,EAAsB,YAAa,GAAGH,EAAe,UAAU,cAAe,EACtH,CAAE,UAAW,yBAA0B,MAAOK,EAA4B,YAAa,yBAA0B,EACjH,CAAE,UAAW,gBAAiB,MAAOC,EAAY,YAAa,GAAGN,EAAe,kBAAoB,CAAC,eAAgB,CACvH,EAEO,MAAA,CACL,YAAAb,EACA,kBAAAC,EACA,cAAAC,EACA,qBAAAc,EACA,iBAAAC,EACA,2BAAAC,EACA,aAAAE,EACA,UAAAC,CACF,QACOrD,EAAO,CACN,cAAA,MAAM,6BAA8BA,CAAK,EAC3C,IAAI,MAAM,6BAA6B,CAAA,CAC/C,CAIF,MAAM,kBAAkBsD,EAA2D,CAC7E,GAAA,CACF,MAAMC,EAAgB,MAAM,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAE3EC,EAAmC,CACvC,GAAGF,EACH,GAAIC,CACN,EAGM,aAAAE,EAAe1D,EAAI,MAAO2D,GAAyB,CAEvD,MAAMC,EAAiB7D,EAAIC,EAAI,oBAAqBwD,CAAa,EAC5CG,EAAA,IAAIC,EAAgBH,CAAe,EAGpDF,EAAY,OAAS,QAAUA,EAAY,OAAS,QACtD,MAAM,KAAK,qBAAqBA,EAAY,SAAUA,EAAY,OAAQ,MAAOI,CAAoB,EAC5FJ,EAAY,OAAS,SACxB,MAAA,KAAK,qBAAqBA,EAAY,SAAU,CAACA,EAAY,OAAQ,WAAYI,CAAoB,EACvGJ,EAAY,YACd,MAAM,KAAK,qBAAqBA,EAAY,WAAYA,EAAY,OAAQ,MAAOI,CAAoB,GAEhGJ,EAAY,OAAS,UAC9B,MAAM,KAAK,sBAAsBA,EAAY,SAAUA,EAAY,OAAQ,MAAOI,CAAoB,EAC7FJ,EAAY,OAAS,WACxB,MAAA,KAAK,sBAAsBA,EAAY,SAAU,CAACA,EAAY,OAAQ,WAAYI,CAAoB,CAC9G,CACD,EAEMH,QACAvD,EAAO,CACN,cAAA,MAAM,+BAAgCA,CAAK,EAC7C,IAAI,MAAM,8BAA8B,CAAA,CAChD,CAIF,MAAM,mBAAmBI,EAAiD,CACpE,GAAA,CACF,MAAMwD,EAAa9D,EAAIC,EAAI,oBAAqB,YAAYK,CAAM,EAAE,EAC9DyD,EAAa,MAAM9B,EAAO6B,CAAU,EAEtC,OAAAC,EAAW,SACNA,EAAW,KAAK,EAGlB,WACA7D,EAAO,CACN,cAAA,MAAM,mCAAoCA,CAAK,EACjD,IAAI,MAAM,iCAAiC,CAAA,CACnD,CAIF,MAAM,0BAA0BI,EAAgBW,EAAsB,GAAgC,CAChG,GAAA,CACF,MAAMV,EAAIC,EACRC,EAAWR,EAAI,mBAAmB,EAClCS,EAAM,WAAY,KAAMJ,CAAM,EAC9BY,EAAQ,YAAa,MAAM,EAC3BP,EAAMM,CAAW,CACnB,EAGA,OADiB,MAAMJ,EAAQN,CAAC,GAChB,KAAK,IAAIP,GAAOA,EAAI,MAAyB,QACtDE,EAAO,CACN,cAAA,MAAM,0CAA2CA,CAAK,EACxD,IAAI,MAAM,wCAAwC,CAAA,CAC1D,CAIF,MAAM,uBACJ8D,EACAC,EACAC,EACAC,EACApB,EACiB,CACb,GAAA,CAEF,MAAMqB,EAAe,MAAM,KAAK,mBAAmBH,CAAO,EAC1D,GAAI,CAACG,GAAgBA,EAAa,QAAUD,EACpC,MAAA,IAAI,MAAM,sBAAsB,EAIxC,MAAME,EAAoB,MAAM,KAAK,iBAAiBtB,EAAgBmB,CAAO,EAgCtE,OA7Be,MAAM,KAAK,kBAAkB,CACjD,KAAM,QACN,WAAYA,EACZ,SAAUD,EACV,OAAQI,EAAkB,aAC1B,WAAYA,EAAkB,YAC9B,YAAa,CACX,CAAE,KAAM,UAAW,MAAOA,EAAkB,kBAAmB,OAAQ,0BAA2B,UAAW,QAAS,EACtH,CAAE,KAAM,WAAY,MAAOA,EAAkB,cAAe,OAAQ,0BAA2B,UAAW,QAAS,EACnH,CAAE,KAAM,aAAc,MAAOA,EAAkB,qBAAsB,OAAQ,mBAAoB,UAAW,QAAS,CACvH,EACA,YAAa,mCAAmCL,CAAU,GAC1D,SAAU,CACR,QAAS,iBACT,iBAAkBjB,EAAe,iBAAiB,cACpD,EACA,kBAAmBiB,EACnB,gBAAiBjB,EAAe,gBAChC,OAAQ,YACR,UAAWlD,EAAU,IAAI,EACzB,SAAU,CACR,gBAAiBkD,EAAe,SAChC,iBAAkBA,EAAe,kBAAoB,EACrD,WAAYA,EAAe,WAC3B,iBAAkBA,EAAe,iBAAiB,kBAAoB,CAAC,EACvE,iBAAkBA,EAAe,kBAAoB,CAAA,CAAC,CACxD,CACD,QAGM7C,EAAO,CACN,cAAA,MAAM,qCAAsCA,CAAK,EACnD,IAAI,MAAM,oCAAoC,CAAA,CACtD,CAIF,MAAM,kBAAkBoE,EAAiE,CACnF,GAAA,CACF,MAAMC,EAAY,WAAW,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAE5EC,EAA2B,CAC/B,GAAGF,EACH,GAAIC,EACJ,UAAW1E,EAAU,IAAI,CAC3B,EAEA,aAAME,EAAOC,EAAIC,EAAI,gBAAiBsE,CAAS,EAAGC,CAAW,EAGzDF,EAAQ,eACJ,MAAA,KAAK,kBAAkBA,EAAQ,aAAa,EAG7CC,QACArE,EAAO,CACN,cAAA,MAAM,+BAAgCA,CAAK,EAC7C,IAAI,MAAM,+BAA+B,CAAA,CACjD,CAIF,MAAM,mBACJqE,EACAE,EACAC,EACe,CACX,GAAA,CACF,MAAMC,EAAa3E,EAAIC,EAAI,gBAAiBsE,CAAS,EAUjD,GARJ,MAAMlE,EAAUsE,EAAY,CAC1B,OAAQ,WACR,WAAAF,EACA,WAAAC,EACA,WAAY7E,EAAU,IAAI,CAAA,CAC3B,EAGG4E,EAAW,mBAAqB,EAAG,CAC/B,MAAAG,EAAa,MAAM3C,EAAO0C,CAAU,EACtC,GAAAC,EAAW,SAAU,CACjB,MAAAN,EAAUM,EAAW,KAAK,EAEhC,MAAM,KAAK,kBAAkB,CAC3B,KAAMH,EAAW,iBAAmB,EAAI,QAAU,UAClD,SAAUH,EAAQ,gBAClB,OAAQ,KAAK,IAAIG,EAAW,gBAAgB,EAC5C,WAAY,KAAK,IAAIA,EAAW,gBAAgB,EAChD,YAAa,CAAC,EACd,YAAa,kCAAkCA,EAAW,WAAW,GACrE,SAAU,CACR,QAAS,QACT,UAAW,oBACb,EACA,kBAAmBH,EAAQ,cAC3B,OAAQ,YACR,UAAWzE,EAAU,IAAI,EACzB,SAAU,CACR,WAAY,MACZ,iBAAkB,CAAC,EACnB,iBAAkB,CAAC,kBAAkB,CAAA,CACvC,CACD,CAAA,CACH,QAEKK,EAAO,CACN,cAAA,MAAM,gCAAiCA,CAAK,EAC9C,IAAI,MAAM,gCAAgC,CAAA,CAClD,CAIF,MAAM,6BAA6BI,EAA8C,CAC3E,GAAA,CAEF,MAAMuE,EAAe,MAAM,KAAK,0BAA0BvE,EAAQ,GAAG,EAG/DwE,EAAiB,KAAK,wBAAwBD,CAAY,EAC1DE,EAAuB,KAAK,8BAA8BF,CAAY,EACtEG,EAAkB,KAAK,yBAAyBH,CAAY,EAC5DI,EAAwB,KAAK,+BAA+BJ,CAAY,EACxEK,EAAmB,KAAK,0BAA0BL,CAAY,EAC9DM,EAAqB,KAAK,4BAA4BN,CAAY,EAElEO,EAAuB,KAAK,OAC/BN,EAAiBC,EAAuBC,EACxCC,EAAwBC,EAAmBC,GAAsB,CACpE,EAEME,EAA+B,CACnC,OAAA/E,EACA,eAAAwE,EACA,qBAAAC,EACA,gBAAAC,EACA,sBAAAC,EACA,iBAAAC,EACA,mBAAAC,EACA,qBAAAC,EACA,eAAgBvF,EAAU,IAAI,CAChC,EAGA,aAAME,EAAOC,EAAIC,EAAI,wBAAyBK,CAAM,EAAG+E,CAAO,EAEvDA,QACAnF,EAAO,CACN,cAAA,MAAM,2CAA4CA,CAAK,EACzD,IAAI,MAAM,2CAA2C,CAAA,CAC7D,CAIF,MAAc,qBACZI,EACA6D,EACAmB,EACA1B,EACe,CACf,MAAME,EAAa9D,EAAIC,EAAI,oBAAqB,YAAYK,CAAM,EAAE,EAEpE,GAAIsD,EAAsB,CACxB,MAAMG,EAAa,MAAMH,EAAqB,IAAIE,CAAU,EACxD,GAAAC,EAAW,SAAU,CACjB,MAAAwB,EAAiBxB,EAAW,KAAA,EAAO,QACnCyB,EAAaF,IAAc,MAAQC,EAAiBpB,EAASoB,EAAiBpB,EAEpFP,EAAqB,OAAOE,EAAY,CACtC,QAAS,KAAK,IAAI,EAAG0B,CAAU,EAC/B,aAAc3F,EAAU,IAAI,EAC5B,GAAIyF,IAAc,OAAS,CAAE,YAAavB,EAAW,KAAO,EAAA,YAAcI,CAAO,EACjF,GAAImB,IAAc,YAAc,CAAE,WAAYvB,EAAW,KAAA,EAAO,WAAaI,CAAO,CAAA,CACrF,CAAA,CACH,KACK,CACC,MAAAJ,EAAa,MAAM9B,EAAO6B,CAAU,EACtC,GAAAC,EAAW,SAAU,CACjB,MAAAwB,EAAiBxB,EAAW,KAAA,EAAO,QACnCyB,EAAaF,IAAc,MAAQC,EAAiBpB,EAASoB,EAAiBpB,EAEpF,MAAM9D,EAAUyD,EAAY,CAC1B,QAAS,KAAK,IAAI,EAAG0B,CAAU,EAC/B,aAAc3F,EAAU,IAAI,EAC5B,GAAIyF,IAAc,OAAS,CAAE,YAAavB,EAAW,KAAO,EAAA,YAAcI,CAAO,EACjF,GAAImB,IAAc,YAAc,CAAE,WAAYvB,EAAW,KAAA,EAAO,WAAaI,CAAO,CAAA,CACrF,CAAA,CACH,CACF,CAGF,MAAc,sBACZ7D,EACA6D,EACAmB,EACA1B,EACe,CACf,MAAME,EAAa9D,EAAIC,EAAI,oBAAqB,YAAYK,CAAM,EAAE,EAEpE,GAAIsD,EAAsB,CACxB,MAAMG,EAAa,MAAMH,EAAqB,IAAIE,CAAU,EACxD,GAAAC,EAAW,SAAU,CACjB,MAAA0B,EAAkB1B,EAAW,KAAA,EAAO,cACpC2B,EAAcJ,IAAc,MAAQG,EAAkBtB,EAASsB,EAAkBtB,EAEvFP,EAAqB,OAAOE,EAAY,CACtC,cAAe,KAAK,IAAI,EAAG4B,CAAW,EACtC,aAAc7F,EAAU,IAAI,CAAA,CAC7B,CAAA,CACH,CACF,CAGF,MAAc,kBAAkB4D,EAAsC,CACpE,MAAMI,EAAiB7D,EAAIC,EAAI,oBAAqBwD,CAAa,EACjE,MAAMpD,EAAUwD,EAAgB,CAC9B,OAAQ,UAAA,CACT,CAAA,CAGK,wBAAwBgB,EAAyC,CAGvE,MAAMc,EAAmBd,EAAa,OAAae,GAAAA,EAAG,OAAS,MAAM,EAC/DC,EAAoBhB,EAAa,OAAae,GAAAA,EAAG,OAAS,OAAO,EAEvE,GAAID,EAAiB,SAAW,GAAKE,EAAkB,SAAW,EAAU,MAAA,IAEtE,MAAAC,EAAYH,EAAiB,OAAO,CAACI,EAAKH,IAAOG,EAAMH,EAAG,OAAQ,CAAC,EACnEI,EAAaH,EAAkB,OAAO,CAACE,EAAKH,IAAOG,EAAMH,EAAG,OAAQ,CAAC,EAGrEK,EAAQH,EAAY,EAAIE,EAAaF,EAAY,EACvD,OAAO,KAAK,IAAI,IAAK,KAAK,IAAI,EAAG,IAAM,KAAK,IAAIG,EAAQ,CAAC,EAAI,GAAG,CAAC,CAAA,CAG3D,8BAA8BpB,EAAyC,CAC7E,MAAMqB,EAAuBrB,EAAa,OACxCe,GAAAA,EAAG,iBAAiB,uBACpBA,EAAG,iBAAiB,4BACtB,EAEO,OAAA,KAAK,IAAI,IAAMM,EAAqB,OAAS,KAAK,IAAIrB,EAAa,OAAQ,CAAC,EAAK,GAAG,CAAA,CAGrF,yBAAyBA,EAAyC,CACxE,MAAMsB,EAAwBtB,EAAa,UACzCe,EAAG,iBAAiB,kBACpBA,EAAG,SAAS,YAAc,mBAC5B,EAEO,OAAA,KAAK,IAAI,IAAMO,EAAsB,OAAS,KAAK,IAAItB,EAAa,OAAQ,CAAC,EAAK,GAAG,CAAA,CAGtF,+BAA+BA,EAAyC,CAC9E,MAAMuB,EAA4BvB,EAAa,OAAOe,GACpDA,EAAG,iBAAiB,qBACtB,EAEO,OAAA,KAAK,IAAI,IAAMQ,EAA0B,OAAS,KAAK,IAAIvB,EAAa,OAAQ,CAAC,EAAK,GAAG,CAAA,CAG1F,0BAA0BA,EAAyC,CACzE,MAAMwB,EAAuBxB,EAAa,UACxCe,EAAG,OAAS,QAAUA,EAAG,SAAS,UAAY,gBAChD,EAEO,OAAA,KAAK,IAAI,IAAMS,EAAqB,OAAS,KAAK,IAAIxB,EAAa,OAAQ,CAAC,EAAK,GAAG,CAAA,CAGrF,4BAA4BA,EAAyC,CAC3E,MAAMyB,EAAuBzB,EAAa,UACxCe,EAAG,OAAS,SAAWA,EAAG,SAAS,UAAY,gBACjD,EAEO,OAAA,KAAK,IAAI,IAAMU,EAAqB,OAAS,KAAK,IAAIzB,EAAa,OAAQ,CAAC,EAAK,GAAG,CAAA,CAE/F,CAEa,MAAA0B,EAAqB,IAAI3D,GCrpBzB4D,GAA8D,CAAC,CAC1E,wBAAAC,EACA,qBAAAC,CACF,IAAM,CACE,KAAA,CAAE,KAAAC,CAAK,EAAIC,EAAQ,EACnB,CAACC,EAAWC,CAAY,EAAIC,EAAAA,SAA6D,UAAU,EACnG,CAACjH,EAAckH,CAAe,EAAID,EAAAA,SAA8B,IAAI,EACpE,CAACE,EAAmBC,CAAoB,EAAIH,EAAAA,SAA4B,CAAA,CAAE,EAC1E,CAACI,EAAkBC,CAAmB,EAAIL,EAAAA,SAA4B,CAAA,CAAE,EACxE,CAACM,EAAiBC,CAAkB,EAAIP,EAAAA,SAAiC,IAAI,EAC7E,CAACQ,EAASC,CAAU,EAAIT,EAAAA,SAAS,EAAI,EAE3CU,EAAAA,UAAU,IAAM,CACVd,GACgBe,EAAA,CACpB,EACC,CAACf,CAAI,CAAC,EAET,MAAMe,EAAoB,SAAY,CACpC,GAAKf,EAEL,CAAAa,EAAW,EAAI,EACX,GAAA,CACI,KAAA,CAAC/H,EAASkI,EAAeC,EAAeC,CAAW,EAAI,MAAM,QAAQ,IAAI,CAC7ElF,EAAoB,wBAAwBgE,EAAK,GAAG,EACpDhE,EAAoB,yBAAyBgE,EAAK,IAAK,QAAQ,EAC/DhE,EAAoB,yBAAyBgE,EAAK,IAAK,QAAQ,EAC/DJ,EAAmB,mBAAmBI,EAAK,GAAG,CAAA,CAC/C,EAOD,GALAK,EAAgBvH,CAAO,EACvByH,EAAqB,CAAC,GAAGS,EAAe,GAAGC,CAAa,CAAC,EACzDN,EAAmBO,CAAW,EAG1BF,EAAc,OAAS,GAAKC,EAAc,OAAS,EAAG,CAExD,MAAME,EADa,CAAC,GAAGH,EAAe,GAAGC,CAAa,EACnB,MAAM,EAAG,CAAC,EAAE,IAC7CrG,GAAAoB,EAAoB,4BAA4BpB,EAAM,EAAE,CAC1D,EAEMwG,GADiB,MAAM,QAAQ,IAAID,CAAe,GACrB,KAAA,EAAO,KAAK,CAACE,EAAGC,IACjDA,EAAE,YAAY,QAAUD,EAAE,YAAY,OACxC,EACAZ,EAAoBW,EAAY,MAAM,EAAG,EAAE,CAAC,CAAA,QAEvC7H,EAAO,CACN,QAAA,MAAM,gCAAiCA,CAAK,CAAA,QACpD,CACAsH,EAAW,EAAK,CAAA,EAEpB,EAEMU,EAAiB,IACpBC,OAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,SAChC,MACC,CAAA,SAAA,CAAAA,MAAC,OAAI,UAAU,mCACZ,SAActI,GAAA,cAAc,QAAU,EACzC,EACCsI,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAe,iBAAA,CAAA,CAAA,CACxD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,SAChC,MACC,CAAA,SAAA,CAAAA,MAAC,OAAI,UAAU,oCACZ,SAActI,GAAA,cAAc,QAAU,EACzC,EACCsI,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAe,iBAAA,CAAA,CAAA,CACxD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,SAChC,MACC,CAAA,SAAA,CAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,qCACZ,SAAkBnB,EAAA,UAAYoB,EAAE,SAAW,QAAQ,EAAE,MACxD,CAAA,EACCD,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAkB,oBAAA,CAAA,CAAA,CAC3D,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAC,IAAA,SAC/B,MACC,CAAA,SAAA,CAAAA,MAAC,MAAI,CAAA,UAAU,qCACZ,SAAAf,GAAiB,SAAW,EAC/B,EACCe,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAY,cAAA,CAAA,CAAA,CACrD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wEACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAa,gBAAA,EACvDA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,wEAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,iFAAiF,SAEnG,gBAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,0EACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAe,kBAAA,EACzDA,EAAA,IAAA,IAAA,CAAE,UAAU,sBAAsB,SAEnC,mEAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,mFAAmF,SAErG,iBAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,4EACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAmB,sBAAA,EAC7DA,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAEpC,gEAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAAS3B,EACT,UAAU,qFACX,SAAA,mBAAA,CAAA,CAED,CACF,CAAA,CAAA,EACF,EAGA0B,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAwB,2BAAA,EACjFD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACZ,SAAA,CAAiBhB,EAAA,MAAM,EAAG,CAAC,EAAE,IAC5BzF,GAAAyG,EAAAA,KAAC,MAAqB,CAAA,UAAU,8DAC9B,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,4BACX,SAAA,CAAQzG,EAAA,YAAY,OAAO,CAAC,EAAE,YAAgB,EAAAA,EAAQ,YAAY,MAAM,CAAC,EAAE,UAAA,EAC9E,EACAyG,EAAAA,KAAC,IAAE,CAAA,UAAU,wBACV,SAAA,CAAQzG,EAAA,SAAS,cAAYA,EAAQ,QAAA,EACxC,QACC,MAAI,CAAA,UAAU,yBACZ,SAAAA,EAAQ,iBAAiB,IAAI4G,GAC3BF,EAAA,IAAA,OAAA,CAA2B,UAAU,+DACnC,SAAAE,EAAQ,SADAA,EAAQ,OAEnB,CACD,CACH,CAAA,CAAA,EACF,EACAH,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAW,uBACd1G,EAAQ,SAAW,YAAc,iBACjCA,EAAQ,SAAW,YAAc,gBACjCA,EAAQ,SAAW,cAAgB,kBACnC,eACF,GACG,SAAQA,EAAA,OAAO,QAAQ,IAAK,GAAG,EAAE,YACpC,CAAA,CAAA,EACC0G,EAAAA,IAAA,MAAA,CAAI,UAAU,wBACZ,SAAI,IAAA,KAAK1G,EAAQ,YAAY,QAAU,GAAI,EAAE,mBAAA,CAChD,CAAA,CAAA,CACF,CAAA,CAAA,GA5BQA,EAAQ,EA6BlB,CACD,EAEAyF,EAAiB,SAAW,GAC1BgB,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EACjCA,EAAAA,IAAC,KAAE,SAAwB,0BAAA,CAAA,EAC1BA,EAAA,IAAA,IAAA,CAAE,UAAU,UAAU,SAAkD,oDAAA,CAAA,CAAA,CAC3E,CAAA,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIG,EAAkB,IACrBJ,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAyB,4BAAA,EAC5EA,EAAA,IAAA,SAAA,CAAO,UAAU,kFAAkF,SAEpG,yBAAA,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAgB,mBAAA,EACjEA,EAAA,IAAC,MAAI,CAAA,UAAU,wCACZ,SAAAtI,GAAc,cAAc,IAC3B0I,GAAAL,EAAAA,KAAC,MAAwB,CAAA,UAAU,wCACjC,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAI,EAAM,UAAU,EAC1DJ,EAAA,IAAA,OAAA,CAAK,UAAW,kCACfI,EAAM,mBAAqB,UAAYA,EAAM,mBAAqB,SAC9D,8BACA,2BACN,GACG,WAAM,gBACT,CAAA,CAAA,EACF,EACAL,EAAAA,KAAC,IAAE,CAAA,UAAU,6BACV,SAAA,CAAMK,EAAA,kBAAkB,mBAAA,EAC3B,EACCA,EAAM,iBAAmBA,EAAM,gBAAgB,OAAS,GACvDJ,MAAC,MAAI,CAAA,UAAU,uBACZ,SAAAI,EAAM,gBAAgB,IAAIC,GACxBL,EAAA,IAAA,OAAA,CAA2B,UAAU,0DACnC,WAAQ,OADA,EAAAK,EAAQ,OAEnB,CACD,CACH,CAAA,EAEFN,EAAAA,KAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,wBACb,SAAA,CAAMK,EAAA,mBAAmB,eAAA,EAC5B,EACCJ,EAAA,IAAA,SAAA,CAAO,UAAU,4CAA4C,SAE9D,MAAA,CAAA,CAAA,CACF,CAAA,CAAA,GA9BQI,EAAM,OA+BhB,CACD,GACEL,EAAAA,KAAA,MAAA,CAAI,UAAU,4CACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EACjCA,EAAAA,IAAC,KAAE,SAA4B,8BAAA,CAAA,EAC9BA,EAAA,IAAA,SAAA,CAAO,UAAU,yCAAyC,SAE3D,+BAAA,CAAA,CAAA,CAAA,CACF,CAEJ,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAY,eAAA,EAC7DA,EAAAA,IAAC,MAAI,CAAA,UAAU,YACZ,SAAAnB,EACE,OAAgB1F,GAAAA,EAAM,WAAaoF,GAAM,KAAOpF,EAAM,SAAW,QAAQ,EACzE,IAAIA,GACF6G,EAAAA,IAAA,MAAA,CAAmB,UAAU,wCAC5B,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,4BACX,SAAA,CAAM5G,EAAA,QAAQ,aAAA,EACjB,EACA4G,EAAAA,KAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,CAAA,aACxB5G,EAAM,eAAe,cAAc,MAC7CA,EAAM,eAAe,kBAAkB,qBAAA,EAC1C,EACCA,EAAM,iBACJ4G,OAAA,OAAA,CAAK,UAAU,4EACb,SAAA,CAAM5G,EAAA,gBAAgB,mBAAA,CACzB,CAAA,CAAA,EAEJ,EACA4G,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,qEAAqE,SAEvF,mBAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,kFAAkF,SAEpG,eAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAxBQ,EAAA7G,EAAM,EAyBhB,CACD,CACL,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGImH,EAAiB,IACpBP,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAqB,wBAAA,EACxEA,EAAA,IAAA,SAAA,CAAO,UAAU,oFAAoF,SAEtG,mBAAA,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAc,iBAAA,EAC/DA,EAAA,IAAC,MAAI,CAAA,UAAU,wCACZ,SAAAtI,GAAc,cAAc,IAC3B6I,GAAAR,EAAAA,KAAC,MAAuB,CAAA,UAAU,wCAChC,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAO,EAAK,UAAU,EAC1DR,EAAAA,KAAC,OAAK,CAAA,UAAU,sDACb,SAAA,CAAKQ,EAAA,aAAa,MAAIA,EAAK,WAAA,CAC9B,CAAA,CAAA,EACF,EACCP,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA8B,WAAK,WAAW,EAC3DD,EAAAA,KAAC,MAAI,CAAA,UAAU,6BAA6B,SAAA,CAAA,oBACxBQ,EAAK,eAAe,aAAA,EACxC,EACCA,EAAK,qBAAuBA,EAAK,oBAAoB,OAAS,GAC5DP,MAAA,MAAA,CAAI,UAAU,4BACZ,WAAK,oBAAoB,OACvBD,OAAA,OAAA,CAAwB,UAAU,0DAChC,SAAA,CAAK9G,EAAA,QAAQ,KAAGA,EAAK,SAAS,aADtB,CAAA,EAAAA,EAAK,OAEhB,CACD,EACH,EAED+G,EAAA,IAAA,SAAA,CAAO,UAAU,8EAA8E,SAEhG,aAAA,CAAA,CAAA,GAtBQO,EAAK,OAuBf,CACD,GACER,EAAAA,KAAA,MAAA,CAAI,UAAU,4CACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EACjCA,EAAAA,IAAC,KAAE,SAAyB,2BAAA,CAAA,EAC3BA,EAAA,IAAA,SAAA,CAAO,UAAU,2CAA2C,SAE7D,8BAAA,CAAA,CAAA,CAAA,CACF,CAEJ,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAY,eAAA,EAC7DA,EAAAA,IAAC,MAAI,CAAA,UAAU,YACZ,SAAAnB,EACE,OAAgB1F,GAAAA,EAAM,WAAaoF,GAAM,KAAOpF,EAAM,SAAW,QAAQ,EACzE,IAAIA,GACF6G,EAAAA,IAAA,MAAA,CAAmB,UAAU,wCAC5B,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,4BACX,SAAA,CAAM5G,EAAA,QAAQ,WAAA,EACjB,EACA4G,EAAAA,KAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,CAAA,aACxB5G,EAAM,eAAe,cAAc,MAC7CA,EAAM,eAAe,kBAAkB,qBAAA,EAC1C,QACC,MAAI,CAAA,UAAU,OACb,SAAC6G,MAAA,MAAA,CAAI,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,gCACV,MAAO,CAAE,MAAO,GAAG7G,EAAM,eAAe,aAAa,GAAI,CAAA,GAE7D,CACF,CAAA,EACCA,EAAM,iBACJ4G,OAAA,OAAA,CAAK,UAAU,4EACb,SAAA,CAAM5G,EAAA,gBAAgB,oBAAA,CACzB,CAAA,CAAA,EAEJ,EACA4G,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,uEAAuE,SAEzF,oBAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,kFAAkF,SAEpG,cAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAhCQ,EAAA7G,EAAM,EAiChB,CACD,CACL,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIqH,EAAiB,IACpBT,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAiB,oBAAA,EACpEA,EAAA,IAAA,SAAA,CAAO,UAAU,sFAAsF,SAExG,sBAAA,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,YACZ,SAAA,CAAAhB,EAAiB,IAAIzF,GACnByG,EAAAA,KAAA,MAAA,CAAqB,UAAU,wCAC9B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,4BACX,SAAA,CAAQzG,EAAA,YAAY,OAAO,CAAC,EAAE,YAAgB,EAAAA,EAAQ,YAAY,MAAM,CAAC,EAAE,UAAA,EAC9E,EACC0G,EAAAA,IAAA,IAAA,CAAE,UAAU,wBACV,SAAI,IAAA,KAAK1G,EAAQ,YAAY,QAAU,GAAI,EAAE,eAAA,CAChD,CAAA,CAAA,EACF,EACA0G,EAAAA,IAAC,QAAK,UAAW,kCACf1G,EAAQ,SAAW,YAAc,8BACjCA,EAAQ,SAAW,YAAc,4BACjCA,EAAQ,SAAW,cAAgB,gCACnC,2BACF,GACG,WAAQ,OAAO,QAAQ,IAAK,GAAG,CAClC,CAAA,CAAA,EACF,EAEAyG,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,oCAAoC,SAAS,YAAA,EAC7DD,EAAAA,KAAC,OAAK,CAAA,UAAU,6BAA8B,SAAA,CAAQzG,EAAA,SAAS,UAAA,CAAQ,CAAA,CAAA,EACzE,SACC,MACC,CAAA,SAAA,CAAC0G,EAAA,IAAA,OAAA,CAAK,UAAU,oCAAoC,SAAS,YAAA,EAC5DA,EAAA,IAAA,OAAA,CAAK,UAAU,6BAA8B,WAAQ,QAAS,CAAA,CAAA,EACjE,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,OAAA,CAAK,UAAU,oCAAoC,SAAQ,WAAA,QAC3D,OAAK,CAAA,UAAU,6BAA8B,SAAA1G,EAAQ,oBAAsB,CAAE,CAAA,CAAA,CAChF,CAAA,CAAA,EACF,EAECA,EAAQ,mBAAmB,OAAS,GAClCyG,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,oCAAoC,SAAW,cAAA,QAC9D,KAAG,CAAA,UAAU,kCACX,SAAA1G,EAAQ,mBAAmB,IAAI,CAACmH,EAAWC,UACzC,KAAe,CAAA,UAAU,YAAa,SAA9BD,CAAA,EAAAC,CAAwC,CAClD,CACH,CAAA,CAAA,EACF,EAGDpH,EAAQ,iBAAiB,OAAS,SAChC,MAAI,CAAA,UAAU,4BACZ,SAAAA,EAAQ,iBAAiB,IAAI4G,GAC3BH,EAAAA,KAAA,OAAA,CAA2B,UAAU,0DACnC,SAAA,CAAQG,EAAA,QAAQ,UADR,CAAA,EAAAA,EAAQ,OAEnB,CACD,EACH,EAGD5G,EAAQ,SAAW,aAAeA,EAAQ,UACzC0G,EAAA,IAAC,MAAI,CAAA,UAAU,4BACb,SAAAD,OAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,oCAAoC,SAAe,kBAAA,EACnEA,EAAA,IAAC,MAAI,CAAA,UAAU,oBACZ,SAAA,CAAC,EAAG,EAAG,EAAG,EAAG,CAAC,EAAE,IAAIW,GAClBX,EAAA,IAAA,OAAA,CAAgB,UAAW,WAC1BW,GAAQrH,EAAQ,SAAS,cAAgB,kBAAoB,eAC/D,GAAI,SAAA,GAAA,EAFOqH,CAIX,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,GAtEMrH,EAAQ,EAwElB,CACD,EAEAyF,EAAiB,SAAW,GAC1BgB,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EACjCA,EAAAA,IAAC,KAAE,SAAyB,2BAAA,CAAA,EAC3BA,EAAA,IAAA,IAAA,CAAE,UAAU,UAAU,SAAkD,oDAAA,CAAA,CAAA,CAC3E,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,CAAA,EACF,EAGF,OAAIb,EAEAa,MAAC,OAAI,UAAU,yCACb,eAAC,MAAI,CAAA,UAAU,+DAA+D,CAChF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAA0B,6BAAA,EAC/EA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,oGAAA,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,gCACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,iBACZ,SAAA,CACC,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,IAAK,EAChD,CAAE,GAAI,YAAa,MAAO,YAAa,KAAM,IAAK,EAClD,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,IAAK,EAChD,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,IAAK,CAAA,EAChD,IACAY,GAAAb,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMrB,EAAakC,EAAI,EAAS,EACzC,UAAW,4CACTnC,IAAcmC,EAAI,GACd,gCACA,4EACN,GAEA,SAAA,CAAAZ,EAAA,IAAC,OAAK,CAAA,UAAU,OAAQ,SAAAY,EAAI,KAAK,EAChCA,EAAI,KAAA,CAAA,EATAA,EAAI,EAAA,CAWZ,EACH,CACF,CAAA,EAGCnC,IAAc,YAAcqB,EAAe,EAC3CrB,IAAc,aAAe0B,EAAgB,EAC7C1B,IAAc,YAAc6B,EAAe,EAC3C7B,IAAc,YAAc+B,EAAe,CAAA,EAC9C,CAEJ,EC5VA,MAAMK,EAAmB,CAEvB,MAAM,yBAAyBC,EAA8G,CACvI,GAAA,CACF,MAAMC,EAAY,WAAW,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAGlF,GAAID,EAAQ,iBAAmBA,EAAQ,gBAAgB,OAAS,EAAG,CAC3D,MAAAvJ,EAAa,MAAMC,EAA0B,wBAAwB,CACzE,GAAIuJ,EACJ,MAAOD,EAAQ,MACf,QAASA,EAAQ,YACjB,KAAM,sBACN,QAASA,EAAQ,gBAAgB,CAAC,EAAE,QACpC,OAAQ,CACN,OAAQA,EAAQ,OAChB,oBAAqB,kBACvB,EACA,aAAc,CACZ,OAAQ,UACR,WAAY,CAAC,EACb,YAAa,CAAC,EACd,WAAY,IACd,EACA,WAAY,CACV,MAAO,EACP,MAAO,EACP,OAAQ,EACR,SAAU,EACV,mBAAoB,CACtB,EACA,UAAWrJ,EAAU,IAAI,EACzB,aAAcA,EAAU,IAAI,CAAA,CAC7B,EAEG,CAACF,EAAW,SAAWA,EAAW,gBAEpC,MAAM,KAAK,8BACTuJ,EAAQ,gBAAgB,IAAIE,GAAOA,EAAI,OAAO,EAC9CD,CACF,CACF,CAGF,MAAME,EAAyC,CAC7C,GAAGH,EACH,GAAIC,EACJ,MAAO,EACP,UAAW,EACX,UAAWtJ,EAAU,IAAI,EACzB,UAAWA,EAAU,IAAI,CAC3B,EAEA,aAAME,EAAOC,EAAIC,EAAI,uBAAwBkJ,CAAS,EAAGE,CAAkB,EAEpEF,QACAjJ,EAAO,CACN,cAAA,MAAM,sCAAuCA,CAAK,EACpD,IAAI,MAAM,sCAAsC,CAAA,CACxD,CAIF,MAAM,0BACJoJ,EAUArI,EAAsB,GACS,CAC3B,GAAA,CACF,IAAIV,EAAIC,EACNC,EAAWR,EAAI,sBAAsB,EACrCS,EAAM,SAAU,KAAM,QAAQ,EAC9BQ,EAAQ,YAAa,MAAM,EAC3BP,EAAMM,CAAW,CACnB,EAGIqI,EAAY,cACd/I,EAAIC,EAAMD,EAAGG,EAAM,cAAe,KAAM4I,EAAY,WAAW,CAAC,GAI9D,IAAAC,GADa,MAAM1I,EAAQN,CAAC,GACR,KAAK,IAAIP,GAAOA,EAAI,MAA4B,EAGxE,GAAIsJ,EAAY,MAAO,CACf,MAAAE,EAAaF,EAAY,MAAM,YAAY,EACjDC,EAAWA,EAAS,OAClBL,GAAAA,EAAQ,MAAM,YAAc,EAAA,SAASM,CAAU,GAC/CN,EAAQ,YAAY,YAAY,EAAE,SAASM,CAAU,CACvD,CAAA,CAGF,OAAIF,EAAY,kBACdC,EAAWA,EAAS,OAAOL,GACzBA,EAAQ,gBAAgB,QAAYE,EAAI,UAAYE,EAAY,eAAe,CACjF,GAGEA,EAAY,aACdC,EAAWA,EAAS,OAAOL,GACzBA,EAAQ,QAAQ,UAAYI,EAAY,WAAY,KACpDJ,EAAQ,QAAQ,UAAYI,EAAY,WAAY,GACtD,GAGKC,QACArJ,EAAO,CACN,cAAA,MAAM,wCAAyCA,CAAK,EACtD,IAAI,MAAM,uCAAuC,CAAA,CACzD,CAIF,MAAM,+BAA+BI,EAAgBW,EAAsB,GAAmC,CACxG,GAAA,CAEF,MAAMwI,EAAc,MAAM,KAAK,oBAAoBnJ,CAAM,EACzD,GAAI,CAACmJ,EAEI,OAAA,KAAK,mBAAmBxI,CAAW,EAI5C,MAAMyI,EAAwC,CAAC,EAEpC,UAAAC,KAAgBF,EAAY,cAAe,CAC9C,MAAAG,EAAgB,MAAM,KAAK,0BAA0B,CACzD,MAAOD,EAAa,UACpB,YAAa,kBACZ,CAAC,EAEYD,EAAA,KAAK,GAAGE,CAAa,CAAA,CAIjC,MAAAC,EAAoBJ,EAAY,cAAc,QAAQd,GAC1DA,EAAK,qBAAqB,OAAYtH,EAAK,OAAO,GAAK,CAAA,CACzD,EAEA,UAAWyI,KAAWD,EAAmB,CACjC,MAAAE,EAAmB,MAAM,KAAK,0BAA0B,CAC5D,gBAAiBD,GAChB,CAAC,EAEYJ,EAAA,KAAK,GAAGK,CAAgB,CAAA,CAQnC,OAJuBL,EAAgB,OAAO,CAACR,EAASJ,EAAOkB,IACpElB,IAAUkB,EAAK,UAAeC,GAAAA,EAAE,KAAOf,EAAQ,EAAE,CACnD,EAE6B,MAAM,EAAGjI,CAAW,QAC1Cf,EAAO,CACN,cAAA,MAAM,8CAA+CA,CAAK,EAC5D,IAAI,MAAM,4CAA4C,CAAA,CAC9D,CAIF,MAAM,0BACJiJ,EACAe,EACAC,EACAC,EACiB,CACb,GAAA,CACF,MAAMpG,EAAa,YAAY,KAAK,IAAA,CAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAG9EqG,EAAa,MAAMpI,EAAOjC,EAAIC,EAAI,uBAAwBkJ,CAAS,CAAC,EACtE,GAAA,CAACkB,EAAW,SACR,MAAA,IAAI,MAAM,mBAAmB,EAG/B,MAAAnB,EAAUmB,EAAW,KAAK,EAE1BC,EAAuC,CAC3C,GAAItG,EACJ,qBAAsBmF,EACtB,YAAAe,EACA,WAAYhB,EAAQ,OACpB,QAASA,EAAQ,QACjB,aAAc,aACd,UAAW,CACT,mBAAoBkB,EAAc,oBAAsB,CAAC,EACzD,aAAcA,EAAc,cAAgB,CAAC,EAC7C,SAAUA,EAAc,UAAY,CAAC,EACrC,kBAAmBA,EAAc,mBAAqB,CACpD,KAAM,eACN,OAAQlB,EAAQ,QAAQ,SACxB,SAAU,aACZ,EACA,mBAAoBkB,EAAc,oBAAsB,CAAC,EACzD,mBAAoBA,EAAc,oBAAsB,CACtD,aAAc,GACd,aAAc,SAChB,EACA,0BAA2BA,EAAc,2BAA6B,CACpE,gBAAiB,SACjB,oBAAqB,EACvB,EACA,yBAA0BA,EAAc,0BAA4B,CAClE,MAAO,CAAC,qBAAsB,YAAa,aAAa,EACxD,2BAA4B,EAAA,CAEhC,EACA,WAAY,CAAC,EACb,eAAgB,CAAC,CACf,GAAI,OAAO,KAAK,IAAK,CAAA,GACrB,SAAUF,EACV,QAASC,EACT,UAAWtK,EAAU,IAAI,EACzB,KAAM,UAAA,CACP,EACD,SAAU,CAAC,EACX,iBAAkBqJ,EAAQ,gBAAgB,IAAYE,IAAA,CACpD,QAASA,EAAI,QACb,aAAcA,EAAI,aAClB,mBAAoB,CAAC,cAAcA,EAAI,OAAO,mBAAmB,EACjE,kBAAmB,CAAC,kCAAmC,4BAA4B,CAAA,EACnF,EACF,OAAQ,WACR,UAAWvJ,EAAU,IAAI,EACzB,gBAAiBA,EAAU,SAAS,IAAI,KAAK,KAAK,MAAQ,GAAK,GAAK,GAAK,GAAK,GAAI,CAAC,EACnF,aAAc,CACZ,gBAAiB,EACjB,eAAgB,EAChB,sBAAuB,EACvB,sBAAuB,EACvB,oBAAqB,CAAA,CAEzB,EAEA,aAAME,EAAOC,EAAIC,EAAI,sBAAuB+D,CAAU,EAAGsG,CAAiB,EAG1E,MAAMjK,EAAUL,EAAIC,EAAI,uBAAwBkJ,CAAS,EAAG,CAC1D,UAAWD,EAAQ,UAAY,EAC/B,UAAWrJ,EAAU,IAAI,CAAA,CAC1B,EAEMmE,QACA9D,EAAO,CACN,cAAA,MAAM,uCAAwCA,CAAK,EACrD,IAAI,MAAM,uCAAuC,CAAA,CACzD,CAIF,MAAM,8BACJ8D,EACAxB,EACAC,EACe,CACX,GAAA,CACF,MAAM8H,EAAcvK,EAAIC,EAAI,sBAAuB+D,CAAU,EAEvDwG,EAAkB,CACtB,OAAAhI,EACA,UAAW3C,EAAU,IAAI,CAC3B,EAEI2C,IAAW,SACFgI,EAAA,UAAY3K,EAAU,IAAI,EAC5B2C,IAAW,cACTgI,EAAA,cAAgB3K,EAAU,IAAI,GAGvC4C,IACF+H,EAAW,YAAc/H,GAGrB,MAAApC,EAAUkK,EAAaC,CAAU,QAChCtK,EAAO,CACN,cAAA,MAAM,4CAA6CA,CAAK,EAC1D,IAAI,MAAM,4CAA4C,CAAA,CAC9D,CAIF,MAAM,2BAA2BI,EAA+C,CAC1E,GAAA,CACF,MAAMC,EAAIC,EACRC,EAAWR,EAAI,sBAAsB,EACrCS,EAAM,SAAU,KAAMJ,CAAM,EAC5BY,EAAQ,YAAa,MAAM,CAC7B,EAGA,OADiB,MAAML,EAAQN,CAAC,GAChB,KAAK,IAAIP,GAAOA,EAAI,MAA4B,QACzDE,EAAO,CACN,cAAA,MAAM,2CAA4CA,CAAK,EACzD,IAAI,MAAM,yCAAyC,CAAA,CAC3D,CAIF,MAAM,0BAA0BI,EAAgBgC,EAA+D,CACzG,GAAA,CACE,IAAA/B,EAEJ,GAAI+B,IAAS,YACP/B,EAAAC,EACFC,EAAWR,EAAI,qBAAqB,EACpCS,EAAM,cAAe,KAAMJ,CAAM,EACjCY,EAAQ,YAAa,MAAM,CAC7B,UACSoB,IAAS,WACd/B,EAAAC,EACFC,EAAWR,EAAI,qBAAqB,EACpCS,EAAM,aAAc,KAAMJ,CAAM,EAChCY,EAAQ,YAAa,MAAM,CAC7B,MACK,CAEL,MAAMuJ,EAAiBjK,EACrBC,EAAWR,EAAI,qBAAqB,EACpCS,EAAM,cAAe,KAAMJ,CAAM,EACjCY,EAAQ,YAAa,MAAM,CAC7B,EAEMwJ,EAAgBlK,EACpBC,EAAWR,EAAI,qBAAqB,EACpCS,EAAM,aAAc,KAAMJ,CAAM,EAChCY,EAAQ,YAAa,MAAM,CAC7B,EAEM,CAACyJ,EAAmBC,CAAgB,EAAI,MAAM,QAAQ,IAAI,CAC9D/J,EAAQ4J,CAAc,EACtB5J,EAAQ6J,CAAa,CAAA,CACtB,EAEKG,EAAqBF,EAAkB,KAAK,IAAI3K,GAAOA,EAAI,MAA2B,EACtF8K,EAAoBF,EAAiB,KAAK,IAAI5K,GAAOA,EAAI,MAA2B,EAE1F,MAAO,CAAC,GAAG6K,EAAoB,GAAGC,CAAiB,EAChD,KAAK,CAAC9C,EAAGC,IAAMA,EAAE,UAAU,QAAUD,EAAE,UAAU,OAAO,CAAA,CAI7D,OADiB,MAAMnH,EAAQN,CAAC,GAChB,KAAK,IAAIP,GAAOA,EAAI,MAA2B,QACxDE,EAAO,CACN,cAAA,MAAM,0CAA2CA,CAAK,EACxD,IAAI,MAAM,wCAAwC,CAAA,CAC1D,CAIF,MAAM,oBAAoBe,EAAsB,GAAmC,CAC7E,GAAA,CACF,MAAMV,EAAIC,EACRC,EAAWR,EAAI,sBAAsB,EACrCS,EAAM,SAAU,KAAM,QAAQ,EAC9BA,EAAM,gBAAiB,IAAKb,EAAU,KAAK,EAC3CqB,EAAQ,gBAAiB,MAAM,EAC/BP,EAAMM,CAAW,CACnB,EAGA,OADiB,MAAMJ,EAAQN,CAAC,GAChB,KAAK,IAAIP,GAAOA,EAAI,MAA4B,QACzDE,EAAO,CACN,cAAA,MAAM,mCAAoCA,CAAK,EACjD,IAAI,MAAM,iCAAiC,CAAA,CACnD,CAIF,MAAM,mBAAmBe,EAAsB,GAAmC,CAC5E,GAAA,CACF,MAAMV,EAAIC,EACRC,EAAWR,EAAI,sBAAsB,EACrCS,EAAM,SAAU,KAAM,QAAQ,EAC9BQ,EAAQ,QAAS,MAAM,EACvBP,EAAMM,CAAW,CACnB,EAGA,OADiB,MAAMJ,EAAQN,CAAC,GAChB,KAAK,IAAIP,GAAOA,EAAI,MAA4B,QACzDE,EAAO,CACN,cAAA,MAAM,kCAAmCA,CAAK,EAChD,IAAI,MAAM,gCAAgC,CAAA,CAClD,CAIF,MAAM,sBAAsBiJ,EAAkC,CACxD,GAAA,CACF,MAAM4B,EAAa/K,EAAIC,EAAI,uBAAwBkJ,CAAS,EACtDkB,EAAa,MAAMpI,EAAO8I,CAAU,EAEtC,GAAAV,EAAW,SAAU,CACvB,MAAMW,EAAeX,EAAW,KAAK,EAAE,OAAS,EAChD,MAAMhK,EAAU0K,EAAY,CAC1B,MAAOC,EAAe,EACtB,UAAWnL,EAAU,IAAI,CAAA,CAC1B,CAAA,QAEIK,EAAO,CACN,QAAA,MAAM,oCAAqCA,CAAK,CAAA,CAE1D,CAIF,MAAc,oBAAoBI,EAA8B,CAC1D,GAAA,CACF,MAAMC,EAAIC,EACRC,EAAWR,EAAI,gBAAgB,EAC/BS,EAAM,SAAU,KAAMJ,CAAM,EAC5BK,EAAM,CAAC,CACT,EAEMC,EAAW,MAAMC,EAAQN,CAAC,EAChC,OAAOK,EAAS,MAAQ,KAAOA,EAAS,KAAK,CAAC,EAAE,KAAK,QAC9CV,EAAO,CACN,eAAA,MAAM,oCAAqCA,CAAK,EACjD,IAAA,CACT,CAGF,MAAc,8BAA8B+K,EAAoB9B,EAAkC,CAExF,QAAA,IAAI,oDAAoD8B,EAAS,KAAK,IAAI,CAAC,mBAAmB9B,CAAS,EAAE,CAAA,CAErH,CAEa,MAAA+B,EAAqB,IAAIjC,GC9mBzBkC,GAA4D,CAAC,CACxE,qBAAAzE,EACA,yBAAA0E,CACF,IAAM,CACE,KAAA,CAAE,KAAAzE,CAAK,EAAIC,EAAQ,EACnB,CAACC,EAAWC,CAAY,EAAIC,EAAAA,SAA4D,QAAQ,EAChG,CAACwC,EAAU8B,CAAW,EAAItE,EAAAA,SAA+B,CAAA,CAAE,EAC3D,CAACuE,EAAYC,CAAa,EAAIxE,EAAAA,SAA+B,CAAA,CAAE,EAC/D,CAACyE,EAAWC,CAAY,EAAI1E,EAAAA,SAA8B,CAAA,CAAE,EAC5D,CAACM,EAAiBC,CAAkB,EAAIP,EAAAA,SAAiC,IAAI,EAC7E,CAACQ,EAASC,CAAU,EAAIT,EAAAA,SAAS,EAAI,EACrC,CAACuC,EAAaoC,CAAc,EAAI3E,EAAAA,SAAS,EAAE,EAC3C,CAAC4E,EAAkBC,CAAmB,EAAI7E,EAAAA,SAAS,EAAE,EACrD,CAAC8E,EAAiBC,CAAkB,EAAI/E,EAAAA,SAAS,EAAE,EAEnDgF,EAAa,CACjB,aAAc,WAAY,gBAAiB,YAAa,UACxD,QAAS,qBAAsB,cAAe,aAAc,WAC9D,EAEMd,EAAW,CACf,OAAQ,QAAS,YAAa,UAAW,QAAS,SAClD,SAAU,QAAS,QAAS,UAAW,UACzC,EAEAxD,EAAAA,UAAU,IAAM,CACVd,GACkBqF,EAAA,CACtB,EACC,CAACrF,CAAI,CAAC,EAET,MAAMqF,EAAsB,SAAY,CACtC,GAAKrF,EAEL,CAAAa,EAAW,EAAI,EACX,GAAA,CACI,KAAA,CAACyE,EAAaC,EAAcC,EAAetE,CAAW,EAAI,MAAM,QAAQ,IAAI,CAChFqD,EAAmB,0BAA0B,CAAC,EAAG,EAAE,EACnDA,EAAmB,2BAA2BvE,EAAK,GAAG,EACtDuE,EAAmB,0BAA0BvE,EAAK,GAAG,EACrDJ,EAAmB,mBAAmBI,EAAK,GAAG,CAAA,CAC/C,EAED0E,EAAYY,CAAW,EACvBV,EAAcW,CAAY,EAC1BT,EAAaU,CAAa,EAC1B7E,EAAmBO,CAAW,QACvB3H,EAAO,CACN,QAAA,MAAM,kCAAmCA,CAAK,CAAA,QACtD,CACAsH,EAAW,EAAK,CAAA,EAEpB,EAEM4E,EAAe,SAAY,CAC/B5E,EAAW,EAAI,EACX,GAAA,CACI,MAAA6E,EAAgB,MAAMnB,EAAmB,0BAA0B,CACvE,MAAO5B,EACP,SAAUqC,EACV,gBAAiBE,CAAA,CAClB,EACDR,EAAYgB,CAAa,QAClBnM,EAAO,CACN,QAAA,MAAM,4BAA6BA,CAAK,CAAA,QAChD,CACAsH,EAAW,EAAK,CAAA,CAEpB,EAEM8E,EAAyB,MAAOnD,GAAsB,CAC1D,GAAKxC,EAED,GAAA,CAGI,MAAA3C,EAAa,MAAMkH,EAAmB,0BAC1C/B,EACAxC,EAAK,IAJiB,oFAMtB,CACE,mBAAoB,CAAC,wBAAyB,wBAAwB,EACtE,kBAAmB,CACjB,KAAM,eACN,OAAQ,EACR,SAAU,aAAA,CACZ,CAEJ,EAEM,MAAA,uCAAuC3C,CAAU,EAAE,EACrCgI,EAAA,QACb9L,EAAO,CACN,QAAA,MAAM,6BAA8BA,CAAK,EACjD,MAAM,gDAAgD,CAAA,CAE1D,EAEMqM,EAAe,IAClBpE,OAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2DACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAAAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,4CACZ,MAAOkB,EACP,SAAWkD,GAAMd,EAAec,EAAE,OAAO,KAAK,EAC9C,UAAU,wGAAA,CAAA,EAEd,EACArE,EAAA,KAAC,SAAA,CACC,MAAOwD,EACP,SAAWa,GAAMZ,EAAoBY,EAAE,OAAO,KAAK,EACnD,UAAU,kGAEV,SAAA,CAACpE,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAc,iBAAA,EAC9B2D,EAAW,IACVU,GAAArE,EAAA,IAAC,UAAsB,MAAOqE,EAAW,SAA5BA,CAAA,EAAAA,CAAqC,CACnD,CAAA,CAAA,CACH,EACAtE,EAAA,KAAC,SAAA,CACC,MAAO0D,EACP,SAAWW,GAAMV,EAAmBU,EAAE,OAAO,KAAK,EAClD,UAAU,kGAEV,SAAA,CAACpE,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAY,eAAA,EAC5B6C,EAAS,IAAInB,GACX1B,EAAA,IAAA,SAAA,CAAqB,MAAO0B,EAAS,UAAU,aAAc,SAAjDA,CAAA,EAAAA,CAAyD,CACvE,CAAA,CAAA,CAAA,CACH,EACF,EACA1B,EAAAA,IAAC,MAAI,CAAA,UAAU,OACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAASgE,EACT,UAAU,kFACX,SAAA,QAAA,CAAA,CAGH,CAAA,CAAA,EACF,EAGAjE,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAA4B,+BAAA,EACpFA,EAAA,IAAA,MAAA,CAAI,UAAU,uDACZ,WAAS,MAAM,EAAG,CAAC,EAAE,IAAIc,GACvBf,EAAA,KAAA,MAAA,CAAqB,UAAU,0EAC9B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,8BAA+B,SAAAc,EAAQ,MAAM,EAC1Dd,EAAA,IAAA,OAAA,CAAK,UAAW,kCACfc,EAAQ,cAAgB,iBACpB,8BACA,2BACN,GACG,SAAAA,EAAQ,cAAgB,iBAAmB,WAAa,SAC3D,CAAA,CAAA,EACF,EACAf,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,sCACZ,SAAA,CAAAe,EAAQ,QAAQ,SAAS,eAAA,EAC5B,EACAf,EAAAA,KAAC,MAAI,CAAA,UAAU,wBACZ,SAAA,CAAQe,EAAA,MAAM,QAAA,CACjB,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAECd,EAAA,IAAA,IAAA,CAAE,UAAU,0CACV,WAAQ,YACX,EAECc,EAAQ,gBAAgB,OAAS,SAC/B,MAAI,CAAA,UAAU,4BACZ,SAAQA,EAAA,gBAAgB,IACvBT,GAAAL,MAAC,QAA2B,UAAU,0DACnC,WAAQ,OADA,EAAAK,EAAQ,OAEnB,CACD,CACH,CAAA,EAGFN,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wBACZ,SAAA,CAAQe,EAAA,UAAU,YAAA,EACrB,EACAd,EAAA,IAAC,SAAA,CACC,QAAS,IAAMkE,EAAuBpD,EAAQ,EAAE,EAChD,UAAU,uFACX,SAAA,SAAA,CAAA,CAED,CACF,CAAA,CAAA,GA9CQA,EAAQ,EA+ClB,CACD,CACH,CAAA,CAAA,EACF,EAGAf,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uEACb,SAAA,CAAAC,EAAA,IAAC,MAAI,CAAA,UAAU,mCAAoC,SAAAmB,EAAS,OAAO,EAClEnB,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAe,iBAAA,CAAA,CAAA,EACxD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,oCACZ,SAASmB,EAAA,UAAYU,EAAE,cAAgB,gBAAgB,EAAE,MAC5D,CAAA,EACC7B,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAgB,kBAAA,CAAA,CAAA,EACzD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAAAC,MAAC,OAAI,UAAU,qCACZ,SAAI,IAAA,IAAImB,EAAS,QAAQU,GAAKA,EAAE,gBAAgB,IAASyC,GAAAA,EAAE,OAAO,CAAC,CAAC,EAAE,KACzE,EACCtE,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAoB,sBAAA,CAAA,CAAA,EAC7D,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAU,qCACZ,SAAAf,GAAiB,SAAW,EAC/B,EACCe,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAY,cAAA,CAAA,CAAA,CACrD,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIuE,EAAmB,IACtBxE,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAyB,4BAAA,EAC7EA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMtB,EAAa,QAAQ,EACpC,UAAU,oFACX,SAAA,oBAAA,CAAA,CAED,EACF,EAEAqB,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACZ,SAAA,CAAAmD,EAAW,IAAIpC,GACbf,EAAAA,KAAA,MAAA,CAAqB,UAAU,2DAC9B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,8BAA+B,SAAAc,EAAQ,MAAM,EAC1Dd,EAAA,IAAA,OAAA,CAAK,UAAW,kCACfc,EAAQ,SAAW,SAAW,8BAC9BA,EAAQ,SAAW,SAAW,gCAC9B,2BACF,GACG,WAAQ,MACX,CAAA,CAAA,EACF,QACC,MAAI,CAAA,UAAU,aACb,SAACf,EAAA,KAAA,MAAA,CAAI,UAAU,sCACZ,SAAA,CAAAe,EAAQ,QAAQ,SAAS,eAAA,CAAA,CAC5B,CACF,CAAA,CAAA,EACF,EAECd,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA8B,WAAQ,YAAY,EAE/DD,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAM,SAAA,EAC7CA,EAAA,IAAA,OAAA,CAAK,UAAU,yCAA0C,WAAQ,KAAM,CAAA,CAAA,EAC1E,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAU,aAAA,EACjDA,EAAA,IAAA,OAAA,CAAK,UAAU,yCAA0C,WAAQ,SAAU,CAAA,CAAA,CAC9E,CAAA,CAAA,EACF,EAECc,EAAQ,gBAAgB,OAAS,SAC/B,MAAI,CAAA,UAAU,4BACZ,SAAQA,EAAA,gBAAgB,IACvBT,GAAAL,MAAC,QAA2B,UAAU,0DACnC,WAAQ,OADA,EAAAK,EAAQ,OAEnB,CACD,CACH,CAAA,EAGFN,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,yFAAyF,SAE3G,OAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,4EAA4E,SAE9F,gBAAA,CAAA,CAAA,CACF,CAAA,CAAA,GAjDQc,EAAQ,EAkDlB,CACD,EAEAoC,EAAW,SAAW,GACpBnD,EAAA,KAAA,MAAA,CAAI,UAAU,6CACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAe,kBAAA,EACrEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,wGAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMtB,EAAa,QAAQ,EACpC,UAAU,oFACX,SAAA,2BAAA,CAAA,CAED,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGI8F,EAAkB,IACrBzE,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAwB,2BAAA,EAE5ED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACZ,SAAA,CAAAqD,EAAU,IAAIqB,GACZ1E,EAAAA,KAAA,MAAA,CAAsB,UAAU,2DAC/B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,8BACX,SAAA,CAAS0E,EAAA,QAAQ,WAAA,EACpB,EACAzE,EAAAA,IAAC,KAAE,UAAU,wBACV,WAAS,cAAgBzB,GAAM,IAAM,mBAAqB,kBAC7D,CAAA,CAAA,EACF,EACCyB,EAAA,IAAA,OAAA,CAAK,UAAW,kCACfyE,EAAS,SAAW,UAAYA,EAAS,SAAW,cAAgB,8BACpEA,EAAS,SAAW,YAAc,4BAClCA,EAAS,SAAW,YAAcA,EAAS,SAAW,cAAgB,gCACtE,2BACF,GACG,SAASA,EAAA,OAAO,QAAQ,IAAK,GAAG,CACnC,CAAA,CAAA,EACF,EAEA1E,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,oCAAoC,SAAK,QAAA,EACzDA,EAAAA,IAAC,QAAK,UAAU,wCACb,WAAS,aAAa,QAAQ,IAAK,GAAG,CACzC,CAAA,CAAA,EACF,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,OAAA,CAAK,UAAU,oCAAoC,SAAW,cAAA,EAC9DA,EAAAA,IAAA,OAAA,CAAK,UAAU,6BACb,SAAI,IAAA,KAAKyE,EAAS,UAAU,QAAU,GAAI,EAAE,mBAAA,CAC/C,CAAA,CAAA,EACF,SACC,MACC,CAAA,SAAA,CAACzE,EAAA,IAAA,OAAA,CAAK,UAAU,oCAAoC,SAAa,gBAAA,EAChEA,EAAAA,IAAA,OAAA,CAAK,UAAU,6BACb,SAAI,IAAA,KAAKyE,EAAS,gBAAgB,QAAU,GAAI,EAAE,mBAAA,CACrD,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAECA,EAAS,iBAAiB,OAAS,GACjC1E,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,oCAAoC,SAAkB,qBAAA,QACrE,MAAI,CAAA,UAAU,4BACZ,SAAAyE,EAAS,iBAAiB,IAAIvE,GAC5BF,EAAA,IAAA,OAAA,CAA2B,UAAU,0DACnC,SAAAE,EAAQ,SADAA,EAAQ,OAEnB,CACD,CACH,CAAA,CAAA,EACF,EAGFH,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,qEAAqE,SAEvF,eAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,kFAAkF,SAEpG,WAAA,EACCyE,EAAS,SAAW,YAAcA,EAAS,aAAelG,GAAM,KAC9DyB,EAAA,IAAA,SAAA,CAAO,UAAU,uEAAuE,SAEzF,QAAA,CAAA,CAAA,CAEJ,CAAA,CAAA,GAlEQyE,EAAS,EAmEnB,CACD,EAEArB,EAAU,SAAW,GACnBrD,EAAA,KAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAgB,mBAAA,EACtEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,yEAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMtB,EAAa,QAAQ,EACpC,UAAU,kFACX,SAAA,oBAAA,CAAA,CAED,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGIgG,EAAe,IAClB1E,EAAAA,IAAA,MAAA,CAAI,UAAU,oBACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAA0B,6BAAA,EAEnFD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,eAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,0FACf,SAAA,CAACC,EAAAA,IAAA,QAAA,CAAM,KAAK,QAAQ,KAAK,cAAc,MAAM,iBAAiB,UAAU,MAAO,CAAA,SAC9E,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,MAAA,CAAI,UAAU,4BAA4B,SAAe,kBAAA,EACzDA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAoB,sBAAA,CAAA,CAAA,CAC7D,CAAA,CAAA,EACF,EACAD,EAAAA,KAAC,QAAM,CAAA,UAAU,0FACf,SAAA,CAACC,EAAAA,IAAA,QAAA,CAAM,KAAK,QAAQ,KAAK,cAAc,MAAM,gBAAgB,UAAU,MAAO,CAAA,SAC7E,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,MAAA,CAAI,UAAU,4BAA4B,SAAc,iBAAA,EACxDA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAA2B,6BAAA,CAAA,CAAA,CACpE,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,UAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,UAAU,yGACV,YAAY,2DAAA,CAAA,CACd,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,gBAAA,EACAA,EAAA,IAAC,WAAA,CACC,KAAM,EACN,UAAU,yGACV,YAAY,+FAAA,CAAA,CACd,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,WAAA,EACAD,EAAAA,KAAC,SAAO,CAAA,UAAU,yGAChB,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAe,kBAAA,EAC/B2D,EAAW,IACVU,GAAArE,EAAA,IAAC,UAAsB,MAAOqE,EAAW,SAA5BA,CAAA,EAAAA,CAAqC,CACnD,CAAA,CACH,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACrE,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,mBAAA,EACAD,EAAAA,KAAC,SAAO,CAAA,UAAU,yGAChB,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAyB,4BAAA,EACzC6C,EAAS,IAAInB,GACX1B,EAAA,IAAA,SAAA,CAAqB,MAAO0B,EAAS,UAAU,aAAc,SAAjDA,CAAA,EAAAA,CAAyD,CACvE,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAC1B,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAEhE,+BAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,IAAI,IACJ,UAAU,yGACV,YAAY,UAAA,CAAA,CACd,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAA+B,kCAAA,EAChFD,EAAAA,KAAC,KAAG,CAAA,UAAU,oCACZ,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAwD,0DAAA,CAAA,EAC5DA,EAAAA,IAAC,MAAG,SAAkD,oDAAA,CAAA,EACtDA,EAAAA,IAAC,MAAG,SAAyD,2DAAA,CAAA,EAC7DA,EAAAA,IAAC,MAAG,SAAwD,0DAAA,CAAA,CAAA,CAC9D,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAAS,IAAMtB,EAAa,QAAQ,EACpC,UAAU,oFACX,SAAA,QAAA,CAED,EACCsB,EAAA,IAAA,SAAA,CAAO,UAAU,yEAAyE,SAE3F,gBAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGF,OAAIb,EAEAa,MAAC,OAAI,UAAU,yCACb,eAAC,MAAI,CAAA,UAAU,+DAA+D,CAChF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAA8B,iCAAA,EACnFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,4FAAA,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,gCACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,iBACZ,SAAA,CACC,CAAE,GAAI,SAAU,MAAO,SAAU,KAAM,IAAK,EAC5C,CAAE,GAAI,cAAe,MAAO,cAAe,KAAM,IAAK,EACtD,CAAE,GAAI,YAAa,MAAO,YAAa,KAAM,IAAK,EAClD,CAAE,GAAI,SAAU,MAAO,SAAU,KAAM,GAAI,CAAA,EAC3C,IACAY,GAAAb,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMrB,EAAakC,EAAI,EAAS,EACzC,UAAW,4CACTnC,IAAcmC,EAAI,GACd,gCACA,4EACN,GAEA,SAAA,CAAAZ,EAAA,IAAC,OAAK,CAAA,UAAU,OAAQ,SAAAY,EAAI,KAAK,EAChCA,EAAI,KAAA,CAAA,EATAA,EAAI,EAAA,CAWZ,EACH,CACF,CAAA,EAGCnC,IAAc,UAAY0F,EAAa,EACvC1F,IAAc,eAAiB8F,EAAiB,EAChD9F,IAAc,aAAe+F,EAAgB,EAC7C/F,IAAc,UAAYiG,EAAa,CAAA,EAC1C,CAEJ,EC7jBaC,GAA4D,CAAC,CACxE,wBAAAtG,EACA,yBAAA2E,CACF,IAAM,CACE,KAAA,CAAE,KAAAzE,CAAK,EAAIC,EAAQ,EACnB,CAACC,EAAWC,CAAY,EAAIC,EAAAA,SAA+D,UAAU,EACrG,CAACM,EAAiBC,CAAkB,EAAIP,EAAAA,SAAiC,IAAI,EAC7E,CAAClC,EAAcmI,CAAe,EAAIjG,EAAAA,SAA4B,CAAA,CAAE,EAChE,CAACkG,EAAqBC,CAAsB,EAAInG,EAAAA,SAAqC,IAAI,EACzF,CAACQ,EAASC,CAAU,EAAIT,EAAAA,SAAS,EAAI,EAE3CU,EAAAA,UAAU,IAAM,CACVd,GACkBwG,EAAA,CACtB,EACC,CAACxG,CAAI,CAAC,EAET,MAAMwG,EAAsB,SAAY,CACtC,GAAKxG,EAEL,CAAAa,EAAW,EAAI,EACX,GAAA,CACF,KAAM,CAAC1E,EAASsK,EAAoB/H,CAAO,EAAI,MAAM,QAAQ,IAAI,CAC/DkB,EAAmB,mBAAmBI,EAAK,GAAG,EAC9CJ,EAAmB,0BAA0BI,EAAK,IAAK,EAAE,EACzDJ,EAAmB,6BAA6BI,EAAK,GAAG,CAAA,CACzD,EAEDW,EAAmBxE,CAAO,EAC1BkK,EAAgBI,CAAkB,EAClCF,EAAuB7H,CAAO,QACvBnF,EAAO,CACN,QAAA,MAAM,mCAAoCA,CAAK,CAAA,QACvD,CACAsH,EAAW,EAAK,CAAA,EAEpB,EAEMU,EAAiB,IACpBC,OAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,4EACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAmB,sBAAA,QAC7D,MAAI,CAAA,UAAU,qBAAsB,SAAAf,GAAiB,SAAW,EAAE,EAClEe,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAA+B,iCAAA,CAAA,CAAA,EACrE,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,0BAA0B,SAAkB,qBAAA,EAC3DD,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAAyB,SAAA,CAAiBd,GAAA,kBAAkB,QAAQ,CAAC,GAAK,MAAM,GAAA,EAAC,EAC/Fe,EAAA,IAAA,MAAA,CAAI,UAAU,0BAA0B,SAAc,iBAAA,EACvDD,EAAAA,KAAC,MAAI,CAAA,UAAU,sBAAyB,SAAA,GAAAd,GAAiB,mBAAqB,GAAK,KAAK,QAAQ,CAAC,EAAE,GAAA,CAAC,CAAA,CAAA,CACtG,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAc,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,SAChC,MACC,CAAA,SAAA,CAAAA,MAAC,MAAI,CAAA,UAAU,oCACZ,SAAAf,GAAiB,aAAe,EACnC,EACCe,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAY,cAAA,CAAA,CAAA,CACrD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,SAChC,MACC,CAAA,SAAA,CAAAA,MAAC,MAAI,CAAA,UAAU,mCACZ,SAAAf,GAAiB,YAAc,EAClC,EACCe,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAW,aAAA,CAAA,CAAA,CACpD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAC,IAAA,SAC/B,MACC,CAAA,SAAA,CAAAA,MAAC,MAAI,CAAA,UAAU,qCACZ,SAAAf,GAAiB,iBAAmB,EACvC,EACCe,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAgB,kBAAA,CAAA,CAAA,CACzD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,SAChC,MACC,CAAA,SAAA,CAAAA,MAAC,MAAI,CAAA,UAAU,qCACZ,SAAAf,GAAiB,eAAiB,EACrC,EACCe,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAgB,kBAAA,CAAA,CAAA,CACzD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,EAGC6E,GACC9E,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAqB,wBAAA,EAC9ED,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACZ,SAAA,CAAoB8E,EAAA,qBAAqB,MAAA,EAC5C,EACC7E,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAgB,mBAAA,EACvDA,EAAAA,IAAC,MAAI,CAAA,UAAU,2CACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,gCACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,oBAAoB,GAAI,CAAA,CAAA,CAEnE,CAAA,CAAA,EACF,EAEA9E,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0CACZ,SAAA,CAAoB8E,EAAA,qBAAqB,MAAA,EAC5C,EACC7E,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAqB,wBAAA,EAC5DA,EAAAA,IAAC,MAAI,CAAA,UAAU,2CACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,iCACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,oBAAoB,GAAI,CAAA,CAAA,CAEnE,CAAA,CAAA,EACF,EAEA9E,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACZ,SAAA,CAAoB8E,EAAA,sBAAsB,MAAA,EAC7C,EACC7E,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAuB,0BAAA,EAC9DA,EAAAA,IAAC,MAAI,CAAA,UAAU,2CACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,+BACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,qBAAqB,GAAI,CAAA,CAAA,CAEpE,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAIF9E,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAmB,sBAAA,EAC5ED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACZ,SAAA,CAAatD,EAAA,MAAM,EAAG,CAAC,EAAE,IACxBrB,GAAA2E,EAAAA,KAAC,MAAyB,CAAA,UAAU,8DAClC,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAW,iBACd5E,EAAY,OAAS,QAAUA,EAAY,OAAS,QAAU,iBAC9DA,EAAY,OAAS,QAAU,gBAC/B,eACF,GACG,SAAAA,EAAY,OAAS,QAAUA,EAAY,OAAS,QAAU,KAC9DA,EAAY,OAAS,QAAU,KAAO,IACzC,SACC,MACC,CAAA,SAAA,CAAA4E,EAAA,IAAC,MAAI,CAAA,UAAU,4BAA6B,SAAA5E,EAAY,YAAY,EACnE4E,EAAAA,IAAA,MAAA,CAAI,UAAU,wBACZ,SAAI,IAAA,KAAK5E,EAAY,UAAU,QAAU,GAAI,EAAE,mBAClD,CAAA,CAAA,EACCA,EAAY,iBACV4E,MAAA,OAAA,CAAK,UAAU,4EACb,SAAA5E,EAAY,gBAAgB,cAC/B,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EACA2E,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAA,OAAC,MAAI,CAAA,UAAW,iBACd3E,EAAY,OAAS,QAAUA,EAAY,OAAS,QAAU,iBAC9DA,EAAY,OAAS,QAAU,eAC/B,eACF,GACG,SAAA,CAAYA,EAAA,OAAS,QAAUA,EAAY,OAAS,QAAU,IAC9DA,EAAY,OAAS,QAAU,IAAM,GACrCA,EAAY,MAAA,EACf,EACC4E,EAAA,IAAA,MAAA,CAAI,UAAU,wBACZ,WAAY,MACf,CAAA,CAAA,CACF,CAAA,CAAA,GAnCQ5E,EAAY,EAoCtB,CACD,EAEAqB,EAAa,SAAW,GACtBsD,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EACjCA,EAAAA,IAAC,KAAE,SAAmB,qBAAA,CAAA,EACrBA,EAAA,IAAA,IAAA,CAAE,UAAU,UAAU,SAA2E,6EAAA,CAAA,CAAA,CACpG,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAY,eAAA,EAC5DA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,uDAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAASgD,EACT,UAAU,2FACX,SAAA,gBAAA,CAAA,CAED,EACF,EAEAjD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAa,gBAAA,EAC7DA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,qDAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAAS3B,EACT,UAAU,yFACX,SAAA,eAAA,CAAA,CAED,EACF,EAEA0B,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAiB,oBAAA,EACjEA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,4DAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,6FAA6F,SAE/G,kBAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIiF,EAAqB,IACxBlF,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAmB,sBAAA,EACvED,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACA,EAAAA,KAAA,SAAA,CAAO,UAAU,sDAChB,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAS,YAAA,EACzBA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAM,SAAA,EAC1BA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,QAAA,EAC1BA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,OAAA,CAAA,CAAA,EAC7B,EACAD,EAAAA,KAAC,SAAO,CAAA,UAAU,sDAChB,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAQ,WAAA,EACxBA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAS,YAAA,EAC7BA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAU,aAAA,EAC/BA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAS,WAAA,CAAA,CAAA,CAChC,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,kBACb,SAACD,EAAA,KAAA,QAAA,CAAM,UAAU,SACf,SAAA,CAAAC,MAAC,QAAM,CAAA,UAAU,aACf,SAAAD,EAAA,KAAC,KACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,OAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,cAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,OAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,SAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,SAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,kBAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACAA,EAAA,IAAC,QAAM,CAAA,UAAU,oCACd,SAAAvD,EAAa,IACZrB,GAAA2E,EAAA,KAAC,KAAwB,CAAA,UAAU,mBACjC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,oDACX,SAAI,IAAA,KAAK5E,EAAY,UAAU,QAAU,GAAI,EAAE,mBAClD,CAAA,CAAA,EACA4E,EAAA,IAAC,KAAG,CAAA,UAAU,kCACZ,SAAAA,EAAAA,IAAC,OAAI,UAAU,oBAAqB,SAAY5E,EAAA,WAAA,CAAY,CAC9D,CAAA,EACA4E,EAAAA,IAAC,MAAG,UAAU,8BACZ,eAAC,OAAK,CAAA,UAAW,4DACf5E,EAAY,OAAS,QAAUA,EAAY,OAAS,QAAU,8BAC9DA,EAAY,OAAS,QAAU,4BAC/B,2BACF,GACG,SAAYA,EAAA,IAAA,CACf,CACF,CAAA,QACC,KAAG,CAAA,UAAU,kDACZ,SAAC2E,EAAAA,KAAA,OAAA,CAAK,UACJ3E,EAAY,OAAS,QAAUA,EAAY,OAAS,QAAU,iBAC9DA,EAAY,OAAS,QAAU,eAC/B,gBAEC,SAAA,CAAYA,EAAA,OAAS,QAAUA,EAAY,OAAS,QAAU,IAC9DA,EAAY,OAAS,QAAU,IAAM,GACrCA,EAAY,MAAA,CAAA,CACf,CACF,CAAA,EACA4E,EAAAA,IAAC,KAAG,CAAA,UAAU,8BACZ,SAAAA,EAAA,IAAC,QAAK,UAAW,4DACf5E,EAAY,SAAW,YAAc,8BACrCA,EAAY,SAAW,UAAY,gCACnCA,EAAY,SAAW,SAAW,0BAClC,2BACF,GACG,SAAYA,EAAA,MAAA,CACf,CACF,CAAA,EACC4E,EAAA,IAAA,KAAA,CAAG,UAAU,oDACX,WAAY,gBACXA,EAAA,IAAC,OAAK,CAAA,UAAU,0DACb,SAAA5E,EAAY,gBAAgB,cAAA,CAC/B,EAEA,GAEJ,CAAA,CAAA,GA7COA,EAAY,EA8CrB,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECqB,EAAa,SAAW,GACtBsD,EAAA,KAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAmB,sBAAA,EACzEA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,sFAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGIkF,EAAgB,IACnBnF,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAqB,wBAAA,EAExE6E,EAGG9E,EAAA,KAAAoF,WAAA,CAAA,SAAA,CAAAnF,EAAAA,IAAC,OAAI,UAAU,2DACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACZ,SAAA,CAAoB8E,EAAA,qBAAqB,MAAA,EAC5C,EACC7E,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAsB,yBAAA,EAC9EA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,0EAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,2CACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,4DACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,oBAAoB,GAAI,CAAA,CAAA,CAEnE,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGA9E,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAiB,oBAAA,EAClED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAe,iBAAA,CAAA,SACpB,OAAM,CAAA,SAAA,CAAoB6E,EAAA,eAAe,MAAA,CAAI,CAAA,CAAA,EAChD,EACA7E,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,+BACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,cAAc,GAAI,CAAA,CAAA,CAE7D,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAC9E,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAiB,mBAAA,CAAA,SACtB,OAAM,CAAA,SAAA,CAAoB6E,EAAA,iBAAiB,MAAA,CAAI,CAAA,CAAA,EAClD,EACA7E,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,gCACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,gBAAgB,GAAI,CAAA,CAAA,CAE/D,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAC9E,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAmB,qBAAA,CAAA,SACxB,OAAM,CAAA,SAAA,CAAoB6E,EAAA,mBAAmB,MAAA,CAAI,CAAA,CAAA,EACpD,EACA7E,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,iCACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,kBAAkB,GAAI,CAAA,CAAA,CAEjE,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEA9E,EAAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAe,kBAAA,EAChED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAqB,uBAAA,CAAA,SAC1B,OAAM,CAAA,SAAA,CAAoB6E,EAAA,qBAAqB,MAAA,CAAI,CAAA,CAAA,EACtD,EACA7E,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,iCACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,oBAAoB,GAAI,CAAA,CAAA,CAEnE,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAC9E,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAuB,yBAAA,CAAA,SAC5B,OAAM,CAAA,SAAA,CAAoB6E,EAAA,sBAAsB,MAAA,CAAI,CAAA,CAAA,EACvD,EACA7E,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,+BACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,qBAAqB,GAAI,CAAA,CAAA,CAEpE,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAC9E,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAgB,kBAAA,CAAA,SACrB,OAAM,CAAA,SAAA,CAAoB6E,EAAA,gBAAgB,MAAA,CAAI,CAAA,CAAA,EACjD,EACA7E,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,gCACV,MAAO,CAAE,MAAO,GAAG6E,EAAoB,eAAe,GAAI,CAAA,CAAA,CAE9D,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGA9E,EAAAA,KAAC,MAAI,CAAA,UAAU,mDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAA2B,8BAAA,EAC5ED,EAAAA,KAAC,MAAI,CAAA,UAAU,8DACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAA+B,kCAAA,EAChED,EAAAA,KAAC,KAAG,CAAA,UAAU,YACZ,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAyC,2CAAA,CAAA,EAC7CA,EAAAA,IAAC,MAAG,SAA0C,4CAAA,CAAA,EAC9CA,EAAAA,IAAC,MAAG,SAA0C,4CAAA,CAAA,CAAA,CAChD,CAAA,CAAA,EACF,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAwB,2BAAA,EACzDD,EAAAA,KAAC,KAAG,CAAA,UAAU,YACZ,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAA0C,4CAAA,CAAA,EAC9CA,EAAAA,IAAC,MAAG,SAAqC,uCAAA,CAAA,EACzCA,EAAAA,IAAC,MAAG,SAA+B,iCAAA,CAAA,CAAA,CACrC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAwB,2BAAA,EAC9EA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,kEAAA,CAAA,CAAA,CACF,CAAA,CAAA,EAEJ,EAGF,OAAIb,EAEAa,MAAC,OAAI,UAAU,yCACb,eAAC,MAAI,CAAA,UAAU,iEAAiE,CAClF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAsB,yBAAA,EAC3EA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,sFAAA,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,gCACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,iBACZ,SAAA,CACC,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,IAAK,EAChD,CAAE,GAAI,eAAgB,MAAO,eAAgB,KAAM,IAAK,EACxD,CAAE,GAAI,UAAW,MAAO,mBAAoB,KAAM,IAAK,EACvD,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,IAAK,CAAA,EAChD,IACAY,GAAAb,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMrB,EAAakC,EAAI,EAAS,EACzC,UAAW,4CACTnC,IAAcmC,EAAI,GACd,oCACA,4EACN,GAEA,SAAA,CAAAZ,EAAA,IAAC,OAAK,CAAA,UAAU,OAAQ,SAAAY,EAAI,KAAK,EAChCA,EAAI,KAAA,CAAA,EATAA,EAAI,EAAA,CAWZ,EACH,CACF,CAAA,EAGCnC,IAAc,YAAcqB,EAAe,EAC3CrB,IAAc,gBAAkBwG,EAAmB,EACnDxG,IAAc,WAAayG,EAAc,EACzCzG,IAAc,YACZsB,OAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAW,cAAA,EACjEA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,iEAAA,CAAA,CAAA,CACF,CAAA,CAAA,EAEJ,CAEJ,EC/jBaoF,GAAkC,IAAM,CACnD,KAAM,CAACC,EAAYC,CAAa,EAAI3G,EAAAA,SAAqB,UAAU,EAE7D4G,EAAmB,IACtBvF,EAAAA,IAAA,MAAA,CAAI,UAAU,8CACb,SAACA,MAAA,MAAA,CAAI,UAAU,yBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,iBACZ,SAAA,CACC,CAAE,GAAI,WAAY,MAAO,WAAY,KAAM,KAAM,YAAa,iCAAkC,EAChG,CAAE,GAAI,gBAAiB,MAAO,gBAAiB,KAAM,KAAM,YAAa,kCAAmC,EAC3G,CAAE,GAAI,cAAe,MAAO,cAAe,KAAM,KAAM,YAAa,gCAAiC,EACrG,CAAE,GAAI,eAAgB,MAAO,eAAgB,KAAM,IAAK,YAAa,6BAA8B,CAAA,EACnG,IACAY,GAAAb,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMuF,EAAc1E,EAAI,EAAgB,EACjD,UAAW,8DACTyE,IAAezE,EAAI,GACf,oCACA,4EACN,GACA,MAAOA,EAAI,YAEX,SAAA,CAAAZ,EAAA,IAAC,OAAK,CAAA,UAAU,OAAQ,SAAAY,EAAI,KAAK,EAChCA,EAAI,KAAA,CAAA,EAVAA,EAAI,EAAA,CAYZ,CACH,CAAA,CACF,CAAA,EACF,EAGId,EAAiB,IACpBC,OAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAwC,2CAAA,EAC7FA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,8GAAA,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,wCAAwC,SAAK,QAAA,EAC3DA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAkB,oBAAA,CAAA,CAAA,EAC3D,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,yCAAyC,SAAK,QAAA,EAC5DA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAoB,sBAAA,CAAA,CAAA,EAC7D,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,0CAA0C,SAAM,SAAA,EAC9DA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAiB,mBAAA,CAAA,CAAA,EAC1D,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,0CAA0C,SAAE,KAAA,EAC1DA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAkB,oBAAA,CAAA,CAAA,CAC3D,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,QAAS,IAAMuF,EAAc,eAAe,EAC5C,UAAU,4IAEV,SAAA,CAACtF,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAA4B,+BAAA,EACtEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,8HAAA,QACC,MAAI,CAAA,UAAU,kCACb,SAACA,EAAA,IAAA,OAAA,CAAK,0BAAe,CAAA,CACvB,CAAA,CAAA,CAAA,CACF,EAEAD,EAAA,KAAC,MAAA,CACC,QAAS,IAAMuF,EAAc,aAAa,EAC1C,UAAU,gJAEV,SAAA,CAACtF,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAA8B,iCAAA,EACxEA,EAAA,IAAA,IAAA,CAAE,UAAU,sBAAsB,SAEnC,4HAAA,QACC,MAAI,CAAA,UAAU,mCACb,SAACA,EAAA,IAAA,OAAA,CAAK,gCAAqB,CAAA,CAC7B,CAAA,CAAA,CAAA,CACF,EAEAD,EAAA,KAAC,MAAA,CACC,QAAS,IAAMuF,EAAc,cAAc,EAC3C,UAAU,oJAEV,SAAA,CAACtF,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAC,IAAA,EAC/BA,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAA4B,+BAAA,EACtEA,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAEpC,kHAAA,QACC,MAAI,CAAA,UAAU,oCACb,SAACA,EAAA,IAAA,OAAA,CAAK,2BAAgB,CAAA,CACxB,CAAA,CAAA,CAAA,CAAA,CACF,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,gEACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,oDAAoD,SAA4B,+BAAA,EAC9FD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,mFACb,SAAAA,EAAA,IAAC,QAAK,UAAU,WAAW,cAAE,CAC/B,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAiB,oBAAA,EACjEA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,kGAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,oFACb,SAAAA,EAAA,IAAC,QAAK,UAAU,WAAW,cAAE,CAC/B,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAe,kBAAA,EAC/DA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,kGAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,qFACb,SAAAA,EAAA,IAAC,QAAK,UAAU,WAAW,cAAE,CAC/B,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAqB,wBAAA,EACrEA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,iGAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,qFACb,SAAAA,EAAA,IAAC,QAAK,UAAU,WAAW,cAAE,CAC/B,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAgB,mBAAA,EAChEA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,mGAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uFACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAE,KAAA,EAChCA,EAAA,IAAA,KAAA,CAAG,UAAU,qCAAqC,SAAuC,yCAAA,CAAA,CAAA,EAC5F,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAGpC,2KAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,qCAAqC,SAAsB,yBAAA,EACxEA,EAAA,IAAA,IAAA,CAAE,UAAU,0BAA0B,SAEvC,qHAAA,CAAA,CAAA,EACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,mDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,qCAAqC,SAAsB,yBAAA,EACxEA,EAAA,IAAA,IAAA,CAAE,UAAU,0BAA0B,SAEvC,6GAAA,CAAA,CAAA,EACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,mDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,qCAAqC,SAA0B,6BAAA,EAC5EA,EAAA,IAAA,IAAA,CAAE,UAAU,0BAA0B,SAEvC,qGAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,gEACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,oDAAoD,SAAe,kBAAA,EACjFD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,2EACb,SAAAA,EAAA,IAAC,QAAK,UAAU,UAAU,iBAAK,CACjC,CAAA,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAa,gBAAA,EACxDA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAA2C,6CAAA,CAAA,CAAA,CAClF,CAAA,CAAA,EACF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAGrC,+MAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,+CACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,uDAAuD,SAAe,kBAAA,EACtFA,EAAAA,IAAC,QAAK,SAAqB,uBAAA,CAAA,CAAA,CAC7B,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,4EACb,SAAAA,EAAA,IAAC,QAAK,UAAU,UAAU,iBAAK,CACjC,CAAA,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAiB,oBAAA,EAC5DA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAqC,uCAAA,CAAA,CAAA,CAC5E,CAAA,CAAA,EACF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAGrC,yMAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,+CACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,qDAAqD,SAAgB,mBAAA,EACrFA,EAAAA,IAAC,QAAK,SAAqB,uBAAA,CAAA,CAAA,CAC7B,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIwF,EAAqB,IACzBxF,EAAA,IAAC5B,GAAA,CACC,wBAAyB,IAAMkH,EAAc,aAAa,EAC1D,qBAAsB,IAAMA,EAAc,cAAc,CAAA,CAC1D,EAGIG,EAAoB,IACxBzF,EAAA,IAAC+C,GAAA,CACC,qBAAsB,IAAMuC,EAAc,cAAc,EACxD,yBAA0B,IAAMA,EAAc,eAAe,CAAA,CAC/D,EAGII,EAAoB,IACxB1F,EAAA,IAAC2E,GAAA,CACC,wBAAyB,IAAMW,EAAc,aAAa,EAC1D,yBAA0B,IAAMA,EAAc,eAAe,CAAA,CAC/D,EAIA,OAAAvF,EAAA,KAAC,MAAI,CAAA,UAAU,0BACZ,SAAA,CAAiBwF,EAAA,SAEjB,OACE,CAAA,SAAA,CAAAF,IAAe,YAAcvF,EAAe,EAC5CuF,IAAe,iBAAmBG,EAAmB,EACrDH,IAAe,eAAiBI,EAAkB,EAClDJ,IAAe,gBAAkBK,EAAkB,CAAA,CACtD,CAAA,CAAA,EACF,CAEJ"}