{"version": 3, "file": "CulturalOnboardingPage-Cu7AFwSZ.js", "sources": ["../../node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/CheckIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/HandRaisedIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/MapIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js", "../../src/features/auth/components/CulturalIdentitySelector.tsx", "../../src/features/auth/components/WelcomeTour.tsx", "../../src/pages/CulturalOnboardingPage.tsx"], "sourcesContent": ["import * as React from \"react\";\nfunction AcademicCapIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(AcademicCapIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ArrowLeftIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowLeftIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ArrowRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowRightIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m4.5 12.75 6 6 9-13.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction HandRaisedIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M10.05 4.575a1.575 1.575 0 1 0-3.15 0v3m3.15-3v-1.5a1.575 1.575 0 0 1 3.15 0v1.5m-3.15 0 .075 5.925m3.075.75V4.575m0 0a1.575 1.575 0 0 1 3.15 0V15M6.9 7.575a1.575 1.575 0 1 0-3.15 0v8.175a6.75 6.75 0 0 0 6.75 6.75h2.018a5.25 5.25 0 0 0 3.712-1.538l1.732-1.732a5.25 5.25 0 0 0 1.538-3.712l.003-2.024a.668.668 0 0 1 .198-.471 1.575 1.575 0 1 0-2.228-2.228 3.818 3.818 0 0 0-1.12 2.687M6.9 7.575V12m6.27 4.318A4.49 4.49 0 0 1 16.35 15m.002 0h-.002\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(HandRaisedIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction InformationCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(InformationCircleIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction MapIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 6.75V15m6-6v8.25m.503 3.498 4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 0 0-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MapIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction UsersIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UsersIcon);\nexport default ForwardRef;", "import React, { useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { CheckIcon, InformationCircleIcon } from '@heroicons/react/24/outline'\nimport Button from '@/components/ui/Button'\nimport Card, { CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card'\nimport { clsx } from 'clsx'\n\ninterface CulturalIdentityOption {\n  id: string\n  name: string\n  description: string\n  region?: string\n}\n\ninterface CulturalIdentitySelectorProps {\n  selectedIdentities: string[]\n  onSelectionChange: (identities: string[]) => void\n  onContinue: () => void\n  onSkip: () => void\n}\n\nconst CulturalIdentitySelector: React.FC<CulturalIdentitySelectorProps> = ({\n  selectedIdentities,\n  onSelectionChange,\n  onContinue,\n  onSkip,\n}) => {\n  const { t } = useTranslation()\n  const [showTooltip, setShowTooltip] = useState<string | null>(null)\n\n  // South African cultural identities\n  const culturalIdentities: CulturalIdentityOption[] = [\n    {\n      id: 'zulu',\n      name: t('cultural.identities.zulu'),\n      description: 'The largest ethnic group in South Africa, primarily in KwaZulu-Natal',\n      region: 'KwaZulu-Natal',\n    },\n    {\n      id: 'xhosa',\n      name: t('cultural.identities.xhosa'),\n      description: 'Primarily in Eastern Cape and Western Cape provinces',\n      region: 'Eastern Cape, Western Cape',\n    },\n    {\n      id: 'afrikaans',\n      name: t('cultural.identities.afrikaans'),\n      description: 'Afrikaans-speaking community across South Africa',\n      region: 'Western Cape, Northern Cape',\n    },\n    {\n      id: 'english',\n      name: t('cultural.identities.english'),\n      description: 'English-speaking South African community',\n      region: 'Gauteng, KwaZulu-Natal',\n    },\n    {\n      id: 'sotho',\n      name: t('cultural.identities.sotho'),\n      description: 'Sesotho-speaking community, primarily in Free State',\n      region: 'Free State, Gauteng',\n    },\n    {\n      id: 'tswana',\n      name: t('cultural.identities.tswana'),\n      description: 'Setswana-speaking community in North West province',\n      region: 'North West, Gauteng',\n    },\n    {\n      id: 'tsonga',\n      name: t('cultural.identities.tsonga'),\n      description: 'Xitsonga-speaking community in Limpopo and Mpumalanga',\n      region: 'Limpopo, Mpumalanga',\n    },\n    {\n      id: 'venda',\n      name: t('cultural.identities.venda'),\n      description: 'Tshivenda-speaking community in Limpopo',\n      region: 'Limpopo',\n    },\n    {\n      id: 'swazi',\n      name: t('cultural.identities.swazi'),\n      description: 'SiSwati-speaking community in Mpumalanga',\n      region: 'Mpumalanga',\n    },\n    {\n      id: 'ndebele',\n      name: t('cultural.identities.ndebele'),\n      description: 'IsiNdebele-speaking community in Mpumalanga and Limpopo',\n      region: 'Mpumalanga, Limpopo',\n    },\n    {\n      id: 'indian',\n      name: t('cultural.identities.indian'),\n      description: 'South African Indian community',\n      region: 'KwaZulu-Natal, Gauteng',\n    },\n    {\n      id: 'coloured',\n      name: t('cultural.identities.coloured'),\n      description: 'Mixed-race South African community',\n      region: 'Western Cape, Northern Cape',\n    },\n    {\n      id: 'other',\n      name: t('cultural.identities.other'),\n      description: 'Other cultural backgrounds and mixed heritage',\n      region: 'All provinces',\n    },\n  ]\n\n  const handleIdentityToggle = (identityId: string) => {\n    const newSelection = selectedIdentities.includes(identityId)\n      ? selectedIdentities.filter(id => id !== identityId)\n      : [...selectedIdentities, identityId]\n    \n    onSelectionChange(newSelection)\n  }\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\" variant=\"cultural\">\n      <CardHeader>\n        <CardTitle className=\"text-center text-cultural-gradient\">\n          {t('auth.culturalIdentity.title')}\n        </CardTitle>\n        <CardDescription className=\"text-center\">\n          {t('auth.culturalIdentity.description')}\n        </CardDescription>\n        \n        <div className=\"flex items-center justify-center space-x-4 text-sm text-gray-600\">\n          <span className=\"flex items-center\">\n            <InformationCircleIcon className=\"w-4 h-4 mr-1\" />\n            {t('auth.culturalIdentity.multiple')}\n          </span>\n          <span className=\"flex items-center\">\n            <InformationCircleIcon className=\"w-4 h-4 mr-1\" />\n            {t('auth.culturalIdentity.optional')}\n          </span>\n        </div>\n      </CardHeader>\n\n      <CardContent>\n        <div className=\"space-y-6\">\n          {/* Cultural Identity Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n            {culturalIdentities.map((identity) => (\n              <div\n                key={identity.id}\n                className={clsx(\n                  'relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200',\n                  'hover:shadow-md focus-within:ring-2 focus-within:ring-cultural-500',\n                  selectedIdentities.includes(identity.id)\n                    ? 'border-cultural-500 bg-cultural-50'\n                    : 'border-gray-200 hover:border-cultural-300'\n                )}\n                onClick={() => handleIdentityToggle(identity.id)}\n                onMouseEnter={() => setShowTooltip(identity.id)}\n                onMouseLeave={() => setShowTooltip(null)}\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-medium text-gray-900\">{identity.name}</h3>\n                    {identity.region && (\n                      <p className=\"text-sm text-gray-500 mt-1\">{identity.region}</p>\n                    )}\n                  </div>\n                  \n                  <div className={clsx(\n                    'w-5 h-5 rounded border-2 flex items-center justify-center',\n                    selectedIdentities.includes(identity.id)\n                      ? 'border-cultural-500 bg-cultural-500'\n                      : 'border-gray-300'\n                  )}>\n                    {selectedIdentities.includes(identity.id) && (\n                      <CheckIcon className=\"w-3 h-3 text-white\" />\n                    )}\n                  </div>\n                </div>\n\n                {/* Tooltip */}\n                {showTooltip === identity.id && (\n                  <div className=\"absolute z-10 bottom-full left-0 right-0 mb-2 p-2 bg-gray-900 text-white text-xs rounded shadow-lg\">\n                    {identity.description}\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Selection Summary */}\n          {selectedIdentities.length > 0 && (\n            <div className=\"p-4 bg-ubuntu-50 border border-ubuntu-200 rounded-lg\">\n              <h4 className=\"font-medium text-ubuntu-800 mb-2\">\n                Selected Cultural Identities ({selectedIdentities.length})\n              </h4>\n              <div className=\"flex flex-wrap gap-2\">\n                {selectedIdentities.map((identityId) => {\n                  const identity = culturalIdentities.find(i => i.id === identityId)\n                  return (\n                    <span\n                      key={identityId}\n                      className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-ubuntu-100 text-ubuntu-800\"\n                    >\n                      {identity?.name}\n                    </span>\n                  )\n                })}\n              </div>\n            </div>\n          )}\n\n          {/* Privacy Notice */}\n          <div className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n            <p className=\"text-sm text-blue-800\">\n              🔒 {t('auth.culturalIdentity.privacy')}\n            </p>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-3\">\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              fullWidth\n              onClick={onSkip}\n            >\n              {t('auth.culturalIdentity.skip')}\n            </Button>\n            \n            <Button\n              variant=\"cultural\"\n              size=\"lg\"\n              fullWidth\n              onClick={onContinue}\n            >\n              {t('auth.culturalIdentity.continue')}\n            </Button>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\nexport default CulturalIdentitySelector\n", "import React, { useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { \n  MapIcon, \n  UsersIcon, \n  AcademicCapIcon, \n  HandRaisedIcon,\n  ArrowRightIcon,\n  ArrowLeftIcon\n} from '@heroicons/react/24/outline'\nimport Button from '@/components/ui/Button'\nimport Card, { CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card'\n\ninterface WelcomeTourProps {\n  onComplete: () => void\n  onSkip: () => void\n  userCulturalIdentities: string[]\n}\n\ninterface TourStep {\n  id: string\n  title: string\n  description: string\n  icon: React.ReactNode\n  features: string[]\n  culturalContext?: string\n}\n\nconst WelcomeTour: React.FC<WelcomeTourProps> = ({\n  onComplete,\n  onSkip,\n  userCulturalIdentities,\n}) => {\n  const { t } = useTranslation()\n  const [currentStepIndex, setCurrentStepIndex] = useState(0)\n\n  const tourSteps: TourStep[] = [\n    {\n      id: 'platform-overview',\n      title: 'Welcome to Ubuntu Connect',\n      description: 'A platform that celebrates South African cultural diversity while building bridges between communities.',\n      icon: <HandRaisedIcon className=\"w-12 h-12 text-cultural-500\" />,\n      features: [\n        'Connect with people from all 11 official language groups',\n        'Share and learn about different cultural traditions',\n        'Collaborate on projects that unite communities',\n        'Respect and celebrate our Rainbow Nation heritage'\n      ],\n      culturalContext: userCulturalIdentities.length > 0 \n        ? `We see you've selected ${userCulturalIdentities.length} cultural ${userCulturalIdentities.length === 1 ? 'identity' : 'identities'}. This will help us connect you with relevant communities and content.`\n        : 'You can always add your cultural identities later to get personalized recommendations.'\n    },\n    {\n      id: 'cultural-discovery',\n      title: 'Discover Cultural Heritage',\n      description: 'Explore the rich tapestry of South African cultures through interactive maps, stories, and traditions.',\n      icon: <MapIcon className=\"w-12 h-12 text-ubuntu-500\" />,\n      features: [\n        'Interactive cultural map of South Africa',\n        'Traditional stories and historical content',\n        'Language learning opportunities',\n        'Cultural events and celebrations'\n      ],\n      culturalContext: 'Every culture has unique wisdom to share. Ubuntu Connect helps preserve and share this knowledge respectfully.'\n    },\n    {\n      id: 'community-features',\n      title: 'Join Diverse Communities',\n      description: 'Find communities based on location, interests, or cultural background. Build meaningful cross-cultural connections.',\n      icon: <UsersIcon className=\"w-12 h-12 text-cultural-500\" />,\n      features: [\n        'Location-based community discovery',\n        'Cross-cultural collaboration projects',\n        'Skill sharing and mentorship',\n        'Cultural exchange programs'\n      ],\n      culturalContext: 'Ubuntu philosophy teaches us \"I am because we are.\" Together, we are stronger and more creative.'\n    },\n    {\n      id: 'knowledge-exchange',\n      title: 'Share Knowledge & Skills',\n      description: 'Exchange skills, mentor others, and learn from diverse perspectives in our knowledge marketplace.',\n      icon: <AcademicCapIcon className=\"w-12 h-12 text-ubuntu-500\" />,\n      features: [\n        'Skill-based matching system',\n        'Time banking for fair exchange',\n        'Cultural context in learning',\n        'Recognition and achievements'\n      ],\n      culturalContext: 'Every person has valuable knowledge. Ubuntu Connect helps you share your gifts and learn from others.'\n    }\n  ]\n\n  const currentStep = tourSteps[currentStepIndex]\n  const isLastStep = currentStepIndex === tourSteps.length - 1\n\n  const handleNext = () => {\n    if (isLastStep) {\n      onComplete()\n    } else {\n      setCurrentStepIndex(currentStepIndex + 1)\n    }\n  }\n\n  const handlePrevious = () => {\n    if (currentStepIndex > 0) {\n      setCurrentStepIndex(currentStepIndex - 1)\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-3xl mx-auto\" variant=\"cultural\">\n      <CardHeader>\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            {currentStep.icon}\n            <div>\n              <CardTitle className=\"text-2xl text-cultural-gradient\">\n                {currentStep.title}\n              </CardTitle>\n              <CardDescription className=\"text-lg\">\n                {currentStep.description}\n              </CardDescription>\n            </div>\n          </div>\n          \n          <div className=\"text-sm text-gray-500\">\n            {currentStepIndex + 1} of {tourSteps.length}\n          </div>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className=\"bg-gradient-to-r from-cultural-500 to-ubuntu-500 h-2 rounded-full transition-all duration-300\"\n            style={{ width: `${((currentStepIndex + 1) / tourSteps.length) * 100}%` }}\n          />\n        </div>\n      </CardHeader>\n\n      <CardContent>\n        <div className=\"space-y-6\">\n          {/* Features List */}\n          <div className=\"space-y-3\">\n            <h4 className=\"font-semibold text-gray-800\">Key Features:</h4>\n            <ul className=\"space-y-2\">\n              {currentStep.features.map((feature, index) => (\n                <li key={index} className=\"flex items-start space-x-3\">\n                  <div className=\"w-2 h-2 bg-cultural-500 rounded-full mt-2 flex-shrink-0\" />\n                  <span className=\"text-gray-700\">{feature}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Cultural Context */}\n          {currentStep.culturalContext && (\n            <div className=\"p-4 bg-ubuntu-50 border border-ubuntu-200 rounded-lg\">\n              <p className=\"text-ubuntu-800 text-sm\">\n                <span className=\"font-medium\">Cultural Insight:</span> {currentStep.culturalContext}\n              </p>\n            </div>\n          )}\n\n          {/* Navigation Buttons */}\n          <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4 pt-4\">\n            <div className=\"flex space-x-3\">\n              <Button\n                variant=\"ghost\"\n                onClick={onSkip}\n              >\n                Skip Tour\n              </Button>\n              \n              {currentStepIndex > 0 && (\n                <Button\n                  variant=\"outline\"\n                  onClick={handlePrevious}\n                  leftIcon={<ArrowLeftIcon className=\"w-4 h-4\" />}\n                >\n                  Previous\n                </Button>\n              )}\n            </div>\n\n            <Button\n              variant=\"cultural\"\n              size=\"lg\"\n              onClick={handleNext}\n              rightIcon={!isLastStep ? <ArrowRightIcon className=\"w-4 h-4\" /> : undefined}\n            >\n              {isLastStep ? 'Start Your Journey' : 'Next'}\n            </Button>\n          </div>\n\n          {/* Step Indicators */}\n          <div className=\"flex justify-center space-x-2 pt-4\">\n            {tourSteps.map((_, index) => (\n              <button\n                key={index}\n                className={`w-3 h-3 rounded-full transition-colors duration-200 ${\n                  index === currentStepIndex\n                    ? 'bg-cultural-500'\n                    : index < currentStepIndex\n                    ? 'bg-ubuntu-400'\n                    : 'bg-gray-300'\n                }`}\n                onClick={() => setCurrentStepIndex(index)}\n                aria-label={`Go to step ${index + 1}`}\n              />\n            ))}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\nexport default WelcomeTour\n", "import React, { useState } from 'react'\nimport { useNavigate } from 'react-router-dom'\nimport { useTranslation } from 'react-i18next'\nimport CulturalIdentitySelector from '@/features/auth/components/CulturalIdentitySelector'\nimport WelcomeTour from '@/features/auth/components/WelcomeTour'\nimport { useCultural } from '@/store'\nimport { useAuth } from '@/features/auth/hooks/useAuth'\n\ntype OnboardingStep = 'cultural-identity' | 'welcome-tour' | 'complete'\n\nconst CulturalOnboardingPage: React.FC = () => {\n  const [currentStep, setCurrentStep] = useState<OnboardingStep>('cultural-identity')\n  const [selectedIdentities, setSelectedIdentities] = useState<string[]>([])\n  const navigate = useNavigate()\n  const { t } = useTranslation()\n  const { setCulturalIdentities } = useCultural()\n  const { user } = useAuth()\n\n  const handleCulturalIdentityComplete = () => {\n    // Save cultural identities to store\n    setCulturalIdentities(selectedIdentities)\n    \n    // Move to welcome tour\n    setCurrentStep('welcome-tour')\n  }\n\n  const handleSkipCulturalIdentity = () => {\n    // Move to welcome tour without setting identities\n    setCurrentStep('welcome-tour')\n  }\n\n  const handleWelcomeTourComplete = () => {\n    // Mark onboarding as complete and navigate to home\n    setCurrentStep('complete')\n    navigate('/')\n  }\n\n  const handleSkipWelcomeTour = () => {\n    // Skip tour and go directly to home\n    navigate('/')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-cultural-50 via-white to-ubuntu-50\">\n      {/* Progress Indicator */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"py-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-cultural-500 to-ubuntu-500 rounded-full\" />\n                <h1 className=\"text-xl font-bold text-cultural-gradient\">\n                  {t('app.name')}\n                </h1>\n              </div>\n              \n              <div className=\"flex items-center space-x-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  currentStep === 'cultural-identity' ? 'bg-cultural-500' : 'bg-gray-300'\n                }`} />\n                <div className={`w-3 h-3 rounded-full ${\n                  currentStep === 'welcome-tour' ? 'bg-cultural-500' : 'bg-gray-300'\n                }`} />\n                <span className=\"text-sm text-gray-600 ml-2\">\n                  Welcome Setup\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <main className=\"flex-1 flex items-center justify-center p-4 py-8\">\n        {currentStep === 'cultural-identity' && (\n          <CulturalIdentitySelector\n            selectedIdentities={selectedIdentities}\n            onSelectionChange={setSelectedIdentities}\n            onContinue={handleCulturalIdentityComplete}\n            onSkip={handleSkipCulturalIdentity}\n          />\n        )}\n\n        {currentStep === 'welcome-tour' && (\n          <WelcomeTour\n            onComplete={handleWelcomeTourComplete}\n            onSkip={handleSkipWelcomeTour}\n            userCulturalIdentities={selectedIdentities}\n          />\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"p-4 text-center text-sm text-gray-600\">\n        <p>Welcome to Ubuntu Connect, {user?.displayName || 'friend'}!</p>\n        <p className=\"mt-1\">\n          Let's set up your cultural journey • Building bridges across cultures\n        </p>\n      </footer>\n    </div>\n  )\n}\n\nexport default CulturalOnboardingPage\n"], "names": ["AcademicCapIcon", "title", "titleId", "props", "svgRef", "React.createElement", "ForwardRef", "React.forwardRef", "ArrowLeftIcon", "ArrowRightIcon", "CheckIcon", "HandRaisedIcon", "InformationCircleIcon", "MapIcon", "UsersIcon", "CulturalIdentitySelector", "selectedIdentities", "onSelectionChange", "onContinue", "onSkip", "t", "useTranslation", "showTooltip", "setShowTooltip", "useState", "culturalIdentities", "handleIdentityToggle", "identityId", "newSelection", "id", "jsxs", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "identity", "clsx", "i", "<PERSON><PERSON>", "WelcomeTour", "onComplete", "userCulturalIdentities", "currentStepIndex", "setCurrentStepIndex", "tourSteps", "currentStep", "isLastStep", "handleNext", "handlePrevious", "feature", "index", "_", "CulturalOnboardingPage", "setCurrentStep", "setSelectedIdentities", "navigate", "useNavigate", "setCulturalIdentities", "useCultural", "user", "useAuth", "handleCulturalIdentityComplete", "handleSkipCulturalIdentity", "handleWelcomeTourComplete", "handleSkipWelcomeTour"], "mappings": "6LACA,SAASA,EAAgB,CACvB,MAAAC,EACA,QAAAC,EACA,GAAGC,CACL,EAAGC,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBF,CACpB,EAAEC,CAAK,EAAGF,EAAqBI,EAAAA,cAAoB,QAAS,CAC3D,GAAIH,CACL,EAAED,CAAK,EAAI,KAAmBI,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,kdACP,CAAG,CAAC,CACJ,CACA,MAAMC,EAA2BC,EAAgB,WAACP,CAAe,ECvBjE,SAASQ,EAAc,CACrB,MAAAP,EACA,QAAAC,EACA,GAAGC,CACL,EAAGC,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBF,CACpB,EAAEC,CAAK,EAAGF,EAAqBI,EAAAA,cAAoB,QAAS,CAC3D,GAAIH,CACL,EAAED,CAAK,EAAI,KAAmBI,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,qCACP,CAAG,CAAC,CACJ,CACA,MAAMC,EAA2BC,EAAgB,WAACC,CAAa,ECvB/D,SAASC,EAAe,CACtB,MAAAR,EACA,QAAAC,EACA,GAAGC,CACL,EAAGC,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBF,CACpB,EAAEC,CAAK,EAAGF,EAAqBI,EAAAA,cAAoB,QAAS,CAC3D,GAAIH,CACL,EAAED,CAAK,EAAI,KAAmBI,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,qCACP,CAAG,CAAC,CACJ,CACA,MAAMC,EAA2BC,EAAgB,WAACE,CAAc,ECvBhE,SAASC,EAAU,CACjB,MAAAT,EACA,QAAAC,EACA,GAAGC,CACL,EAAGC,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBF,CACpB,EAAEC,CAAK,EAAGF,EAAqBI,EAAAA,cAAoB,QAAS,CAC3D,GAAIH,CACL,EAAED,CAAK,EAAI,KAAmBI,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,uBACP,CAAG,CAAC,CACJ,CACA,MAAMC,EAA2BC,EAAgB,WAACG,CAAS,ECvB3D,SAASC,EAAe,CACtB,MAAAV,EACA,QAAAC,EACA,GAAGC,CACL,EAAGC,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBF,CACpB,EAAEC,CAAK,EAAGF,EAAqBI,EAAAA,cAAoB,QAAS,CAC3D,GAAIH,CACL,EAAED,CAAK,EAAI,KAAmBI,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,8bACP,CAAG,CAAC,CACJ,CACA,MAAMC,EAA2BC,EAAgB,WAACI,CAAc,ECvBhE,SAASC,EAAsB,CAC7B,MAAAX,EACA,QAAAC,EACA,GAAGC,CACL,EAAGC,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBF,CACpB,EAAEC,CAAK,EAAGF,EAAqBI,EAAAA,cAAoB,QAAS,CAC3D,GAAIH,CACL,EAAED,CAAK,EAAI,KAAmBI,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,0JACP,CAAG,CAAC,CACJ,CACA,MAAMC,EAA2BC,EAAgB,WAACK,CAAqB,ECvBvE,SAASC,EAAQ,CACf,MAAAZ,EACA,QAAAC,EACA,GAAGC,CACL,EAAGC,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBF,CACpB,EAAEC,CAAK,EAAGF,EAAqBI,EAAAA,cAAoB,QAAS,CAC3D,GAAIH,CACL,EAAED,CAAK,EAAI,KAAmBI,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,sUACP,CAAG,CAAC,CACJ,CACA,MAAMC,EAA2BC,EAAgB,WAACM,CAAO,ECvBzD,SAASC,EAAU,CACjB,MAAAb,EACA,QAAAC,EACA,GAAGC,CACL,EAAGC,EAAQ,CACT,OAAoBC,gBAAoB,MAAO,OAAO,OAAO,CAC3D,MAAO,6BACP,KAAM,OACN,QAAS,YACT,YAAa,IACb,OAAQ,eACR,cAAe,OACf,YAAa,OACb,IAAKD,EACL,kBAAmBF,CACpB,EAAEC,CAAK,EAAGF,EAAqBI,EAAAA,cAAoB,QAAS,CAC3D,GAAIH,CACL,EAAED,CAAK,EAAI,KAAmBI,EAAAA,cAAoB,OAAQ,CACzD,cAAe,QACf,eAAgB,QAChB,EAAG,2XACP,CAAG,CAAC,CACJ,CACA,MAAMC,EAA2BC,EAAgB,WAACO,CAAS,ECHrDC,EAAoE,CAAC,CACzE,mBAAAC,EACA,kBAAAC,EACA,WAAAC,EACA,OAAAC,CACF,IAAM,CACE,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvB,CAACC,EAAaC,CAAc,EAAIC,EAAAA,SAAwB,IAAI,EAG5DC,EAA+C,CACnD,CACE,GAAI,OACJ,KAAML,EAAE,0BAA0B,EAClC,YAAa,uEACb,OAAQ,eACV,EACA,CACE,GAAI,QACJ,KAAMA,EAAE,2BAA2B,EACnC,YAAa,uDACb,OAAQ,4BACV,EACA,CACE,GAAI,YACJ,KAAMA,EAAE,+BAA+B,EACvC,YAAa,mDACb,OAAQ,6BACV,EACA,CACE,GAAI,UACJ,KAAMA,EAAE,6BAA6B,EACrC,YAAa,2CACb,OAAQ,wBACV,EACA,CACE,GAAI,QACJ,KAAMA,EAAE,2BAA2B,EACnC,YAAa,sDACb,OAAQ,qBACV,EACA,CACE,GAAI,SACJ,KAAMA,EAAE,4BAA4B,EACpC,YAAa,qDACb,OAAQ,qBACV,EACA,CACE,GAAI,SACJ,KAAMA,EAAE,4BAA4B,EACpC,YAAa,wDACb,OAAQ,qBACV,EACA,CACE,GAAI,QACJ,KAAMA,EAAE,2BAA2B,EACnC,YAAa,0CACb,OAAQ,SACV,EACA,CACE,GAAI,QACJ,KAAMA,EAAE,2BAA2B,EACnC,YAAa,2CACb,OAAQ,YACV,EACA,CACE,GAAI,UACJ,KAAMA,EAAE,6BAA6B,EACrC,YAAa,0DACb,OAAQ,qBACV,EACA,CACE,GAAI,SACJ,KAAMA,EAAE,4BAA4B,EACpC,YAAa,iCACb,OAAQ,wBACV,EACA,CACE,GAAI,WACJ,KAAMA,EAAE,8BAA8B,EACtC,YAAa,qCACb,OAAQ,6BACV,EACA,CACE,GAAI,QACJ,KAAMA,EAAE,2BAA2B,EACnC,YAAa,gDACb,OAAQ,eAAA,CAEZ,EAEMM,EAAwBC,GAAuB,CACnD,MAAMC,EAAeZ,EAAmB,SAASW,CAAU,EACvDX,EAAmB,OAAaa,GAAAA,IAAOF,CAAU,EACjD,CAAC,GAAGX,EAAoBW,CAAU,EAEtCV,EAAkBW,CAAY,CAChC,EAEA,OACGE,EAAAA,KAAAC,EAAA,CAAK,UAAU,2BAA2B,QAAQ,WACjD,SAAA,CAAAD,OAACE,EACC,CAAA,SAAA,CAAAC,MAACC,EAAU,CAAA,UAAU,qCAClB,SAAAd,EAAE,6BAA6B,EAClC,QACCe,EAAgB,CAAA,UAAU,cACxB,SAAAf,EAAE,mCAAmC,EACxC,EAEAU,EAAAA,KAAC,MAAI,CAAA,UAAU,mEACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,oBACd,SAAA,CAACG,EAAAA,IAAArB,EAAA,CAAsB,UAAU,cAAe,CAAA,EAC/CQ,EAAE,gCAAgC,CAAA,EACrC,EACAU,EAAAA,KAAC,OAAK,CAAA,UAAU,oBACd,SAAA,CAACG,EAAAA,IAAArB,EAAA,CAAsB,UAAU,cAAe,CAAA,EAC/CQ,EAAE,gCAAgC,CAAA,CACrC,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAECa,MAAAG,EAAA,CACC,SAACN,EAAAA,KAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAAAG,MAAC,OAAI,UAAU,wCACZ,SAAmBR,EAAA,IAAKY,GACvBP,EAAA,KAAC,MAAA,CAEC,UAAWQ,EACT,8EACA,qEACAtB,EAAmB,SAASqB,EAAS,EAAE,EACnC,qCACA,2CACN,EACA,QAAS,IAAMX,EAAqBW,EAAS,EAAE,EAC/C,aAAc,IAAMd,EAAec,EAAS,EAAE,EAC9C,aAAc,IAAMd,EAAe,IAAI,EAEvC,SAAA,CAACO,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAAAG,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAI,EAAS,KAAK,EACxDA,EAAS,QACRJ,EAAA,IAAC,KAAE,UAAU,6BAA8B,WAAS,MAAO,CAAA,CAAA,EAE/D,EAEAA,MAAC,OAAI,UAAWK,EACd,4DACAtB,EAAmB,SAASqB,EAAS,EAAE,EACnC,sCACA,iBACN,EACG,SAAmBrB,EAAA,SAASqB,EAAS,EAAE,GACrCJ,EAAA,IAAAvB,EAAA,CAAU,UAAU,oBAAqB,CAAA,CAE9C,CAAA,CAAA,EACF,EAGCY,IAAgBe,EAAS,IACxBJ,EAAAA,IAAC,OAAI,UAAU,qGACZ,WAAS,WACZ,CAAA,CAAA,CAAA,EApCGI,EAAS,EAuCjB,CAAA,EACH,EAGCrB,EAAmB,OAAS,GAC1Bc,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,mCAAmC,SAAA,CAAA,iCAChBd,EAAmB,OAAO,GAAA,EAC3D,QACC,MAAI,CAAA,UAAU,uBACZ,SAAmBA,EAAA,IAAKW,GAAe,CACtC,MAAMU,EAAWZ,EAAmB,KAAUc,GAAAA,EAAE,KAAOZ,CAAU,EAE/D,OAAAM,EAAA,IAAC,OAAA,CAEC,UAAU,wGAET,SAAUI,GAAA,IAAA,EAHNV,CAIP,CAAA,CAEH,CACH,CAAA,CAAA,EACF,QAID,MAAI,CAAA,UAAU,mDACb,SAACG,EAAA,KAAA,IAAA,CAAE,UAAU,wBAAwB,SAAA,CAAA,MAC/BV,EAAE,+BAA+B,CAAA,CAAA,CACvC,CACF,CAAA,EAGAU,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAG,EAAA,IAACO,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAS,GACT,QAASrB,EAER,WAAE,4BAA4B,CAAA,CACjC,EAEAc,EAAA,IAACO,EAAA,CACC,QAAQ,WACR,KAAK,KACL,UAAS,GACT,QAAStB,EAER,WAAE,gCAAgC,CAAA,CAAA,CACrC,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,ECvNMuB,EAA0C,CAAC,CAC/C,WAAAC,EACA,OAAAvB,EACA,uBAAAwB,CACF,IAAM,CACE,KAAA,CAAE,EAAAvB,CAAE,EAAIC,EAAe,EACvB,CAACuB,EAAkBC,CAAmB,EAAIrB,EAAAA,SAAS,CAAC,EAEpDsB,EAAwB,CAC5B,CACE,GAAI,oBACJ,MAAO,4BACP,YAAa,0GACb,KAAMb,EAAAA,IAACtB,EAAe,CAAA,UAAU,6BAA8B,CAAA,EAC9D,SAAU,CACR,2DACA,sDACA,iDACA,mDACF,EACA,gBAAiBgC,EAAuB,OAAS,EAC7C,0BAA0BA,EAAuB,MAAM,aAAaA,EAAuB,SAAW,EAAI,WAAa,YAAY,yEACnI,wFACN,EACA,CACE,GAAI,qBACJ,MAAO,6BACP,YAAa,yGACb,KAAMV,EAAAA,IAACpB,EAAQ,CAAA,UAAU,2BAA4B,CAAA,EACrD,SAAU,CACR,2CACA,6CACA,kCACA,kCACF,EACA,gBAAiB,gHACnB,EACA,CACE,GAAI,qBACJ,MAAO,2BACP,YAAa,sHACb,KAAMoB,EAAAA,IAACnB,EAAU,CAAA,UAAU,6BAA8B,CAAA,EACzD,SAAU,CACR,qCACA,wCACA,+BACA,4BACF,EACA,gBAAiB,kGACnB,EACA,CACE,GAAI,qBACJ,MAAO,2BACP,YAAa,oGACb,KAAMmB,EAAAA,IAACjC,EAAgB,CAAA,UAAU,2BAA4B,CAAA,EAC7D,SAAU,CACR,8BACA,iCACA,+BACA,8BACF,EACA,gBAAiB,uGAAA,CAErB,EAEM+C,EAAcD,EAAUF,CAAgB,EACxCI,EAAaJ,IAAqBE,EAAU,OAAS,EAErDG,EAAa,IAAM,CACnBD,EACSN,EAAA,EAEXG,EAAoBD,EAAmB,CAAC,CAE5C,EAEMM,EAAiB,IAAM,CACvBN,EAAmB,GACrBC,EAAoBD,EAAmB,CAAC,CAE5C,EAEA,OACGd,EAAAA,KAAAC,EAAA,CAAK,UAAU,2BAA2B,QAAQ,WACjD,SAAA,CAAAD,OAACE,EACC,CAAA,SAAA,CAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACZ,SAAA,CAAYiB,EAAA,YACZ,MACC,CAAA,SAAA,CAAAd,EAAA,IAACC,EAAU,CAAA,UAAU,kCAClB,SAAAa,EAAY,MACf,EACCd,EAAA,IAAAE,EAAA,CAAgB,UAAU,UACxB,WAAY,WACf,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAL,EAAAA,KAAC,MAAI,CAAA,UAAU,wBACZ,SAAA,CAAmBc,EAAA,EAAE,OAAKE,EAAU,MAAA,CACvC,CAAA,CAAA,EACF,EAGAb,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,gGACV,MAAO,CAAE,MAAO,IAAKW,EAAmB,GAAKE,EAAU,OAAU,GAAG,GAAI,CAAA,CAAA,CAE5E,CAAA,CAAA,EACF,EAECb,MAAAG,EAAA,CACC,SAACN,EAAAA,KAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACG,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAa,gBAAA,EACxDA,EAAA,IAAA,KAAA,CAAG,UAAU,YACX,SAAYc,EAAA,SAAS,IAAI,CAACI,EAASC,IACjCtB,OAAA,KAAA,CAAe,UAAU,6BACxB,SAAA,CAACG,EAAAA,IAAA,MAAA,CAAI,UAAU,yDAA0D,CAAA,EACxEA,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAiB,SAAQkB,CAAA,CAAA,CAAA,CAFlC,EAAAC,CAGT,CACD,CACH,CAAA,CAAA,EACF,EAGCL,EAAY,iBACVd,EAAAA,IAAA,MAAA,CAAI,UAAU,uDACb,SAAAH,EAAA,KAAC,IAAE,CAAA,UAAU,0BACX,SAAA,CAACG,EAAA,IAAA,OAAA,CAAK,UAAU,cAAc,SAAiB,oBAAA,EAAO,IAAEc,EAAY,eAAA,CAAA,CACtE,CACF,CAAA,EAIFjB,EAAAA,KAAC,MAAI,CAAA,UAAU,oEACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAAAG,EAAA,IAACO,EAAA,CACC,QAAQ,QACR,QAASrB,EACV,SAAA,WAAA,CAED,EAECyB,EAAmB,GAClBX,EAAA,IAACO,EAAA,CACC,QAAQ,UACR,QAASU,EACT,SAAUjB,EAAAA,IAACzB,EAAc,CAAA,UAAU,SAAU,CAAA,EAC9C,SAAA,UAAA,CAAA,CAED,EAEJ,EAEAyB,EAAA,IAACO,EAAA,CACC,QAAQ,WACR,KAAK,KACL,QAASS,EACT,UAAYD,EAAsD,aAAxCvC,EAAe,CAAA,UAAU,SAAU,CAAA,EAE5D,WAAa,qBAAuB,MAAA,CAAA,CACvC,EACF,EAGAwB,EAAAA,IAAC,OAAI,UAAU,qCACZ,WAAU,IAAI,CAACoB,EAAGD,IACjBnB,EAAA,IAAC,SAAA,CAEC,UAAW,uDACTmB,IAAUR,EACN,kBACAQ,EAAQR,EACR,gBACA,aACN,GACA,QAAS,IAAMC,EAAoBO,CAAK,EACxC,aAAY,cAAcA,EAAQ,CAAC,EAAA,EAT9BA,CAAA,CAWR,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,EC9MME,EAAmC,IAAM,CAC7C,KAAM,CAACP,EAAaQ,CAAc,EAAI/B,EAAAA,SAAyB,mBAAmB,EAC5E,CAACR,EAAoBwC,CAAqB,EAAIhC,EAAAA,SAAmB,CAAA,CAAE,EACnEiC,EAAWC,EAAY,EACvB,CAAE,EAAAtC,CAAE,EAAIC,EAAe,EACvB,CAAE,sBAAAsC,CAAsB,EAAIC,EAAY,EACxC,CAAE,KAAAC,CAAK,EAAIC,EAAQ,EAEnBC,EAAiC,IAAM,CAE3CJ,EAAsB3C,CAAkB,EAGxCuC,EAAe,cAAc,CAC/B,EAEMS,EAA6B,IAAM,CAEvCT,EAAe,cAAc,CAC/B,EAEMU,EAA4B,IAAM,CAEtCV,EAAe,UAAU,EACzBE,EAAS,GAAG,CACd,EAEMS,EAAwB,IAAM,CAElCT,EAAS,GAAG,CACd,EAGE,OAAA3B,EAAA,KAAC,MAAI,CAAA,UAAU,yEAEb,SAAA,CAAAG,MAAC,MAAI,CAAA,UAAU,8CACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,yCACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,OACb,SAACH,OAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACG,EAAAA,IAAA,MAAA,CAAI,UAAU,uEAAwE,CAAA,QACtF,KAAG,CAAA,UAAU,2CACX,SAAAb,EAAE,UAAU,CACf,CAAA,CAAA,EACF,EAEAU,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAG,EAAAA,IAAC,OAAI,UAAW,wBACdc,IAAgB,oBAAsB,kBAAoB,aAC5D,GAAI,EACJd,EAAAA,IAAC,OAAI,UAAW,wBACdc,IAAgB,eAAiB,kBAAoB,aACvD,GAAI,EACHd,EAAA,IAAA,OAAA,CAAK,UAAU,6BAA6B,SAE7C,eAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,EAGAH,EAAAA,KAAC,OAAK,CAAA,UAAU,mDACb,SAAA,CAAAiB,IAAgB,qBACfd,EAAA,IAAClB,EAAA,CACC,mBAAAC,EACA,kBAAmBwC,EACnB,WAAYO,EACZ,OAAQC,CAAA,CACV,EAGDjB,IAAgB,gBACfd,EAAA,IAACQ,EAAA,CACC,WAAYwB,EACZ,OAAQC,EACR,uBAAwBlD,CAAA,CAAA,CAC1B,EAEJ,EAGAc,EAAAA,KAAC,SAAO,CAAA,UAAU,wCAChB,SAAA,CAAAA,OAAC,IAAE,CAAA,SAAA,CAAA,8BAA4B+B,GAAM,aAAe,SAAS,GAAA,EAAC,EAC7D5B,EAAA,IAAA,IAAA,CAAE,UAAU,OAAO,SAEpB,uEAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}