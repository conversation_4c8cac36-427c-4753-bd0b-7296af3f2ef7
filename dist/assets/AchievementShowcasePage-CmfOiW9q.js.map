{"version": 3, "file": "AchievementShowcasePage-CmfOiW9q.js", "sources": ["../../src/services/achievementGalleryService.ts", "../../src/components/AchievementGallery.tsx", "../../src/services/recognitionEngineService.ts", "../../src/components/RecognitionDashboard.tsx", "../../src/services/culturalCurationService.ts", "../../src/components/CulturalCurationDashboard.tsx", "../../src/pages/AchievementShowcasePage.tsx"], "sourcesContent": ["import {\n  Achievement,\n  AchievementCategory,\n  AchievementFilters,\n  VerificationStatus,\n  GeographicLocation,\n  SearchQuery,\n  TimelineEvent,\n  EngagementReport\n} from '../types/achievement';\n\nexport interface AchievementCreationData {\n  title: string;\n  description: string;\n  category: AchievementCategory;\n  culturalContext: {\n    primaryCulture: string;\n    secondaryCultures: string[];\n    culturalSignificance: string;\n    traditionalElements: string[];\n    crossCulturalElements: string[];\n  };\n  location: GeographicLocation;\n  timeframe: {\n    startDate: Date;\n    endDate: Date;\n  };\n  mediaContent: {\n    type: 'image' | 'video' | 'audio' | 'document';\n    url: string;\n    caption: string;\n    culturalContext?: string;\n  }[];\n  submissionSource: {\n    submitterId: string;\n    submitterRole: 'community_member' | 'cultural_representative' | 'verified_expert';\n    communityEndorsements: string[];\n  };\n}\n\nexport interface AchievementUpdateData {\n  title?: string;\n  description?: string;\n  culturalContext?: any;\n  verification?: VerificationStatus;\n  mediaContent?: any[];\n}\n\nclass AchievementGalleryService {\n  private achievements: Map<string, Achievement> = new Map();\n  private featuredAchievements: string[] = [];\n  private categoryAchievements: Map<string, string[]> = new Map();\n  private locationAchievements: Map<string, string[]> = new Map();\n\n  // Achievement Management\n  async createAchievement(data: AchievementCreationData): Promise<Achievement> {\n    const achievement: Achievement = {\n      id: this.generateId(),\n      title: data.title,\n      description: data.description,\n      category: {\n        primary: data.category.primary,\n        secondary: data.category.secondary || [],\n        crossCultural: data.culturalContext.crossCulturalElements.length > 0,\n        nationalSignificance: this.determineNationalSignificance(data.location, data.culturalContext)\n      },\n      culturalContext: {\n        primaryCulture: data.culturalContext.primaryCulture,\n        secondaryCultures: data.culturalContext.secondaryCultures,\n        culturalSignificance: data.culturalContext.culturalSignificance,\n        traditionalElements: data.culturalContext.traditionalElements,\n        modernAdaptations: [],\n        crossCulturalElements: data.culturalContext.crossCulturalElements.map(element => ({\n          element,\n          culturalOrigins: [data.culturalContext.primaryCulture, ...data.culturalContext.secondaryCultures],\n          collaborationType: 'cultural_exchange',\n          impactDescription: 'Cross-cultural collaboration and understanding'\n        }))\n      },\n      location: data.location,\n      timeframe: {\n        startDate: data.timeframe.startDate,\n        endDate: data.timeframe.endDate,\n        historicalPeriod: this.determineHistoricalPeriod(data.timeframe.startDate),\n        culturalCalendarAlignment: []\n      },\n      mediaContent: data.mediaContent.map(media => ({\n        id: this.generateId(),\n        type: media.type,\n        url: media.url,\n        caption: media.caption,\n        culturalContext: media.culturalContext || '',\n        accessibility: {\n          altText: media.caption,\n          transcription: '',\n          culturalDescription: media.culturalContext || ''\n        },\n        permissions: {\n          publicAccess: true,\n          culturalCommunityAccess: true,\n          commercialUse: false,\n          attribution: 'required'\n        }\n      })),\n      verification: {\n        status: 'pending',\n        reviewers: [],\n        verificationScore: 0,\n        factCheckResults: [],\n        communityApproval: {\n          approvalScore: 0,\n          totalVotes: 0,\n          culturalRepresentativeApproval: false,\n          communityEndorsements: data.submissionSource.communityEndorsements\n        },\n        culturalApproval: {\n          culturalRepresentativeId: '',\n          approvalStatus: 'pending',\n          culturalAccuracyScore: 0,\n          traditionalKnowledgeValidation: false,\n          culturalSensitivityCheck: 'pending'\n        }\n      },\n      communityEndorsements: data.submissionSource.communityEndorsements.map(endorsement => ({\n        endorserId: endorsement,\n        endorsementType: 'community_support',\n        endorsementDate: new Date(),\n        endorsementDetails: 'Community member endorsement',\n        culturalContext: data.culturalContext.primaryCulture\n      })),\n      culturalSignificance: {\n        significanceLevel: 'community',\n        culturalImpact: data.culturalContext.culturalSignificance,\n        traditionalKnowledgeElements: data.culturalContext.traditionalElements,\n        modernRelevance: 'Contemporary cultural significance',\n        preservationValue: 8,\n        educationalValue: 7\n      },\n      relatedAchievements: [],\n      socialImpact: {\n        communityBenefit: 'Positive community impact',\n        culturalPreservation: 'Cultural knowledge preservation',\n        crossCulturalUnderstanding: 'Enhanced cross-cultural understanding',\n        inspirationalValue: 8,\n        roleModelPotential: 7\n      },\n      submissionSource: {\n        submitterId: data.submissionSource.submitterId,\n        submissionDate: new Date(),\n        submitterRole: data.submissionSource.submitterRole,\n        submissionMethod: 'web_form',\n        verificationDocuments: [],\n        communityEndorsements: data.submissionSource.communityEndorsements\n      },\n      engagementMetrics: {\n        views: 0,\n        shares: 0,\n        culturalDiscoveries: 0,\n        inspirationSaves: 0,\n        crossCulturalEngagement: 0\n      },\n      createdAt: new Date(),\n      updatedAt: new Date()\n    };\n\n    this.achievements.set(achievement.id, achievement);\n    this.indexAchievement(achievement);\n    return achievement;\n  }\n\n  async getAchievement(achievementId: string): Promise<Achievement | null> {\n    return this.achievements.get(achievementId) || null;\n  }\n\n  async updateAchievement(achievementId: string, updates: AchievementUpdateData): Promise<Achievement> {\n    const achievement = this.achievements.get(achievementId);\n    if (!achievement) {\n      throw new Error('Achievement not found');\n    }\n\n    const updatedAchievement = {\n      ...achievement,\n      ...updates,\n      updatedAt: new Date()\n    };\n\n    this.achievements.set(achievementId, updatedAchievement);\n    return updatedAchievement;\n  }\n\n  async deleteAchievement(achievementId: string): Promise<boolean> {\n    const deleted = this.achievements.delete(achievementId);\n    this.removeFromIndexes(achievementId);\n    return deleted;\n  }\n\n  // Gallery Browsing\n  async getAchievements(filters: AchievementFilters): Promise<Achievement[]> {\n    let achievements = Array.from(this.achievements.values());\n\n    if (filters.category) {\n      achievements = achievements.filter(a => a.category.primary === filters.category);\n    }\n\n    if (filters.culturalContext) {\n      achievements = achievements.filter(a =>\n        a.culturalContext.primaryCulture === filters.culturalContext ||\n        a.culturalContext.secondaryCultures.includes(filters.culturalContext!)\n      );\n    }\n\n    if (filters.location) {\n      achievements = achievements.filter(a =>\n        a.location.province === filters.location?.province ||\n        a.location.city === filters.location?.city\n      );\n    }\n\n    if (filters.timeframe) {\n      achievements = achievements.filter(a =>\n        a.timeframe.startDate >= filters.timeframe!.startDate &&\n        a.timeframe.endDate <= filters.timeframe!.endDate\n      );\n    }\n\n    if (filters.verificationStatus) {\n      achievements = achievements.filter(a => a.verification.status === filters.verificationStatus);\n    }\n\n    // Sort by relevance and engagement\n    achievements.sort((a, b) => {\n      const scoreA = this.calculateRelevanceScore(a, filters);\n      const scoreB = this.calculateRelevanceScore(b, filters);\n      return scoreB - scoreA;\n    });\n\n    return achievements.slice(0, filters.limit || 50);\n  }\n\n  async getFeaturedAchievements(): Promise<Achievement[]> {\n    const featured = this.featuredAchievements\n      .map(id => this.achievements.get(id))\n      .filter(Boolean) as Achievement[];\n\n    return featured.sort((a, b) => b.engagementMetrics.views - a.engagementMetrics.views);\n  }\n\n  async getAchievementsByCategory(category: AchievementCategory): Promise<Achievement[]> {\n    const categoryIds = this.categoryAchievements.get(category.primary) || [];\n    return categoryIds\n      .map(id => this.achievements.get(id))\n      .filter(Boolean) as Achievement[];\n  }\n\n  async getAchievementsByLocation(location: GeographicLocation): Promise<Achievement[]> {\n    const locationKey = `${location.province}-${location.city}`;\n    const locationIds = this.locationAchievements.get(locationKey) || [];\n    return locationIds\n      .map(id => this.achievements.get(id))\n      .filter(Boolean) as Achievement[];\n  }\n\n  // Search and Discovery\n  async searchAchievements(query: SearchQuery): Promise<Achievement[]> {\n    const achievements = Array.from(this.achievements.values());\n    const searchTerms = query.searchText.toLowerCase().split(' ');\n\n    const results = achievements.filter(achievement => {\n      const searchableText = [\n        achievement.title,\n        achievement.description,\n        achievement.culturalContext.culturalSignificance,\n        achievement.culturalContext.primaryCulture,\n        ...achievement.culturalContext.secondaryCultures,\n        ...achievement.culturalContext.traditionalElements\n      ].join(' ').toLowerCase();\n\n      return searchTerms.every(term => searchableText.includes(term));\n    });\n\n    // Apply additional filters\n    let filteredResults = results;\n\n    if (query.filters?.category) {\n      filteredResults = filteredResults.filter(a => a.category.primary === query.filters!.category);\n    }\n\n    if (query.filters?.culturalContext) {\n      filteredResults = filteredResults.filter(a =>\n        a.culturalContext.primaryCulture === query.filters!.culturalContext ||\n        a.culturalContext.secondaryCultures.includes(query.filters!.culturalContext!)\n      );\n    }\n\n    // Sort by relevance\n    filteredResults.sort((a, b) => {\n      const scoreA = this.calculateSearchRelevance(a, query.searchText);\n      const scoreB = this.calculateSearchRelevance(b, query.searchText);\n      return scoreB - scoreA;\n    });\n\n    return filteredResults.slice(0, query.limit || 20);\n  }\n\n  async getAchievementSuggestions(userId: string): Promise<Achievement[]> {\n    // Get user's cultural background and interests\n    const userProfile = await this.getUserProfile(userId);\n    const achievements = Array.from(this.achievements.values());\n\n    // Filter for cross-cultural achievements that might interest the user\n    const suggestions = achievements.filter(achievement => {\n      // Include achievements from other cultures\n      const isDifferentCulture = achievement.culturalContext.primaryCulture !== userProfile.primaryCulture;\n\n      // Include cross-cultural collaborations\n      const isCrossCultural = achievement.category.crossCultural;\n\n      // Include highly rated achievements\n      const isHighQuality = achievement.verification.verificationScore > 80;\n\n      return (isDifferentCulture || isCrossCultural) && isHighQuality;\n    });\n\n    // Sort by potential interest and cultural learning value\n    suggestions.sort((a, b) => {\n      const scoreA = this.calculateSuggestionScore(a, userProfile);\n      const scoreB = this.calculateSuggestionScore(b, userProfile);\n      return scoreB - scoreA;\n    });\n\n    return suggestions.slice(0, 10);\n  }\n\n  async getPopularAchievements(timeframe: string): Promise<Achievement[]> {\n    const achievements = Array.from(this.achievements.values());\n    const now = new Date();\n    let startDate: Date;\n\n    switch (timeframe) {\n      case 'week':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case 'month':\n        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        break;\n      case 'year':\n        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);\n        break;\n      default:\n        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    }\n\n    const recentAchievements = achievements.filter(a => a.createdAt >= startDate);\n\n    recentAchievements.sort((a, b) => {\n      const popularityA = a.engagementMetrics.views + a.engagementMetrics.shares * 3 + a.engagementMetrics.inspirationSaves * 2;\n      const popularityB = b.engagementMetrics.views + b.engagementMetrics.shares * 3 + b.engagementMetrics.inspirationSaves * 2;\n      return popularityB - popularityA;\n    });\n\n    return recentAchievements.slice(0, 20);\n  }\n\n  // Timeline and Historical View\n  async getAchievementTimeline(timeRange: { startDate: Date; endDate: Date }): Promise<TimelineEvent[]> {\n    const achievements = Array.from(this.achievements.values()).filter(a =>\n      a.timeframe.startDate >= timeRange.startDate && a.timeframe.endDate <= timeRange.endDate\n    );\n\n    const timelineEvents: TimelineEvent[] = achievements.map(achievement => ({\n      id: achievement.id,\n      title: achievement.title,\n      date: achievement.timeframe.startDate,\n      culturalContext: achievement.culturalContext.primaryCulture,\n      category: achievement.category.primary,\n      significance: achievement.culturalSignificance.significanceLevel,\n      description: achievement.description,\n      mediaUrl: achievement.mediaContent[0]?.url || '',\n      crossCultural: achievement.category.crossCultural\n    }));\n\n    return timelineEvents.sort((a, b) => a.date.getTime() - b.date.getTime());\n  }\n\n  // Related Achievements\n  async getRelatedAchievements(achievementId: string): Promise<Achievement[]> {\n    const achievement = this.achievements.get(achievementId);\n    if (!achievement) {\n      return [];\n    }\n\n    const allAchievements = Array.from(this.achievements.values());\n    const related = allAchievements.filter(a => {\n      if (a.id === achievementId) return false;\n\n      // Same cultural context\n      const sameCulture = a.culturalContext.primaryCulture === achievement.culturalContext.primaryCulture;\n\n      // Same category\n      const sameCategory = a.category.primary === achievement.category.primary;\n\n      // Same location\n      const sameLocation = a.location.province === achievement.location.province;\n\n      // Similar time period\n      const timeDiff = Math.abs(a.timeframe.startDate.getTime() - achievement.timeframe.startDate.getTime());\n      const sameEra = timeDiff < (50 * 365 * 24 * 60 * 60 * 1000); // Within 50 years\n\n      return sameCulture || sameCategory || sameLocation || sameEra;\n    });\n\n    // Sort by relevance\n    related.sort((a, b) => {\n      const scoreA = this.calculateRelatedScore(a, achievement);\n      const scoreB = this.calculateRelatedScore(b, achievement);\n      return scoreB - scoreA;\n    });\n\n    return related.slice(0, 6);\n  }\n\n  // Engagement Tracking\n  async trackAchievementView(achievementId: string, userId: string): Promise<void> {\n    const achievement = this.achievements.get(achievementId);\n    if (achievement) {\n      achievement.engagementMetrics.views++;\n\n      // Track cross-cultural discovery\n      const userProfile = await this.getUserProfile(userId);\n      if (userProfile.primaryCulture !== achievement.culturalContext.primaryCulture) {\n        achievement.engagementMetrics.crossCulturalEngagement++;\n      }\n\n      this.achievements.set(achievementId, achievement);\n    }\n  }\n\n  async trackCulturalDiscovery(fromCulture: string, toCulture: string, userId: string): Promise<void> {\n    // Track cultural discovery patterns for analytics\n    console.log(`Cultural discovery: ${fromCulture} → ${toCulture} by user ${userId}`);\n  }\n\n  async trackSocialSharing(achievementId: string, _platform: string): Promise<void> {\n    const achievement = this.achievements.get(achievementId);\n    if (achievement) {\n      achievement.engagementMetrics.shares++;\n      this.achievements.set(achievementId, achievement);\n    }\n  }\n\n  async generateEngagementReport(timeframe: { startDate: Date; endDate: Date }): Promise<EngagementReport> {\n    const achievements = Array.from(this.achievements.values()).filter(a =>\n      a.createdAt >= timeframe.startDate && a.createdAt <= timeframe.endDate\n    );\n\n    const totalViews = achievements.reduce((sum, a) => sum + a.engagementMetrics.views, 0);\n    const totalShares = achievements.reduce((sum, a) => sum + a.engagementMetrics.shares, 0);\n    const crossCulturalEngagement = achievements.reduce((sum, a) => sum + a.engagementMetrics.crossCulturalEngagement, 0);\n\n    const culturalDistribution = this.calculateCulturalDistribution(achievements);\n    const categoryDistribution = this.calculateCategoryDistribution(achievements);\n\n    return {\n      timeframe: {\n        ...timeframe,\n        historicalPeriod: 'democratic_era',\n        culturalCalendarAlignment: []\n      },\n      totalAchievements: achievements.length,\n      totalViews,\n      totalShares,\n      crossCulturalEngagement,\n      averageEngagement: totalViews / achievements.length || 0,\n      culturalDistribution,\n      categoryDistribution,\n      topAchievements: achievements\n        .sort((a, b) => b.engagementMetrics.views - a.engagementMetrics.views)\n        .slice(0, 10)\n        .map(a => ({ id: a.id, title: a.title, views: a.engagementMetrics.views }))\n    };\n  }\n\n  // Helper Methods\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n\n  private determineNationalSignificance(location: GeographicLocation, culturalContext: any): 'local' | 'provincial' | 'national' | 'international' {\n    // Simple logic - in real implementation would be more sophisticated\n    if (culturalContext.crossCulturalElements.length > 0) return 'national';\n    if (location.province) return 'provincial';\n    return 'local';\n  }\n\n  private determineHistoricalPeriod(date: Date): string {\n    const year = date.getFullYear();\n    if (year < 1900) return 'pre_colonial';\n    if (year < 1948) return 'early_colonial';\n    if (year < 1994) return 'apartheid_era';\n    return 'democratic_era';\n  }\n\n  private indexAchievement(achievement: Achievement): void {\n    // Index by category\n    const categoryKey = achievement.category.primary;\n    if (!this.categoryAchievements.has(categoryKey)) {\n      this.categoryAchievements.set(categoryKey, []);\n    }\n    this.categoryAchievements.get(categoryKey)!.push(achievement.id);\n\n    // Index by location\n    const locationKey = `${achievement.location.province}-${achievement.location.city}`;\n    if (!this.locationAchievements.has(locationKey)) {\n      this.locationAchievements.set(locationKey, []);\n    }\n    this.locationAchievements.get(locationKey)!.push(achievement.id);\n\n    // Add to featured if high quality\n    if (achievement.verification.verificationScore > 85 && achievement.category.crossCultural) {\n      this.featuredAchievements.push(achievement.id);\n    }\n  }\n\n  private removeFromIndexes(achievementId: string): void {\n    // Remove from all indexes\n    for (const [, ids] of this.categoryAchievements) {\n      const index = ids.indexOf(achievementId);\n      if (index > -1) ids.splice(index, 1);\n    }\n\n    for (const [, ids] of this.locationAchievements) {\n      const index = ids.indexOf(achievementId);\n      if (index > -1) ids.splice(index, 1);\n    }\n\n    const featuredIndex = this.featuredAchievements.indexOf(achievementId);\n    if (featuredIndex > -1) this.featuredAchievements.splice(featuredIndex, 1);\n  }\n\n  private calculateRelevanceScore(achievement: Achievement, _filters: AchievementFilters): number {\n    let score = 0;\n\n    // Base score from verification and engagement\n    score += achievement.verification.verificationScore * 0.3;\n    score += Math.min(achievement.engagementMetrics.views / 100, 50) * 0.2;\n\n    // Bonus for cross-cultural achievements\n    if (achievement.category.crossCultural) score += 20;\n\n    // Bonus for cultural significance\n    score += achievement.culturalSignificance.preservationValue * 2;\n\n    // Bonus for recent achievements\n    const daysSinceCreation = (Date.now() - achievement.createdAt.getTime()) / (1000 * 60 * 60 * 24);\n    if (daysSinceCreation < 30) score += 10;\n\n    return score;\n  }\n\n  private calculateSearchRelevance(achievement: Achievement, searchText: string): number {\n    const searchTerms = searchText.toLowerCase().split(' ');\n    let score = 0;\n\n    // Title matches\n    const titleMatches = searchTerms.filter(term => achievement.title.toLowerCase().includes(term)).length;\n    score += titleMatches * 10;\n\n    // Description matches\n    const descMatches = searchTerms.filter(term => achievement.description.toLowerCase().includes(term)).length;\n    score += descMatches * 5;\n\n    // Cultural context matches\n    const culturalMatches = searchTerms.filter(term =>\n      achievement.culturalContext.culturalSignificance.toLowerCase().includes(term) ||\n      achievement.culturalContext.primaryCulture.toLowerCase().includes(term)\n    ).length;\n    score += culturalMatches * 8;\n\n    return score;\n  }\n\n  private calculateSuggestionScore(achievement: Achievement, userProfile: any): number {\n    let score = 0;\n\n    // Cross-cultural learning potential\n    if (achievement.culturalContext.primaryCulture !== userProfile.primaryCulture) {\n      score += 30;\n    }\n\n    // Cross-cultural collaboration\n    if (achievement.category.crossCultural) {\n      score += 25;\n    }\n\n    // Quality and verification\n    score += achievement.verification.verificationScore * 0.2;\n\n    // Educational value\n    score += achievement.culturalSignificance.educationalValue * 3;\n\n    // Engagement metrics\n    score += Math.min(achievement.engagementMetrics.views / 50, 20);\n\n    return score;\n  }\n\n  private calculateRelatedScore(candidate: Achievement, reference: Achievement): number {\n    let score = 0;\n\n    // Same culture\n    if (candidate.culturalContext.primaryCulture === reference.culturalContext.primaryCulture) {\n      score += 30;\n    }\n\n    // Same category\n    if (candidate.category.primary === reference.category.primary) {\n      score += 25;\n    }\n\n    // Same location\n    if (candidate.location.province === reference.location.province) {\n      score += 20;\n    }\n\n    // Similar time period\n    const timeDiff = Math.abs(candidate.timeframe.startDate.getTime() - reference.timeframe.startDate.getTime());\n    const yearsDiff = timeDiff / (365 * 24 * 60 * 60 * 1000);\n    if (yearsDiff < 10) score += 15;\n    else if (yearsDiff < 50) score += 10;\n\n    // Quality bonus\n    score += candidate.verification.verificationScore * 0.1;\n\n    return score;\n  }\n\n  private calculateCulturalDistribution(achievements: Achievement[]): { [culture: string]: number } {\n    const distribution: { [culture: string]: number } = {};\n    achievements.forEach(achievement => {\n      const culture = achievement.culturalContext.primaryCulture;\n      distribution[culture] = (distribution[culture] || 0) + 1;\n    });\n    return distribution;\n  }\n\n  private calculateCategoryDistribution(achievements: Achievement[]): { [category: string]: number } {\n    const distribution: { [category: string]: number } = {};\n    achievements.forEach(achievement => {\n      const category = achievement.category.primary;\n      distribution[category] = (distribution[category] || 0) + 1;\n    });\n    return distribution;\n  }\n\n  private async getUserProfile(_userId: string): Promise<any> {\n    // Simulate getting user profile\n    return {\n      primaryCulture: 'Zulu',\n      interests: ['sports', 'arts', 'community_service'],\n      culturalLearningGoals: ['cross_cultural_understanding']\n    };\n  }\n}\n\nexport const achievementGalleryService = new AchievementGalleryService();", "import React, { useState, useEffect } from 'react';\nimport {\n  Achievement,\n  AchievementFilters,\n  TimelineEvent\n} from '../types/achievement';\nimport { achievementGalleryService } from '../services/achievementGalleryService';\n\ninterface AchievementGalleryProps {\n  userId: string;\n}\n\nconst AchievementGallery: React.FC<AchievementGalleryProps> = ({ userId }) => {\n  const [achievements, setAchievements] = useState<Achievement[]>([]);\n  const [featuredAchievements, setFeaturedAchievements] = useState<Achievement[]>([]);\n  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);\n  const [filters, setFilters] = useState<AchievementFilters>({});\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [activeView, setActiveView] = useState<'gallery' | 'timeline' | 'featured' | 'suggestions'>('gallery');\n  const [timelineEvents, setTimelineEvents] = useState<TimelineEvent[]>([]);\n  const [suggestions, setSuggestions] = useState<Achievement[]>([]);\n\n  const categories = [\n    { value: 'sports', label: 'Sports & Athletics' },\n    { value: 'arts', label: 'Arts & Culture' },\n    { value: 'business', label: 'Business & Innovation' },\n    { value: 'education', label: 'Education & Learning' },\n    { value: 'innovation', label: 'Innovation & Technology' },\n    { value: 'community_service', label: 'Community Service' }\n  ];\n\n  const cultures = [\n    'Zulu', 'Xhosa', 'Afrikaans', 'English', 'Sotho', 'Tswana', \n    'Pedi', 'Venda', 'Tsonga', 'Ndebele', 'Swati'\n  ];\n\n\n\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n\n  useEffect(() => {\n    if (searchQuery || Object.keys(filters).length > 0) {\n      performSearch();\n    } else {\n      loadAchievements();\n    }\n  }, [filters, searchQuery]);\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      const [featured, userSuggestions] = await Promise.all([\n        achievementGalleryService.getFeaturedAchievements(),\n        achievementGalleryService.getAchievementSuggestions(userId)\n      ]);\n      \n      setFeaturedAchievements(featured);\n      setSuggestions(userSuggestions);\n      \n      await loadAchievements();\n    } catch (error) {\n      console.error('Error loading initial data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAchievements = async () => {\n    try {\n      const results = await achievementGalleryService.getAchievements(filters);\n      setAchievements(results);\n    } catch (error) {\n      console.error('Error loading achievements:', error);\n    }\n  };\n\n  const performSearch = async () => {\n    if (!searchQuery.trim()) {\n      await loadAchievements();\n      return;\n    }\n\n    try {\n      const searchResults = await achievementGalleryService.searchAchievements({\n        searchText: searchQuery,\n        filters,\n        limit: 20\n      });\n      setAchievements(searchResults);\n    } catch (error) {\n      console.error('Error searching achievements:', error);\n    }\n  };\n\n  const loadTimeline = async () => {\n    try {\n      const timeRange = {\n        startDate: new Date(1900, 0, 1),\n        endDate: new Date()\n      };\n      const timeline = await achievementGalleryService.getAchievementTimeline(timeRange);\n      setTimelineEvents(timeline);\n    } catch (error) {\n      console.error('Error loading timeline:', error);\n    }\n  };\n\n  const handleAchievementClick = async (achievement: Achievement) => {\n    setSelectedAchievement(achievement);\n    \n    // Track view\n    await achievementGalleryService.trackAchievementView(achievement.id, userId);\n    \n    // Update engagement metrics\n    const updatedAchievements = achievements.map(a => \n      a.id === achievement.id \n        ? { ...a, engagementMetrics: { ...a.engagementMetrics, views: a.engagementMetrics.views + 1 } }\n        : a\n    );\n    setAchievements(updatedAchievements);\n  };\n\n  const handleShare = async (achievement: Achievement, platform: string) => {\n    await achievementGalleryService.trackSocialSharing(achievement.id, platform);\n    \n    // In a real implementation, this would open sharing dialog\n    console.log(`Sharing ${achievement.title} on ${platform}`);\n  };\n\n  const handleFilterChange = (filterType: string, value: any) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({});\n    setSearchQuery('');\n  };\n\n  const renderAchievementCard = (achievement: Achievement) => (\n    <div \n      key={achievement.id}\n      className=\"bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow\"\n      onClick={() => handleAchievementClick(achievement)}\n    >\n      {achievement.mediaContent[0] && (\n        <div className=\"h-48 bg-gray-200 relative\">\n          <img \n            src={achievement.mediaContent[0].url} \n            alt={achievement.mediaContent[0].caption}\n            className=\"w-full h-full object-cover\"\n          />\n          <div className=\"absolute top-2 right-2\">\n            <span className={`px-2 py-1 rounded-full text-xs text-white ${\n              achievement.verification.status === 'verified' ? 'bg-green-500' :\n              achievement.verification.status === 'pending' ? 'bg-yellow-500' :\n              'bg-gray-500'\n            }`}>\n              {achievement.verification.status}\n            </span>\n          </div>\n        </div>\n      )}\n      \n      <div className=\"p-4\">\n        <h3 className=\"font-semibold text-lg text-gray-900 mb-2\">{achievement.title}</h3>\n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">{achievement.description}</p>\n        \n        <div className=\"flex flex-wrap gap-2 mb-3\">\n          <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs\">\n            {achievement.category.primary}\n          </span>\n          <span className=\"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs\">\n            {achievement.culturalContext.primaryCulture}\n          </span>\n          {achievement.category.crossCultural && (\n            <span className=\"px-2 py-1 bg-green-100 text-green-800 rounded text-xs\">\n              Cross-Cultural\n            </span>\n          )}\n        </div>\n        \n        <div className=\"flex justify-between items-center text-sm text-gray-500\">\n          <span>{achievement.location.city}, {achievement.location.province}</span>\n          <span>{achievement.timeframe.startDate.getFullYear()}</span>\n        </div>\n        \n        <div className=\"flex justify-between items-center mt-3 pt-3 border-t\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <span className=\"flex items-center\">\n              <svg className=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\"/>\n                <path fillRule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clipRule=\"evenodd\"/>\n              </svg>\n              {achievement.engagementMetrics.views}\n            </span>\n            <span className=\"flex items-center\">\n              <svg className=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z\"/>\n              </svg>\n              {achievement.engagementMetrics.shares}\n            </span>\n          </div>\n          \n          <div className=\"flex space-x-1\">\n            <button \n              onClick={(e) => {\n                e.stopPropagation();\n                handleShare(achievement, 'facebook');\n              }}\n              className=\"p-1 text-gray-400 hover:text-blue-600\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z\" clipRule=\"evenodd\"/>\n              </svg>\n            </button>\n            <button \n              onClick={(e) => {\n                e.stopPropagation();\n                handleShare(achievement, 'twitter');\n              }}\n              className=\"p-1 text-gray-400 hover:text-blue-400\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderFilters = () => (\n    <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n      <div className=\"flex flex-wrap gap-4 items-center\">\n        <div className=\"flex-1 min-w-64\">\n          <input\n            type=\"text\"\n            placeholder=\"Search achievements...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n        \n        <select\n          value={filters.category || ''}\n          onChange={(e) => handleFilterChange('category', e.target.value || undefined)}\n          className=\"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\"\n        >\n          <option value=\"\">All Categories</option>\n          {categories.map(cat => (\n            <option key={cat.value} value={cat.value}>{cat.label}</option>\n          ))}\n        </select>\n        \n        <select\n          value={filters.culturalContext || ''}\n          onChange={(e) => handleFilterChange('culturalContext', e.target.value || undefined)}\n          className=\"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\"\n        >\n          <option value=\"\">All Cultures</option>\n          {cultures.map(culture => (\n            <option key={culture} value={culture}>{culture}</option>\n          ))}\n        </select>\n        \n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={filters.crossCultural || false}\n            onChange={(e) => handleFilterChange('crossCultural', e.target.checked || undefined)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm\">Cross-Cultural Only</span>\n        </label>\n        \n        {(Object.keys(filters).length > 0 || searchQuery) && (\n          <button\n            onClick={clearFilters}\n            className=\"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600\"\n          >\n            Clear Filters\n          </button>\n        )}\n      </div>\n    </div>\n  );\n\n  const renderTimeline = () => (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <h3 className=\"text-xl font-semibold mb-6\">Achievement Timeline</h3>\n      <div className=\"space-y-6\">\n        {timelineEvents.map((event) => (\n          <div key={event.id} className=\"flex items-start space-x-4\">\n            <div className=\"flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\n              <span className=\"text-blue-600 font-semibold\">{event.date.getFullYear()}</span>\n            </div>\n            <div className=\"flex-1\">\n              <h4 className=\"font-medium text-gray-900\">{event.title}</h4>\n              <p className=\"text-sm text-gray-600 mt-1\">{event.description}</p>\n              <div className=\"flex items-center space-x-4 mt-2\">\n                <span className=\"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs\">\n                  {event.culturalContext}\n                </span>\n                <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs\">\n                  {event.category}\n                </span>\n                {event.crossCultural && (\n                  <span className=\"px-2 py-1 bg-green-100 text-green-800 rounded text-xs\">\n                    Cross-Cultural\n                  </span>\n                )}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const renderViewTabs = () => (\n    <div className=\"flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6\">\n      {[\n        { key: 'gallery', label: 'Gallery', icon: '🖼️' },\n        { key: 'featured', label: 'Featured', icon: '⭐' },\n        { key: 'suggestions', label: 'For You', icon: '💡' },\n        { key: 'timeline', label: 'Timeline', icon: '📅' }\n      ].map(tab => (\n        <button\n          key={tab.key}\n          onClick={() => {\n            setActiveView(tab.key as any);\n            if (tab.key === 'timeline') loadTimeline();\n          }}\n          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n            activeView === tab.key\n              ? 'bg-white text-blue-600 shadow-sm'\n              : 'text-gray-600 hover:text-gray-900'\n          }`}\n        >\n          <span>{tab.icon}</span>\n          <span>{tab.label}</span>\n        </button>\n      ))}\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">South African Achievement Gallery</h1>\n        <p className=\"text-gray-600\">\n          Discover and celebrate the remarkable achievements of South Africans across all cultures and communities\n        </p>\n      </div>\n\n      {renderViewTabs()}\n      \n      {activeView !== 'timeline' && renderFilters()}\n\n      {activeView === 'gallery' && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {achievements.map(renderAchievementCard)}\n        </div>\n      )}\n\n      {activeView === 'featured' && (\n        <div>\n          <h2 className=\"text-2xl font-semibold mb-6\">Featured Achievements</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {featuredAchievements.map(renderAchievementCard)}\n          </div>\n        </div>\n      )}\n\n      {activeView === 'suggestions' && (\n        <div>\n          <h2 className=\"text-2xl font-semibold mb-6\">Recommended for You</h2>\n          <p className=\"text-gray-600 mb-6\">\n            Discover achievements from different cultures to broaden your perspective and inspire cross-cultural understanding\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {suggestions.map(renderAchievementCard)}\n          </div>\n        </div>\n      )}\n\n      {activeView === 'timeline' && renderTimeline()}\n\n      {achievements.length === 0 && activeView === 'gallery' && (\n        <div className=\"text-center py-12\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No achievements found</h3>\n          <p className=\"text-gray-600\">Try adjusting your search criteria or filters</p>\n        </div>\n      )}\n\n      {/* Achievement Detail Modal */}\n      {selectedAchievement && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-start mb-4\">\n                <h2 className=\"text-2xl font-bold text-gray-900\">{selectedAchievement.title}</h2>\n                <button\n                  onClick={() => setSelectedAchievement(null)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              \n              {selectedAchievement.mediaContent[0] && (\n                <img \n                  src={selectedAchievement.mediaContent[0].url}\n                  alt={selectedAchievement.mediaContent[0].caption}\n                  className=\"w-full h-64 object-cover rounded-lg mb-4\"\n                />\n              )}\n              \n              <p className=\"text-gray-700 mb-6\">{selectedAchievement.description}</p>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Cultural Context</h3>\n                  <p className=\"text-sm text-gray-600 mb-4\">{selectedAchievement.culturalContext.culturalSignificance}</p>\n                  \n                  <h3 className=\"font-semibold mb-2\">Location & Time</h3>\n                  <p className=\"text-sm text-gray-600\">\n                    {selectedAchievement.location.city}, {selectedAchievement.location.province}<br/>\n                    {selectedAchievement.timeframe.startDate.getFullYear()} - {selectedAchievement.timeframe.endDate.getFullYear()}\n                  </p>\n                </div>\n                \n                <div>\n                  <h3 className=\"font-semibold mb-2\">Impact & Significance</h3>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span>Cultural Impact:</span>\n                      <span className=\"font-medium\">{selectedAchievement.culturalSignificance.preservationValue}/10</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Educational Value:</span>\n                      <span className=\"font-medium\">{selectedAchievement.culturalSignificance.educationalValue}/10</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Inspirational Value:</span>\n                      <span className=\"font-medium\">{selectedAchievement.socialImpact.inspirationalValue}/10</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AchievementGallery;\n", "import {\n  UserBadge,\n  BadgeType,\n  BadgeLevel,\n  CrossCulturalContext,\n  InteractionType,\n  BadgeImpactMetrics,\n  BadgeRequirement,\n  LearningOutcome\n} from '../types/achievement';\n\nexport interface BadgeEligibility {\n  badgeType: BadgeType;\n  eligible: boolean;\n  progress: number; // 0-100 percentage\n  requirements: BadgeRequirement[];\n  nextMilestone: string;\n  estimatedTimeToEarn: string;\n}\n\nexport interface BadgeAward {\n  badge: UserBadge;\n  celebrationMessage: string;\n  culturalContext: string;\n  nextLevelInfo: BadgeLevel | null;\n  sharingRecommendations: string[];\n}\n\nexport interface InteractionEvent {\n  userId: string;\n  interactionType: InteractionType;\n  participantCultures: string[];\n  duration: number;\n  qualityScore: number;\n  culturalLearningOutcomes: LearningOutcome[];\n  timestamp: Date;\n  verificationMethod: 'automatic' | 'community_validation' | 'expert_review';\n}\n\nexport interface ServiceActivity {\n  userId: string;\n  activityType: 'volunteering' | 'organizing' | 'mentoring' | 'facilitating' | 'advocating';\n  communityBenefited: string;\n  culturalContext: string;\n  hoursContributed: number;\n  impactDescription: string;\n  verificationDocuments: string[];\n  communityEndorsements: string[];\n  timestamp: Date;\n}\n\nexport interface BridgeBuildingEvent {\n  userId: string;\n  eventType: 'connection_facilitation' | 'conflict_resolution' | 'cultural_education' | 'inclusive_advocacy';\n  culturesInvolved: string[];\n  participantsConnected: number;\n  sustainableOutcome: boolean;\n  impactMeasurement: string;\n  communityFeedback: string[];\n  timestamp: Date;\n}\n\nclass RecognitionEngineService {\n  private userBadges: Map<string, UserBadge[]> = new Map();\n  private badgeDefinitions: Map<string, BadgeType> = new Map();\n  private userInteractions: Map<string, InteractionEvent[]> = new Map();\n  private userServiceActivities: Map<string, ServiceActivity[]> = new Map();\n  private userBridgeBuilding: Map<string, BridgeBuildingEvent[]> = new Map();\n\n  constructor() {\n    this.initializeBadgeDefinitions();\n  }\n\n  // Badge Management\n  async checkBadgeEligibility(userId: string): Promise<BadgeEligibility[]> {\n    const eligibilities: BadgeEligibility[] = [];\n    const userProgress = await this.getUserProgress(userId);\n\n    for (const [badgeId, badgeType] of this.badgeDefinitions) {\n      const progress = this.calculateBadgeProgress(userProgress, badgeType);\n      const eligible = progress >= 100;\n\n      eligibilities.push({\n        badgeType,\n        eligible,\n        progress: Math.min(progress, 100),\n        requirements: badgeType.requirements,\n        nextMilestone: this.getNextMilestone(progress, badgeType),\n        estimatedTimeToEarn: this.estimateTimeToEarn(progress, badgeType, userProgress)\n      });\n    }\n\n    return eligibilities.sort((a, b) => b.progress - a.progress);\n  }\n\n  async awardBadge(userId: string, badgeType: BadgeType): Promise<BadgeAward> {\n    const userProgress = await this.getUserProgress(userId);\n    const level = this.determineBadgeLevel(userProgress, badgeType);\n\n    const badge: UserBadge = {\n      id: this.generateId(),\n      userId,\n      badgeType,\n      level,\n      earnedDate: new Date(),\n      verificationStatus: {\n        status: 'verified',\n        verifiedBy: 'system',\n        verificationDate: new Date(),\n        communityEndorsements: 0\n      },\n      culturalContext: this.extractCulturalContext(userProgress, badgeType),\n      impactMetrics: this.calculateImpactMetrics(userProgress, badgeType),\n      sharingPermissions: {\n        publicProfile: true,\n        communitySharing: true,\n        socialMediaSharing: true,\n        culturalRepresentativeSharing: true\n      }\n    };\n\n    // Store the badge\n    const userBadgeList = this.userBadges.get(userId) || [];\n    userBadgeList.push(badge);\n    this.userBadges.set(userId, userBadgeList);\n\n    // Determine next level\n    const nextLevel = this.getNextBadgeLevel(level);\n\n    return {\n      badge,\n      celebrationMessage: this.generateCelebrationMessage(badge),\n      culturalContext: this.getCulturalContextMessage(badge.culturalContext),\n      nextLevelInfo: nextLevel,\n      sharingRecommendations: this.generateSharingRecommendations(badge)\n    };\n  }\n\n  async getBadgeProgress(userId: string, badgeType: BadgeType): Promise<any> {\n    const userProgress = await this.getUserProgress(userId);\n    const progress = this.calculateBadgeProgress(userProgress, badgeType);\n\n    return {\n      badgeType,\n      currentProgress: progress,\n      requirements: badgeType.requirements.map(req => ({\n        requirement: req.requirement,\n        target: req.quantitativeTarget || req.qualitativeTarget,\n        current: this.getCurrentRequirementProgress(userProgress, req),\n        completed: this.isRequirementCompleted(userProgress, req)\n      })),\n      estimatedCompletion: this.estimateTimeToEarn(progress, badgeType, userProgress),\n      nextActions: this.getNextActions(userProgress, badgeType)\n    };\n  }\n\n  async getUserBadges(userId: string): Promise<UserBadge[]> {\n    return this.userBadges.get(userId) || [];\n  }\n\n  // Interaction Tracking\n  async recordCrossCulturalInteraction(interaction: InteractionEvent): Promise<void> {\n    const userInteractions = this.userInteractions.get(interaction.userId) || [];\n    userInteractions.push(interaction);\n    this.userInteractions.set(interaction.userId, userInteractions);\n\n    // Check for badge eligibility after recording interaction\n    await this.checkAndAwardAutomaticBadges(interaction.userId);\n  }\n\n  async trackCommunityServiceActivity(activity: ServiceActivity): Promise<void> {\n    const userActivities = this.userServiceActivities.get(activity.userId) || [];\n    userActivities.push(activity);\n    this.userServiceActivities.set(activity.userId, userActivities);\n\n    // Check for badge eligibility\n    await this.checkAndAwardAutomaticBadges(activity.userId);\n  }\n\n  async measureBridgeBuildingImpact(impact: BridgeBuildingEvent): Promise<BadgeImpactMetrics> {\n    const userBridgeBuilding = this.userBridgeBuilding.get(impact.userId) || [];\n    userBridgeBuilding.push(impact);\n    this.userBridgeBuilding.set(impact.userId, userBridgeBuilding);\n\n    // Calculate cumulative impact metrics\n    const totalConnections = userBridgeBuilding.reduce((sum, event) => sum + event.participantsConnected, 0);\n    const totalCultures = new Set(userBridgeBuilding.flatMap(event => event.culturesInvolved)).size;\n    const sustainableOutcomes = userBridgeBuilding.filter(event => event.sustainableOutcome).length;\n\n    const impactMetrics: BadgeImpactMetrics = {\n      crossCulturalConnections: totalConnections,\n      communityServiceHours: 0, // Will be calculated from service activities\n      culturalLearningMoments: userBridgeBuilding.length,\n      bridgeBuildingInstances: userBridgeBuilding.length,\n      mentorshipRelationships: userBridgeBuilding.filter(e => e.eventType === 'connection_facilitation').length\n    };\n\n    // Check for badge eligibility\n    await this.checkAndAwardAutomaticBadges(impact.userId);\n\n    return impactMetrics;\n  }\n\n  // Analytics and Scoring\n  async calculateEngagementScore(userId: string, timeframe: { startDate: Date; endDate: Date }): Promise<any> {\n    const interactions = this.userInteractions.get(userId) || [];\n    const serviceActivities = this.userServiceActivities.get(userId) || [];\n    const bridgeBuilding = this.userBridgeBuilding.get(userId) || [];\n\n    const timeframeInteractions = interactions.filter(i =>\n      i.timestamp >= timeframe.startDate && i.timestamp <= timeframe.endDate\n    );\n    const timeframeService = serviceActivities.filter(a =>\n      a.timestamp >= timeframe.startDate && a.timestamp <= timeframe.endDate\n    );\n    const timeframeBridgeBuilding = bridgeBuilding.filter(b =>\n      b.timestamp >= timeframe.startDate && b.timestamp <= timeframe.endDate\n    );\n\n    const crossCulturalScore = this.calculateCrossCulturalScore(timeframeInteractions);\n    const serviceScore = this.calculateServiceScore(timeframeService);\n    const bridgeBuildingScore = this.calculateBridgeBuildingScore(timeframeBridgeBuilding);\n\n    return {\n      totalScore: crossCulturalScore + serviceScore + bridgeBuildingScore,\n      crossCulturalEngagement: crossCulturalScore,\n      communityService: serviceScore,\n      bridgeBuilding: bridgeBuildingScore,\n      culturalDiversity: this.calculateCulturalDiversityScore(timeframeInteractions),\n      consistency: this.calculateConsistencyScore(timeframeInteractions, timeframeService),\n      growth: this.calculateGrowthScore(userId, timeframe)\n    };\n  }\n\n  async generateRecognitionReport(userId: string): Promise<any> {\n    const badges = await this.getUserBadges(userId);\n    const interactions = this.userInteractions.get(userId) || [];\n    const serviceActivities = this.userServiceActivities.get(userId) || [];\n    const bridgeBuilding = this.userBridgeBuilding.get(userId) || [];\n\n    const totalCultures = new Set([\n      ...interactions.flatMap(i => i.participantCultures),\n      ...serviceActivities.map(a => a.culturalContext),\n      ...bridgeBuilding.flatMap(b => b.culturesInvolved)\n    ]).size;\n\n    const totalServiceHours = serviceActivities.reduce((sum, a) => sum + a.hoursContributed, 0);\n    const totalConnections = bridgeBuilding.reduce((sum, b) => sum + b.participantsConnected, 0);\n\n    return {\n      userId,\n      reportDate: new Date(),\n      summary: {\n        totalBadges: badges.length,\n        badgesByLevel: this.groupBadgesByLevel(badges),\n        culturalEngagement: {\n          culturesEngaged: totalCultures,\n          crossCulturalInteractions: interactions.length,\n          averageQualityScore: this.calculateAverageQuality(interactions)\n        },\n        communityImpact: {\n          serviceHours: totalServiceHours,\n          communitiesServed: new Set(serviceActivities.map(a => a.communityBenefited)).size,\n          connectionsFormed: totalConnections\n        },\n        achievements: badges.map(b => ({\n          badgeType: b.badgeType.specificType,\n          level: b.level.level,\n          earnedDate: b.earnedDate,\n          culturalContext: b.culturalContext.culturesInvolved\n        }))\n      },\n      recommendations: this.generatePersonalizedRecommendations(userId, badges, interactions, serviceActivities)\n    };\n  }\n\n  async getLeaderboard(category: string, timeframe: { startDate: Date; endDate: Date }): Promise<any[]> {\n    const allUsers = new Set([\n      ...this.userInteractions.keys(),\n      ...this.userServiceActivities.keys(),\n      ...this.userBridgeBuilding.keys()\n    ]);\n\n    const leaderboardEntries = [];\n\n    for (const userId of allUsers) {\n      const score = await this.calculateEngagementScore(userId, timeframe);\n      const badges = await this.getUserBadges(userId);\n\n      let categoryScore = 0;\n      switch (category) {\n        case 'cross_cultural_engagement':\n          categoryScore = score.crossCulturalEngagement;\n          break;\n        case 'community_service':\n          categoryScore = score.communityService;\n          break;\n        case 'bridge_building':\n          categoryScore = score.bridgeBuilding;\n          break;\n        default:\n          categoryScore = score.totalScore;\n      }\n\n      leaderboardEntries.push({\n        userId,\n        score: categoryScore,\n        badges: badges.length,\n        culturalDiversity: score.culturalDiversity,\n        rank: 0 // Will be set after sorting\n      });\n    }\n\n    // Sort and assign ranks\n    leaderboardEntries.sort((a, b) => b.score - a.score);\n    leaderboardEntries.forEach((entry, index) => {\n      entry.rank = index + 1;\n    });\n\n    return leaderboardEntries.slice(0, 50); // Top 50\n  }\n\n  // Helper Methods\n  private initializeBadgeDefinitions(): void {\n    // Cross-Cultural Engagement Badges\n    this.badgeDefinitions.set('cultural_bridge_builder', {\n      category: 'cross_cultural_engagement',\n      specificType: 'Cultural Bridge Builder',\n      description: 'Connects people from different cultural backgrounds',\n      requirements: [\n        {\n          requirement: 'Facilitate meaningful conversations between 5+ different cultures',\n          quantitativeTarget: 5,\n          verificationMethod: 'automatic'\n        },\n        {\n          requirement: 'Maintain cross-cultural relationships for 3+ months',\n          quantitativeTarget: 3,\n          verificationMethod: 'community_validation'\n        }\n      ],\n      culturalScope: {\n        minimumCultures: 3,\n        crossCulturalRequired: true,\n        communityServiceRequired: false\n      },\n      difficultyLevel: 'intermediate'\n    });\n\n    this.badgeDefinitions.set('ubuntu_ambassador', {\n      category: 'bridge_building',\n      specificType: 'Ubuntu Ambassador',\n      description: 'Embodies Ubuntu philosophy in cross-cultural interactions',\n      requirements: [\n        {\n          requirement: 'Demonstrate Ubuntu principles in 10+ interactions',\n          quantitativeTarget: 10,\n          verificationMethod: 'community_validation'\n        },\n        {\n          requirement: 'Receive community endorsements from 3+ cultures',\n          quantitativeTarget: 3,\n          verificationMethod: 'expert_review'\n        }\n      ],\n      culturalScope: {\n        minimumCultures: 3,\n        crossCulturalRequired: true,\n        communityServiceRequired: true\n      },\n      difficultyLevel: 'advanced'\n    });\n\n    this.badgeDefinitions.set('community_service_champion', {\n      category: 'community_service',\n      specificType: 'Community Service Champion',\n      description: 'Dedicated to serving diverse communities',\n      requirements: [\n        {\n          requirement: 'Complete 50+ hours of community service',\n          quantitativeTarget: 50,\n          verificationMethod: 'automatic'\n        },\n        {\n          requirement: 'Serve communities from 2+ different cultures',\n          quantitativeTarget: 2,\n          verificationMethod: 'community_validation'\n        }\n      ],\n      culturalScope: {\n        minimumCultures: 2,\n        crossCulturalRequired: false,\n        communityServiceRequired: true\n      },\n      difficultyLevel: 'intermediate'\n    });\n  }\n\n  private async getUserProgress(userId: string): Promise<any> {\n    const interactions = this.userInteractions.get(userId) || [];\n    const serviceActivities = this.userServiceActivities.get(userId) || [];\n    const bridgeBuilding = this.userBridgeBuilding.get(userId) || [];\n\n    return {\n      crossCulturalInteractions: interactions.length,\n      culturesEngaged: new Set(interactions.flatMap(i => i.participantCultures)).size,\n      serviceHours: serviceActivities.reduce((sum, a) => sum + a.hoursContributed, 0),\n      communitiesServed: new Set(serviceActivities.map(a => a.communityBenefited)).size,\n      bridgeBuildingEvents: bridgeBuilding.length,\n      connectionsFormed: bridgeBuilding.reduce((sum, b) => sum + b.participantsConnected, 0),\n      averageQualityScore: this.calculateAverageQuality(interactions),\n      sustainableOutcomes: bridgeBuilding.filter(b => b.sustainableOutcome).length\n    };\n  }\n\n  private calculateBadgeProgress(userProgress: any, badgeType: BadgeType): number {\n    let totalProgress = 0;\n    let totalRequirements = badgeType.requirements.length;\n\n    for (const requirement of badgeType.requirements) {\n      const progress = this.getCurrentRequirementProgress(userProgress, requirement);\n      const target = requirement.quantitativeTarget || 1;\n      const requirementProgress = Math.min((progress / target) * 100, 100);\n      totalProgress += requirementProgress;\n    }\n\n    return totalProgress / totalRequirements;\n  }\n\n  private getCurrentRequirementProgress(userProgress: any, requirement: BadgeRequirement): number {\n    const req = requirement.requirement.toLowerCase();\n\n    if (req.includes('conversation') || req.includes('interaction')) {\n      return userProgress.crossCulturalInteractions;\n    }\n    if (req.includes('culture') && req.includes('different')) {\n      return userProgress.culturesEngaged;\n    }\n    if (req.includes('service') || req.includes('hour')) {\n      return userProgress.serviceHours;\n    }\n    if (req.includes('community') && req.includes('serve')) {\n      return userProgress.communitiesServed;\n    }\n    if (req.includes('connection') || req.includes('bridge')) {\n      return userProgress.connectionsFormed;\n    }\n    if (req.includes('ubuntu') || req.includes('principle')) {\n      return userProgress.sustainableOutcomes;\n    }\n\n    return 0;\n  }\n\n  private isRequirementCompleted(userProgress: any, requirement: BadgeRequirement): boolean {\n    const current = this.getCurrentRequirementProgress(userProgress, requirement);\n    const target = requirement.quantitativeTarget || 1;\n    return current >= target;\n  }\n\n  private determineBadgeLevel(userProgress: any, badgeType: BadgeType): BadgeLevel {\n    const totalScore = userProgress.crossCulturalInteractions +\n                     userProgress.serviceHours +\n                     userProgress.bridgeBuildingEvents * 5;\n\n    if (totalScore >= 200) return { level: 'platinum', pointsRequired: 200, specialPrivileges: ['Cultural Ambassador Status'] };\n    if (totalScore >= 100) return { level: 'gold', pointsRequired: 100, specialPrivileges: ['Community Leadership'] };\n    if (totalScore >= 50) return { level: 'silver', pointsRequired: 50, specialPrivileges: ['Mentorship Opportunities'] };\n    return { level: 'bronze', pointsRequired: 25, specialPrivileges: ['Recognition Badge'] };\n  }\n\n  private extractCulturalContext(userProgress: any, badgeType: BadgeType): CrossCulturalContext {\n    return {\n      culturesInvolved: ['Zulu', 'English', 'Afrikaans'], // Simplified - would be extracted from actual data\n      interactionType: {\n        type: 'collaboration',\n        duration: 120,\n        qualityScore: 8.5,\n        participants: []\n      },\n      impactLevel: 'community',\n      sustainabilityMetrics: {\n        ongoingEngagement: true,\n        relationshipDevelopment: 8,\n        culturalUnderstandingGrowth: 9,\n        communityImpact: 7\n      },\n      bridgeBuildingScore: 85,\n      culturalLearningOutcomes: []\n    };\n  }\n\n  private calculateImpactMetrics(userProgress: any, badgeType: BadgeType): BadgeImpactMetrics {\n    return {\n      crossCulturalConnections: userProgress.connectionsFormed,\n      communityServiceHours: userProgress.serviceHours,\n      culturalLearningMoments: userProgress.crossCulturalInteractions,\n      bridgeBuildingInstances: userProgress.bridgeBuildingEvents,\n      mentorshipRelationships: Math.floor(userProgress.bridgeBuildingEvents / 2)\n    };\n  }\n\n  private generateCelebrationMessage(badge: UserBadge): string {\n    const messages = {\n      'Cultural Bridge Builder': 'Congratulations! You\\'ve earned the Cultural Bridge Builder badge for connecting diverse communities through Ubuntu spirit!',\n      'Ubuntu Ambassador': 'Amazing! You\\'ve become an Ubuntu Ambassador, embodying \"I am because we are\" in all your interactions!',\n      'Community Service Champion': 'Well done! Your dedication to serving diverse communities has earned you the Community Service Champion badge!'\n    };\n\n    return messages[badge.badgeType.specificType as keyof typeof messages] ||\n           `Congratulations on earning the ${badge.badgeType.specificType} badge!`;\n  }\n\n  private getCulturalContextMessage(context: CrossCulturalContext): string {\n    const cultures = context.culturesInvolved.join(', ');\n    return `This achievement represents meaningful engagement across ${cultures} cultures, embodying the Ubuntu philosophy of interconnectedness.`;\n  }\n\n  private generateSharingRecommendations(badge: UserBadge): string[] {\n    return [\n      'Share your achievement with your cultural community',\n      'Inspire others by posting about your cross-cultural journey',\n      'Mentor someone who is starting their cultural bridge-building journey',\n      'Celebrate with the communities you\\'ve connected with'\n    ];\n  }\n\n  private getNextBadgeLevel(currentLevel: BadgeLevel): BadgeLevel | null {\n    const levels = ['bronze', 'silver', 'gold', 'platinum', 'cultural_ambassador'];\n    const currentIndex = levels.indexOf(currentLevel.level);\n\n    if (currentIndex < levels.length - 1) {\n      const nextLevel = levels[currentIndex + 1];\n      const pointsRequired = [25, 50, 100, 200, 500][currentIndex + 1];\n      const privileges = [\n        ['Recognition Badge'],\n        ['Mentorship Opportunities'],\n        ['Community Leadership'],\n        ['Cultural Ambassador Status'],\n        ['Platform Advisory Role']\n      ][currentIndex + 1];\n\n      return {\n        level: nextLevel as any,\n        pointsRequired,\n        specialPrivileges: privileges\n      };\n    }\n\n    return null;\n  }\n\n  private getNextMilestone(progress: number, badgeType: BadgeType): string {\n    if (progress >= 100) return 'Badge earned! Ready to claim.';\n    if (progress >= 75) return 'Almost there! Complete one more requirement.';\n    if (progress >= 50) return 'Halfway to earning this badge.';\n    if (progress >= 25) return 'Good progress! Keep engaging across cultures.';\n    return 'Start by engaging with different cultural communities.';\n  }\n\n  private estimateTimeToEarn(progress: number, badgeType: BadgeType, userProgress: any): string {\n    const remaining = 100 - progress;\n    const difficulty = badgeType.difficultyLevel;\n\n    if (remaining <= 0) return 'Ready to claim!';\n\n    const baseTime = {\n      'beginner': 2,\n      'intermediate': 4,\n      'advanced': 8,\n      'expert': 16\n    }[difficulty] || 4;\n\n    const estimatedWeeks = Math.ceil((remaining / 100) * baseTime);\n\n    if (estimatedWeeks <= 1) return 'Within a week';\n    if (estimatedWeeks <= 4) return `${estimatedWeeks} weeks`;\n    return `${Math.ceil(estimatedWeeks / 4)} months`;\n  }\n\n  private getNextActions(userProgress: any, badgeType: BadgeType): string[] {\n    const actions = [];\n\n    for (const requirement of badgeType.requirements) {\n      if (!this.isRequirementCompleted(userProgress, requirement)) {\n        const current = this.getCurrentRequirementProgress(userProgress, requirement);\n        const target = requirement.quantitativeTarget || 1;\n        const remaining = target - current;\n\n        actions.push(`${requirement.requirement} (${remaining} more needed)`);\n      }\n    }\n\n    return actions;\n  }\n\n  private async checkAndAwardAutomaticBadges(userId: string): Promise<void> {\n    const eligibilities = await this.checkBadgeEligibility(userId);\n    const currentBadges = await this.getUserBadges(userId);\n    const earnedBadgeTypes = new Set(currentBadges.map(b => b.badgeType.specificType));\n\n    for (const eligibility of eligibilities) {\n      if (eligibility.eligible && !earnedBadgeTypes.has(eligibility.badgeType.specificType)) {\n        await this.awardBadge(userId, eligibility.badgeType);\n      }\n    }\n  }\n\n  private calculateCrossCulturalScore(interactions: InteractionEvent[]): number {\n    return interactions.reduce((sum, interaction) => {\n      const cultureBonus = interaction.participantCultures.length * 5;\n      const qualityBonus = interaction.qualityScore * 2;\n      const durationBonus = Math.min(interaction.duration / 30, 10);\n      return sum + cultureBonus + qualityBonus + durationBonus;\n    }, 0);\n  }\n\n  private calculateServiceScore(activities: ServiceActivity[]): number {\n    return activities.reduce((sum, activity) => {\n      const hoursScore = activity.hoursContributed * 2;\n      const cultureBonus = activity.culturalContext !== 'same' ? 10 : 0;\n      return sum + hoursScore + cultureBonus;\n    }, 0);\n  }\n\n  private calculateBridgeBuildingScore(events: BridgeBuildingEvent[]): number {\n    return events.reduce((sum, event) => {\n      const connectionBonus = event.participantsConnected * 5;\n      const cultureBonus = event.culturesInvolved.length * 10;\n      const sustainabilityBonus = event.sustainableOutcome ? 20 : 0;\n      return sum + connectionBonus + cultureBonus + sustainabilityBonus;\n    }, 0);\n  }\n\n  private calculateCulturalDiversityScore(interactions: InteractionEvent[]): number {\n    const uniqueCultures = new Set(interactions.flatMap(i => i.participantCultures));\n    return Math.min(uniqueCultures.size * 10, 100);\n  }\n\n  private calculateConsistencyScore(interactions: InteractionEvent[], activities: ServiceActivity[]): number {\n    const allEvents = [...interactions, ...activities];\n    if (allEvents.length < 2) return 0;\n\n    // Calculate consistency based on regular engagement over time\n    const timeSpan = Math.max(...allEvents.map(e => e.timestamp.getTime())) -\n                   Math.min(...allEvents.map(e => e.timestamp.getTime()));\n    const weeks = timeSpan / (7 * 24 * 60 * 60 * 1000);\n    const eventsPerWeek = allEvents.length / weeks;\n\n    return Math.min(eventsPerWeek * 20, 100);\n  }\n\n  private calculateGrowthScore(userId: string, timeframe: { startDate: Date; endDate: Date }): number {\n    // Simplified growth calculation\n    const interactions = this.userInteractions.get(userId) || [];\n    const recentInteractions = interactions.filter(i => i.timestamp >= timeframe.startDate);\n    const olderInteractions = interactions.filter(i => i.timestamp < timeframe.startDate);\n\n    if (olderInteractions.length === 0) return 50; // New user baseline\n\n    const recentAvgQuality = this.calculateAverageQuality(recentInteractions);\n    const olderAvgQuality = this.calculateAverageQuality(olderInteractions);\n\n    return Math.max(0, Math.min(100, 50 + (recentAvgQuality - olderAvgQuality) * 10));\n  }\n\n  private calculateAverageQuality(interactions: InteractionEvent[]): number {\n    if (interactions.length === 0) return 0;\n    return interactions.reduce((sum, i) => sum + i.qualityScore, 0) / interactions.length;\n  }\n\n  private groupBadgesByLevel(badges: UserBadge[]): { [level: string]: number } {\n    const groups: { [level: string]: number } = {};\n    badges.forEach(badge => {\n      groups[badge.level.level] = (groups[badge.level.level] || 0) + 1;\n    });\n    return groups;\n  }\n\n  private generatePersonalizedRecommendations(\n    userId: string,\n    badges: UserBadge[],\n    interactions: InteractionEvent[],\n    activities: ServiceActivity[]\n  ): string[] {\n    const recommendations = [];\n\n    const culturesEngaged = new Set(interactions.flatMap(i => i.participantCultures));\n    if (culturesEngaged.size < 3) {\n      recommendations.push('Try engaging with more diverse cultural communities to broaden your perspective');\n    }\n\n    if (activities.length < 2) {\n      recommendations.push('Consider volunteering in community service to earn service-related badges');\n    }\n\n    const recentActivity = [...interactions, ...activities].filter(\n      event => event.timestamp > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n    );\n\n    if (recentActivity.length < 3) {\n      recommendations.push('Increase your engagement frequency to maintain momentum in badge earning');\n    }\n\n    if (badges.length === 0) {\n      recommendations.push('Start with the Cultural Bridge Builder badge by facilitating conversations between different cultures');\n    }\n\n    return recommendations;\n  }\n\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n}\n\nexport const recognitionEngineService = new RecognitionEngineService();", "import React, { useState, useEffect } from 'react';\nimport {\n  UserBadge,\n  BadgeEligibility,\n  BadgeAward,\n  InteractionEvent,\n  ServiceActivity\n} from '../types/achievement';\nimport { recognitionEngineService } from '../services/recognitionEngineService';\n\ninterface RecognitionDashboardProps {\n  userId: string;\n}\n\nconst RecognitionDashboard: React.FC<RecognitionDashboardProps> = ({ userId }) => {\n  const [userBadges, setUserBadges] = useState<UserBadge[]>([]);\n  const [eligibleBadges, setEligibleBadges] = useState<BadgeEligibility[]>([]);\n  const [engagementScore, setEngagementScore] = useState<any>(null);\n  const [recognitionReport, setRecognitionReport] = useState<any>(null);\n  const [leaderboard, setLeaderboard] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState<'badges' | 'progress' | 'leaderboard' | 'impact'>('badges');\n  const [recentAward, setRecentAward] = useState<BadgeAward | null>(null);\n\n  useEffect(() => {\n    loadUserData();\n  }, [userId]);\n\n  const loadUserData = async () => {\n    try {\n      setLoading(true);\n      const [badges, eligibility, score, report, leaderboardData] = await Promise.all([\n        recognitionEngineService.getUserBadges(userId),\n        recognitionEngineService.checkBadgeEligibility(userId),\n        recognitionEngineService.calculateEngagementScore(userId, {\n          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),\n          endDate: new Date()\n        }),\n        recognitionEngineService.generateRecognitionReport(userId),\n        recognitionEngineService.getLeaderboard('total', {\n          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),\n          endDate: new Date()\n        })\n      ]);\n\n      setUserBadges(badges);\n      setEligibleBadges(eligibility);\n      setEngagementScore(score);\n      setRecognitionReport(report);\n      setLeaderboard(leaderboardData);\n    } catch (error) {\n      console.error('Error loading user data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClaimBadge = async (badgeType: any) => {\n    try {\n      const award = await recognitionEngineService.awardBadge(userId, badgeType);\n      setRecentAward(award);\n      \n      // Refresh data\n      await loadUserData();\n    } catch (error) {\n      console.error('Error claiming badge:', error);\n    }\n  };\n\n  const recordInteraction = async () => {\n    // Simulate recording a cross-cultural interaction\n    const interaction: InteractionEvent = {\n      userId,\n      interactionType: {\n        type: 'conversation',\n        duration: 45,\n        qualityScore: 8.5,\n        participants: ['user1', 'user2']\n      },\n      participantCultures: ['Zulu', 'Afrikaans'],\n      duration: 45,\n      qualityScore: 8.5,\n      culturalLearningOutcomes: [\n        {\n          outcome: 'Learned about traditional Zulu greetings',\n          culturalContext: 'Zulu',\n          learningType: 'knowledge',\n          participants: [userId]\n        }\n      ],\n      timestamp: new Date(),\n      verificationMethod: 'automatic'\n    };\n\n    await recognitionEngineService.recordCrossCulturalInteraction(interaction);\n    await loadUserData();\n  };\n\n  const recordServiceActivity = async () => {\n    // Simulate recording community service\n    const activity: ServiceActivity = {\n      userId,\n      activityType: 'volunteering',\n      communityBenefited: 'Local Zulu Community',\n      culturalContext: 'Zulu',\n      hoursContributed: 4,\n      impactDescription: 'Helped organize cultural festival',\n      verificationDocuments: [],\n      communityEndorsements: ['community-leader-1'],\n      timestamp: new Date()\n    };\n\n    await recognitionEngineService.trackCommunityServiceActivity(activity);\n    await loadUserData();\n  };\n\n  const renderBadgeCard = (badge: UserBadge) => (\n    <div key={badge.id} className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-3\">\n          <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold ${\n            badge.level.level === 'platinum' ? 'bg-gray-400' :\n            badge.level.level === 'gold' ? 'bg-yellow-500' :\n            badge.level.level === 'silver' ? 'bg-gray-300' :\n            'bg-orange-600'\n          }`}>\n            {badge.level.level === 'cultural_ambassador' ? '👑' :\n             badge.level.level === 'platinum' ? '💎' :\n             badge.level.level === 'gold' ? '🥇' :\n             badge.level.level === 'silver' ? '🥈' : '🥉'}\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900\">{badge.badgeType.specificType}</h3>\n            <p className=\"text-sm text-gray-600 capitalize\">{badge.level.level} Level</p>\n          </div>\n        </div>\n        <span className=\"text-xs text-gray-500\">\n          {badge.earnedDate.toLocaleDateString()}\n        </span>\n      </div>\n      \n      <p className=\"text-sm text-gray-700 mb-4\">{badge.badgeType.description}</p>\n      \n      <div className=\"space-y-2\">\n        <div className=\"flex justify-between text-sm\">\n          <span className=\"text-gray-600\">Cultures Involved:</span>\n          <span className=\"font-medium\">{badge.culturalContext.culturesInvolved.join(', ')}</span>\n        </div>\n        <div className=\"flex justify-between text-sm\">\n          <span className=\"text-gray-600\">Impact Level:</span>\n          <span className=\"font-medium capitalize\">{badge.culturalContext.impactLevel}</span>\n        </div>\n        <div className=\"flex justify-between text-sm\">\n          <span className=\"text-gray-600\">Bridge Building Score:</span>\n          <span className=\"font-medium\">{badge.culturalContext.bridgeBuildingScore}/100</span>\n        </div>\n      </div>\n\n      {badge.level.specialPrivileges.length > 0 && (\n        <div className=\"mt-4 pt-4 border-t\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Special Privileges:</h4>\n          <ul className=\"text-xs text-gray-600 space-y-1\">\n            {badge.level.specialPrivileges.map((privilege, index) => (\n              <li key={index}>• {privilege}</li>\n            ))}\n          </ul>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderEligibilityCard = (eligibility: BadgeEligibility) => (\n    <div key={eligibility.badgeType.specificType} className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"font-semibold text-gray-900\">{eligibility.badgeType.specificType}</h3>\n        <span className={`px-2 py-1 rounded-full text-xs ${\n          eligibility.eligible ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'\n        }`}>\n          {eligibility.eligible ? 'Ready to Claim!' : `${Math.round(eligibility.progress)}% Complete`}\n        </span>\n      </div>\n      \n      <p className=\"text-sm text-gray-700 mb-4\">{eligibility.badgeType.description}</p>\n      \n      <div className=\"mb-4\">\n        <div className=\"flex justify-between text-sm mb-1\">\n          <span>Progress</span>\n          <span>{Math.round(eligibility.progress)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div \n            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n            style={{ width: `${Math.min(eligibility.progress, 100)}%` }}\n          ></div>\n        </div>\n      </div>\n      \n      <div className=\"space-y-2 mb-4\">\n        <h4 className=\"text-sm font-medium text-gray-900\">Requirements:</h4>\n        {eligibility.requirements.map((req: any, index: number) => (\n          <div key={index} className=\"text-xs text-gray-600\">\n            • {req.requirement}\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"text-sm text-gray-600 mb-4\">\n        <strong>Next Milestone:</strong> {eligibility.nextMilestone}\n      </div>\n      \n      <div className=\"text-sm text-gray-600 mb-4\">\n        <strong>Estimated Time:</strong> {eligibility.estimatedTimeToEarn}\n      </div>\n      \n      {eligibility.eligible && (\n        <button\n          onClick={() => handleClaimBadge(eligibility.badgeType)}\n          className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\"\n        >\n          Claim Badge\n        </button>\n      )}\n    </div>\n  );\n\n  const renderEngagementScore = () => (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h3 className=\"text-xl font-semibold mb-6\">Your Engagement Score (Last 30 Days)</h3>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n        <div className=\"text-center\">\n          <div className=\"text-3xl font-bold text-blue-600\">{engagementScore?.totalScore || 0}</div>\n          <div className=\"text-sm text-gray-600\">Total Score</div>\n        </div>\n        <div className=\"text-center\">\n          <div className=\"text-3xl font-bold text-green-600\">{engagementScore?.culturalDiversity || 0}</div>\n          <div className=\"text-sm text-gray-600\">Cultural Diversity</div>\n        </div>\n        <div className=\"text-center\">\n          <div className=\"text-3xl font-bold text-purple-600\">{engagementScore?.consistency || 0}</div>\n          <div className=\"text-sm text-gray-600\">Consistency</div>\n        </div>\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <div className=\"bg-blue-50 rounded-lg p-4\">\n          <h4 className=\"font-medium text-blue-900 mb-2\">Cross-Cultural Engagement</h4>\n          <div className=\"text-2xl font-bold text-blue-600\">{engagementScore?.crossCulturalEngagement || 0}</div>\n          <div className=\"text-sm text-blue-700\">Points from cultural interactions</div>\n        </div>\n        \n        <div className=\"bg-green-50 rounded-lg p-4\">\n          <h4 className=\"font-medium text-green-900 mb-2\">Community Service</h4>\n          <div className=\"text-2xl font-bold text-green-600\">{engagementScore?.communityService || 0}</div>\n          <div className=\"text-sm text-green-700\">Points from service activities</div>\n        </div>\n        \n        <div className=\"bg-purple-50 rounded-lg p-4\">\n          <h4 className=\"font-medium text-purple-900 mb-2\">Bridge Building</h4>\n          <div className=\"text-2xl font-bold text-purple-600\">{engagementScore?.bridgeBuilding || 0}</div>\n          <div className=\"text-sm text-purple-700\">Points from connecting communities</div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderLeaderboard = () => (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h3 className=\"text-xl font-semibold mb-6\">Community Leaderboard</h3>\n      \n      <div className=\"space-y-4\">\n        {leaderboard.slice(0, 10).map((entry, index) => (\n          <div key={entry.userId} className={`flex items-center justify-between p-3 rounded-lg ${\n            entry.userId === userId ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'\n          }`}>\n            <div className=\"flex items-center space-x-3\">\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${\n                index === 0 ? 'bg-yellow-500' :\n                index === 1 ? 'bg-gray-400' :\n                index === 2 ? 'bg-orange-600' :\n                'bg-gray-300'\n              }`}>\n                {index + 1}\n              </div>\n              <div>\n                <div className=\"font-medium\">\n                  {entry.userId === userId ? 'You' : `User ${entry.userId.slice(-4)}`}\n                </div>\n                <div className=\"text-sm text-gray-600\">\n                  {entry.badges} badges • {entry.culturalDiversity} cultural diversity\n                </div>\n              </div>\n            </div>\n            <div className=\"text-right\">\n              <div className=\"font-bold text-lg\">{entry.score}</div>\n              <div className=\"text-sm text-gray-600\">points</div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const renderImpactSummary = () => (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h3 className=\"text-xl font-semibold mb-6\">Your Cultural Impact</h3>\n      \n      {recognitionReport && (\n        <div className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-600\">\n                {recognitionReport.summary.culturalEngagement.culturesEngaged}\n              </div>\n              <div className=\"text-sm text-gray-600\">Cultures Engaged</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-green-600\">\n                {recognitionReport.summary.communityImpact.serviceHours}\n              </div>\n              <div className=\"text-sm text-gray-600\">Service Hours</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-purple-600\">\n                {recognitionReport.summary.communityImpact.connectionsFormed}\n              </div>\n              <div className=\"text-sm text-gray-600\">Connections Formed</div>\n            </div>\n          </div>\n          \n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-3\">Recent Achievements</h4>\n            <div className=\"space-y-2\">\n              {recognitionReport.summary.achievements.slice(0, 5).map((achievement: any, index: number) => (\n                <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <span className=\"text-sm\">{achievement.badgeType}</span>\n                  <span className=\"text-xs text-gray-500\">{achievement.earnedDate}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n          \n          {recognitionReport.recommendations.length > 0 && (\n            <div>\n              <h4 className=\"font-medium text-gray-900 mb-3\">Recommendations</h4>\n              <ul className=\"space-y-2\">\n                {recognitionReport.recommendations.map((rec: string, index: number) => (\n                  <li key={index} className=\"text-sm text-gray-700\">• {rec}</li>\n                ))}\n              </ul>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Recognition Dashboard</h1>\n        <p className=\"text-gray-600\">\n          Track your cross-cultural engagement and earn recognition for building bridges between communities\n        </p>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n        <h2 className=\"text-lg font-semibold mb-4\">Quick Actions</h2>\n        <div className=\"flex flex-wrap gap-4\">\n          <button\n            onClick={recordInteraction}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n          >\n            Record Cross-Cultural Interaction\n          </button>\n          <button\n            onClick={recordServiceActivity}\n            className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\"\n          >\n            Log Community Service\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6\">\n        {[\n          { key: 'badges', label: 'My Badges', icon: '🏆' },\n          { key: 'progress', label: 'Progress', icon: '📈' },\n          { key: 'leaderboard', label: 'Leaderboard', icon: '🥇' },\n          { key: 'impact', label: 'Impact', icon: '🌍' }\n        ].map(tab => (\n          <button\n            key={tab.key}\n            onClick={() => setActiveTab(tab.key as any)}\n            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === tab.key\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <span>{tab.icon}</span>\n            <span>{tab.label}</span>\n          </button>\n        ))}\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'badges' && (\n        <div>\n          <h2 className=\"text-2xl font-semibold mb-6\">Your Badges ({userBadges.length})</h2>\n          {userBadges.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {userBadges.map(renderBadgeCard)}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No badges yet</h3>\n              <p className=\"text-gray-600 mb-4\">Start engaging with different cultures to earn your first badge!</p>\n              <button\n                onClick={recordInteraction}\n                className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                Record Your First Interaction\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {activeTab === 'progress' && (\n        <div>\n          <h2 className=\"text-2xl font-semibold mb-6\">Badge Progress</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {eligibleBadges.map(renderEligibilityCard)}\n          </div>\n        </div>\n      )}\n\n      {activeTab === 'leaderboard' && renderLeaderboard()}\n\n      {activeTab === 'impact' && (\n        <div className=\"space-y-6\">\n          {renderEngagementScore()}\n          {renderImpactSummary()}\n        </div>\n      )}\n\n      {/* Badge Award Modal */}\n      {recentAward && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl\">🏆</span>\n              </div>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Congratulations!</h2>\n              <h3 className=\"text-lg font-semibold text-blue-600 mb-4\">\n                {recentAward.badge.badgeType.specificType}\n              </h3>\n              <p className=\"text-gray-700 mb-4\">{recentAward.celebrationMessage}</p>\n              <p className=\"text-sm text-gray-600 mb-6\">{recentAward.culturalContext}</p>\n              \n              {recentAward.nextLevelInfo && (\n                <div className=\"bg-blue-50 rounded-lg p-4 mb-4\">\n                  <h4 className=\"font-medium text-blue-900 mb-2\">Next Level</h4>\n                  <p className=\"text-sm text-blue-700\">\n                    Earn {recentAward.nextLevelInfo.pointsRequired} more points to reach {recentAward.nextLevelInfo.level} level\n                  </p>\n                </div>\n              )}\n              \n              <button\n                onClick={() => setRecentAward(null)}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700\"\n              >\n                Continue\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default RecognitionDashboard;\n", "import {\n  CulturalAchievementCuration,\n  CurationMetadata,\n  CulturalValidation,\n  HistoricalVerification,\n  PublicationStatus,\n  CommunityEndorsementData,\n  TraditionalElement,\n  CulturalProtocol,\n  SensitivityLevel,\n  ValidationCriterion,\n  ExpertConsultation,\n  HistoricalSource,\n  KnowledgeKeeper\n} from '../types/achievement';\n\nexport interface CurationRequest {\n  achievementId: string;\n  culturalRepresentativeId: string;\n  curationReason: string;\n  proposedSignificance: string;\n  traditionalElements: string[];\n  culturalProtocols: string[];\n  sensitivityLevel: 'public' | 'community_only' | 'restricted' | 'sacred';\n  communityConsultationRequired: boolean;\n}\n\nexport interface ValidationRequest {\n  curationId: string;\n  validatorId: string;\n  validatorType: 'historian' | 'cultural_expert' | 'traditional_knowledge_keeper' | 'academic';\n  validationCriteria: string[];\n  expertiseAreas: string[];\n}\n\nexport interface CommunityReviewData {\n  curationId: string;\n  reviewerId: string;\n  reviewType: 'accuracy' | 'cultural_appropriateness' | 'completeness' | 'sensitivity';\n  rating: number;\n  feedback: string;\n  culturalContext: string;\n}\n\nexport interface PublicationDecision {\n  curationId: string;\n  decision: 'approve' | 'reject' | 'request_revision';\n  visibility: 'public' | 'community_only' | 'cultural_representatives_only';\n  conditions: string[];\n  reviewNotes: string;\n}\n\nclass CulturalCurationService {\n  private curations: Map<string, CulturalAchievementCuration> = new Map();\n  private validationQueue: Map<string, ValidationRequest[]> = new Map();\n  private communityReviews: Map<string, CommunityReviewData[]> = new Map();\n  private expertNetwork: Map<string, any> = new Map();\n\n  constructor() {\n    this.initializeExpertNetwork();\n  }\n\n  // Curation Management\n  async submitCurationRequest(request: CurationRequest): Promise<CulturalAchievementCuration> {\n    const curation: CulturalAchievementCuration = {\n      id: this.generateId(),\n      culturalRepresentativeId: request.culturalRepresentativeId,\n      achievementId: request.achievementId,\n      curationMetadata: {\n        culturalSignificance: {\n          significanceLevel: 'community',\n          culturalImpact: request.proposedSignificance,\n          traditionalKnowledgeElements: request.traditionalElements,\n          modernRelevance: 'Contemporary cultural significance',\n          preservationValue: 8,\n          educationalValue: 7\n        },\n        traditionalElements: request.traditionalElements.map(element => ({\n          element,\n          significance: 'Traditional cultural practice',\n          authenticity: 'verified' as const\n        })),\n        modernRelevance: {\n          contemporarySignificance: request.proposedSignificance,\n          modernApplications: [],\n          culturalContinuity: 'Strong connection to traditional practices',\n          adaptationRespect: true\n        },\n        culturalProtocols: request.culturalProtocols.map(protocol => ({\n          protocol,\n          description: 'Cultural protocol for respectful engagement',\n          importance: 'required' as const,\n          culturalContext: 'Traditional cultural practice'\n        })),\n        sensitivityLevel: {\n          level: request.sensitivityLevel,\n          restrictions: this.determineSensitivityRestrictions(request.sensitivityLevel),\n          accessRequirements: this.determineAccessRequirements(request.sensitivityLevel)\n        },\n        sharingRestrictions: this.determineSharingRestrictions(request.sensitivityLevel),\n        communityPermissions: {\n          hasPermission: false,\n          grantedBy: '',\n          grantDate: new Date(),\n          conditions: [],\n          revocable: true\n        }\n      },\n      culturalValidation: {\n        primaryValidator: request.culturalRepresentativeId,\n        secondaryValidators: [],\n        validationCriteria: this.getDefaultValidationCriteria(),\n        expertConsultations: [],\n        communityFeedback: [],\n        validationScore: 0,\n        culturalAccuracyRating: 0\n      },\n      communityEndorsement: {\n        endorsements: [],\n        votingResults: {\n          totalVotes: 0,\n          approvalPercentage: 0,\n          culturalCommunityVotes: 0,\n          generalCommunityVotes: 0,\n          votingPeriod: {\n            startDate: new Date(),\n            endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 2 weeks\n            historicalPeriod: 'democratic_era',\n            culturalCalendarAlignment: []\n          }\n        },\n        representativeApproval: {\n          approvedBy: '',\n          approvalDate: new Date(),\n          approvalNotes: '',\n          conditions: []\n        }\n      },\n      historicalVerification: {\n        primarySources: [],\n        secondarySources: [],\n        expertVerification: [],\n        traditionalKnowledgeKeepers: [],\n        verificationLevel: 'preliminary',\n        accuracyConfidence: 0,\n        factCheckingResults: []\n      },\n      crossCulturalElements: [],\n      publicationStatus: {\n        status: 'draft',\n        visibility: 'cultural_representatives_only',\n        featuredStatus: false\n      },\n      impactMetrics: {\n        communityEngagement: 0,\n        culturalLearning: 0,\n        crossCulturalDiscovery: 0,\n        historicalPreservation: 0,\n        educationalValue: 0\n      }\n    };\n\n    this.curations.set(curation.id, curation);\n    \n    // Initiate validation process if community consultation is required\n    if (request.communityConsultationRequired) {\n      await this.initiateValidationProcess(curation.id);\n    }\n\n    return curation;\n  }\n\n  async getCuration(curationId: string): Promise<CulturalAchievementCuration | null> {\n    return this.curations.get(curationId) || null;\n  }\n\n  async updateCurationMetadata(curationId: string, metadata: Partial<CurationMetadata>): Promise<CulturalAchievementCuration> {\n    const curation = this.curations.get(curationId);\n    if (!curation) {\n      throw new Error('Curation not found');\n    }\n\n    curation.curationMetadata = {\n      ...curation.curationMetadata,\n      ...metadata\n    };\n\n    this.curations.set(curationId, curation);\n    return curation;\n  }\n\n  // Validation Process\n  async initiateValidationProcess(curationId: string): Promise<void> {\n    const curation = this.curations.get(curationId);\n    if (!curation) {\n      throw new Error('Curation not found');\n    }\n\n    // Identify required validators based on cultural context and sensitivity\n    const requiredValidators = await this.identifyRequiredValidators(curation);\n    \n    // Create validation requests\n    const validationRequests: ValidationRequest[] = requiredValidators.map(validator => ({\n      curationId,\n      validatorId: validator.id,\n      validatorType: validator.type,\n      validationCriteria: this.getValidationCriteriaForType(validator.type),\n      expertiseAreas: validator.expertiseAreas\n    }));\n\n    this.validationQueue.set(curationId, validationRequests);\n\n    // Notify validators\n    await this.notifyValidators(validationRequests);\n  }\n\n  async submitExpertValidation(\n    curationId: string, \n    validatorId: string, \n    validation: ExpertConsultation\n  ): Promise<void> {\n    const curation = this.curations.get(curationId);\n    if (!curation) {\n      throw new Error('Curation not found');\n    }\n\n    curation.culturalValidation.expertConsultations.push(validation);\n    \n    // Update validation score\n    await this.updateValidationScore(curationId);\n    \n    this.curations.set(curationId, curation);\n  }\n\n  async addHistoricalSource(curationId: string, source: HistoricalSource): Promise<void> {\n    const curation = this.curations.get(curationId);\n    if (!curation) {\n      throw new Error('Curation not found');\n    }\n\n    if (source.reliability >= 8) {\n      curation.historicalVerification.primarySources.push(source);\n    } else {\n      curation.historicalVerification.secondarySources.push(source);\n    }\n\n    // Update verification level based on sources\n    await this.updateVerificationLevel(curationId);\n    \n    this.curations.set(curationId, curation);\n  }\n\n  async consultTraditionalKnowledgeKeeper(\n    curationId: string, \n    keeper: KnowledgeKeeper\n  ): Promise<void> {\n    const curation = this.curations.get(curationId);\n    if (!curation) {\n      throw new Error('Curation not found');\n    }\n\n    curation.historicalVerification.traditionalKnowledgeKeepers.push(keeper);\n    \n    // Traditional knowledge keeper consultation carries high weight\n    curation.historicalVerification.accuracyConfidence += 20;\n    \n    this.curations.set(curationId, curation);\n  }\n\n  // Community Review Process\n  async submitCommunityReview(review: CommunityReviewData): Promise<void> {\n    const reviews = this.communityReviews.get(review.curationId) || [];\n    reviews.push(review);\n    this.communityReviews.set(review.curationId, reviews);\n\n    const curation = this.curations.get(review.curationId);\n    if (curation) {\n      curation.culturalValidation.communityFeedback.push({\n        feedbackId: this.generateId(),\n        providerId: review.reviewerId,\n        feedbackType: review.reviewType,\n        feedback: review.feedback,\n        rating: review.rating,\n        date: new Date()\n      });\n\n      // Update community approval metrics\n      await this.updateCommunityApproval(review.curationId);\n      \n      this.curations.set(review.curationId, curation);\n    }\n  }\n\n  async getCommunityReviews(curationId: string): Promise<CommunityReviewData[]> {\n    return this.communityReviews.get(curationId) || [];\n  }\n\n  async calculateCommunityConsensus(curationId: string): Promise<any> {\n    const reviews = this.communityReviews.get(curationId) || [];\n    const curation = this.curations.get(curationId);\n    \n    if (!curation || reviews.length === 0) {\n      return { consensus: 'insufficient_data', confidence: 0 };\n    }\n\n    const averageRating = reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;\n    const culturalCommunityReviews = reviews.filter(r => \n      this.isCulturalCommunityMember(r.reviewerId, r.culturalContext)\n    );\n    \n    const culturalConsensus = culturalCommunityReviews.length > 0 \n      ? culturalCommunityReviews.reduce((sum, r) => sum + r.rating, 0) / culturalCommunityReviews.length\n      : 0;\n\n    const consensus = averageRating >= 7 && culturalConsensus >= 7 ? 'approved' :\n                     averageRating <= 4 || culturalConsensus <= 4 ? 'rejected' : 'mixed';\n\n    return {\n      consensus,\n      confidence: Math.min(reviews.length * 10, 100),\n      averageRating,\n      culturalConsensus,\n      totalReviews: reviews.length,\n      culturalCommunityReviews: culturalCommunityReviews.length\n    };\n  }\n\n  // Publication Management\n  async makePublicationDecision(decision: PublicationDecision): Promise<CulturalAchievementCuration> {\n    const curation = this.curations.get(decision.curationId);\n    if (!curation) {\n      throw new Error('Curation not found');\n    }\n\n    const newStatus: PublicationStatus = {\n      status: decision.decision === 'approve' ? 'published' : \n              decision.decision === 'reject' ? 'archived' : 'cultural_review',\n      publishDate: decision.decision === 'approve' ? new Date() : undefined,\n      visibility: decision.visibility,\n      featuredStatus: false\n    };\n\n    curation.publicationStatus = newStatus;\n\n    // If approved, update impact metrics\n    if (decision.decision === 'approve') {\n      await this.updateImpactMetrics(decision.curationId);\n    }\n\n    this.curations.set(decision.curationId, curation);\n    return curation;\n  }\n\n  async getPublishedCurations(filters?: any): Promise<CulturalAchievementCuration[]> {\n    const published = Array.from(this.curations.values()).filter(c => \n      c.publicationStatus.status === 'published'\n    );\n\n    if (filters?.culturalContext) {\n      return published.filter(c => \n        c.curationMetadata.culturalSignificance.traditionalKnowledgeElements\n          .some(element => element.includes(filters.culturalContext))\n      );\n    }\n\n    if (filters?.sensitivityLevel) {\n      return published.filter(c => \n        c.curationMetadata.sensitivityLevel.level === filters.sensitivityLevel\n      );\n    }\n\n    return published.sort((a, b) => \n      b.impactMetrics.communityEngagement - a.impactMetrics.communityEngagement\n    );\n  }\n\n  async getFeaturedCurations(): Promise<CulturalAchievementCuration[]> {\n    return Array.from(this.curations.values()).filter(c => \n      c.publicationStatus.status === 'published' && c.publicationStatus.featuredStatus\n    );\n  }\n\n  // Analytics and Reporting\n  async generateCurationReport(timeframe: { startDate: Date; endDate: Date }): Promise<any> {\n    const curations = Array.from(this.curations.values());\n    const timeframeCurations = curations.filter(c => \n      c.publicationStatus.publishDate && \n      c.publicationStatus.publishDate >= timeframe.startDate && \n      c.publicationStatus.publishDate <= timeframe.endDate\n    );\n\n    const totalCurations = timeframeCurations.length;\n    const publishedCurations = timeframeCurations.filter(c => c.publicationStatus.status === 'published').length;\n    const averageValidationScore = timeframeCurations.reduce((sum, c) => sum + c.culturalValidation.validationScore, 0) / totalCurations || 0;\n\n    const culturalDistribution = this.calculateCulturalDistribution(timeframeCurations);\n    const sensitivityDistribution = this.calculateSensitivityDistribution(timeframeCurations);\n\n    return {\n      timeframe,\n      totalCurations,\n      publishedCurations,\n      publicationRate: (publishedCurations / totalCurations) * 100 || 0,\n      averageValidationScore,\n      culturalDistribution,\n      sensitivityDistribution,\n      topCurations: timeframeCurations\n        .sort((a, b) => b.impactMetrics.communityEngagement - a.impactMetrics.communityEngagement)\n        .slice(0, 10)\n        .map(c => ({\n          id: c.id,\n          culturalSignificance: c.curationMetadata.culturalSignificance.culturalImpact,\n          engagement: c.impactMetrics.communityEngagement\n        }))\n    };\n  }\n\n  async getCurationsByRepresentative(representativeId: string): Promise<CulturalAchievementCuration[]> {\n    return Array.from(this.curations.values()).filter(c => \n      c.culturalRepresentativeId === representativeId\n    );\n  }\n\n  async getPendingValidations(validatorId: string): Promise<ValidationRequest[]> {\n    const allValidations: ValidationRequest[] = [];\n    \n    for (const [curationId, requests] of this.validationQueue) {\n      const userRequests = requests.filter(r => r.validatorId === validatorId);\n      allValidations.push(...userRequests);\n    }\n\n    return allValidations;\n  }\n\n  // Helper Methods\n  private initializeExpertNetwork(): void {\n    // Initialize expert network with cultural representatives, historians, etc.\n    this.expertNetwork.set('expert-1', {\n      id: 'expert-1',\n      type: 'cultural_expert',\n      expertiseAreas: ['Zulu traditions', 'oral history'],\n      culturalContext: 'Zulu',\n      credentials: ['PhD Cultural Studies', 'Community Elder']\n    });\n\n    this.expertNetwork.set('expert-2', {\n      id: 'expert-2',\n      type: 'historian',\n      expertiseAreas: ['South African history', 'colonial period'],\n      culturalContext: 'Academic',\n      credentials: ['PhD History', 'Published researcher']\n    });\n\n    this.expertNetwork.set('keeper-1', {\n      id: 'keeper-1',\n      type: 'traditional_knowledge_keeper',\n      expertiseAreas: ['traditional medicine', 'ceremonial practices'],\n      culturalContext: 'Xhosa',\n      credentials: ['Traditional healer', 'Community recognized']\n    });\n  }\n\n  private async identifyRequiredValidators(curation: CulturalAchievementCuration): Promise<any[]> {\n    const validators = [];\n    const sensitivityLevel = curation.curationMetadata.sensitivityLevel.level;\n    \n    // Always require cultural expert for the primary culture\n    const culturalExperts = Array.from(this.expertNetwork.values()).filter(expert => \n      expert.type === 'cultural_expert'\n    );\n    if (culturalExperts.length > 0) {\n      validators.push(culturalExperts[0]);\n    }\n\n    // Require historian for historical claims\n    if (curation.historicalVerification.primarySources.length > 0) {\n      const historians = Array.from(this.expertNetwork.values()).filter(expert => \n        expert.type === 'historian'\n      );\n      if (historians.length > 0) {\n        validators.push(historians[0]);\n      }\n    }\n\n    // Require traditional knowledge keeper for sacred or restricted content\n    if (sensitivityLevel === 'sacred' || sensitivityLevel === 'restricted') {\n      const keepers = Array.from(this.expertNetwork.values()).filter(expert => \n        expert.type === 'traditional_knowledge_keeper'\n      );\n      if (keepers.length > 0) {\n        validators.push(keepers[0]);\n      }\n    }\n\n    return validators;\n  }\n\n  private getDefaultValidationCriteria(): ValidationCriterion[] {\n    return [\n      { criterion: 'Cultural accuracy', weight: 30, passed: false, notes: '' },\n      { criterion: 'Historical accuracy', weight: 25, passed: false, notes: '' },\n      { criterion: 'Cultural sensitivity', weight: 25, passed: false, notes: '' },\n      { criterion: 'Community appropriateness', weight: 20, passed: false, notes: '' }\n    ];\n  }\n\n  private getValidationCriteriaForType(validatorType: string): string[] {\n    const criteria = {\n      'cultural_expert': ['Cultural accuracy', 'Traditional knowledge validation', 'Community appropriateness'],\n      'historian': ['Historical accuracy', 'Source verification', 'Factual consistency'],\n      'traditional_knowledge_keeper': ['Sacred knowledge protocols', 'Cultural sensitivity', 'Traditional authenticity'],\n      'academic': ['Academic rigor', 'Research methodology', 'Citation accuracy']\n    };\n\n    return criteria[validatorType as keyof typeof criteria] || ['General validation'];\n  }\n\n  private async notifyValidators(requests: ValidationRequest[]): Promise<void> {\n    // In a real implementation, this would send notifications to validators\n    console.log(`Notifying ${requests.length} validators for validation requests`);\n  }\n\n  private async updateValidationScore(curationId: string): Promise<void> {\n    const curation = this.curations.get(curationId);\n    if (!curation) return;\n\n    const consultations = curation.culturalValidation.expertConsultations;\n    if (consultations.length === 0) return;\n\n    const averageConfidence = consultations.reduce((sum, c) => sum + c.confidence, 0) / consultations.length;\n    curation.culturalValidation.validationScore = averageConfidence;\n    curation.culturalValidation.culturalAccuracyRating = averageConfidence;\n  }\n\n  private async updateVerificationLevel(curationId: string): Promise<void> {\n    const curation = this.curations.get(curationId);\n    if (!curation) return;\n\n    const verification = curation.historicalVerification;\n    const primarySources = verification.primarySources.length;\n    const secondarySources = verification.secondarySources.length;\n    const expertVerifications = verification.expertVerification.length;\n    const knowledgeKeepers = verification.traditionalKnowledgeKeepers.length;\n\n    if (knowledgeKeepers > 0 && primarySources > 2 && expertVerifications > 1) {\n      verification.verificationLevel = 'culturally_endorsed';\n    } else if (primarySources > 1 && expertVerifications > 0) {\n      verification.verificationLevel = 'expert_verified';\n    } else if (primarySources > 0 || secondarySources > 1) {\n      verification.verificationLevel = 'validated';\n    }\n\n    // Update confidence based on sources and verifications\n    verification.accuracyConfidence = Math.min(\n      (primarySources * 20) + (secondarySources * 10) + (expertVerifications * 15) + (knowledgeKeepers * 25),\n      100\n    );\n  }\n\n  private async updateCommunityApproval(curationId: string): Promise<void> {\n    const reviews = this.communityReviews.get(curationId) || [];\n    const curation = this.curations.get(curationId);\n    if (!curation) return;\n\n    const totalVotes = reviews.length;\n    const approvalVotes = reviews.filter(r => r.rating >= 7).length;\n    const approvalPercentage = totalVotes > 0 ? (approvalVotes / totalVotes) * 100 : 0;\n\n    const culturalCommunityVotes = reviews.filter(r => \n      this.isCulturalCommunityMember(r.reviewerId, r.culturalContext)\n    ).length;\n\n    curation.communityEndorsement.votingResults = {\n      ...curation.communityEndorsement.votingResults,\n      totalVotes,\n      approvalPercentage,\n      culturalCommunityVotes,\n      generalCommunityVotes: totalVotes - culturalCommunityVotes\n    };\n  }\n\n  private async updateImpactMetrics(curationId: string): Promise<void> {\n    const curation = this.curations.get(curationId);\n    if (!curation) return;\n\n    // Simulate impact metrics calculation\n    curation.impactMetrics = {\n      communityEngagement: Math.floor(Math.random() * 100) + 50,\n      culturalLearning: Math.floor(Math.random() * 100) + 40,\n      crossCulturalDiscovery: Math.floor(Math.random() * 100) + 30,\n      historicalPreservation: curation.historicalVerification.accuracyConfidence,\n      educationalValue: curation.curationMetadata.culturalSignificance.educationalValue * 10\n    };\n  }\n\n  private determineSensitivityRestrictions(level: string): string[] {\n    const restrictions = {\n      'public': [],\n      'community_only': ['Requires community membership'],\n      'restricted': ['Requires cultural representative approval', 'Limited sharing'],\n      'sacred': ['Sacred content - restricted access', 'No commercial use', 'Cultural protocols required']\n    };\n\n    return restrictions[level as keyof typeof restrictions] || [];\n  }\n\n  private determineAccessRequirements(level: string): string[] {\n    const requirements = {\n      'public': [],\n      'community_only': ['Community membership verification'],\n      'restricted': ['Cultural representative endorsement', 'Purpose statement'],\n      'sacred': ['Traditional knowledge keeper approval', 'Cultural protocol training', 'Community elder endorsement']\n    };\n\n    return requirements[level as keyof typeof requirements] || [];\n  }\n\n  private determineSharingRestrictions(level: string): any[] {\n    const baseRestrictions = [\n      {\n        restrictionType: 'attribution_required',\n        description: 'Attribution to cultural community required',\n        enforcement: 'automatic'\n      }\n    ];\n\n    if (level === 'sacred' || level === 'restricted') {\n      baseRestrictions.push({\n        restrictionType: 'no_commercial_use',\n        description: 'Commercial use prohibited',\n        enforcement: 'community_moderated'\n      });\n    }\n\n    if (level === 'sacred') {\n      baseRestrictions.push({\n        restrictionType: 'sacred_content',\n        description: 'Sacred cultural content - special protocols apply',\n        enforcement: 'expert_reviewed'\n      });\n    }\n\n    return baseRestrictions;\n  }\n\n  private isCulturalCommunityMember(reviewerId: string, culturalContext: string): boolean {\n    // Simplified check - in real implementation would verify community membership\n    return Math.random() > 0.5; // 50% chance for simulation\n  }\n\n  private calculateCulturalDistribution(curations: CulturalAchievementCuration[]): { [culture: string]: number } {\n    const distribution: { [culture: string]: number } = {};\n    curations.forEach(curation => {\n      curation.curationMetadata.culturalSignificance.traditionalKnowledgeElements.forEach(element => {\n        // Simplified - would extract culture from element\n        const culture = 'Zulu'; // Placeholder\n        distribution[culture] = (distribution[culture] || 0) + 1;\n      });\n    });\n    return distribution;\n  }\n\n  private calculateSensitivityDistribution(curations: CulturalAchievementCuration[]): { [level: string]: number } {\n    const distribution: { [level: string]: number } = {};\n    curations.forEach(curation => {\n      const level = curation.curationMetadata.sensitivityLevel.level;\n      distribution[level] = (distribution[level] || 0) + 1;\n    });\n    return distribution;\n  }\n\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n}\n\nexport const culturalCurationService = new CulturalCurationService();\n", "import React, { useState, useEffect } from 'react';\nimport {\n  CulturalAchievementCuration,\n  CurationRequest,\n  ValidationRequest,\n  CommunityReviewData,\n  PublicationDecision\n} from '../types/achievement';\nimport { culturalCurationService } from '../services/culturalCurationService';\n\ninterface CulturalCurationDashboardProps {\n  userId: string;\n  userRole: 'cultural_representative' | 'expert' | 'community_member';\n}\n\nconst CulturalCurationDashboard: React.FC<CulturalCurationDashboardProps> = ({ userId, userRole }) => {\n  const [curations, setCurations] = useState<CulturalAchievementCuration[]>([]);\n  const [pendingValidations, setPendingValidations] = useState<ValidationRequest[]>([]);\n  const [selectedCuration, setSelectedCuration] = useState<CulturalAchievementCuration | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState<'my_curations' | 'pending_review' | 'published' | 'analytics'>('my_curations');\n  const [showCurationForm, setShowCurationForm] = useState(false);\n  const [curationReport, setCurationReport] = useState<any>(null);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, [userId, userRole]);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      if (userRole === 'cultural_representative') {\n        const userCurations = await culturalCurationService.getCurationsByRepresentative(userId);\n        setCurations(userCurations);\n      }\n      \n      if (userRole === 'expert') {\n        const validations = await culturalCurationService.getPendingValidations(userId);\n        setPendingValidations(validations);\n      }\n      \n      const report = await culturalCurationService.generateCurationReport({\n        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),\n        endDate: new Date()\n      });\n      setCurationReport(report);\n      \n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmitCuration = async (formData: any) => {\n    try {\n      const request: CurationRequest = {\n        achievementId: formData.achievementId,\n        culturalRepresentativeId: userId,\n        curationReason: formData.reason,\n        proposedSignificance: formData.significance,\n        traditionalElements: formData.traditionalElements.split(',').map((e: string) => e.trim()),\n        culturalProtocols: formData.protocols.split(',').map((p: string) => p.trim()),\n        sensitivityLevel: formData.sensitivityLevel,\n        communityConsultationRequired: formData.requiresConsultation\n      };\n\n      const curation = await culturalCurationService.submitCurationRequest(request);\n      setCurations(prev => [...prev, curation]);\n      setShowCurationForm(false);\n    } catch (error) {\n      console.error('Error submitting curation:', error);\n    }\n  };\n\n  const handleValidationSubmission = async (validationId: string, findings: string, confidence: number) => {\n    try {\n      const validation = {\n        expertId: userId,\n        expertType: 'cultural_expert' as const,\n        consultationDate: new Date(),\n        findings,\n        recommendations: [findings],\n        confidence\n      };\n\n      await culturalCurationService.submitExpertValidation(validationId, userId, validation);\n      \n      // Remove from pending validations\n      setPendingValidations(prev => prev.filter(v => v.curationId !== validationId));\n    } catch (error) {\n      console.error('Error submitting validation:', error);\n    }\n  };\n\n  const handleCommunityReview = async (curationId: string, reviewData: any) => {\n    try {\n      const review: CommunityReviewData = {\n        curationId,\n        reviewerId: userId,\n        reviewType: reviewData.type,\n        rating: reviewData.rating,\n        feedback: reviewData.feedback,\n        culturalContext: reviewData.culturalContext\n      };\n\n      await culturalCurationService.submitCommunityReview(review);\n    } catch (error) {\n      console.error('Error submitting review:', error);\n    }\n  };\n\n  const handlePublicationDecision = async (curationId: string, decision: any) => {\n    try {\n      const publicationDecision: PublicationDecision = {\n        curationId,\n        decision: decision.action,\n        visibility: decision.visibility,\n        conditions: decision.conditions || [],\n        reviewNotes: decision.notes\n      };\n\n      await culturalCurationService.makePublicationDecision(publicationDecision);\n      await loadDashboardData();\n    } catch (error) {\n      console.error('Error making publication decision:', error);\n    }\n  };\n\n  const renderCurationCard = (curation: CulturalAchievementCuration) => (\n    <div \n      key={curation.id}\n      className=\"bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow\"\n      onClick={() => setSelectedCuration(curation)}\n    >\n      <div className=\"flex justify-between items-start mb-4\">\n        <h3 className=\"font-semibold text-lg text-gray-900\">\n          Cultural Significance Curation\n        </h3>\n        <span className={`px-2 py-1 rounded-full text-xs ${\n          curation.publicationStatus.status === 'published' ? 'bg-green-100 text-green-800' :\n          curation.publicationStatus.status === 'cultural_review' ? 'bg-yellow-100 text-yellow-800' :\n          curation.publicationStatus.status === 'community_review' ? 'bg-blue-100 text-blue-800' :\n          'bg-gray-100 text-gray-800'\n        }`}>\n          {curation.publicationStatus.status.replace('_', ' ')}\n        </span>\n      </div>\n      \n      <p className=\"text-gray-700 mb-4\">\n        {curation.curationMetadata.culturalSignificance.culturalImpact}\n      </p>\n      \n      <div className=\"space-y-2 mb-4\">\n        <div className=\"flex justify-between text-sm\">\n          <span className=\"text-gray-600\">Sensitivity Level:</span>\n          <span className=\"font-medium capitalize\">{curation.curationMetadata.sensitivityLevel.level}</span>\n        </div>\n        <div className=\"flex justify-between text-sm\">\n          <span className=\"text-gray-600\">Validation Score:</span>\n          <span className=\"font-medium\">{curation.culturalValidation.validationScore}/100</span>\n        </div>\n        <div className=\"flex justify-between text-sm\">\n          <span className=\"text-gray-600\">Community Approval:</span>\n          <span className=\"font-medium\">{curation.communityEndorsement.votingResults.approvalPercentage}%</span>\n        </div>\n      </div>\n      \n      <div className=\"flex flex-wrap gap-2\">\n        {curation.curationMetadata.traditionalElements.slice(0, 3).map((element, index) => (\n          <span key={index} className=\"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs\">\n            {element.element}\n          </span>\n        ))}\n        {curation.curationMetadata.traditionalElements.length > 3 && (\n          <span className=\"px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs\">\n            +{curation.curationMetadata.traditionalElements.length - 3} more\n          </span>\n        )}\n      </div>\n    </div>\n  );\n\n  const renderValidationCard = (validation: ValidationRequest) => (\n    <div key={validation.curationId} className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex justify-between items-start mb-4\">\n        <h3 className=\"font-semibold text-lg text-gray-900\">Validation Request</h3>\n        <span className=\"px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs\">\n          {validation.validatorType.replace('_', ' ')}\n        </span>\n      </div>\n      \n      <div className=\"space-y-3 mb-4\">\n        <div>\n          <h4 className=\"font-medium text-gray-900 mb-2\">Validation Criteria:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            {validation.validationCriteria.map((criterion: string, index: number) => (\n              <li key={index}>• {criterion}</li>\n            ))}\n          </ul>\n        </div>\n        \n        <div>\n          <h4 className=\"font-medium text-gray-900 mb-2\">Your Expertise Areas:</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {validation.expertiseAreas.map((area: string, index: number) => (\n              <span key={index} className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs\">\n                {area}\n              </span>\n            ))}\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"space-y-3\">\n        <textarea\n          placeholder=\"Enter your validation findings...\"\n          className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\"\n          rows={3}\n          id={`findings-${validation.curationId}`}\n        />\n        \n        <div className=\"flex items-center space-x-4\">\n          <label className=\"text-sm font-medium text-gray-700\">Confidence Level:</label>\n          <input\n            type=\"range\"\n            min=\"0\"\n            max=\"100\"\n            defaultValue=\"80\"\n            className=\"flex-1\"\n            id={`confidence-${validation.curationId}`}\n          />\n          <span className=\"text-sm text-gray-600\">80%</span>\n        </div>\n        \n        <button\n          onClick={() => {\n            const findings = (document.getElementById(`findings-${validation.curationId}`) as HTMLTextAreaElement).value;\n            const confidence = parseInt((document.getElementById(`confidence-${validation.curationId}`) as HTMLInputElement).value);\n            handleValidationSubmission(validation.curationId, findings, confidence);\n          }}\n          className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700\"\n        >\n          Submit Validation\n        </button>\n      </div>\n    </div>\n  );\n\n  const renderCurationForm = () => (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto p-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Submit Cultural Curation</h2>\n          <button\n            onClick={() => setShowCurationForm(false)}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n        \n        <form onSubmit={(e) => {\n          e.preventDefault();\n          const formData = new FormData(e.target as HTMLFormElement);\n          const data = Object.fromEntries(formData.entries());\n          (data as any).requiresConsultation = (formData.get('requiresConsultation') as string) === 'on';\n          handleSubmitCuration(data);\n        }}>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Achievement ID</label>\n              <input\n                type=\"text\"\n                name=\"achievementId\"\n                required\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Enter achievement ID to curate\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Curation Reason</label>\n              <textarea\n                name=\"reason\"\n                required\n                rows={3}\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Why is this achievement culturally significant?\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Cultural Significance</label>\n              <textarea\n                name=\"significance\"\n                required\n                rows={3}\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Describe the cultural significance and impact\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Traditional Elements</label>\n              <input\n                type=\"text\"\n                name=\"traditionalElements\"\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Comma-separated list of traditional elements\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Cultural Protocols</label>\n              <input\n                type=\"text\"\n                name=\"protocols\"\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Comma-separated list of cultural protocols\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Sensitivity Level</label>\n              <select\n                name=\"sensitivityLevel\"\n                required\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"public\">Public</option>\n                <option value=\"community_only\">Community Only</option>\n                <option value=\"restricted\">Restricted</option>\n                <option value=\"sacred\">Sacred</option>\n              </select>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                name=\"requiresConsultation\"\n                id=\"requiresConsultation\"\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"requiresConsultation\" className=\"text-sm text-gray-700\">\n                Requires community consultation\n              </label>\n            </div>\n          </div>\n          \n          <div className=\"flex space-x-4 mt-6\">\n            <button\n              type=\"button\"\n              onClick={() => setShowCurationForm(false)}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n            >\n              Submit Curation\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n\n  const renderAnalytics = () => (\n    <div className=\"space-y-6\">\n      {curationReport && (\n        <>\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h3 className=\"text-xl font-semibold mb-6\">Curation Analytics (Last 30 Days)</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-blue-600\">{curationReport.totalCurations}</div>\n                <div className=\"text-sm text-gray-600\">Total Curations</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-600\">{curationReport.publishedCurations}</div>\n                <div className=\"text-sm text-gray-600\">Published</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-purple-600\">{Math.round(curationReport.publicationRate)}%</div>\n                <div className=\"text-sm text-gray-600\">Publication Rate</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-orange-600\">{Math.round(curationReport.averageValidationScore)}</div>\n                <div className=\"text-sm text-gray-600\">Avg Validation Score</div>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h4 className=\"font-semibold mb-4\">Cultural Distribution</h4>\n              <div className=\"space-y-2\">\n                {Object.entries(curationReport.culturalDistribution).map(([culture, count]) => (\n                  <div key={culture} className=\"flex justify-between\">\n                    <span className=\"text-sm text-gray-600\">{culture}</span>\n                    <span className=\"font-medium\">{count as number}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n            \n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h4 className=\"font-semibold mb-4\">Sensitivity Distribution</h4>\n              <div className=\"space-y-2\">\n                {Object.entries(curationReport.sensitivityDistribution).map(([level, count]) => (\n                  <div key={level} className=\"flex justify-between\">\n                    <span className=\"text-sm text-gray-600 capitalize\">{level.replace('_', ' ')}</span>\n                    <span className=\"font-medium\">{count as number}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h4 className=\"font-semibold mb-4\">Top Curations by Engagement</h4>\n            <div className=\"space-y-3\">\n              {curationReport.topCurations.map((curation: any, index: number) => (\n                <div key={curation.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <div>\n                    <span className=\"font-medium\">#{index + 1}</span>\n                    <span className=\"ml-2 text-sm text-gray-700\">{curation.culturalSignificance}</span>\n                  </div>\n                  <span className=\"text-sm font-medium text-blue-600\">{curation.engagement} engagement</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      <div className=\"flex justify-between items-center mb-8\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Cultural Curation Dashboard</h1>\n          <p className=\"text-gray-600\">\n            Curate and validate culturally significant achievements with community oversight\n          </p>\n        </div>\n        \n        {userRole === 'cultural_representative' && (\n          <button\n            onClick={() => setShowCurationForm(true)}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium\"\n          >\n            Submit New Curation\n          </button>\n        )}\n      </div>\n\n      {/* Tabs */}\n      <div className=\"flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6\">\n        {[\n          { key: 'my_curations', label: 'My Curations', icon: '📝', show: userRole === 'cultural_representative' },\n          { key: 'pending_review', label: 'Pending Review', icon: '⏳', show: userRole === 'expert' },\n          { key: 'published', label: 'Published', icon: '✅', show: true },\n          { key: 'analytics', label: 'Analytics', icon: '📊', show: true }\n        ].filter(tab => tab.show).map(tab => (\n          <button\n            key={tab.key}\n            onClick={() => setActiveTab(tab.key as any)}\n            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === tab.key\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <span>{tab.icon}</span>\n            <span>{tab.label}</span>\n          </button>\n        ))}\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'my_curations' && (\n        <div>\n          <h2 className=\"text-2xl font-semibold mb-6\">Your Curations ({curations.length})</h2>\n          {curations.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {curations.map(renderCurationCard)}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No curations yet</h3>\n              <p className=\"text-gray-600 mb-4\">Start curating culturally significant achievements</p>\n              <button\n                onClick={() => setShowCurationForm(true)}\n                className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                Submit Your First Curation\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {activeTab === 'pending_review' && (\n        <div>\n          <h2 className=\"text-2xl font-semibold mb-6\">Pending Validations ({pendingValidations.length})</h2>\n          {pendingValidations.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {pendingValidations.map(renderValidationCard)}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No pending validations</h3>\n              <p className=\"text-gray-600\">All validation requests have been completed</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {activeTab === 'published' && (\n        <div>\n          <h2 className=\"text-2xl font-semibold mb-6\">Published Curations</h2>\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-600\">Published curations will be displayed here</p>\n          </div>\n        </div>\n      )}\n\n      {activeTab === 'analytics' && renderAnalytics()}\n\n      {/* Curation Form Modal */}\n      {showCurationForm && renderCurationForm()}\n\n      {/* Curation Detail Modal */}\n      {selectedCuration && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto p-6\">\n            <div className=\"flex justify-between items-start mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900\">Curation Details</h2>\n              <button\n                onClick={() => setSelectedCuration(null)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            </div>\n            \n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"font-semibold mb-2\">Cultural Significance</h3>\n                <p className=\"text-gray-700\">{selectedCuration.curationMetadata.culturalSignificance.culturalImpact}</p>\n              </div>\n              \n              <div>\n                <h3 className=\"font-semibold mb-2\">Traditional Elements</h3>\n                <div className=\"flex flex-wrap gap-2\">\n                  {selectedCuration.curationMetadata.traditionalElements.map((element, index) => (\n                    <span key={index} className=\"px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm\">\n                      {element.element}\n                    </span>\n                  ))}\n                </div>\n              </div>\n              \n              <div>\n                <h3 className=\"font-semibold mb-2\">Validation Status</h3>\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <span className=\"text-sm text-gray-600\">Validation Score:</span>\n                    <span className=\"ml-2 font-medium\">{selectedCuration.culturalValidation.validationScore}/100</span>\n                  </div>\n                  <div>\n                    <span className=\"text-sm text-gray-600\">Cultural Accuracy:</span>\n                    <span className=\"ml-2 font-medium\">{selectedCuration.culturalValidation.culturalAccuracyRating}/100</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div>\n                <h3 className=\"font-semibold mb-2\">Community Approval</h3>\n                <div className=\"bg-gray-50 rounded-lg p-4\">\n                  <div className=\"flex justify-between mb-2\">\n                    <span className=\"text-sm text-gray-600\">Approval Rate:</span>\n                    <span className=\"font-medium\">{selectedCuration.communityEndorsement.votingResults.approvalPercentage}%</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm text-gray-600\">Total Votes:</span>\n                    <span className=\"font-medium\">{selectedCuration.communityEndorsement.votingResults.totalVotes}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CulturalCurationDashboard;\n", "import React, { useState } from 'react';\nimport AchievementGallery from '../components/AchievementGallery';\nimport RecognitionDashboard from '../components/RecognitionDashboard';\nimport CulturalCurationDashboard from '../components/CulturalCurationDashboard';\n\ninterface AchievementShowcasePageProps {\n  userId: string;\n  userRole?: 'cultural_representative' | 'expert' | 'community_member';\n}\n\nconst AchievementShowcasePage: React.FC<AchievementShowcasePageProps> = ({ \n  userId, \n  userRole = 'community_member' \n}) => {\n  const [activeSection, setActiveSection] = useState<'gallery' | 'recognition' | 'curation'>('gallery');\n\n  const renderSectionContent = () => {\n    switch (activeSection) {\n      case 'gallery':\n        return <AchievementGallery userId={userId} />;\n      case 'recognition':\n        return <RecognitionDashboard userId={userId} />;\n      case 'curation':\n        return <CulturalCurationDashboard userId={userId} userRole={userRole} />;\n      default:\n        return <AchievementGallery userId={userId} />;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Achievement Showcase & Recognition</h1>\n              <p className=\"text-gray-600 mt-1\">\n                Discover, celebrate, and preserve South African achievements across all cultures\n              </p>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                <button\n                  onClick={() => setActiveSection('gallery')}\n                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                    activeSection === 'gallery'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  Gallery\n                </button>\n                <button\n                  onClick={() => setActiveSection('recognition')}\n                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                    activeSection === 'recognition'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  Recognition\n                </button>\n                <button\n                  onClick={() => setActiveSection('curation')}\n                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                    activeSection === 'curation'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  Curation\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Section Description */}\n      <div className=\"bg-blue-50 border-b border-blue-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          {activeSection === 'gallery' && (\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-blue-900\">South African Achievement Gallery</h3>\n                <p className=\"text-blue-700\">\n                  Explore a comprehensive collection of verified achievements from across South Africa's \n                  diverse cultural communities, celebrating excellence in sports, arts, business, education, and community service.\n                </p>\n              </div>\n            </div>\n          )}\n          \n          {activeSection === 'recognition' && (\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-blue-900\">Cross-Cultural Engagement Recognition</h3>\n                <p className=\"text-blue-700\">\n                  Earn badges and recognition for meaningful cross-cultural engagement, community service, \n                  and bridge-building activities that strengthen Ubuntu connections across diverse communities.\n                </p>\n              </div>\n            </div>\n          )}\n          \n          {activeSection === 'curation' && (\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-blue-900\">Cultural Representative Achievement Curation</h3>\n                <p className=\"text-blue-700\">\n                  Cultural representatives curate and validate achievements with community oversight, \n                  ensuring cultural accuracy, sensitivity, and appropriate preservation of traditional knowledge.\n                </p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"py-8\">\n        {renderSectionContent()}\n      </div>\n\n      {/* Ubuntu Philosophy Footer */}\n      <div className=\"bg-gray-900 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-xl font-semibold mb-4\">Celebrating Excellence Through Ubuntu</h3>\n            <p className=\"text-gray-300 max-w-3xl mx-auto\">\n              Our achievement showcase celebrates the principle that individual excellence \n              contributes to collective prosperity. By recognizing and preserving achievements \n              across all cultures, we honor the diverse talents that make South Africa strong \n              while building bridges of understanding and mutual respect.\n            </p>\n          </div>\n          \n          <div className=\"mt-8 grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"bg-blue-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-medium mb-2\">Discover & Celebrate</h4>\n              <p className=\"text-gray-400 text-sm\">\n                Explore achievements from all South African cultures and communities\n              </p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"bg-green-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-medium mb-2\">Earn Recognition</h4>\n              <p className=\"text-gray-400 text-sm\">\n                Build bridges between cultures and earn badges for meaningful engagement\n              </p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"bg-purple-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\" />\n                </svg>\n              </div>\n              <h4 className=\"font-medium mb-2\">Preserve Heritage</h4>\n              <p className=\"text-gray-400 text-sm\">\n                Ensure cultural accuracy and sensitivity in achievement documentation\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"mt-8 pt-8 border-t border-gray-700\">\n            <div className=\"text-center\">\n              <h4 className=\"font-medium mb-4\">Recognition Categories</h4>\n              <div className=\"flex flex-wrap justify-center gap-4 text-sm\">\n                <span className=\"px-3 py-1 bg-gray-800 rounded-full\">🏆 Sports & Athletics</span>\n                <span className=\"px-3 py-1 bg-gray-800 rounded-full\">🎨 Arts & Culture</span>\n                <span className=\"px-3 py-1 bg-gray-800 rounded-full\">💼 Business & Innovation</span>\n                <span className=\"px-3 py-1 bg-gray-800 rounded-full\">📚 Education & Learning</span>\n                <span className=\"px-3 py-1 bg-gray-800 rounded-full\">🔬 Innovation & Technology</span>\n                <span className=\"px-3 py-1 bg-gray-800 rounded-full\">🤝 Community Service</span>\n                <span className=\"px-3 py-1 bg-gray-800 rounded-full\">🌍 Cross-Cultural Bridge Building</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AchievementShowcasePage;\n"], "names": ["AchievementGalleryService", "data", "achievement", "element", "media", "endorsement", "achievementId", "updates", "updatedAchievement", "deleted", "filters", "achievements", "a", "b", "scoreA", "id", "category", "location", "locationKey", "query", "searchTerms", "filteredResults", "searchableText", "term", "userId", "userProfile", "suggestions", "isDifferentCulture", "isCrossCultural", "isHighQuality", "timeframe", "now", "startDate", "recentAchievements", "popularityA", "timeRange", "related", "sameCulture", "sameCategory", "sameLocation", "sameEra", "fromCulture", "toCulture", "_platform", "totalViews", "sum", "totalShares", "crossCulturalEngagement", "culturalDistribution", "categoryDistribution", "culturalContext", "date", "year", "categoryKey", "ids", "index", "featuredIndex", "_filters", "score", "searchText", "title<PERSON><PERSON><PERSON>", "descMatch<PERSON>", "culturalMatches", "candidate", "reference", "yearsDiff", "distribution", "culture", "_userId", "achievementGalleryService", "AchievementGallery", "setAchievements", "useState", "featuredAchievements", "setFeaturedAchievements", "selectedAchievement", "setSelectedAchievement", "setFilters", "searchQuery", "setSearch<PERSON>uery", "loading", "setLoading", "activeView", "setActiveView", "timelineEvents", "setTimelineEvents", "setSuggestions", "categories", "cultures", "useEffect", "loadInitialData", "performSearch", "loadAchievements", "featured", "userSuggestions", "error", "results", "searchResults", "loadTimeline", "timeline", "handleAchievementClick", "updatedAchievements", "handleShare", "platform", "handleFilterChange", "filterType", "value", "prev", "clearFilters", "renderAchievementCard", "jsxs", "jsx", "e", "renderFilters", "cat", "renderTimeline", "event", "renderViewTabs", "tab", "RecognitionEngineService", "eligibilities", "userProgress", "badgeId", "badgeType", "progress", "eligible", "level", "badge", "userBadgeList", "nextLevel", "req", "interaction", "userInteractions", "activity", "userActivities", "impact", "userBridgeBuilding", "totalConnections", "impactMetrics", "interactions", "serviceActivities", "bridgeBuilding", "timeframeInteractions", "i", "timeframeService", "timeframeBridgeBuilding", "crossCulturalScore", "serviceScore", "bridgeBuildingScore", "badges", "totalCultures", "totalServiceHours", "allUsers", "leaderboardEntries", "categoryScore", "entry", "totalProgress", "totalRequirements", "requirement", "target", "requirementProgress", "current", "totalScore", "context", "currentLevel", "levels", "currentIndex", "pointsRequired", "privileges", "remaining", "difficulty", "baseTime", "estimatedWeeks", "actions", "currentBadges", "earnedBadgeTypes", "eligibility", "cultureBonus", "qualityBonus", "durationBonus", "activities", "hoursScore", "events", "connectionBonus", "sustainabilityBonus", "uniqueCultures", "allEvents", "weeks", "eventsPerWeek", "recentInteractions", "olderInteractions", "recentAvgQuality", "olderAvgQuality", "groups", "recommendations", "recognitionEngineService", "RecognitionDashboard", "userBadges", "setUserBadges", "eligibleBadges", "setEligibleBadges", "engagementScore", "setEngagementScore", "recognitionReport", "setRecognitionReport", "leaderboard", "setLeaderboard", "activeTab", "setActiveTab", "recentAward", "setRecentAward", "loadUserData", "report", "leaderboardData", "handleClaimBadge", "award", "recordInteraction", "recordServiceActivity", "renderBadgeCard", "privilege", "renderEligibilityCard", "renderEngagementScore", "renderLeaderboard", "renderImpactSummary", "rec", "CulturalCurationService", "request", "curation", "protocol", "curationId", "metadata", "validationRequests", "validator", "validatorId", "validation", "source", "keeper", "review", "reviews", "averageRating", "r", "culturalCommunityReviews", "culturalConsensus", "decision", "newStatus", "published", "c", "timeframeCurations", "totalCurations", "publishedCurations", "averageValidationScore", "sensitivityDistribution", "representativeId", "allValidations", "requests", "userRequests", "validators", "sensitivityLevel", "culturalExperts", "expert", "historians", "keepers", "validatorType", "consultations", "averageConfidence", "verification", "primarySources", "secondarySources", "expertVerifications", "knowledgeKeepers", "totalVotes", "approvalVotes", "approvalPercentage", "culturalCommunityVotes", "baseRestrictions", "reviewerId", "curations", "culturalCurationService", "CulturalCurationDashboard", "userRole", "setCurations", "pendingValidations", "setPendingValidations", "selectedCuration", "setSelectedCuration", "showCurationForm", "setShowCurationForm", "curationReport", "setCurationReport", "loadDashboardData", "userCurations", "validations", "handleSubmitCuration", "formData", "p", "handleValidationSubmission", "validationId", "findings", "confidence", "v", "renderCurationCard", "renderValidationCard", "criterion", "area", "renderCurationForm", "renderAnalytics", "Fragment", "count", "AchievementShowcasePage", "activeSection", "setActiveSection", "renderSectionContent"], "mappings": "gHAgDA,MAAMA,CAA0B,CACtB,iBAA6C,IAC7C,qBAAiC,CAAC,EAClC,yBAAkD,IAClD,yBAAkD,IAG1D,MAAM,kBAAkBC,EAAqD,CAC3E,MAAMC,EAA2B,CAC/B,GAAI,KAAK,WAAW,EACpB,MAAOD,EAAK,MACZ,YAAaA,EAAK,YAClB,SAAU,CACR,QAASA,EAAK,SAAS,QACvB,UAAWA,EAAK,SAAS,WAAa,CAAC,EACvC,cAAeA,EAAK,gBAAgB,sBAAsB,OAAS,EACnE,qBAAsB,KAAK,8BAA8BA,EAAK,SAAUA,EAAK,eAAe,CAC9F,EACA,gBAAiB,CACf,eAAgBA,EAAK,gBAAgB,eACrC,kBAAmBA,EAAK,gBAAgB,kBACxC,qBAAsBA,EAAK,gBAAgB,qBAC3C,oBAAqBA,EAAK,gBAAgB,oBAC1C,kBAAmB,CAAC,EACpB,sBAAuBA,EAAK,gBAAgB,sBAAsB,IAAgBE,IAAA,CAChF,QAAAA,EACA,gBAAiB,CAACF,EAAK,gBAAgB,eAAgB,GAAGA,EAAK,gBAAgB,iBAAiB,EAChG,kBAAmB,oBACnB,kBAAmB,gDAAA,EACnB,CACJ,EACA,SAAUA,EAAK,SACf,UAAW,CACT,UAAWA,EAAK,UAAU,UAC1B,QAASA,EAAK,UAAU,QACxB,iBAAkB,KAAK,0BAA0BA,EAAK,UAAU,SAAS,EACzE,0BAA2B,CAAA,CAC7B,EACA,aAAcA,EAAK,aAAa,IAAcG,IAAA,CAC5C,GAAI,KAAK,WAAW,EACpB,KAAMA,EAAM,KACZ,IAAKA,EAAM,IACX,QAASA,EAAM,QACf,gBAAiBA,EAAM,iBAAmB,GAC1C,cAAe,CACb,QAASA,EAAM,QACf,cAAe,GACf,oBAAqBA,EAAM,iBAAmB,EAChD,EACA,YAAa,CACX,aAAc,GACd,wBAAyB,GACzB,cAAe,GACf,YAAa,UAAA,CACf,EACA,EACF,aAAc,CACZ,OAAQ,UACR,UAAW,CAAC,EACZ,kBAAmB,EACnB,iBAAkB,CAAC,EACnB,kBAAmB,CACjB,cAAe,EACf,WAAY,EACZ,+BAAgC,GAChC,sBAAuBH,EAAK,iBAAiB,qBAC/C,EACA,iBAAkB,CAChB,yBAA0B,GAC1B,eAAgB,UAChB,sBAAuB,EACvB,+BAAgC,GAChC,yBAA0B,SAAA,CAE9B,EACA,sBAAuBA,EAAK,iBAAiB,sBAAsB,IAAoBI,IAAA,CACrF,WAAYA,EACZ,gBAAiB,oBACjB,oBAAqB,KACrB,mBAAoB,+BACpB,gBAAiBJ,EAAK,gBAAgB,cAAA,EACtC,EACF,qBAAsB,CACpB,kBAAmB,YACnB,eAAgBA,EAAK,gBAAgB,qBACrC,6BAA8BA,EAAK,gBAAgB,oBACnD,gBAAiB,qCACjB,kBAAmB,EACnB,iBAAkB,CACpB,EACA,oBAAqB,CAAC,EACtB,aAAc,CACZ,iBAAkB,4BAClB,qBAAsB,kCACtB,2BAA4B,wCAC5B,mBAAoB,EACpB,mBAAoB,CACtB,EACA,iBAAkB,CAChB,YAAaA,EAAK,iBAAiB,YACnC,mBAAoB,KACpB,cAAeA,EAAK,iBAAiB,cACrC,iBAAkB,WAClB,sBAAuB,CAAC,EACxB,sBAAuBA,EAAK,iBAAiB,qBAC/C,EACA,kBAAmB,CACjB,MAAO,EACP,OAAQ,EACR,oBAAqB,EACrB,iBAAkB,EAClB,wBAAyB,CAC3B,EACA,cAAe,KACf,cAAe,IACjB,EAEA,YAAK,aAAa,IAAIC,EAAY,GAAIA,CAAW,EACjD,KAAK,iBAAiBA,CAAW,EAC1BA,CAAA,CAGT,MAAM,eAAeI,EAAoD,CACvE,OAAO,KAAK,aAAa,IAAIA,CAAa,GAAK,IAAA,CAGjD,MAAM,kBAAkBA,EAAuBC,EAAsD,CACnG,MAAML,EAAc,KAAK,aAAa,IAAII,CAAa,EACvD,GAAI,CAACJ,EACG,MAAA,IAAI,MAAM,uBAAuB,EAGzC,MAAMM,EAAqB,CACzB,GAAGN,EACH,GAAGK,EACH,cAAe,IACjB,EAEK,YAAA,aAAa,IAAID,EAAeE,CAAkB,EAChDA,CAAA,CAGT,MAAM,kBAAkBF,EAAyC,CAC/D,MAAMG,EAAU,KAAK,aAAa,OAAOH,CAAa,EACtD,YAAK,kBAAkBA,CAAa,EAC7BG,CAAA,CAIT,MAAM,gBAAgBC,EAAqD,CACzE,IAAIC,EAAe,MAAM,KAAK,KAAK,aAAa,QAAQ,EAExD,OAAID,EAAQ,WACVC,EAAeA,EAAa,OAAOC,GAAKA,EAAE,SAAS,UAAYF,EAAQ,QAAQ,GAG7EA,EAAQ,kBACVC,EAAeA,EAAa,OAAOC,GACjCA,EAAE,gBAAgB,iBAAmBF,EAAQ,iBAC7CE,EAAE,gBAAgB,kBAAkB,SAASF,EAAQ,eAAgB,CACvE,GAGEA,EAAQ,WACVC,EAAeA,EAAa,OAAOC,GACjCA,EAAE,SAAS,WAAaF,EAAQ,UAAU,UAC1CE,EAAE,SAAS,OAASF,EAAQ,UAAU,IACxC,GAGEA,EAAQ,YACVC,EAAeA,EAAa,OAAOC,GACjCA,EAAE,UAAU,WAAaF,EAAQ,UAAW,WAC5CE,EAAE,UAAU,SAAWF,EAAQ,UAAW,OAC5C,GAGEA,EAAQ,qBACVC,EAAeA,EAAa,OAAOC,GAAKA,EAAE,aAAa,SAAWF,EAAQ,kBAAkB,GAIjFC,EAAA,KAAK,CAACC,EAAGC,IAAM,CAC1B,MAAMC,EAAS,KAAK,wBAAwBF,EAAGF,CAAO,EAEtD,OADe,KAAK,wBAAwBG,EAAGH,CAAO,EACtCI,CAAA,CACjB,EAEMH,EAAa,MAAM,EAAGD,EAAQ,OAAS,EAAE,CAAA,CAGlD,MAAM,yBAAkD,CAK/C,OAJU,KAAK,qBACnB,IAAUK,GAAA,KAAK,aAAa,IAAIA,CAAE,CAAC,EACnC,OAAO,OAAO,EAED,KAAK,CAACH,EAAGC,IAAMA,EAAE,kBAAkB,MAAQD,EAAE,kBAAkB,KAAK,CAAA,CAGtF,MAAM,0BAA0BI,EAAuD,CAE9E,OADa,KAAK,qBAAqB,IAAIA,EAAS,OAAO,GAAK,CAAC,GAErE,IAAID,GAAM,KAAK,aAAa,IAAIA,CAAE,CAAC,EACnC,OAAO,OAAO,CAAA,CAGnB,MAAM,0BAA0BE,EAAsD,CACpF,MAAMC,EAAc,GAAGD,EAAS,QAAQ,IAAIA,EAAS,IAAI,GAElD,OADa,KAAK,qBAAqB,IAAIC,CAAW,GAAK,CAAC,GAEhE,IAAIH,GAAM,KAAK,aAAa,IAAIA,CAAE,CAAC,EACnC,OAAO,OAAO,CAAA,CAInB,MAAM,mBAAmBI,EAA4C,CACnE,MAAMR,EAAe,MAAM,KAAK,KAAK,aAAa,QAAQ,EACpDS,EAAcD,EAAM,WAAW,YAAY,EAAE,MAAM,GAAG,EAgB5D,IAAIE,EAdYV,EAAa,OAAsBT,GAAA,CACjD,MAAMoB,EAAiB,CACrBpB,EAAY,MACZA,EAAY,YACZA,EAAY,gBAAgB,qBAC5BA,EAAY,gBAAgB,eAC5B,GAAGA,EAAY,gBAAgB,kBAC/B,GAAGA,EAAY,gBAAgB,mBAAA,EAC/B,KAAK,GAAG,EAAE,YAAY,EAExB,OAAOkB,EAAY,MAAMG,GAAQD,EAAe,SAASC,CAAI,CAAC,CAAA,CAC/D,EAKG,OAAAJ,EAAM,SAAS,WACCE,EAAAA,EAAgB,OAAYT,GAAAA,EAAE,SAAS,UAAYO,EAAM,QAAS,QAAQ,GAG1FA,EAAM,SAAS,kBACjBE,EAAkBA,EAAgB,OAChCT,GAAAA,EAAE,gBAAgB,iBAAmBO,EAAM,QAAS,iBACpDP,EAAE,gBAAgB,kBAAkB,SAASO,EAAM,QAAS,eAAgB,CAC9E,GAIcE,EAAA,KAAK,CAACT,EAAGC,IAAM,CAC7B,MAAMC,EAAS,KAAK,yBAAyBF,EAAGO,EAAM,UAAU,EAEhE,OADe,KAAK,yBAAyBN,EAAGM,EAAM,UAAU,EAChDL,CAAA,CACjB,EAEMO,EAAgB,MAAM,EAAGF,EAAM,OAAS,EAAE,CAAA,CAGnD,MAAM,0BAA0BK,EAAwC,CAEtE,MAAMC,EAAc,MAAM,KAAK,eAAeD,CAAM,EAI9CE,EAHe,MAAM,KAAK,KAAK,aAAa,QAAQ,EAGzB,OAAsBxB,GAAA,CAErD,MAAMyB,EAAqBzB,EAAY,gBAAgB,iBAAmBuB,EAAY,eAGhFG,EAAkB1B,EAAY,SAAS,cAGvC2B,EAAgB3B,EAAY,aAAa,kBAAoB,GAEnE,OAAQyB,GAAsBC,IAAoBC,CAAA,CACnD,EAGW,OAAAH,EAAA,KAAK,CAACd,EAAGC,IAAM,CACzB,MAAMC,EAAS,KAAK,yBAAyBF,EAAGa,CAAW,EAE3D,OADe,KAAK,yBAAyBZ,EAAGY,CAAW,EAC3CX,CAAA,CACjB,EAEMY,EAAY,MAAM,EAAG,EAAE,CAAA,CAGhC,MAAM,uBAAuBI,EAA2C,CACtE,MAAMnB,EAAe,MAAM,KAAK,KAAK,aAAa,QAAQ,EACpDoB,MAAU,KACZ,IAAAC,EAEJ,OAAQF,EAAW,CACjB,IAAK,OACSE,EAAA,IAAI,KAAKD,EAAI,QAAA,EAAY,EAAI,GAAK,GAAK,GAAK,GAAI,EAC5D,MACF,IAAK,QACSC,EAAA,IAAI,KAAKD,EAAI,QAAA,EAAY,GAAK,GAAK,GAAK,GAAK,GAAI,EAC7D,MACF,IAAK,OACSC,EAAA,IAAI,KAAKD,EAAI,QAAA,EAAY,IAAM,GAAK,GAAK,GAAK,GAAI,EAC9D,MACF,QACcC,EAAA,IAAI,KAAKD,EAAI,QAAA,EAAY,GAAK,GAAK,GAAK,GAAK,GAAI,CAAA,CAGjE,MAAME,EAAqBtB,EAAa,OAAYC,GAAAA,EAAE,WAAaoB,CAAS,EAEzD,OAAAC,EAAA,KAAK,CAACrB,EAAGC,IAAM,CAC1B,MAAAqB,EAActB,EAAE,kBAAkB,MAAQA,EAAE,kBAAkB,OAAS,EAAIA,EAAE,kBAAkB,iBAAmB,EAExH,OADoBC,EAAE,kBAAkB,MAAQA,EAAE,kBAAkB,OAAS,EAAIA,EAAE,kBAAkB,iBAAmB,EACnGqB,CAAA,CACtB,EAEMD,EAAmB,MAAM,EAAG,EAAE,CAAA,CAIvC,MAAM,uBAAuBE,EAAyE,CAiBpG,OAhBqB,MAAM,KAAK,KAAK,aAAa,OAAQ,CAAA,EAAE,OAAO,GACjE,EAAE,UAAU,WAAaA,EAAU,WAAa,EAAE,UAAU,SAAWA,EAAU,OACnF,EAEqD,IAAoBjC,IAAA,CACvE,GAAIA,EAAY,GAChB,MAAOA,EAAY,MACnB,KAAMA,EAAY,UAAU,UAC5B,gBAAiBA,EAAY,gBAAgB,eAC7C,SAAUA,EAAY,SAAS,QAC/B,aAAcA,EAAY,qBAAqB,kBAC/C,YAAaA,EAAY,YACzB,SAAUA,EAAY,aAAa,CAAC,GAAG,KAAO,GAC9C,cAAeA,EAAY,SAAS,aAAA,EACpC,EAEoB,KAAK,CAAC,EAAGW,IAAM,EAAE,KAAK,QAAA,EAAYA,EAAE,KAAK,QAAA,CAAS,CAAA,CAI1E,MAAM,uBAAuBP,EAA+C,CAC1E,MAAMJ,EAAc,KAAK,aAAa,IAAII,CAAa,EACvD,GAAI,CAACJ,EACH,MAAO,CAAC,EAIJ,MAAAkC,EADkB,MAAM,KAAK,KAAK,aAAa,QAAQ,EAC7B,OAAYxB,GAAA,CACtC,GAAAA,EAAE,KAAON,EAAsB,MAAA,GAGnC,MAAM+B,EAAczB,EAAE,gBAAgB,iBAAmBV,EAAY,gBAAgB,eAG/EoC,EAAe1B,EAAE,SAAS,UAAYV,EAAY,SAAS,QAG3DqC,EAAe3B,EAAE,SAAS,WAAaV,EAAY,SAAS,SAI5DsC,EADW,KAAK,IAAI5B,EAAE,UAAU,UAAU,QAAQ,EAAIV,EAAY,UAAU,UAAU,QAAA,CAAS,EACzE,GAAK,IAAM,GAAK,GAAK,GAAK,IAE/C,OAAAmC,GAAeC,GAAgBC,GAAgBC,CAAA,CACvD,EAGO,OAAAJ,EAAA,KAAK,CAACxB,EAAGC,IAAM,CACrB,MAAMC,EAAS,KAAK,sBAAsBF,EAAGV,CAAW,EAExD,OADe,KAAK,sBAAsBW,EAAGX,CAAW,EACxCY,CAAA,CACjB,EAEMsB,EAAQ,MAAM,EAAG,CAAC,CAAA,CAI3B,MAAM,qBAAqB9B,EAAuBkB,EAA+B,CAC/E,MAAMtB,EAAc,KAAK,aAAa,IAAII,CAAa,EACnDJ,IACFA,EAAY,kBAAkB,SAGV,MAAM,KAAK,eAAesB,CAAM,GACpC,iBAAmBtB,EAAY,gBAAgB,gBAC7DA,EAAY,kBAAkB,0BAG3B,KAAA,aAAa,IAAII,EAAeJ,CAAW,EAClD,CAGF,MAAM,uBAAuBuC,EAAqBC,EAAmBlB,EAA+B,CAElG,QAAQ,IAAI,uBAAuBiB,CAAW,MAAMC,CAAS,YAAYlB,CAAM,EAAE,CAAA,CAGnF,MAAM,mBAAmBlB,EAAuBqC,EAAkC,CAChF,MAAMzC,EAAc,KAAK,aAAa,IAAII,CAAa,EACnDJ,IACFA,EAAY,kBAAkB,SACzB,KAAA,aAAa,IAAII,EAAeJ,CAAW,EAClD,CAGF,MAAM,yBAAyB4B,EAA0E,CACvG,MAAMnB,EAAe,MAAM,KAAK,KAAK,aAAa,OAAQ,CAAA,EAAE,UAC1DC,EAAE,WAAakB,EAAU,WAAalB,EAAE,WAAakB,EAAU,OACjE,EAEMc,EAAajC,EAAa,OAAO,CAACkC,EAAKjC,IAAMiC,EAAMjC,EAAE,kBAAkB,MAAO,CAAC,EAC/EkC,EAAcnC,EAAa,OAAO,CAACkC,EAAKjC,IAAMiC,EAAMjC,EAAE,kBAAkB,OAAQ,CAAC,EACjFmC,EAA0BpC,EAAa,OAAO,CAACkC,EAAKjC,IAAMiC,EAAMjC,EAAE,kBAAkB,wBAAyB,CAAC,EAE9GoC,EAAuB,KAAK,8BAA8BrC,CAAY,EACtEsC,EAAuB,KAAK,8BAA8BtC,CAAY,EAErE,MAAA,CACL,UAAW,CACT,GAAGmB,EACH,iBAAkB,iBAClB,0BAA2B,CAAA,CAC7B,EACA,kBAAmBnB,EAAa,OAChC,WAAAiC,EACA,YAAAE,EACA,wBAAAC,EACA,kBAAmBH,EAAajC,EAAa,QAAU,EACvD,qBAAAqC,EACA,qBAAAC,EACA,gBAAiBtC,EACd,KAAK,CAACC,EAAGC,IAAMA,EAAE,kBAAkB,MAAQD,EAAE,kBAAkB,KAAK,EACpE,MAAM,EAAG,EAAE,EACX,IAAIA,IAAM,CAAE,GAAIA,EAAE,GAAI,MAAOA,EAAE,MAAO,MAAOA,EAAE,kBAAkB,OAAQ,CAC9E,CAAA,CAIM,YAAqB,CACpB,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAA,CAGvC,8BAA8BK,EAA8BiC,EAA6E,CAE/I,OAAIA,EAAgB,sBAAsB,OAAS,EAAU,WACzDjC,EAAS,SAAiB,aACvB,OAAA,CAGD,0BAA0BkC,EAAoB,CAC9C,MAAAC,EAAOD,EAAK,YAAY,EAC1B,OAAAC,EAAO,KAAa,eACpBA,EAAO,KAAa,iBACpBA,EAAO,KAAa,gBACjB,gBAAA,CAGD,iBAAiBlD,EAAgC,CAEjD,MAAAmD,EAAcnD,EAAY,SAAS,QACpC,KAAK,qBAAqB,IAAImD,CAAW,GAC5C,KAAK,qBAAqB,IAAIA,EAAa,CAAA,CAAE,EAE/C,KAAK,qBAAqB,IAAIA,CAAW,EAAG,KAAKnD,EAAY,EAAE,EAGzD,MAAAgB,EAAc,GAAGhB,EAAY,SAAS,QAAQ,IAAIA,EAAY,SAAS,IAAI,GAC5E,KAAK,qBAAqB,IAAIgB,CAAW,GAC5C,KAAK,qBAAqB,IAAIA,EAAa,CAAA,CAAE,EAE/C,KAAK,qBAAqB,IAAIA,CAAW,EAAG,KAAKhB,EAAY,EAAE,EAG3DA,EAAY,aAAa,kBAAoB,IAAMA,EAAY,SAAS,eACrE,KAAA,qBAAqB,KAAKA,EAAY,EAAE,CAC/C,CAGM,kBAAkBI,EAA6B,CAErD,SAAW,CAAG,CAAAgD,CAAG,IAAK,KAAK,qBAAsB,CACzC,MAAAC,EAAQD,EAAI,QAAQhD,CAAa,EACnCiD,EAAQ,IAAQD,EAAA,OAAOC,EAAO,CAAC,CAAA,CAGrC,SAAW,CAAG,CAAAD,CAAG,IAAK,KAAK,qBAAsB,CACzC,MAAAC,EAAQD,EAAI,QAAQhD,CAAa,EACnCiD,EAAQ,IAAQD,EAAA,OAAOC,EAAO,CAAC,CAAA,CAGrC,MAAMC,EAAgB,KAAK,qBAAqB,QAAQlD,CAAa,EACjEkD,EAAgB,IAAI,KAAK,qBAAqB,OAAOA,EAAe,CAAC,CAAA,CAGnE,wBAAwBtD,EAA0BuD,EAAsC,CAC9F,IAAIC,EAAQ,EAGH,OAAAA,GAAAxD,EAAY,aAAa,kBAAoB,GACtDwD,GAAS,KAAK,IAAIxD,EAAY,kBAAkB,MAAQ,IAAK,EAAE,EAAI,GAG/DA,EAAY,SAAS,gBAAwBwD,GAAA,IAGxCA,GAAAxD,EAAY,qBAAqB,kBAAoB,GAGnC,KAAK,IAAQ,EAAAA,EAAY,UAAU,QAAQ,IAAM,IAAO,GAAK,GAAK,IACrE,KAAawD,GAAA,IAE9BA,CAAA,CAGD,yBAAyBxD,EAA0ByD,EAA4B,CACrF,MAAMvC,EAAcuC,EAAW,YAAY,EAAE,MAAM,GAAG,EACtD,IAAID,EAAQ,EAGN,MAAAE,EAAexC,EAAY,OAAeG,GAAArB,EAAY,MAAM,YAAA,EAAc,SAASqB,CAAI,CAAC,EAAE,OAChGmC,GAASE,EAAe,GAGlB,MAAAC,EAAczC,EAAY,OAAeG,GAAArB,EAAY,YAAY,YAAA,EAAc,SAASqB,CAAI,CAAC,EAAE,OACrGmC,GAASG,EAAc,EAGvB,MAAMC,EAAkB1C,EAAY,OAClCG,GAAArB,EAAY,gBAAgB,qBAAqB,YAAc,EAAA,SAASqB,CAAI,GAC5ErB,EAAY,gBAAgB,eAAe,YAAY,EAAE,SAASqB,CAAI,CAAA,EACtE,OACF,OAAAmC,GAASI,EAAkB,EAEpBJ,CAAA,CAGD,yBAAyBxD,EAA0BuB,EAA0B,CACnF,IAAIiC,EAAQ,EAGZ,OAAIxD,EAAY,gBAAgB,iBAAmBuB,EAAY,iBACpDiC,GAAA,IAIPxD,EAAY,SAAS,gBACdwD,GAAA,IAIFA,GAAAxD,EAAY,aAAa,kBAAoB,GAG7CwD,GAAAxD,EAAY,qBAAqB,iBAAmB,EAG7DwD,GAAS,KAAK,IAAIxD,EAAY,kBAAkB,MAAQ,GAAI,EAAE,EAEvDwD,CAAA,CAGD,sBAAsBK,EAAwBC,EAAgC,CACpF,IAAIN,EAAQ,EAGRK,EAAU,gBAAgB,iBAAmBC,EAAU,gBAAgB,iBAChEN,GAAA,IAIPK,EAAU,SAAS,UAAYC,EAAU,SAAS,UAC3CN,GAAA,IAIPK,EAAU,SAAS,WAAaC,EAAU,SAAS,WAC5CN,GAAA,IAKX,MAAMO,EADW,KAAK,IAAIF,EAAU,UAAU,UAAU,QAAQ,EAAIC,EAAU,UAAU,UAAU,QAAA,CAAS,GAC7E,IAAM,GAAK,GAAK,GAAK,KAC/C,OAAAC,EAAY,GAAaP,GAAA,GACpBO,EAAY,KAAaP,GAAA,IAGzBA,GAAAK,EAAU,aAAa,kBAAoB,GAE7CL,CAAA,CAGD,8BAA8B/C,EAA4D,CAChG,MAAMuD,EAA8C,CAAC,EACrD,OAAAvD,EAAa,QAAuBT,GAAA,CAC5B,MAAAiE,EAAUjE,EAAY,gBAAgB,eAC5CgE,EAAaC,CAAO,GAAKD,EAAaC,CAAO,GAAK,GAAK,CAAA,CACxD,EACMD,CAAA,CAGD,8BAA8BvD,EAA6D,CACjG,MAAMuD,EAA+C,CAAC,EACtD,OAAAvD,EAAa,QAAuBT,GAAA,CAC5B,MAAAc,EAAWd,EAAY,SAAS,QACtCgE,EAAalD,CAAQ,GAAKkD,EAAalD,CAAQ,GAAK,GAAK,CAAA,CAC1D,EACMkD,CAAA,CAGT,MAAc,eAAeE,EAA+B,CAEnD,MAAA,CACL,eAAgB,OAChB,UAAW,CAAC,SAAU,OAAQ,mBAAmB,EACjD,sBAAuB,CAAC,8BAA8B,CACxD,CAAA,CAEJ,CAEa,MAAAC,EAA4B,IAAIrE,EC5oBvCsE,EAAwD,CAAC,CAAE,OAAA9C,KAAa,CAC5E,KAAM,CAACb,EAAc4D,CAAe,EAAIC,EAAAA,SAAwB,CAAA,CAAE,EAC5D,CAACC,EAAsBC,CAAuB,EAAIF,EAAAA,SAAwB,CAAA,CAAE,EAC5E,CAACG,EAAqBC,CAAsB,EAAIJ,EAAAA,SAA6B,IAAI,EACjF,CAAC9D,EAASmE,CAAU,EAAIL,EAAAA,SAA6B,CAAA,CAAE,EACvD,CAACM,EAAaC,CAAc,EAAIP,EAAAA,SAAS,EAAE,EAC3C,CAACQ,EAASC,CAAU,EAAIT,EAAAA,SAAS,EAAI,EACrC,CAACU,EAAYC,CAAa,EAAIX,EAAAA,SAA8D,SAAS,EACrG,CAACY,EAAgBC,CAAiB,EAAIb,EAAAA,SAA0B,CAAA,CAAE,EAClE,CAAC9C,EAAa4D,CAAc,EAAId,EAAAA,SAAwB,CAAA,CAAE,EAE1De,EAAa,CACjB,CAAE,MAAO,SAAU,MAAO,oBAAqB,EAC/C,CAAE,MAAO,OAAQ,MAAO,gBAAiB,EACzC,CAAE,MAAO,WAAY,MAAO,uBAAwB,EACpD,CAAE,MAAO,YAAa,MAAO,sBAAuB,EACpD,CAAE,MAAO,aAAc,MAAO,yBAA0B,EACxD,CAAE,MAAO,oBAAqB,MAAO,mBAAoB,CAC3D,EAEMC,EAAW,CACf,OAAQ,QAAS,YAAa,UAAW,QAAS,SAClD,OAAQ,QAAS,SAAU,UAAW,OACxC,EAIAC,EAAAA,UAAU,IAAM,CACEC,EAAA,CAClB,EAAG,EAAE,EAELD,EAAAA,UAAU,IAAM,CACVX,GAAe,OAAO,KAAKpE,CAAO,EAAE,OAAS,EACjCiF,EAAA,EAEGC,EAAA,CACnB,EACC,CAAClF,EAASoE,CAAW,CAAC,EAEzB,MAAMY,EAAkB,SAAY,CAC9B,GAAA,CACFT,EAAW,EAAI,EACf,KAAM,CAACY,EAAUC,CAAe,EAAI,MAAM,QAAQ,IAAI,CACpDzB,EAA0B,wBAAwB,EAClDA,EAA0B,0BAA0B7C,CAAM,CAAA,CAC3D,EAEDkD,EAAwBmB,CAAQ,EAChCP,EAAeQ,CAAe,EAE9B,MAAMF,EAAiB,QAChBG,EAAO,CACN,QAAA,MAAM,8BAA+BA,CAAK,CAAA,QAClD,CACAd,EAAW,EAAK,CAAA,CAEpB,EAEMW,EAAmB,SAAY,CAC/B,GAAA,CACF,MAAMI,EAAU,MAAM3B,EAA0B,gBAAgB3D,CAAO,EACvE6D,EAAgByB,CAAO,QAChBD,EAAO,CACN,QAAA,MAAM,8BAA+BA,CAAK,CAAA,CAEtD,EAEMJ,EAAgB,SAAY,CAC5B,GAAA,CAACb,EAAY,OAAQ,CACvB,MAAMc,EAAiB,EACvB,MAAA,CAGE,GAAA,CACI,MAAAK,EAAgB,MAAM5B,EAA0B,mBAAmB,CACvE,WAAYS,EACZ,QAAApE,EACA,MAAO,EAAA,CACR,EACD6D,EAAgB0B,CAAa,QACtBF,EAAO,CACN,QAAA,MAAM,gCAAiCA,CAAK,CAAA,CAExD,EAEMG,EAAe,SAAY,CAC3B,GAAA,CACF,MAAM/D,EAAY,CAChB,UAAW,IAAI,KAAK,KAAM,EAAG,CAAC,EAC9B,YAAa,IACf,EACMgE,EAAW,MAAM9B,EAA0B,uBAAuBlC,CAAS,EACjFkD,EAAkBc,CAAQ,QACnBJ,EAAO,CACN,QAAA,MAAM,0BAA2BA,CAAK,CAAA,CAElD,EAEMK,EAAyB,MAAOlG,GAA6B,CACjE0E,EAAuB1E,CAAW,EAGlC,MAAMmE,EAA0B,qBAAqBnE,EAAY,GAAIsB,CAAM,EAG3E,MAAM6E,EAAsB1F,EAAa,OACvCC,EAAE,KAAOV,EAAY,GACjB,CAAE,GAAGU,EAAG,kBAAmB,CAAE,GAAGA,EAAE,kBAAmB,MAAOA,EAAE,kBAAkB,MAAQ,CAAA,GACxFA,CACN,EACA2D,EAAgB8B,CAAmB,CACrC,EAEMC,EAAc,MAAOpG,EAA0BqG,IAAqB,CACxE,MAAMlC,EAA0B,mBAAmBnE,EAAY,GAAIqG,CAAQ,EAG3E,QAAQ,IAAI,WAAWrG,EAAY,KAAK,OAAOqG,CAAQ,EAAE,CAC3D,EAEMC,EAAqB,CAACC,EAAoBC,IAAe,CAC7D7B,EAAoB8B,IAAA,CAClB,GAAGA,EACH,CAACF,CAAU,EAAGC,CAAA,EACd,CACJ,EAEME,EAAe,IAAM,CACzB/B,EAAW,CAAA,CAAE,EACbE,EAAe,EAAE,CACnB,EAEM8B,EAAyB3G,GAC7B4G,EAAA,KAAC,MAAA,CAEC,UAAU,iGACV,QAAS,IAAMV,EAAuBlG,CAAW,EAEhD,SAAA,CAAAA,EAAY,aAAa,CAAC,GACxB4G,EAAAA,KAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAAAC,EAAA,IAAC,MAAA,CACC,IAAK7G,EAAY,aAAa,CAAC,EAAE,IACjC,IAAKA,EAAY,aAAa,CAAC,EAAE,QACjC,UAAU,4BAAA,CACZ,EACA6G,EAAAA,IAAC,OAAI,UAAU,yBACb,eAAC,OAAK,CAAA,UAAW,6CACf7G,EAAY,aAAa,SAAW,WAAa,eACjDA,EAAY,aAAa,SAAW,UAAY,gBAChD,aACF,GACG,SAAAA,EAAY,aAAa,MAC5B,CAAA,CACF,CAAA,CAAA,EACF,EAGF4G,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,2CAA4C,SAAA7G,EAAY,MAAM,EAC3E6G,EAAA,IAAA,IAAA,CAAE,UAAU,0CAA2C,WAAY,YAAY,EAEhFD,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,sDACb,SAAA7G,EAAY,SAAS,QACxB,QACC,OAAK,CAAA,UAAU,0DACb,SAAAA,EAAY,gBAAgB,eAC/B,EACCA,EAAY,SAAS,qBACnB,OAAK,CAAA,UAAU,wDAAwD,SAExE,gBAAA,CAAA,CAAA,EAEJ,EAEA4G,EAAAA,KAAC,MAAI,CAAA,UAAU,0DACb,SAAA,CAAAA,OAAC,OAAM,CAAA,SAAA,CAAA5G,EAAY,SAAS,KAAK,KAAGA,EAAY,SAAS,QAAA,EAAS,QACjE,OAAM,CAAA,SAAAA,EAAY,UAAU,UAAU,aAAc,CAAA,CAAA,EACvD,EAEA4G,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,oBACd,SAAA,CAAAA,OAAC,OAAI,UAAU,eAAe,KAAK,eAAe,QAAQ,YACxD,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAK,EAAE,iCAAiC,CAAA,QACxC,OAAK,CAAA,SAAS,UAAU,EAAE,0IAA0I,SAAS,SAAS,CAAA,CAAA,EACzL,EACC7G,EAAY,kBAAkB,KAAA,EACjC,EACA4G,EAAAA,KAAC,OAAK,CAAA,UAAU,oBACd,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,eAAe,KAAK,eAAe,QAAQ,YACxD,SAACA,EAAA,IAAA,OAAA,CAAK,EAAE,wJAAwJ,CAAA,EAClK,EACC7G,EAAY,kBAAkB,MAAA,CACjC,CAAA,CAAA,EACF,EAEA4G,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAAUC,GAAM,CACdA,EAAE,gBAAgB,EAClBV,EAAYpG,EAAa,UAAU,CACrC,EACA,UAAU,wCAEV,eAAC,MAAI,CAAA,UAAU,UAAU,KAAK,eAAe,QAAQ,YACnD,SAAC6G,MAAA,OAAA,CAAK,SAAS,UAAU,EAAE,yQAAyQ,SAAS,UAAS,CACxT,CAAA,CAAA,CACF,EACAA,EAAA,IAAC,SAAA,CACC,QAAUC,GAAM,CACdA,EAAE,gBAAgB,EAClBV,EAAYpG,EAAa,SAAS,CACpC,EACA,UAAU,wCAEV,SAAC6G,EAAA,IAAA,MAAA,CAAI,UAAU,UAAU,KAAK,eAAe,QAAQ,YACnD,SAACA,MAAA,OAAA,CAAK,EAAE,waAAA,CAAwa,CAClb,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,EAxFK7G,EAAY,EAyFnB,EAGI+G,EAAgB,IACnBF,EAAAA,IAAA,MAAA,CAAI,UAAU,sCACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,kBACb,SAAAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,yBACZ,MAAOjC,EACP,SAAWkC,GAAMjC,EAAeiC,EAAE,OAAO,KAAK,EAC9C,UAAU,8GAAA,CAAA,EAEd,EAEAF,EAAA,KAAC,SAAA,CACC,MAAOpG,EAAQ,UAAY,GAC3B,SAAWsG,GAAMR,EAAmB,WAAYQ,EAAE,OAAO,OAAS,MAAS,EAC3E,UAAU,+EAEV,SAAA,CAACD,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAc,iBAAA,EAC9BxB,EAAW,IACV2B,GAAAH,EAAAA,IAAC,SAAuB,CAAA,MAAOG,EAAI,MAAQ,SAAIA,EAAA,KAAA,EAAlCA,EAAI,KAAoC,CACtD,CAAA,CAAA,CACH,EAEAJ,EAAA,KAAC,SAAA,CACC,MAAOpG,EAAQ,iBAAmB,GAClC,SAAWsG,GAAMR,EAAmB,kBAAmBQ,EAAE,OAAO,OAAS,MAAS,EAClF,UAAU,+EAEV,SAAA,CAACD,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAY,eAAA,EAC5BvB,EAAS,IACRrB,GAAA4C,EAAA,IAAC,UAAqB,MAAO5C,EAAU,SAA1BA,CAAA,EAAAA,CAAkC,CAChD,CAAA,CAAA,CACH,EAEA2C,EAAAA,KAAC,QAAM,CAAA,UAAU,oBACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASrG,EAAQ,eAAiB,GAClC,SAAWsG,GAAMR,EAAmB,gBAAiBQ,EAAE,OAAO,SAAW,MAAS,EAClF,UAAU,MAAA,CACZ,EACCD,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAmB,qBAAA,CAAA,CAAA,EAC/C,GAEE,OAAO,KAAKrG,CAAO,EAAE,OAAS,GAAKoE,IACnCiC,EAAA,IAAC,SAAA,CACC,QAASH,EACT,UAAU,gEACX,SAAA,eAAA,CAAA,CAED,CAAA,CAEJ,CACF,CAAA,EAGIO,EAAiB,IACpBL,OAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAoB,uBAAA,EAC/DA,EAAA,IAAC,MAAI,CAAA,UAAU,YACZ,SAAA3B,EAAe,IAAKgC,GACnBN,EAAA,KAAC,MAAmB,CAAA,UAAU,6BAC5B,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,oFACb,SAACA,EAAAA,IAAA,OAAA,CAAK,UAAU,8BAA+B,SAAMK,EAAA,KAAK,YAAY,CAAE,CAAA,EAC1E,EACAN,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAK,EAAM,MAAM,EACtDL,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA8B,WAAM,YAAY,EAC7DD,EAAAA,KAAC,MAAI,CAAA,UAAU,mCACb,SAAA,CAAAC,EAAA,IAAC,OAAK,CAAA,UAAU,0DACb,SAAAK,EAAM,gBACT,EACCL,EAAA,IAAA,OAAA,CAAK,UAAU,sDACb,WAAM,SACT,EACCK,EAAM,eACLL,EAAAA,IAAC,OAAK,CAAA,UAAU,wDAAwD,SAExE,gBAAA,CAAA,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CAAA,GApBQK,EAAM,EAqBhB,CACD,CACH,CAAA,CAAA,EACF,EAGIC,EAAiB,IACpBN,MAAA,MAAA,CAAI,UAAU,iDACZ,SAAA,CACC,CAAE,IAAK,UAAW,MAAO,UAAW,KAAM,KAAM,EAChD,CAAE,IAAK,WAAY,MAAO,WAAY,KAAM,GAAI,EAChD,CAAE,IAAK,cAAe,MAAO,UAAW,KAAM,IAAK,EACnD,CAAE,IAAK,WAAY,MAAO,WAAY,KAAM,IAAK,CAAA,EACjD,IACAO,GAAAR,EAAA,KAAC,SAAA,CAEC,QAAS,IAAM,CACb3B,EAAcmC,EAAI,GAAU,EACxBA,EAAI,MAAQ,YAAyBpB,EAAA,CAC3C,EACA,UAAW,gHACThB,IAAeoC,EAAI,IACf,mCACA,mCACN,GAEA,SAAA,CAACP,EAAAA,IAAA,OAAA,CAAM,WAAI,IAAK,CAAA,EAChBA,EAAAA,IAAC,OAAM,CAAA,SAAAO,EAAI,KAAM,CAAA,CAAA,CAAA,EAZZA,EAAI,GAcZ,CAAA,EACH,EAGF,OAAItC,EAEA+B,MAAC,OAAI,UAAU,wCACb,eAAC,MAAI,CAAA,UAAU,iEAAiE,CAClF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAiC,oCAAA,EACtFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,0GAAA,CAAA,CAAA,EACF,EAECM,EAAe,EAEfnC,IAAe,YAAc+B,EAAc,EAE3C/B,IAAe,WACb6B,MAAA,MAAA,CAAI,UAAU,uDACZ,SAAApG,EAAa,IAAIkG,CAAqB,CACzC,CAAA,EAGD3B,IAAe,YACd4B,EAAAA,KAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAqB,wBAAA,QAChE,MAAI,CAAA,UAAU,uDACZ,SAAqBtC,EAAA,IAAIoC,CAAqB,CACjD,CAAA,CAAA,EACF,EAGD3B,IAAe,eACd4B,EAAAA,KAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAmB,sBAAA,EAC9DA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,qHAAA,QACC,MAAI,CAAA,UAAU,uDACZ,SAAYrF,EAAA,IAAImF,CAAqB,CACxC,CAAA,CAAA,EACF,EAGD3B,IAAe,YAAciC,EAAe,EAE5CxG,EAAa,SAAW,GAAKuE,IAAe,WAC1C4B,OAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAqB,wBAAA,EAC3EA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAA6C,+CAAA,CAAA,CAAA,EAC5E,EAIDpC,GACCoC,EAAA,IAAC,MAAI,CAAA,UAAU,iFACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,oEACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,mCAAoC,SAAApC,EAAoB,MAAM,EAC5EoC,EAAA,IAAC,SAAA,CACC,QAAS,IAAMnC,EAAuB,IAAI,EAC1C,UAAU,oCAEV,SAAAmC,EAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAK,CAAA,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,sBAAuB,CAAA,CAC9F,CAAA,CAAA,CAAA,CACF,EACF,EAECpC,EAAoB,aAAa,CAAC,GACjCoC,EAAA,IAAC,MAAA,CACC,IAAKpC,EAAoB,aAAa,CAAC,EAAE,IACzC,IAAKA,EAAoB,aAAa,CAAC,EAAE,QACzC,UAAU,0CAAA,CACZ,EAGDoC,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAsB,WAAoB,YAAY,EAEnED,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAgB,mBAAA,QAClD,IAAE,CAAA,UAAU,6BAA8B,SAAApC,EAAoB,gBAAgB,qBAAqB,EAEnGoC,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAe,kBAAA,EAClDD,EAAAA,KAAC,IAAE,CAAA,UAAU,wBACV,SAAA,CAAAnC,EAAoB,SAAS,KAAK,KAAGA,EAAoB,SAAS,eAAU,KAAE,EAAA,EAC9EA,EAAoB,UAAU,UAAU,YAAY,EAAE,MAAIA,EAAoB,UAAU,QAAQ,YAAY,CAAA,CAC/G,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACoC,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAqB,wBAAA,EACxDD,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uBACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAgB,kBAAA,CAAA,EACtBD,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAAnC,EAAoB,qBAAqB,kBAAkB,KAAA,CAAG,CAAA,CAAA,EAC/F,EACAmC,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAkB,oBAAA,CAAA,EACxBD,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAAnC,EAAoB,qBAAqB,iBAAiB,KAAA,CAAG,CAAA,CAAA,EAC9F,EACAmC,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAoB,sBAAA,CAAA,EAC1BD,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAAnC,EAAoB,aAAa,mBAAmB,KAAA,CAAG,CAAA,CAAA,CACxF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EAEJ,CAEJ,EC5ZA,MAAM4C,CAAyB,CACrB,eAA2C,IAC3C,qBAA+C,IAC/C,qBAAwD,IACxD,0BAA4D,IAC5D,uBAA6D,IAErE,aAAc,CACZ,KAAK,2BAA2B,CAAA,CAIlC,MAAM,sBAAsB/F,EAA6C,CACvE,MAAMgG,EAAoC,CAAC,EACrCC,EAAe,MAAM,KAAK,gBAAgBjG,CAAM,EAEtD,SAAW,CAACkG,EAASC,CAAS,IAAK,KAAK,iBAAkB,CACxD,MAAMC,EAAW,KAAK,uBAAuBH,EAAcE,CAAS,EAC9DE,EAAWD,GAAY,IAE7BJ,EAAc,KAAK,CACjB,UAAAG,EACA,SAAAE,EACA,SAAU,KAAK,IAAID,EAAU,GAAG,EAChC,aAAcD,EAAU,aACxB,cAAe,KAAK,iBAAiBC,EAAUD,CAAS,EACxD,oBAAqB,KAAK,mBAAmBC,EAAUD,EAAWF,CAAY,CAAA,CAC/E,CAAA,CAGI,OAAAD,EAAc,KAAK,CAAC,EAAG3G,IAAMA,EAAE,SAAW,EAAE,QAAQ,CAAA,CAG7D,MAAM,WAAWW,EAAgBmG,EAA2C,CAC1E,MAAMF,EAAe,MAAM,KAAK,gBAAgBjG,CAAM,EAChDsG,EAAQ,KAAK,oBAAoBL,EAAcE,CAAS,EAExDI,EAAmB,CACvB,GAAI,KAAK,WAAW,EACpB,OAAAvG,EACA,UAAAmG,EACA,MAAAG,EACA,eAAgB,KAChB,mBAAoB,CAClB,OAAQ,WACR,WAAY,SACZ,qBAAsB,KACtB,sBAAuB,CACzB,EACA,gBAAiB,KAAK,uBAAuBL,EAAcE,CAAS,EACpE,cAAe,KAAK,uBAAuBF,EAAcE,CAAS,EAClE,mBAAoB,CAClB,cAAe,GACf,iBAAkB,GAClB,mBAAoB,GACpB,8BAA+B,EAAA,CAEnC,EAGMK,EAAgB,KAAK,WAAW,IAAIxG,CAAM,GAAK,CAAC,EACtDwG,EAAc,KAAKD,CAAK,EACnB,KAAA,WAAW,IAAIvG,EAAQwG,CAAa,EAGnC,MAAAC,EAAY,KAAK,kBAAkBH,CAAK,EAEvC,MAAA,CACL,MAAAC,EACA,mBAAoB,KAAK,2BAA2BA,CAAK,EACzD,gBAAiB,KAAK,0BAA0BA,EAAM,eAAe,EACrE,cAAeE,EACf,uBAAwB,KAAK,+BAA+BF,CAAK,CACnE,CAAA,CAGF,MAAM,iBAAiBvG,EAAgBmG,EAAoC,CACzE,MAAMF,EAAe,MAAM,KAAK,gBAAgBjG,CAAM,EAChDoG,EAAW,KAAK,uBAAuBH,EAAcE,CAAS,EAE7D,MAAA,CACL,UAAAA,EACA,gBAAiBC,EACjB,aAAcD,EAAU,aAAa,IAAYO,IAAA,CAC/C,YAAaA,EAAI,YACjB,OAAQA,EAAI,oBAAsBA,EAAI,kBACtC,QAAS,KAAK,8BAA8BT,EAAcS,CAAG,EAC7D,UAAW,KAAK,uBAAuBT,EAAcS,CAAG,CAAA,EACxD,EACF,oBAAqB,KAAK,mBAAmBN,EAAUD,EAAWF,CAAY,EAC9E,YAAa,KAAK,eAAeA,EAAcE,CAAS,CAC1D,CAAA,CAGF,MAAM,cAAcnG,EAAsC,CACxD,OAAO,KAAK,WAAW,IAAIA,CAAM,GAAK,CAAC,CAAA,CAIzC,MAAM,+BAA+B2G,EAA8C,CACjF,MAAMC,EAAmB,KAAK,iBAAiB,IAAID,EAAY,MAAM,GAAK,CAAC,EAC3EC,EAAiB,KAAKD,CAAW,EACjC,KAAK,iBAAiB,IAAIA,EAAY,OAAQC,CAAgB,EAGxD,MAAA,KAAK,6BAA6BD,EAAY,MAAM,CAAA,CAG5D,MAAM,8BAA8BE,EAA0C,CAC5E,MAAMC,EAAiB,KAAK,sBAAsB,IAAID,EAAS,MAAM,GAAK,CAAC,EAC3EC,EAAe,KAAKD,CAAQ,EAC5B,KAAK,sBAAsB,IAAIA,EAAS,OAAQC,CAAc,EAGxD,MAAA,KAAK,6BAA6BD,EAAS,MAAM,CAAA,CAGzD,MAAM,4BAA4BE,EAA0D,CAC1F,MAAMC,EAAqB,KAAK,mBAAmB,IAAID,EAAO,MAAM,GAAK,CAAC,EAC1EC,EAAmB,KAAKD,CAAM,EAC9B,KAAK,mBAAmB,IAAIA,EAAO,OAAQC,CAAkB,EAGvD,MAAAC,EAAmBD,EAAmB,OAAO,CAAC3F,EAAKuE,IAAUvE,EAAMuE,EAAM,sBAAuB,CAAC,EACjF,IAAI,IAAIoB,EAAmB,QAAiBpB,GAAAA,EAAM,gBAAgB,CAAC,EAAE,KAC/DoB,EAAmB,OAAgBpB,GAAAA,EAAM,kBAAkB,EAAE,OAEzF,MAAMsB,EAAoC,CACxC,yBAA0BD,EAC1B,sBAAuB,EACvB,wBAAyBD,EAAmB,OAC5C,wBAAyBA,EAAmB,OAC5C,wBAAyBA,EAAmB,UAAYxB,EAAE,YAAc,yBAAyB,EAAE,MACrG,EAGM,aAAA,KAAK,6BAA6BuB,EAAO,MAAM,EAE9CG,CAAA,CAIT,MAAM,yBAAyBlH,EAAgBM,EAA6D,CAC1G,MAAM6G,EAAe,KAAK,iBAAiB,IAAInH,CAAM,GAAK,CAAC,EACrDoH,EAAoB,KAAK,sBAAsB,IAAIpH,CAAM,GAAK,CAAC,EAC/DqH,EAAiB,KAAK,mBAAmB,IAAIrH,CAAM,GAAK,CAAC,EAEzDsH,EAAwBH,EAAa,UACzCI,EAAE,WAAajH,EAAU,WAAaiH,EAAE,WAAajH,EAAU,OACjE,EACMkH,EAAmBJ,EAAkB,UACzChI,EAAE,WAAakB,EAAU,WAAalB,EAAE,WAAakB,EAAU,OACjE,EACMmH,EAA0BJ,EAAe,UAC7C,EAAE,WAAa/G,EAAU,WAAa,EAAE,WAAaA,EAAU,OACjE,EAEMoH,EAAqB,KAAK,4BAA4BJ,CAAqB,EAC3EK,EAAe,KAAK,sBAAsBH,CAAgB,EAC1DI,EAAsB,KAAK,6BAA6BH,CAAuB,EAE9E,MAAA,CACL,WAAYC,EAAqBC,EAAeC,EAChD,wBAAyBF,EACzB,iBAAkBC,EAClB,eAAgBC,EAChB,kBAAmB,KAAK,gCAAgCN,CAAqB,EAC7E,YAAa,KAAK,0BAA0BA,EAAuBE,CAAgB,EACnF,OAAQ,KAAK,qBAAqBxH,EAAQM,CAAS,CACrD,CAAA,CAGF,MAAM,0BAA0BN,EAA8B,CAC5D,MAAM6H,EAAS,MAAM,KAAK,cAAc7H,CAAM,EACxCmH,EAAe,KAAK,iBAAiB,IAAInH,CAAM,GAAK,CAAC,EACrDoH,EAAoB,KAAK,sBAAsB,IAAIpH,CAAM,GAAK,CAAC,EAC/DqH,EAAiB,KAAK,mBAAmB,IAAIrH,CAAM,GAAK,CAAC,EAEzD8H,MAAoB,IAAI,CAC5B,GAAGX,EAAa,QAAQI,GAAKA,EAAE,mBAAmB,EAClD,GAAGH,EAAkB,IAAIhI,GAAKA,EAAE,eAAe,EAC/C,GAAGiI,EAAe,QAAQhI,GAAKA,EAAE,gBAAgB,CAClD,CAAA,EAAE,KAEG0I,EAAoBX,EAAkB,OAAO,CAAC/F,EAAKjC,IAAMiC,EAAMjC,EAAE,iBAAkB,CAAC,EACpF6H,EAAmBI,EAAe,OAAO,CAAChG,EAAKhC,IAAMgC,EAAMhC,EAAE,sBAAuB,CAAC,EAEpF,MAAA,CACL,OAAAW,EACA,eAAgB,KAChB,QAAS,CACP,YAAa6H,EAAO,OACpB,cAAe,KAAK,mBAAmBA,CAAM,EAC7C,mBAAoB,CAClB,gBAAiBC,EACjB,0BAA2BX,EAAa,OACxC,oBAAqB,KAAK,wBAAwBA,CAAY,CAChE,EACA,gBAAiB,CACf,aAAcY,EACd,kBAAmB,IAAI,IAAIX,EAAkB,IAAShI,GAAAA,EAAE,kBAAkB,CAAC,EAAE,KAC7E,kBAAmB6H,CACrB,EACA,aAAcY,EAAO,IAAUxI,IAAA,CAC7B,UAAWA,EAAE,UAAU,aACvB,MAAOA,EAAE,MAAM,MACf,WAAYA,EAAE,WACd,gBAAiBA,EAAE,gBAAgB,gBAAA,EACnC,CACJ,EACA,gBAAiB,KAAK,oCAAoCW,EAAQ6H,EAAQV,EAAcC,CAAiB,CAC3G,CAAA,CAGF,MAAM,eAAe5H,EAAkBc,EAA+D,CAC9F,MAAA0H,MAAe,IAAI,CACvB,GAAG,KAAK,iBAAiB,KAAK,EAC9B,GAAG,KAAK,sBAAsB,KAAK,EACnC,GAAG,KAAK,mBAAmB,KAAK,CAAA,CACjC,EAEKC,EAAqB,CAAC,EAE5B,UAAWjI,KAAUgI,EAAU,CAC7B,MAAM9F,EAAQ,MAAM,KAAK,yBAAyBlC,EAAQM,CAAS,EAC7DuH,EAAS,MAAM,KAAK,cAAc7H,CAAM,EAE9C,IAAIkI,EAAgB,EACpB,OAAQ1I,EAAU,CAChB,IAAK,4BACH0I,EAAgBhG,EAAM,wBACtB,MACF,IAAK,oBACHgG,EAAgBhG,EAAM,iBACtB,MACF,IAAK,kBACHgG,EAAgBhG,EAAM,eACtB,MACF,QACEgG,EAAgBhG,EAAM,UAAA,CAG1B+F,EAAmB,KAAK,CACtB,OAAAjI,EACA,MAAOkI,EACP,OAAQL,EAAO,OACf,kBAAmB3F,EAAM,kBACzB,KAAM,CAAA,CACP,CAAA,CAIH,OAAA+F,EAAmB,KAAK,CAAC7I,EAAGC,IAAMA,EAAE,MAAQD,EAAE,KAAK,EAChC6I,EAAA,QAAQ,CAACE,EAAOpG,IAAU,CAC3CoG,EAAM,KAAOpG,EAAQ,CAAA,CACtB,EAEMkG,EAAmB,MAAM,EAAG,EAAE,CAAA,CAI/B,4BAAmC,CAEpC,KAAA,iBAAiB,IAAI,0BAA2B,CACnD,SAAU,4BACV,aAAc,0BACd,YAAa,sDACb,aAAc,CACZ,CACE,YAAa,oEACb,mBAAoB,EACpB,mBAAoB,WACtB,EACA,CACE,YAAa,sDACb,mBAAoB,EACpB,mBAAoB,sBAAA,CAExB,EACA,cAAe,CACb,gBAAiB,EACjB,sBAAuB,GACvB,yBAA0B,EAC5B,EACA,gBAAiB,cAAA,CAClB,EAEI,KAAA,iBAAiB,IAAI,oBAAqB,CAC7C,SAAU,kBACV,aAAc,oBACd,YAAa,4DACb,aAAc,CACZ,CACE,YAAa,oDACb,mBAAoB,GACpB,mBAAoB,sBACtB,EACA,CACE,YAAa,kDACb,mBAAoB,EACpB,mBAAoB,eAAA,CAExB,EACA,cAAe,CACb,gBAAiB,EACjB,sBAAuB,GACvB,yBAA0B,EAC5B,EACA,gBAAiB,UAAA,CAClB,EAEI,KAAA,iBAAiB,IAAI,6BAA8B,CACtD,SAAU,oBACV,aAAc,6BACd,YAAa,2CACb,aAAc,CACZ,CACE,YAAa,0CACb,mBAAoB,GACpB,mBAAoB,WACtB,EACA,CACE,YAAa,+CACb,mBAAoB,EACpB,mBAAoB,sBAAA,CAExB,EACA,cAAe,CACb,gBAAiB,EACjB,sBAAuB,GACvB,yBAA0B,EAC5B,EACA,gBAAiB,cAAA,CAClB,CAAA,CAGH,MAAc,gBAAgBjI,EAA8B,CAC1D,MAAMmH,EAAe,KAAK,iBAAiB,IAAInH,CAAM,GAAK,CAAC,EACrDoH,EAAoB,KAAK,sBAAsB,IAAIpH,CAAM,GAAK,CAAC,EAC/DqH,EAAiB,KAAK,mBAAmB,IAAIrH,CAAM,GAAK,CAAC,EAExD,MAAA,CACL,0BAA2BmH,EAAa,OACxC,gBAAiB,IAAI,IAAIA,EAAa,QAAaI,GAAAA,EAAE,mBAAmB,CAAC,EAAE,KAC3E,aAAcH,EAAkB,OAAO,CAAC/F,EAAKjC,IAAMiC,EAAMjC,EAAE,iBAAkB,CAAC,EAC9E,kBAAmB,IAAI,IAAIgI,EAAkB,IAAShI,GAAAA,EAAE,kBAAkB,CAAC,EAAE,KAC7E,qBAAsBiI,EAAe,OACrC,kBAAmBA,EAAe,OAAO,CAAChG,EAAKhC,IAAMgC,EAAMhC,EAAE,sBAAuB,CAAC,EACrF,oBAAqB,KAAK,wBAAwB8H,CAAY,EAC9D,oBAAqBE,EAAe,OAAYhI,GAAAA,EAAE,kBAAkB,EAAE,MACxE,CAAA,CAGM,uBAAuB4G,EAAmBE,EAA8B,CAC9E,IAAIiC,EAAgB,EAChBC,EAAoBlC,EAAU,aAAa,OAEpC,UAAAmC,KAAenC,EAAU,aAAc,CAChD,MAAMC,EAAW,KAAK,8BAA8BH,EAAcqC,CAAW,EACvEC,EAASD,EAAY,oBAAsB,EAC3CE,EAAsB,KAAK,IAAKpC,EAAWmC,EAAU,IAAK,GAAG,EAClDH,GAAAI,CAAA,CAGnB,OAAOJ,EAAgBC,CAAA,CAGjB,8BAA8BpC,EAAmBqC,EAAuC,CACxF,MAAA5B,EAAM4B,EAAY,YAAY,YAAY,EAEhD,OAAI5B,EAAI,SAAS,cAAc,GAAKA,EAAI,SAAS,aAAa,EACrDT,EAAa,0BAElBS,EAAI,SAAS,SAAS,GAAKA,EAAI,SAAS,WAAW,EAC9CT,EAAa,gBAElBS,EAAI,SAAS,SAAS,GAAKA,EAAI,SAAS,MAAM,EACzCT,EAAa,aAElBS,EAAI,SAAS,WAAW,GAAKA,EAAI,SAAS,OAAO,EAC5CT,EAAa,kBAElBS,EAAI,SAAS,YAAY,GAAKA,EAAI,SAAS,QAAQ,EAC9CT,EAAa,kBAElBS,EAAI,SAAS,QAAQ,GAAKA,EAAI,SAAS,WAAW,EAC7CT,EAAa,oBAGf,CAAA,CAGD,uBAAuBA,EAAmBqC,EAAwC,CACxF,MAAMG,EAAU,KAAK,8BAA8BxC,EAAcqC,CAAW,EACtEC,EAASD,EAAY,oBAAsB,EACjD,OAAOG,GAAWF,CAAA,CAGZ,oBAAoBtC,EAAmBE,EAAkC,CAC/E,MAAMuC,EAAazC,EAAa,0BACfA,EAAa,aACbA,EAAa,qBAAuB,EAEjD,OAAAyC,GAAc,IAAY,CAAE,MAAO,WAAY,eAAgB,IAAK,kBAAmB,CAAC,4BAA4B,CAAE,EACtHA,GAAc,IAAY,CAAE,MAAO,OAAQ,eAAgB,IAAK,kBAAmB,CAAC,sBAAsB,CAAE,EAC5GA,GAAc,GAAW,CAAE,MAAO,SAAU,eAAgB,GAAI,kBAAmB,CAAC,0BAA0B,CAAE,EAC7G,CAAE,MAAO,SAAU,eAAgB,GAAI,kBAAmB,CAAC,mBAAmB,CAAE,CAAA,CAGjF,uBAAuBzC,EAAmBE,EAA4C,CACrF,MAAA,CACL,iBAAkB,CAAC,OAAQ,UAAW,WAAW,EACjD,gBAAiB,CACf,KAAM,gBACN,SAAU,IACV,aAAc,IACd,aAAc,CAAA,CAChB,EACA,YAAa,YACb,sBAAuB,CACrB,kBAAmB,GACnB,wBAAyB,EACzB,4BAA6B,EAC7B,gBAAiB,CACnB,EACA,oBAAqB,GACrB,yBAA0B,CAAA,CAC5B,CAAA,CAGM,uBAAuBF,EAAmBE,EAA0C,CACnF,MAAA,CACL,yBAA0BF,EAAa,kBACvC,sBAAuBA,EAAa,aACpC,wBAAyBA,EAAa,0BACtC,wBAAyBA,EAAa,qBACtC,wBAAyB,KAAK,MAAMA,EAAa,qBAAuB,CAAC,CAC3E,CAAA,CAGM,2BAA2BM,EAA0B,CAOpD,MANU,CACf,0BAA2B,6HAC3B,oBAAqB,yGACrB,6BAA8B,gHAChC,EAEgBA,EAAM,UAAU,YAAqC,GAC9D,kCAAkCA,EAAM,UAAU,YAAY,SAAA,CAG/D,0BAA0BoC,EAAuC,CAEvE,MAAO,4DADUA,EAAQ,iBAAiB,KAAK,IAAI,CACwB,mEAAA,CAGrE,+BAA+BpC,EAA4B,CAC1D,MAAA,CACL,sDACA,8DACA,wEACA,sDACF,CAAA,CAGM,kBAAkBqC,EAA6C,CACrE,MAAMC,EAAS,CAAC,SAAU,SAAU,OAAQ,WAAY,qBAAqB,EACvEC,EAAeD,EAAO,QAAQD,EAAa,KAAK,EAElD,GAAAE,EAAeD,EAAO,OAAS,EAAG,CAC9B,MAAApC,EAAYoC,EAAOC,EAAe,CAAC,EACnCC,EAAiB,CAAC,GAAI,GAAI,IAAK,IAAK,GAAG,EAAED,EAAe,CAAC,EACzDE,EAAa,CACjB,CAAC,mBAAmB,EACpB,CAAC,0BAA0B,EAC3B,CAAC,sBAAsB,EACvB,CAAC,4BAA4B,EAC7B,CAAC,wBAAwB,CAAA,EACzBF,EAAe,CAAC,EAEX,MAAA,CACL,MAAOrC,EACP,eAAAsC,EACA,kBAAmBC,CACrB,CAAA,CAGK,OAAA,IAAA,CAGD,iBAAiB5C,EAAkBD,EAA8B,CACnE,OAAAC,GAAY,IAAY,gCACxBA,GAAY,GAAW,+CACvBA,GAAY,GAAW,iCACvBA,GAAY,GAAW,gDACpB,wDAAA,CAGD,mBAAmBA,EAAkBD,EAAsBF,EAA2B,CAC5F,MAAMgD,EAAY,IAAM7C,EAClB8C,EAAa/C,EAAU,gBAEzB,GAAA8C,GAAa,EAAU,MAAA,kBAE3B,MAAME,EAAW,CACf,SAAY,EACZ,aAAgB,EAChB,SAAY,EACZ,OAAU,EAAA,EACVD,CAAU,GAAK,EAEXE,EAAiB,KAAK,KAAMH,EAAY,IAAOE,CAAQ,EAEzD,OAAAC,GAAkB,EAAU,gBAC5BA,GAAkB,EAAU,GAAGA,CAAc,SAC1C,GAAG,KAAK,KAAKA,EAAiB,CAAC,CAAC,SAAA,CAGjC,eAAenD,EAAmBE,EAAgC,CACxE,MAAMkD,EAAU,CAAC,EAEN,UAAAf,KAAenC,EAAU,aAClC,GAAI,CAAC,KAAK,uBAAuBF,EAAcqC,CAAW,EAAG,CAC3D,MAAMG,EAAU,KAAK,8BAA8BxC,EAAcqC,CAAW,EAEtEW,GADSX,EAAY,oBAAsB,GACtBG,EAE3BY,EAAQ,KAAK,GAAGf,EAAY,WAAW,KAAKW,CAAS,eAAe,CAAA,CAIjE,OAAAI,CAAA,CAGT,MAAc,6BAA6BrJ,EAA+B,CACxE,MAAMgG,EAAgB,MAAM,KAAK,sBAAsBhG,CAAM,EACvDsJ,EAAgB,MAAM,KAAK,cAActJ,CAAM,EAC/CuJ,EAAmB,IAAI,IAAID,EAAc,IAASjK,GAAAA,EAAE,UAAU,YAAY,CAAC,EAEjF,UAAWmK,KAAexD,EACpBwD,EAAY,UAAY,CAACD,EAAiB,IAAIC,EAAY,UAAU,YAAY,GAClF,MAAM,KAAK,WAAWxJ,EAAQwJ,EAAY,SAAS,CAEvD,CAGM,4BAA4BrC,EAA0C,CAC5E,OAAOA,EAAa,OAAO,CAAC9F,EAAKsF,IAAgB,CACzC,MAAA8C,EAAe9C,EAAY,oBAAoB,OAAS,EACxD+C,EAAe/C,EAAY,aAAe,EAC1CgD,EAAgB,KAAK,IAAIhD,EAAY,SAAW,GAAI,EAAE,EACrD,OAAAtF,EAAMoI,EAAeC,EAAeC,GAC1C,CAAC,CAAA,CAGE,sBAAsBC,EAAuC,CACnE,OAAOA,EAAW,OAAO,CAACvI,EAAKwF,IAAa,CACpC,MAAAgD,EAAahD,EAAS,iBAAmB,EACzC4C,EAAe5C,EAAS,kBAAoB,OAAS,GAAK,EAChE,OAAOxF,EAAMwI,EAAaJ,GACzB,CAAC,CAAA,CAGE,6BAA6BK,EAAuC,CAC1E,OAAOA,EAAO,OAAO,CAACzI,EAAKuE,IAAU,CAC7B,MAAAmE,EAAkBnE,EAAM,sBAAwB,EAChD6D,EAAe7D,EAAM,iBAAiB,OAAS,GAC/CoE,EAAsBpE,EAAM,mBAAqB,GAAK,EACrD,OAAAvE,EAAM0I,EAAkBN,EAAeO,GAC7C,CAAC,CAAA,CAGE,gCAAgC7C,EAA0C,CAC1E,MAAA8C,EAAiB,IAAI,IAAI9C,EAAa,QAAa,GAAA,EAAE,mBAAmB,CAAC,EAC/E,OAAO,KAAK,IAAI8C,EAAe,KAAO,GAAI,GAAG,CAAA,CAGvC,0BAA0B9C,EAAkCyC,EAAuC,CACzG,MAAMM,EAAY,CAAC,GAAG/C,EAAc,GAAGyC,CAAU,EAC7C,GAAAM,EAAU,OAAS,EAAU,MAAA,GAKjC,MAAMC,GAFW,KAAK,IAAI,GAAGD,EAAU,IAAI1E,GAAKA,EAAE,UAAU,QAAQ,CAAC,CAAC,EACvD,KAAK,IAAI,GAAG0E,EAAU,OAAS1E,EAAE,UAAU,QAAQ,CAAC,CAAC,IAC1C,EAAI,GAAK,GAAK,GAAK,KACvC4E,EAAgBF,EAAU,OAASC,EAEzC,OAAO,KAAK,IAAIC,EAAgB,GAAI,GAAG,CAAA,CAGjC,qBAAqBpK,EAAgBM,EAAuD,CAElG,MAAM6G,EAAe,KAAK,iBAAiB,IAAInH,CAAM,GAAK,CAAC,EACrDqK,EAAqBlD,EAAa,UAAYI,EAAE,WAAajH,EAAU,SAAS,EAChFgK,EAAoBnD,EAAa,UAAYI,EAAE,UAAYjH,EAAU,SAAS,EAEhF,GAAAgK,EAAkB,SAAW,EAAU,MAAA,IAErC,MAAAC,EAAmB,KAAK,wBAAwBF,CAAkB,EAClEG,EAAkB,KAAK,wBAAwBF,CAAiB,EAE/D,OAAA,KAAK,IAAI,EAAG,KAAK,IAAI,IAAK,IAAMC,EAAmBC,GAAmB,EAAE,CAAC,CAAA,CAG1E,wBAAwBrD,EAA0C,CACpE,OAAAA,EAAa,SAAW,EAAU,EAC/BA,EAAa,OAAO,CAAC9F,EAAK,IAAMA,EAAM,EAAE,aAAc,CAAC,EAAI8F,EAAa,MAAA,CAGzE,mBAAmBU,EAAkD,CAC3E,MAAM4C,EAAsC,CAAC,EAC7C,OAAA5C,EAAO,QAAiBtB,GAAA,CACfkE,EAAAlE,EAAM,MAAM,KAAK,GAAKkE,EAAOlE,EAAM,MAAM,KAAK,GAAK,GAAK,CAAA,CAChE,EACMkE,CAAA,CAGD,oCACNzK,EACA6H,EACAV,EACAyC,EACU,CACV,MAAMc,EAAkB,CAAC,EAGrB,OADoB,IAAI,IAAIvD,EAAa,QAAaI,GAAAA,EAAE,mBAAmB,CAAC,EAC5D,KAAO,GACzBmD,EAAgB,KAAK,iFAAiF,EAGpGd,EAAW,OAAS,GACtBc,EAAgB,KAAK,2EAA2E,EAG3E,CAAC,GAAGvD,EAAc,GAAGyC,CAAU,EAAE,OAC7ChE,GAAAA,EAAM,UAAY,IAAI,KAAK,KAAK,IAAI,EAAI,GAAK,GAAK,GAAK,GAAK,GAAI,CAC3E,EAEmB,OAAS,GAC1B8E,EAAgB,KAAK,0EAA0E,EAG7F7C,EAAO,SAAW,GACpB6C,EAAgB,KAAK,uGAAuG,EAGvHA,CAAA,CAGD,YAAqB,CACpB,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAA,CAEjD,CAEa,MAAAC,EAA2B,IAAI5E,EC9rBtC6E,EAA4D,CAAC,CAAE,OAAA5K,KAAa,CAChF,KAAM,CAAC6K,EAAYC,CAAa,EAAI9H,EAAAA,SAAsB,CAAA,CAAE,EACtD,CAAC+H,EAAgBC,CAAiB,EAAIhI,EAAAA,SAA6B,CAAA,CAAE,EACrE,CAACiI,EAAiBC,CAAkB,EAAIlI,EAAAA,SAAc,IAAI,EAC1D,CAACmI,EAAmBC,CAAoB,EAAIpI,EAAAA,SAAc,IAAI,EAC9D,CAACqI,EAAaC,CAAc,EAAItI,EAAAA,SAAgB,CAAA,CAAE,EAClD,CAACQ,EAASC,CAAU,EAAIT,EAAAA,SAAS,EAAI,EACrC,CAACuI,EAAWC,CAAY,EAAIxI,EAAAA,SAA2D,QAAQ,EAC/F,CAACyI,EAAaC,CAAc,EAAI1I,EAAAA,SAA4B,IAAI,EAEtEiB,EAAAA,UAAU,IAAM,CACD0H,EAAA,CAAA,EACZ,CAAC3L,CAAM,CAAC,EAEX,MAAM2L,EAAe,SAAY,CAC3B,GAAA,CACFlI,EAAW,EAAI,EACT,KAAA,CAACoE,EAAQ2B,EAAatH,EAAO0J,EAAQC,CAAe,EAAI,MAAM,QAAQ,IAAI,CAC9ElB,EAAyB,cAAc3K,CAAM,EAC7C2K,EAAyB,sBAAsB3K,CAAM,EACrD2K,EAAyB,yBAAyB3K,EAAQ,CACxD,UAAW,IAAI,KAAK,KAAK,IAAA,EAAQ,GAAK,GAAK,GAAK,GAAK,GAAI,EACzD,YAAa,IAAK,CACnB,EACD2K,EAAyB,0BAA0B3K,CAAM,EACzD2K,EAAyB,eAAe,QAAS,CAC/C,UAAW,IAAI,KAAK,KAAK,IAAA,EAAQ,GAAK,GAAK,GAAK,GAAK,GAAI,EACzD,YAAa,IACd,CAAA,CAAA,CACF,EAEDG,EAAcjD,CAAM,EACpBmD,EAAkBxB,CAAW,EAC7B0B,EAAmBhJ,CAAK,EACxBkJ,EAAqBQ,CAAM,EAC3BN,EAAeO,CAAe,QACvBtH,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,CAAA,QAC/C,CACAd,EAAW,EAAK,CAAA,CAEpB,EAEMqI,EAAmB,MAAO3F,GAAmB,CAC7C,GAAA,CACF,MAAM4F,EAAQ,MAAMpB,EAAyB,WAAW3K,EAAQmG,CAAS,EACzEuF,EAAeK,CAAK,EAGpB,MAAMJ,EAAa,QACZpH,EAAO,CACN,QAAA,MAAM,wBAAyBA,CAAK,CAAA,CAEhD,EAEMyH,EAAoB,SAAY,CAEpC,MAAMrF,EAAgC,CACpC,OAAA3G,EACA,gBAAiB,CACf,KAAM,eACN,SAAU,GACV,aAAc,IACd,aAAc,CAAC,QAAS,OAAO,CACjC,EACA,oBAAqB,CAAC,OAAQ,WAAW,EACzC,SAAU,GACV,aAAc,IACd,yBAA0B,CACxB,CACE,QAAS,2CACT,gBAAiB,OACjB,aAAc,YACd,aAAc,CAACA,CAAM,CAAA,CAEzB,EACA,cAAe,KACf,mBAAoB,WACtB,EAEM,MAAA2K,EAAyB,+BAA+BhE,CAAW,EACzE,MAAMgF,EAAa,CACrB,EAEMM,EAAwB,SAAY,CAExC,MAAMpF,EAA4B,CAChC,OAAA7G,EACA,aAAc,eACd,mBAAoB,uBACpB,gBAAiB,OACjB,iBAAkB,EAClB,kBAAmB,oCACnB,sBAAuB,CAAC,EACxB,sBAAuB,CAAC,oBAAoB,EAC5C,cAAe,IACjB,EAEM,MAAA2K,EAAyB,8BAA8B9D,CAAQ,EACrE,MAAM8E,EAAa,CACrB,EAEMO,EAAmB3F,GACtBjB,EAAA,KAAA,MAAA,CAAmB,UAAU,oCAC5B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAC,MAAC,OAAI,UAAW,gFACdgB,EAAM,MAAM,QAAU,WAAa,cACnCA,EAAM,MAAM,QAAU,OAAS,gBAC/BA,EAAM,MAAM,QAAU,SAAW,cACjC,eACF,GACG,SAAMA,EAAA,MAAM,QAAU,sBAAwB,KAC9CA,EAAM,MAAM,QAAU,WAAa,KACnCA,EAAM,MAAM,QAAU,OAAS,KAC/BA,EAAM,MAAM,QAAU,SAAW,KAAO,KAC3C,SACC,MACC,CAAA,SAAA,CAAAhB,MAAC,KAAG,CAAA,UAAU,8BAA+B,SAAAgB,EAAM,UAAU,aAAa,EAC1EjB,EAAAA,KAAC,IAAE,CAAA,UAAU,mCAAoC,SAAA,CAAAiB,EAAM,MAAM,MAAM,QAAA,CAAM,CAAA,CAAA,CAC3E,CAAA,CAAA,EACF,QACC,OAAK,CAAA,UAAU,wBACb,SAAMA,EAAA,WAAW,oBACpB,CAAA,CAAA,EACF,QAEC,IAAE,CAAA,UAAU,6BAA8B,SAAAA,EAAM,UAAU,YAAY,EAEvEjB,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAkB,qBAAA,EAClDA,EAAAA,IAAC,QAAK,UAAU,cAAe,WAAM,gBAAgB,iBAAiB,KAAK,IAAI,CAAE,CAAA,CAAA,EACnF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAa,gBAAA,QAC5C,OAAK,CAAA,UAAU,yBAA0B,SAAAgB,EAAM,gBAAgB,WAAY,CAAA,CAAA,EAC9E,EACAjB,EAAAA,KAAC,MAAI,CAAA,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAsB,yBAAA,EACtDD,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAAiB,EAAM,gBAAgB,oBAAoB,MAAA,CAAI,CAAA,CAAA,CAC/E,CAAA,CAAA,EACF,EAECA,EAAM,MAAM,kBAAkB,OAAS,GACrCjB,OAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAmB,sBAAA,EACzEA,EAAA,IAAA,KAAA,CAAG,UAAU,kCACX,SAAMgB,EAAA,MAAM,kBAAkB,IAAI,CAAC4F,EAAWpK,IAC7CuD,EAAAA,KAAC,KAAe,CAAA,SAAA,CAAA,KAAG6G,CAAA,CAAV,EAAApK,CAAoB,CAC9B,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,EAjDMwE,EAAM,EAmDhB,EAGI6F,EAAyB5C,GAC5BlE,EAAA,KAAA,MAAA,CAA6C,UAAU,oCACtD,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,8BAA+B,SAAAiE,EAAY,UAAU,aAAa,QAC/E,OAAK,CAAA,UAAW,kCACfA,EAAY,SAAW,8BAAgC,+BACzD,GACG,SAAYA,EAAA,SAAW,kBAAoB,GAAG,KAAK,MAAMA,EAAY,QAAQ,CAAC,YACjF,CAAA,CAAA,EACF,QAEC,IAAE,CAAA,UAAU,6BAA8B,SAAAA,EAAY,UAAU,YAAY,EAE7ElE,EAAAA,KAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAQ,UAAA,CAAA,SACb,OAAM,CAAA,SAAA,CAAK,KAAA,MAAMiE,EAAY,QAAQ,EAAE,GAAA,CAAC,CAAA,CAAA,EAC3C,EACAjE,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,2DACV,MAAO,CAAE,MAAO,GAAG,KAAK,IAAIiE,EAAY,SAAU,GAAG,CAAC,GAAI,CAAA,CAAA,CAE9D,CAAA,CAAA,EACF,EAEAlE,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,oCAAoC,SAAa,gBAAA,EAC9DiE,EAAY,aAAa,IAAI,CAAC9C,EAAU3E,IACvCuD,EAAA,KAAC,MAAgB,CAAA,UAAU,wBAAwB,SAAA,CAAA,KAC9CoB,EAAI,WAAA,CAAA,EADC3E,CAEV,CACD,CAAA,EACH,EAEAuD,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAe,iBAAA,CAAA,EAAS,IAAEiE,EAAY,aAAA,EAChD,EAEAlE,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAe,iBAAA,CAAA,EAAS,IAAEiE,EAAY,mBAAA,EAChD,EAECA,EAAY,UACXjE,EAAA,IAAC,SAAA,CACC,QAAS,IAAMuG,EAAiBtC,EAAY,SAAS,EACrD,UAAU,yFACX,SAAA,aAAA,CAAA,CAED,GAhDMA,EAAY,UAAU,YAkDhC,EAGI6C,EAAwB,IAC3B/G,OAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAoC,uCAAA,EAE/ED,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAU,mCAAoC,SAAA0F,GAAiB,YAAc,EAAE,EACnF1F,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAW,aAAA,CAAA,CAAA,EACpD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAU,oCAAqC,SAAA0F,GAAiB,mBAAqB,EAAE,EAC3F1F,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAkB,oBAAA,CAAA,CAAA,EAC3D,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAU,qCAAsC,SAAA0F,GAAiB,aAAe,EAAE,EACtF1F,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAW,aAAA,CAAA,CAAA,CACpD,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAyB,4BAAA,QACvE,MAAI,CAAA,UAAU,mCAAoC,SAAA0F,GAAiB,yBAA2B,EAAE,EAChG1F,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAiC,mCAAA,CAAA,CAAA,EAC1E,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,kCAAkC,SAAiB,oBAAA,QAChE,MAAI,CAAA,UAAU,oCAAqC,SAAA0F,GAAiB,kBAAoB,EAAE,EAC1F1F,EAAA,IAAA,MAAA,CAAI,UAAU,yBAAyB,SAA8B,gCAAA,CAAA,CAAA,EACxE,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAe,kBAAA,QAC/D,MAAI,CAAA,UAAU,qCAAsC,SAAA0F,GAAiB,gBAAkB,EAAE,EACzF1F,EAAA,IAAA,MAAA,CAAI,UAAU,0BAA0B,SAAkC,oCAAA,CAAA,CAAA,CAC7E,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGI+G,EAAoB,IACvBhH,OAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAqB,wBAAA,EAEhEA,EAAAA,IAAC,OAAI,UAAU,YACZ,WAAY,MAAM,EAAG,EAAE,EAAE,IAAI,CAAC4C,EAAOpG,IACpCuD,EAAAA,KAAC,OAAuB,UAAW,oDACjC6C,EAAM,SAAWnI,EAAS,oCAAsC,YAClE,GACE,SAAA,CAACsF,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAW,8EACdxD,IAAU,EAAI,gBACdA,IAAU,EAAI,cACdA,IAAU,EAAI,gBACd,aACF,GACG,WAAQ,EACX,SACC,MACC,CAAA,SAAA,CAAAwD,EAAA,IAAC,MAAI,CAAA,UAAU,cACZ,SAAA4C,EAAM,SAAWnI,EAAS,MAAQ,QAAQmI,EAAM,OAAO,MAAM,EAAE,CAAC,GACnE,EACA7C,EAAAA,KAAC,MAAI,CAAA,UAAU,wBACZ,SAAA,CAAM6C,EAAA,OAAO,aAAWA,EAAM,kBAAkB,qBAAA,CACnD,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EACA7C,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,EAAA,IAAC,MAAI,CAAA,UAAU,oBAAqB,SAAA4C,EAAM,MAAM,EAC/C5C,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAM,QAAA,CAAA,CAAA,CAC/C,CAAA,CAAA,GAxBQ4C,EAAM,MAyBhB,CACD,CACH,CAAA,CAAA,EACF,EAGIoE,EAAsB,IACzBjH,OAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAoB,uBAAA,EAE9D4F,GACC7F,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAAAC,MAAC,OAAI,UAAU,mCACZ,SAAkB4F,EAAA,QAAQ,mBAAmB,gBAChD,EACC5F,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAgB,kBAAA,CAAA,CAAA,EACzD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAC,MAAC,OAAI,UAAU,oCACZ,SAAkB4F,EAAA,QAAQ,gBAAgB,aAC7C,EACC5F,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAa,eAAA,CAAA,CAAA,EACtD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAC,MAAC,OAAI,UAAU,qCACZ,SAAkB4F,EAAA,QAAQ,gBAAgB,kBAC7C,EACC5F,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAkB,oBAAA,CAAA,CAAA,CAC3D,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAmB,sBAAA,QACjE,MAAI,CAAA,UAAU,YACZ,SAAkB4F,EAAA,QAAQ,aAAa,MAAM,EAAG,CAAC,EAAE,IAAI,CAACzM,EAAkBqD,IACxEuD,OAAA,MAAA,CAAgB,UAAU,2DACzB,SAAA,CAAAC,EAAA,IAAC,OAAK,CAAA,UAAU,UAAW,SAAA7G,EAAY,UAAU,EAChD6G,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAyB,WAAY,UAAW,CAAA,CAAA,CAFxD,EAAAxD,CAGV,CACD,CACH,CAAA,CAAA,EACF,EAECoJ,EAAkB,gBAAgB,OAAS,UACzC,MACC,CAAA,SAAA,CAAC5F,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAe,kBAAA,EAC7DA,EAAA,IAAA,KAAA,CAAG,UAAU,YACX,SAAkB4F,EAAA,gBAAgB,IAAI,CAACqB,EAAazK,IAClDuD,OAAA,KAAA,CAAe,UAAU,wBAAwB,SAAA,CAAA,KAAGkH,CAAA,CAA5C,EAAAzK,CAAgD,CAC1D,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EAEJ,EAGF,OAAIyB,EAEA+B,MAAC,OAAI,UAAU,wCACb,eAAC,MAAI,CAAA,UAAU,iEAAiE,CAClF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAqB,wBAAA,EAC1EA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,oGAAA,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAa,gBAAA,EACxDD,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAASyG,EACT,UAAU,gEACX,SAAA,mCAAA,CAED,EACAzG,EAAA,IAAC,SAAA,CACC,QAAS0G,EACT,UAAU,kEACX,SAAA,uBAAA,CAAA,CAED,CACF,CAAA,CAAA,EACF,EAGA1G,EAAAA,IAAC,MAAI,CAAA,UAAU,iDACZ,SAAA,CACC,CAAE,IAAK,SAAU,MAAO,YAAa,KAAM,IAAK,EAChD,CAAE,IAAK,WAAY,MAAO,WAAY,KAAM,IAAK,EACjD,CAAE,IAAK,cAAe,MAAO,cAAe,KAAM,IAAK,EACvD,CAAE,IAAK,SAAU,MAAO,SAAU,KAAM,IAAK,CAAA,EAC7C,IACAO,GAAAR,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMkG,EAAa1F,EAAI,GAAU,EAC1C,UAAW,gHACTyF,IAAczF,EAAI,IACd,mCACA,mCACN,GAEA,SAAA,CAACP,EAAAA,IAAA,OAAA,CAAM,WAAI,IAAK,CAAA,EAChBA,EAAAA,IAAC,OAAM,CAAA,SAAAO,EAAI,KAAM,CAAA,CAAA,CAAA,EATZA,EAAI,GAWZ,CAAA,EACH,EAGCyF,IAAc,UACbjG,EAAAA,KAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,8BAA8B,SAAA,CAAA,gBAAcuF,EAAW,OAAO,GAAA,EAAC,EAC5EA,EAAW,OAAS,EACnBtF,EAAA,IAAC,OAAI,UAAU,uDACZ,SAAWsF,EAAA,IAAIqB,CAAe,CACjC,CAAA,EAEC5G,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAa,gBAAA,EACnEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAAgE,mEAAA,EAClGA,EAAA,IAAC,SAAA,CACC,QAASyG,EACT,UAAU,gEACX,SAAA,+BAAA,CAAA,CAED,CACF,CAAA,CAAA,EAEJ,EAGDT,IAAc,YACbjG,EAAAA,KAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAc,iBAAA,QACzD,MAAI,CAAA,UAAU,uDACZ,SAAewF,EAAA,IAAIqB,CAAqB,CAC3C,CAAA,CAAA,EACF,EAGDb,IAAc,eAAiBe,EAAkB,EAEjDf,IAAc,UACZjG,OAAA,MAAA,CAAI,UAAU,YACZ,SAAA,CAAsB+G,EAAA,EACtBE,EAAoB,CAAA,EACvB,EAIDd,GACClG,EAAA,IAAC,MAAI,CAAA,UAAU,iFACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,0CACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,qFACb,SAAAA,EAAA,IAAC,QAAK,UAAU,WAAW,cAAE,CAC/B,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAgB,mBAAA,QACrE,KAAG,CAAA,UAAU,2CACX,SAAYkG,EAAA,MAAM,UAAU,aAC/B,EACClG,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAsB,WAAY,mBAAmB,EACjEA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA8B,WAAY,gBAAgB,EAEtEkG,EAAY,eACVnG,OAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAU,aAAA,EACzDD,EAAAA,KAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,CAAA,QAC7BmG,EAAY,cAAc,eAAe,yBAAuBA,EAAY,cAAc,MAAM,QAAA,CACxG,CAAA,CAAA,EACF,EAGFlG,EAAA,IAAC,SAAA,CACC,QAAS,IAAMmG,EAAe,IAAI,EAClC,UAAU,uEACX,SAAA,UAAA,CAAA,CAED,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EAEJ,CAEJ,ECzbA,MAAMe,CAAwB,CACpB,cAA0D,IAC1D,oBAAwD,IACxD,qBAA2D,IAC3D,kBAAsC,IAE9C,aAAc,CACZ,KAAK,wBAAwB,CAAA,CAI/B,MAAM,sBAAsBC,EAAgE,CAC1F,MAAMC,EAAwC,CAC5C,GAAI,KAAK,WAAW,EACpB,yBAA0BD,EAAQ,yBAClC,cAAeA,EAAQ,cACvB,iBAAkB,CAChB,qBAAsB,CACpB,kBAAmB,YACnB,eAAgBA,EAAQ,qBACxB,6BAA8BA,EAAQ,oBACtC,gBAAiB,qCACjB,kBAAmB,EACnB,iBAAkB,CACpB,EACA,oBAAqBA,EAAQ,oBAAoB,IAAgB/N,IAAA,CAC/D,QAAAA,EACA,aAAc,gCACd,aAAc,UAAA,EACd,EACF,gBAAiB,CACf,yBAA0B+N,EAAQ,qBAClC,mBAAoB,CAAC,EACrB,mBAAoB,6CACpB,kBAAmB,EACrB,EACA,kBAAmBA,EAAQ,kBAAkB,IAAiBE,IAAA,CAC5D,SAAAA,EACA,YAAa,8CACb,WAAY,WACZ,gBAAiB,+BAAA,EACjB,EACF,iBAAkB,CAChB,MAAOF,EAAQ,iBACf,aAAc,KAAK,iCAAiCA,EAAQ,gBAAgB,EAC5E,mBAAoB,KAAK,4BAA4BA,EAAQ,gBAAgB,CAC/E,EACA,oBAAqB,KAAK,6BAA6BA,EAAQ,gBAAgB,EAC/E,qBAAsB,CACpB,cAAe,GACf,UAAW,GACX,cAAe,KACf,WAAY,CAAC,EACb,UAAW,EAAA,CAEf,EACA,mBAAoB,CAClB,iBAAkBA,EAAQ,yBAC1B,oBAAqB,CAAC,EACtB,mBAAoB,KAAK,6BAA6B,EACtD,oBAAqB,CAAC,EACtB,kBAAmB,CAAC,EACpB,gBAAiB,EACjB,uBAAwB,CAC1B,EACA,qBAAsB,CACpB,aAAc,CAAC,EACf,cAAe,CACb,WAAY,EACZ,mBAAoB,EACpB,uBAAwB,EACxB,sBAAuB,EACvB,aAAc,CACZ,cAAe,KACf,QAAS,IAAI,KAAK,KAAK,IAAA,EAAQ,OAAwB,EACvD,iBAAkB,iBAClB,0BAA2B,CAAA,CAAC,CAEhC,EACA,uBAAwB,CACtB,WAAY,GACZ,iBAAkB,KAClB,cAAe,GACf,WAAY,CAAA,CAAC,CAEjB,EACA,uBAAwB,CACtB,eAAgB,CAAC,EACjB,iBAAkB,CAAC,EACnB,mBAAoB,CAAC,EACrB,4BAA6B,CAAC,EAC9B,kBAAmB,cACnB,mBAAoB,EACpB,oBAAqB,CAAA,CACvB,EACA,sBAAuB,CAAC,EACxB,kBAAmB,CACjB,OAAQ,QACR,WAAY,gCACZ,eAAgB,EAClB,EACA,cAAe,CACb,oBAAqB,EACrB,iBAAkB,EAClB,uBAAwB,EACxB,uBAAwB,EACxB,iBAAkB,CAAA,CAEtB,EAEA,YAAK,UAAU,IAAIC,EAAS,GAAIA,CAAQ,EAGpCD,EAAQ,+BACJ,MAAA,KAAK,0BAA0BC,EAAS,EAAE,EAG3CA,CAAA,CAGT,MAAM,YAAYE,EAAiE,CACjF,OAAO,KAAK,UAAU,IAAIA,CAAU,GAAK,IAAA,CAG3C,MAAM,uBAAuBA,EAAoBC,EAA2E,CAC1H,MAAMH,EAAW,KAAK,UAAU,IAAIE,CAAU,EAC9C,GAAI,CAACF,EACG,MAAA,IAAI,MAAM,oBAAoB,EAGtC,OAAAA,EAAS,iBAAmB,CAC1B,GAAGA,EAAS,iBACZ,GAAGG,CACL,EAEK,KAAA,UAAU,IAAID,EAAYF,CAAQ,EAChCA,CAAA,CAIT,MAAM,0BAA0BE,EAAmC,CACjE,MAAMF,EAAW,KAAK,UAAU,IAAIE,CAAU,EAC9C,GAAI,CAACF,EACG,MAAA,IAAI,MAAM,oBAAoB,EAOhC,MAAAI,GAHqB,MAAM,KAAK,2BAA2BJ,CAAQ,GAGN,IAAkBK,IAAA,CACnF,WAAAH,EACA,YAAaG,EAAU,GACvB,cAAeA,EAAU,KACzB,mBAAoB,KAAK,6BAA6BA,EAAU,IAAI,EACpE,eAAgBA,EAAU,cAAA,EAC1B,EAEG,KAAA,gBAAgB,IAAIH,EAAYE,CAAkB,EAGjD,MAAA,KAAK,iBAAiBA,CAAkB,CAAA,CAGhD,MAAM,uBACJF,EACAI,EACAC,EACe,CACf,MAAMP,EAAW,KAAK,UAAU,IAAIE,CAAU,EAC9C,GAAI,CAACF,EACG,MAAA,IAAI,MAAM,oBAAoB,EAG7BA,EAAA,mBAAmB,oBAAoB,KAAKO,CAAU,EAGzD,MAAA,KAAK,sBAAsBL,CAAU,EAEtC,KAAA,UAAU,IAAIA,EAAYF,CAAQ,CAAA,CAGzC,MAAM,oBAAoBE,EAAoBM,EAAyC,CACrF,MAAMR,EAAW,KAAK,UAAU,IAAIE,CAAU,EAC9C,GAAI,CAACF,EACG,MAAA,IAAI,MAAM,oBAAoB,EAGlCQ,EAAO,aAAe,EACfR,EAAA,uBAAuB,eAAe,KAAKQ,CAAM,EAEjDR,EAAA,uBAAuB,iBAAiB,KAAKQ,CAAM,EAIxD,MAAA,KAAK,wBAAwBN,CAAU,EAExC,KAAA,UAAU,IAAIA,EAAYF,CAAQ,CAAA,CAGzC,MAAM,kCACJE,EACAO,EACe,CACf,MAAMT,EAAW,KAAK,UAAU,IAAIE,CAAU,EAC9C,GAAI,CAACF,EACG,MAAA,IAAI,MAAM,oBAAoB,EAG7BA,EAAA,uBAAuB,4BAA4B,KAAKS,CAAM,EAGvET,EAAS,uBAAuB,oBAAsB,GAEjD,KAAA,UAAU,IAAIE,EAAYF,CAAQ,CAAA,CAIzC,MAAM,sBAAsBU,EAA4C,CACtE,MAAMC,EAAU,KAAK,iBAAiB,IAAID,EAAO,UAAU,GAAK,CAAC,EACjEC,EAAQ,KAAKD,CAAM,EACnB,KAAK,iBAAiB,IAAIA,EAAO,WAAYC,CAAO,EAEpD,MAAMX,EAAW,KAAK,UAAU,IAAIU,EAAO,UAAU,EACjDV,IACOA,EAAA,mBAAmB,kBAAkB,KAAK,CACjD,WAAY,KAAK,WAAW,EAC5B,WAAYU,EAAO,WACnB,aAAcA,EAAO,WACrB,SAAUA,EAAO,SACjB,OAAQA,EAAO,OACf,SAAU,IAAK,CAChB,EAGK,MAAA,KAAK,wBAAwBA,EAAO,UAAU,EAEpD,KAAK,UAAU,IAAIA,EAAO,WAAYV,CAAQ,EAChD,CAGF,MAAM,oBAAoBE,EAAoD,CAC5E,OAAO,KAAK,iBAAiB,IAAIA,CAAU,GAAK,CAAC,CAAA,CAGnD,MAAM,4BAA4BA,EAAkC,CAClE,MAAMS,EAAU,KAAK,iBAAiB,IAAIT,CAAU,GAAK,CAAC,EAG1D,GAAI,CAFa,KAAK,UAAU,IAAIA,CAAU,GAE7BS,EAAQ,SAAW,EAClC,MAAO,CAAE,UAAW,oBAAqB,WAAY,CAAE,EAGnD,MAAAC,EAAgBD,EAAQ,OAAO,CAACjM,EAAKmM,IAAMnM,EAAMmM,EAAE,OAAQ,CAAC,EAAIF,EAAQ,OACxEG,EAA2BH,EAAQ,UACvC,KAAK,0BAA0BE,EAAE,WAAYA,EAAE,eAAe,CAChE,EAEME,EAAoBD,EAAyB,OAAS,EACxDA,EAAyB,OAAO,CAACpM,EAAKmM,IAAMnM,EAAMmM,EAAE,OAAQ,CAAC,EAAIC,EAAyB,OAC1F,EAKG,MAAA,CACL,UAJgBF,GAAiB,GAAKG,GAAqB,EAAI,WAChDH,GAAiB,GAAKG,GAAqB,EAAI,WAAa,QAI3E,WAAY,KAAK,IAAIJ,EAAQ,OAAS,GAAI,GAAG,EAC7C,cAAAC,EACA,kBAAAG,EACA,aAAcJ,EAAQ,OACtB,yBAA0BG,EAAyB,MACrD,CAAA,CAIF,MAAM,wBAAwBE,EAAqE,CACjG,MAAMhB,EAAW,KAAK,UAAU,IAAIgB,EAAS,UAAU,EACvD,GAAI,CAAChB,EACG,MAAA,IAAI,MAAM,oBAAoB,EAGtC,MAAMiB,EAA+B,CACnC,OAAQD,EAAS,WAAa,UAAY,YAClCA,EAAS,WAAa,SAAW,WAAa,kBACtD,YAAaA,EAAS,WAAa,UAAY,IAAI,KAAS,OAC5D,WAAYA,EAAS,WACrB,eAAgB,EAClB,EAEA,OAAAhB,EAAS,kBAAoBiB,EAGzBD,EAAS,WAAa,WAClB,MAAA,KAAK,oBAAoBA,EAAS,UAAU,EAGpD,KAAK,UAAU,IAAIA,EAAS,WAAYhB,CAAQ,EACzCA,CAAA,CAGT,MAAM,sBAAsBzN,EAAuD,CACjF,MAAM2O,EAAY,MAAM,KAAK,KAAK,UAAU,OAAQ,CAAA,EAAE,OAAOC,GAC3DA,EAAE,kBAAkB,SAAW,WACjC,EAEA,OAAI5O,GAAS,gBACJ2O,EAAU,OAAOC,GACtBA,EAAE,iBAAiB,qBAAqB,6BACrC,KAAKnP,GAAWA,EAAQ,SAASO,EAAQ,eAAe,CAAC,CAC9D,EAGEA,GAAS,iBACJ2O,EAAU,OACfC,GAAAA,EAAE,iBAAiB,iBAAiB,QAAU5O,EAAQ,gBACxD,EAGK2O,EAAU,KAAK,CAACzO,EAAGC,IACxBA,EAAE,cAAc,oBAAsBD,EAAE,cAAc,mBACxD,CAAA,CAGF,MAAM,sBAA+D,CACnE,OAAO,MAAM,KAAK,KAAK,UAAU,OAAQ,CAAA,EAAE,UACzC0O,EAAE,kBAAkB,SAAW,aAAeA,EAAE,kBAAkB,cACpE,CAAA,CAIF,MAAM,uBAAuBxN,EAA6D,CAExF,MAAMyN,EADY,MAAM,KAAK,KAAK,UAAU,QAAQ,EACf,OACnCD,GAAAA,EAAE,kBAAkB,aACpBA,EAAE,kBAAkB,aAAexN,EAAU,WAC7CwN,EAAE,kBAAkB,aAAexN,EAAU,OAC/C,EAEM0N,EAAiBD,EAAmB,OACpCE,EAAqBF,EAAmB,OAAOD,GAAKA,EAAE,kBAAkB,SAAW,WAAW,EAAE,OAChGI,EAAyBH,EAAmB,OAAO,CAAC1M,EAAKyM,IAAMzM,EAAMyM,EAAE,mBAAmB,gBAAiB,CAAC,EAAIE,GAAkB,EAElIxM,EAAuB,KAAK,8BAA8BuM,CAAkB,EAC5EI,EAA0B,KAAK,iCAAiCJ,CAAkB,EAEjF,MAAA,CACL,UAAAzN,EACA,eAAA0N,EACA,mBAAAC,EACA,gBAAkBA,EAAqBD,EAAkB,KAAO,EAChE,uBAAAE,EACA,qBAAA1M,EACA,wBAAA2M,EACA,aAAcJ,EACX,KAAK,CAAC3O,EAAGC,IAAMA,EAAE,cAAc,oBAAsBD,EAAE,cAAc,mBAAmB,EACxF,MAAM,EAAG,EAAE,EACX,IAAU0O,IAAA,CACT,GAAIA,EAAE,GACN,qBAAsBA,EAAE,iBAAiB,qBAAqB,eAC9D,WAAYA,EAAE,cAAc,mBAAA,EAC5B,CACN,CAAA,CAGF,MAAM,6BAA6BM,EAAkE,CACnG,OAAO,MAAM,KAAK,KAAK,UAAU,OAAQ,CAAA,EAAE,OAAON,GAChDA,EAAE,2BAA6BM,CACjC,CAAA,CAGF,MAAM,sBAAsBnB,EAAmD,CAC7E,MAAMoB,EAAsC,CAAC,EAE7C,SAAW,CAACxB,EAAYyB,CAAQ,IAAK,KAAK,gBAAiB,CACzD,MAAMC,EAAeD,EAAS,OAAYd,GAAAA,EAAE,cAAgBP,CAAW,EACxDoB,EAAA,KAAK,GAAGE,CAAY,CAAA,CAG9B,OAAAF,CAAA,CAID,yBAAgC,CAEjC,KAAA,cAAc,IAAI,WAAY,CACjC,GAAI,WACJ,KAAM,kBACN,eAAgB,CAAC,kBAAmB,cAAc,EAClD,gBAAiB,OACjB,YAAa,CAAC,uBAAwB,iBAAiB,CAAA,CACxD,EAEI,KAAA,cAAc,IAAI,WAAY,CACjC,GAAI,WACJ,KAAM,YACN,eAAgB,CAAC,wBAAyB,iBAAiB,EAC3D,gBAAiB,WACjB,YAAa,CAAC,cAAe,sBAAsB,CAAA,CACpD,EAEI,KAAA,cAAc,IAAI,WAAY,CACjC,GAAI,WACJ,KAAM,+BACN,eAAgB,CAAC,uBAAwB,sBAAsB,EAC/D,gBAAiB,QACjB,YAAa,CAAC,qBAAsB,sBAAsB,CAAA,CAC3D,CAAA,CAGH,MAAc,2BAA2B1B,EAAuD,CAC9F,MAAM6B,EAAa,CAAC,EACdC,EAAmB9B,EAAS,iBAAiB,iBAAiB,MAG9D+B,EAAkB,MAAM,KAAK,KAAK,cAAc,OAAQ,CAAA,EAAE,OAAOC,GACrEA,EAAO,OAAS,iBAClB,EAMA,GALID,EAAgB,OAAS,GAChBF,EAAA,KAAKE,EAAgB,CAAC,CAAC,EAIhC/B,EAAS,uBAAuB,eAAe,OAAS,EAAG,CAC7D,MAAMiC,EAAa,MAAM,KAAK,KAAK,cAAc,OAAQ,CAAA,EAAE,OAAOD,GAChEA,EAAO,OAAS,WAClB,EACIC,EAAW,OAAS,GACXJ,EAAA,KAAKI,EAAW,CAAC,CAAC,CAC/B,CAIE,GAAAH,IAAqB,UAAYA,IAAqB,aAAc,CACtE,MAAMI,EAAU,MAAM,KAAK,KAAK,cAAc,OAAQ,CAAA,EAAE,OAAOF,GAC7DA,EAAO,OAAS,8BAClB,EACIE,EAAQ,OAAS,GACRL,EAAA,KAAKK,EAAQ,CAAC,CAAC,CAC5B,CAGK,OAAAL,CAAA,CAGD,8BAAsD,CACrD,MAAA,CACL,CAAE,UAAW,oBAAqB,OAAQ,GAAI,OAAQ,GAAO,MAAO,EAAG,EACvE,CAAE,UAAW,sBAAuB,OAAQ,GAAI,OAAQ,GAAO,MAAO,EAAG,EACzE,CAAE,UAAW,uBAAwB,OAAQ,GAAI,OAAQ,GAAO,MAAO,EAAG,EAC1E,CAAE,UAAW,4BAA6B,OAAQ,GAAI,OAAQ,GAAO,MAAO,EAAG,CACjF,CAAA,CAGM,6BAA6BM,EAAiC,CAQpE,MAPiB,CACf,gBAAmB,CAAC,oBAAqB,mCAAoC,2BAA2B,EACxG,UAAa,CAAC,sBAAuB,sBAAuB,qBAAqB,EACjF,6BAAgC,CAAC,6BAA8B,uBAAwB,0BAA0B,EACjH,SAAY,CAAC,iBAAkB,uBAAwB,mBAAmB,CAC5E,EAEgBA,CAAsC,GAAK,CAAC,oBAAoB,CAAA,CAGlF,MAAc,iBAAiBR,EAA8C,CAE3E,QAAQ,IAAI,aAAaA,EAAS,MAAM,qCAAqC,CAAA,CAG/E,MAAc,sBAAsBzB,EAAmC,CACrE,MAAMF,EAAW,KAAK,UAAU,IAAIE,CAAU,EAC9C,GAAI,CAACF,EAAU,OAET,MAAAoC,EAAgBpC,EAAS,mBAAmB,oBAC9C,GAAAoC,EAAc,SAAW,EAAG,OAE1B,MAAAC,EAAoBD,EAAc,OAAO,CAAC1N,EAAKyM,IAAMzM,EAAMyM,EAAE,WAAY,CAAC,EAAIiB,EAAc,OAClGpC,EAAS,mBAAmB,gBAAkBqC,EAC9CrC,EAAS,mBAAmB,uBAAyBqC,CAAA,CAGvD,MAAc,wBAAwBnC,EAAmC,CACvE,MAAMF,EAAW,KAAK,UAAU,IAAIE,CAAU,EAC9C,GAAI,CAACF,EAAU,OAEf,MAAMsC,EAAetC,EAAS,uBACxBuC,EAAiBD,EAAa,eAAe,OAC7CE,EAAmBF,EAAa,iBAAiB,OACjDG,EAAsBH,EAAa,mBAAmB,OACtDI,EAAmBJ,EAAa,4BAA4B,OAE9DI,EAAmB,GAAKH,EAAiB,GAAKE,EAAsB,EACtEH,EAAa,kBAAoB,sBACxBC,EAAiB,GAAKE,EAAsB,EACrDH,EAAa,kBAAoB,mBACxBC,EAAiB,GAAKC,EAAmB,KAClDF,EAAa,kBAAoB,aAInCA,EAAa,mBAAqB,KAAK,IACpCC,EAAiB,GAAOC,EAAmB,GAAOC,EAAsB,GAAOC,EAAmB,GACnG,GACF,CAAA,CAGF,MAAc,wBAAwBxC,EAAmC,CACvE,MAAMS,EAAU,KAAK,iBAAiB,IAAIT,CAAU,GAAK,CAAC,EACpDF,EAAW,KAAK,UAAU,IAAIE,CAAU,EAC9C,GAAI,CAACF,EAAU,OAEf,MAAM2C,EAAahC,EAAQ,OACrBiC,EAAgBjC,EAAQ,UAAYE,EAAE,QAAU,CAAC,EAAE,OACnDgC,EAAqBF,EAAa,EAAKC,EAAgBD,EAAc,IAAM,EAE3EG,EAAyBnC,EAAQ,UACrC,KAAK,0BAA0BE,EAAE,WAAYA,EAAE,eAAe,CAAA,EAC9D,OAEFb,EAAS,qBAAqB,cAAgB,CAC5C,GAAGA,EAAS,qBAAqB,cACjC,WAAA2C,EACA,mBAAAE,EACA,uBAAAC,EACA,sBAAuBH,EAAaG,CACtC,CAAA,CAGF,MAAc,oBAAoB5C,EAAmC,CACnE,MAAMF,EAAW,KAAK,UAAU,IAAIE,CAAU,EACzCF,IAGLA,EAAS,cAAgB,CACvB,oBAAqB,KAAK,MAAM,KAAK,OAAO,EAAI,GAAG,EAAI,GACvD,iBAAkB,KAAK,MAAM,KAAK,OAAO,EAAI,GAAG,EAAI,GACpD,uBAAwB,KAAK,MAAM,KAAK,OAAO,EAAI,GAAG,EAAI,GAC1D,uBAAwBA,EAAS,uBAAuB,mBACxD,iBAAkBA,EAAS,iBAAiB,qBAAqB,iBAAmB,EACtF,EAAA,CAGM,iCAAiCrG,EAAyB,CAQzD,MAPc,CACnB,OAAU,CAAC,EACX,eAAkB,CAAC,+BAA+B,EAClD,WAAc,CAAC,4CAA6C,iBAAiB,EAC7E,OAAU,CAAC,qCAAsC,oBAAqB,6BAA6B,CACrG,EAEoBA,CAAkC,GAAK,CAAC,CAAA,CAGtD,4BAA4BA,EAAyB,CAQpD,MAPc,CACnB,OAAU,CAAC,EACX,eAAkB,CAAC,mCAAmC,EACtD,WAAc,CAAC,sCAAuC,mBAAmB,EACzE,OAAU,CAAC,wCAAyC,6BAA8B,6BAA6B,CACjH,EAEoBA,CAAkC,GAAK,CAAC,CAAA,CAGtD,6BAA6BA,EAAsB,CACzD,MAAMoJ,EAAmB,CACvB,CACE,gBAAiB,uBACjB,YAAa,6CACb,YAAa,WAAA,CAEjB,EAEI,OAAApJ,IAAU,UAAYA,IAAU,eAClCoJ,EAAiB,KAAK,CACpB,gBAAiB,oBACjB,YAAa,4BACb,YAAa,qBAAA,CACd,EAGCpJ,IAAU,UACZoJ,EAAiB,KAAK,CACpB,gBAAiB,iBACjB,YAAa,oDACb,YAAa,iBAAA,CACd,EAGIA,CAAA,CAGD,0BAA0BC,EAAoBjO,EAAkC,CAE/E,OAAA,KAAK,SAAW,EAAA,CAGjB,8BAA8BkO,EAAyE,CAC7G,MAAMlN,EAA8C,CAAC,EACrD,OAAAkN,EAAU,QAAoBjD,GAAA,CAC5BA,EAAS,iBAAiB,qBAAqB,6BAA6B,QAAmBhO,GAAA,CAE7F,MAAMgE,EAAU,OAChBD,EAAaC,CAAO,GAAKD,EAAaC,CAAO,GAAK,GAAK,CAAA,CACxD,CAAA,CACF,EACMD,CAAA,CAGD,iCAAiCkN,EAAuE,CAC9G,MAAMlN,EAA4C,CAAC,EACnD,OAAAkN,EAAU,QAAoBjD,GAAA,CACtB,MAAArG,EAAQqG,EAAS,iBAAiB,iBAAiB,MACzDjK,EAAa4D,CAAK,GAAK5D,EAAa4D,CAAK,GAAK,GAAK,CAAA,CACpD,EACM5D,CAAA,CAGD,YAAqB,CACpB,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAA,CAEjD,CAEa,MAAAmN,EAA0B,IAAIpD,ECrpBrCqD,EAAsE,CAAC,CAAE,OAAA9P,EAAQ,SAAA+P,KAAe,CACpG,KAAM,CAACH,EAAWI,CAAY,EAAIhN,EAAAA,SAAwC,CAAA,CAAE,EACtE,CAACiN,EAAoBC,CAAqB,EAAIlN,EAAAA,SAA8B,CAAA,CAAE,EAC9E,CAACmN,EAAkBC,CAAmB,EAAIpN,EAAAA,SAA6C,IAAI,EAC3F,CAACQ,EAASC,CAAU,EAAIT,EAAAA,SAAS,EAAI,EACrC,CAACuI,EAAWC,CAAY,EAAIxI,EAAAA,SAAwE,cAAc,EAClH,CAACqN,EAAkBC,CAAmB,EAAItN,EAAAA,SAAS,EAAK,EACxD,CAACuN,EAAgBC,CAAiB,EAAIxN,EAAAA,SAAc,IAAI,EAE9DiB,EAAAA,UAAU,IAAM,CACIwM,EAAA,CAAA,EACjB,CAACzQ,EAAQ+P,CAAQ,CAAC,EAErB,MAAMU,EAAoB,SAAY,CAChC,GAAA,CAGF,GAFAhN,EAAW,EAAI,EAEXsM,IAAa,0BAA2B,CAC1C,MAAMW,EAAgB,MAAMb,EAAwB,6BAA6B7P,CAAM,EACvFgQ,EAAaU,CAAa,CAAA,CAG5B,GAAIX,IAAa,SAAU,CACzB,MAAMY,EAAc,MAAMd,EAAwB,sBAAsB7P,CAAM,EAC9EkQ,EAAsBS,CAAW,CAAA,CAG7B,MAAA/E,EAAS,MAAMiE,EAAwB,uBAAuB,CAClE,UAAW,IAAI,KAAK,KAAK,IAAA,EAAQ,GAAK,GAAK,GAAK,GAAK,GAAI,EACzD,YAAa,IAAK,CACnB,EACDW,EAAkB5E,CAAM,QAEjBrH,EAAO,CACN,QAAA,MAAM,gCAAiCA,CAAK,CAAA,QACpD,CACAd,EAAW,EAAK,CAAA,CAEpB,EAEMmN,EAAuB,MAAOC,GAAkB,CAChD,GAAA,CACF,MAAMnE,EAA2B,CAC/B,cAAemE,EAAS,cACxB,yBAA0B7Q,EAC1B,eAAgB6Q,EAAS,OACzB,qBAAsBA,EAAS,aAC/B,oBAAqBA,EAAS,oBAAoB,MAAM,GAAG,EAAE,IAAKrL,GAAcA,EAAE,MAAM,EACxF,kBAAmBqL,EAAS,UAAU,MAAM,GAAG,EAAE,IAAKC,GAAcA,EAAE,MAAM,EAC5E,iBAAkBD,EAAS,iBAC3B,8BAA+BA,EAAS,oBAC1C,EAEMlE,EAAW,MAAMkD,EAAwB,sBAAsBnD,CAAO,EAC5EsD,EAAqB7K,GAAA,CAAC,GAAGA,EAAMwH,CAAQ,CAAC,EACxC2D,EAAoB,EAAK,QAClB/L,EAAO,CACN,QAAA,MAAM,6BAA8BA,CAAK,CAAA,CAErD,EAEMwM,EAA6B,MAAOC,EAAsBC,EAAkBC,IAAuB,CACnG,GAAA,CACF,MAAMhE,EAAa,CACjB,SAAUlN,EACV,WAAY,kBACZ,qBAAsB,KACtB,SAAAiR,EACA,gBAAiB,CAACA,CAAQ,EAC1B,WAAAC,CACF,EAEA,MAAMrB,EAAwB,uBAAuBmB,EAAchR,EAAQkN,CAAU,EAGrFgD,KAA8B/K,EAAK,UAAYgM,EAAE,aAAeH,CAAY,CAAC,QACtEzM,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,CAAA,CAEvD,EAoCM6M,EAAsBzE,GAC1BrH,EAAA,KAAC,MAAA,CAEC,UAAU,qFACV,QAAS,IAAM8K,EAAoBzD,CAAQ,EAE3C,SAAA,CAACrH,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAEpD,iCAAA,EACAA,EAAAA,IAAC,OAAK,CAAA,UAAW,kCACfoH,EAAS,kBAAkB,SAAW,YAAc,8BACpDA,EAAS,kBAAkB,SAAW,kBAAoB,gCAC1DA,EAAS,kBAAkB,SAAW,mBAAqB,4BAC3D,2BACF,GACG,SAASA,EAAA,kBAAkB,OAAO,QAAQ,IAAK,GAAG,CACrD,CAAA,CAAA,EACF,QAEC,IAAE,CAAA,UAAU,qBACV,SAASA,EAAA,iBAAiB,qBAAqB,eAClD,EAEArH,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAkB,qBAAA,QACjD,OAAK,CAAA,UAAU,yBAA0B,SAASoH,EAAA,iBAAiB,iBAAiB,KAAM,CAAA,CAAA,EAC7F,EACArH,EAAAA,KAAC,MAAI,CAAA,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAiB,oBAAA,EACjDD,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAAqH,EAAS,mBAAmB,gBAAgB,MAAA,CAAI,CAAA,CAAA,EACjF,EACArH,EAAAA,KAAC,MAAI,CAAA,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAmB,sBAAA,EACnDD,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAAqH,EAAS,qBAAqB,cAAc,mBAAmB,GAAA,CAAC,CAAA,CAAA,CACjG,CAAA,CAAA,EACF,EAEArH,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACZ,SAAA,CAAAqH,EAAS,iBAAiB,oBAAoB,MAAM,EAAG,CAAC,EAAE,IAAI,CAAChO,EAASoD,UACtE,OAAiB,CAAA,UAAU,0DACzB,SAAQpD,EAAA,SADAoD,CAEX,CACD,EACA4K,EAAS,iBAAiB,oBAAoB,OAAS,GACrDrH,OAAA,OAAA,CAAK,UAAU,sDAAsD,SAAA,CAAA,IAClEqH,EAAS,iBAAiB,oBAAoB,OAAS,EAAE,OAAA,CAC7D,CAAA,CAAA,CAEJ,CAAA,CAAA,CAAA,EAhDKA,EAAS,EAiDhB,EAGI0E,EAAwBnE,GAC3B5H,EAAA,KAAA,MAAA,CAAgC,UAAU,oCACzC,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAkB,qBAAA,EACtEA,EAAAA,IAAC,QAAK,UAAU,+DACb,WAAW,cAAc,QAAQ,IAAK,GAAG,CAC5C,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAoB,uBAAA,EACnEA,EAAA,IAAC,KAAG,CAAA,UAAU,kCACX,SAAA2H,EAAW,mBAAmB,IAAI,CAACoE,EAAmBvP,IACrDuD,EAAA,KAAC,KAAe,CAAA,SAAA,CAAA,KAAGgM,CAAA,CAAV,EAAAvP,CAAoB,CAC9B,CACH,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACwD,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAqB,wBAAA,QACnE,MAAI,CAAA,UAAU,uBACZ,SAAA2H,EAAW,eAAe,IAAI,CAACqE,EAAcxP,UAC3C,OAAiB,CAAA,UAAU,sDACzB,SADQwP,CAAA,EAAAxP,CAEX,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAuD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAA,IAAC,WAAA,CACC,YAAY,oCACZ,UAAU,gFACV,KAAM,EACN,GAAI,YAAY2H,EAAW,UAAU,EAAA,CACvC,EAEA5H,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,oCAAoC,SAAiB,oBAAA,EACtEA,EAAA,IAAC,QAAA,CACC,KAAK,QACL,IAAI,IACJ,IAAI,MACJ,aAAa,KACb,UAAU,SACV,GAAI,cAAc2H,EAAW,UAAU,EAAA,CACzC,EACC3H,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAG,KAAA,CAAA,CAAA,EAC7C,EAEAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM,CACb,MAAM0L,EAAY,SAAS,eAAe,YAAY/D,EAAW,UAAU,EAAE,EAA0B,MACjGgE,EAAa,SAAU,SAAS,eAAe,cAAchE,EAAW,UAAU,EAAE,EAAuB,KAAK,EAC3F6D,EAAA7D,EAAW,WAAY+D,EAAUC,CAAU,CACxE,EACA,UAAU,uEACX,SAAA,mBAAA,CAAA,CAED,CACF,CAAA,CAAA,CAAA,EA7DQhE,EAAW,UA8DrB,EAGIsE,EAAqB,IACxBjM,EAAAA,IAAA,MAAA,CAAI,UAAU,iFACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,wEACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAwB,2BAAA,EACzEA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM+K,EAAoB,EAAK,EACxC,UAAU,oCAEV,SAAA/K,EAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAK,CAAA,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,sBAAuB,CAAA,CAC9F,CAAA,CAAA,CAAA,CACF,EACF,EAECD,EAAAA,KAAA,OAAA,CAAK,SAAWE,GAAM,CACrBA,EAAE,eAAe,EACjB,MAAMqL,EAAW,IAAI,SAASrL,EAAE,MAAyB,EACnD/G,EAAO,OAAO,YAAYoS,EAAS,SAAS,EACjDpS,EAAa,qBAAwBoS,EAAS,IAAI,sBAAsB,IAAiB,KAC1FD,EAAqBnS,CAAI,CAEzB,EAAA,SAAA,CAAC6G,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAAc,iBAAA,EAC9EA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,KAAK,gBACL,SAAQ,GACR,UAAU,gFACV,YAAY,gCAAA,CAAA,CACd,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAAe,kBAAA,EAC/EA,EAAA,IAAC,WAAA,CACC,KAAK,SACL,SAAQ,GACR,KAAM,EACN,UAAU,gFACV,YAAY,iDAAA,CAAA,CACd,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAAqB,wBAAA,EACrFA,EAAA,IAAC,WAAA,CACC,KAAK,eACL,SAAQ,GACR,KAAM,EACN,UAAU,gFACV,YAAY,+CAAA,CAAA,CACd,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAAoB,uBAAA,EACpFA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,KAAK,sBACL,UAAU,gFACV,YAAY,8CAAA,CAAA,CACd,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAAkB,qBAAA,EAClFA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,KAAK,YACL,UAAU,gFACV,YAAY,4CAAA,CAAA,CACd,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,+CAA+C,SAAiB,oBAAA,EACjFD,EAAA,KAAC,SAAA,CACC,KAAK,mBACL,SAAQ,GACR,UAAU,gFAEV,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,MAAM,SAAS,SAAM,SAAA,EAC5BA,EAAA,IAAA,SAAA,CAAO,MAAM,iBAAiB,SAAc,iBAAA,EAC5CA,EAAA,IAAA,SAAA,CAAO,MAAM,aAAa,SAAU,aAAA,EACpCA,EAAA,IAAA,SAAA,CAAO,MAAM,SAAS,SAAM,QAAA,CAAA,CAAA,CAAA,CAAA,CAC/B,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,KAAK,uBACL,GAAG,uBACH,UAAU,MAAA,CACZ,QACC,QAAM,CAAA,QAAQ,uBAAuB,UAAU,wBAAwB,SAExE,iCAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAM+K,EAAoB,EAAK,EACxC,UAAU,oFACX,SAAA,QAAA,CAED,EACA/K,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,uEACX,SAAA,iBAAA,CAAA,CAED,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGIkM,EAAkB,IACtBlM,MAAC,OAAI,UAAU,YACZ,YAEGD,EAAAA,KAAAoM,EAAA,SAAA,CAAA,SAAA,CAACpM,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAiC,oCAAA,EAE5ED,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAAAC,EAAA,IAAC,MAAI,CAAA,UAAU,mCAAoC,SAAAgL,EAAe,eAAe,EAChFhL,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAe,iBAAA,CAAA,CAAA,EACxD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAC,EAAA,IAAC,MAAI,CAAA,UAAU,oCAAqC,SAAAgL,EAAe,mBAAmB,EACrFhL,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAS,WAAA,CAAA,CAAA,EAClD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qCAAsC,SAAA,CAAK,KAAA,MAAMiL,EAAe,eAAe,EAAE,GAAA,EAAC,EAChGhL,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAgB,kBAAA,CAAA,CAAA,EACzD,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qCAAsC,cAAK,MAAMgL,EAAe,sBAAsB,EAAE,EACtGhL,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,SAAoB,sBAAA,CAAA,CAAA,CAC7D,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAqB,wBAAA,QACvD,MAAI,CAAA,UAAU,YACZ,SAAO,OAAA,QAAQgL,EAAe,oBAAoB,EAAE,IAAI,CAAC,CAAC5N,EAASgP,CAAK,IACtErM,EAAAA,KAAA,MAAA,CAAkB,UAAU,uBAC3B,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAyB,SAAQ5C,EAAA,EAChD4C,EAAA,IAAA,OAAA,CAAK,UAAU,cAAe,SAAgBoM,CAAA,CAAA,CAAA,CAFvC,EAAAhP,CAGV,CACD,CACH,CAAA,CAAA,EACF,EAEA2C,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAwB,2BAAA,QAC1D,MAAI,CAAA,UAAU,YACZ,SAAO,OAAA,QAAQgL,EAAe,uBAAuB,EAAE,IAAI,CAAC,CAACjK,EAAOqL,CAAK,IACvErM,EAAAA,KAAA,MAAA,CAAgB,UAAU,uBACzB,SAAA,CAAAC,EAAAA,IAAC,QAAK,UAAU,mCAAoC,WAAM,QAAQ,IAAK,GAAG,EAAE,EAC3EA,EAAA,IAAA,OAAA,CAAK,UAAU,cAAe,SAAgBoM,CAAA,CAAA,CAAA,CAFvC,EAAArL,CAGV,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAhB,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAA2B,8BAAA,EAC7DA,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,SAAegL,EAAA,aAAa,IAAI,CAAC5D,EAAe5K,IAC9CuD,OAAA,MAAA,CAAsB,UAAU,2DAC/B,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,cAAc,SAAA,CAAA,IAAEvD,EAAQ,CAAA,EAAE,EACzCwD,EAAA,IAAA,OAAA,CAAK,UAAU,6BAA8B,WAAS,oBAAqB,CAAA,CAAA,EAC9E,EACAD,EAAAA,KAAC,OAAK,CAAA,UAAU,oCAAqC,SAAA,CAASqH,EAAA,WAAW,aAAA,CAAW,CAAA,CAAA,GAL5EA,EAAS,EAMnB,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEJ,CAAA,EAGF,OAAInJ,EAEA+B,MAAC,OAAI,UAAU,wCACb,eAAC,MAAI,CAAA,UAAU,iEAAiE,CAClF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAA2B,8BAAA,EAChFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,kFAAA,CAAA,CAAA,EACF,EAECwK,IAAa,2BACZxK,EAAA,IAAC,SAAA,CACC,QAAS,IAAM+K,EAAoB,EAAI,EACvC,UAAU,4EACX,SAAA,qBAAA,CAAA,CAED,EAEJ,EAGA/K,EAAAA,IAAC,MAAI,CAAA,UAAU,iDACZ,SAAA,CACC,CAAE,IAAK,eAAgB,MAAO,eAAgB,KAAM,KAAM,KAAMwK,IAAa,yBAA0B,EACvG,CAAE,IAAK,iBAAkB,MAAO,iBAAkB,KAAM,IAAK,KAAMA,IAAa,QAAS,EACzF,CAAE,IAAK,YAAa,MAAO,YAAa,KAAM,IAAK,KAAM,EAAK,EAC9D,CAAE,IAAK,YAAa,MAAO,YAAa,KAAM,KAAM,KAAM,EAAK,CAAA,EAC/D,OAAOjK,GAAOA,EAAI,IAAI,EAAE,IACxBA,GAAAR,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMkG,EAAa1F,EAAI,GAAU,EAC1C,UAAW,gHACTyF,IAAczF,EAAI,IACd,mCACA,mCACN,GAEA,SAAA,CAACP,EAAAA,IAAA,OAAA,CAAM,WAAI,IAAK,CAAA,EAChBA,EAAAA,IAAC,OAAM,CAAA,SAAAO,EAAI,KAAM,CAAA,CAAA,CAAA,EATZA,EAAI,GAWZ,CAAA,EACH,EAGCyF,IAAc,gBACbjG,EAAAA,KAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,8BAA8B,SAAA,CAAA,mBAAiBsK,EAAU,OAAO,GAAA,EAAC,EAC9EA,EAAU,OAAS,EAClBrK,EAAA,IAAC,OAAI,UAAU,uDACZ,SAAUqK,EAAA,IAAIwB,CAAkB,CACnC,CAAA,EAEC9L,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAgB,mBAAA,EACtEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAAkD,qDAAA,EACpFA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM+K,EAAoB,EAAI,EACvC,UAAU,gEACX,SAAA,4BAAA,CAAA,CAED,CACF,CAAA,CAAA,EAEJ,EAGD/E,IAAc,kBACbjG,EAAAA,KAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,8BAA8B,SAAA,CAAA,wBAAsB2K,EAAmB,OAAO,GAAA,EAAC,EAC5FA,EAAmB,OAAS,EAC3B1K,EAAA,IAAC,OAAI,UAAU,wCACZ,SAAmB0K,EAAA,IAAIoB,CAAoB,CAC9C,CAAA,EAEC/L,EAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAAsB,yBAAA,EAC5EA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAA2C,6CAAA,CAAA,CAAA,CAC1E,CAAA,CAAA,EAEJ,EAGDgG,IAAc,aACbjG,EAAAA,KAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAmB,sBAAA,EAC/DA,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,gBAAgB,SAAA,4CAA0C,CAAA,CACzE,CAAA,CAAA,EACF,EAGDgG,IAAc,aAAekG,EAAgB,EAG7CpB,GAAoBmB,EAAmB,EAGvCrB,SACE,MAAI,CAAA,UAAU,iFACb,SAAC7K,EAAAA,KAAA,MAAA,CAAI,UAAU,wEACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAgB,mBAAA,EACjEA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM6K,EAAoB,IAAI,EACvC,UAAU,oCAEV,SAAA7K,EAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAK,CAAA,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,sBAAuB,CAAA,CAC9F,CAAA,CAAA,CAAA,CACF,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAqB,wBAAA,QACvD,IAAE,CAAA,UAAU,gBAAiB,SAAiB4K,EAAA,iBAAiB,qBAAqB,cAAe,CAAA,CAAA,EACtG,SAEC,MACC,CAAA,SAAA,CAAC5K,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAoB,uBAAA,QACtD,MAAI,CAAA,UAAU,uBACZ,SAAiB4K,EAAA,iBAAiB,oBAAoB,IAAI,CAACxR,EAASoD,IACnEwD,MAAC,QAAiB,UAAU,+DACzB,WAAQ,OADA,EAAAxD,CAEX,CACD,CACH,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACwD,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAiB,oBAAA,EACpDD,EAAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAiB,oBAAA,EACzDD,EAAAA,KAAC,OAAK,CAAA,UAAU,mBAAoB,SAAA,CAAA6K,EAAiB,mBAAmB,gBAAgB,MAAA,CAAI,CAAA,CAAA,EAC9F,SACC,MACC,CAAA,SAAA,CAAC5K,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAkB,qBAAA,EAC1DD,EAAAA,KAAC,OAAK,CAAA,UAAU,mBAAoB,SAAA,CAAA6K,EAAiB,mBAAmB,uBAAuB,MAAA,CAAI,CAAA,CAAA,CACrG,CAAA,CAAA,CACF,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAC5K,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAkB,qBAAA,EACrDD,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAc,iBAAA,EACtDD,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAA6K,EAAiB,qBAAqB,cAAc,mBAAmB,GAAA,CAAC,CAAA,CAAA,EACzG,EACA7K,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAY,eAAA,QACnD,OAAK,CAAA,UAAU,cAAe,SAAiB4K,EAAA,qBAAqB,cAAc,UAAW,CAAA,CAAA,CAChG,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EAEJ,CAEJ,EC7lBMyB,EAAkE,CAAC,CACvE,OAAA5R,EACA,SAAA+P,EAAW,kBACb,IAAM,CACJ,KAAM,CAAC8B,EAAeC,CAAgB,EAAI9O,EAAAA,SAAiD,SAAS,EAE9F+O,EAAuB,IAAM,CACjC,OAAQF,EAAe,CACrB,IAAK,UACI,OAAAtM,MAACzC,GAAmB,OAAA9C,EAAgB,EAC7C,IAAK,cACI,OAAAuF,MAACqF,GAAqB,OAAA5K,EAAgB,EAC/C,IAAK,WACI,OAAAuF,EAAA,IAACuK,EAA0B,CAAA,OAAA9P,EAAgB,SAAA+P,CAAoB,CAAA,EACxE,QACS,OAAAxK,MAACzC,GAAmB,OAAA9C,EAAgB,CAAA,CAEjD,EAGE,OAAAsF,EAAA,KAAC,MAAI,CAAA,UAAU,0BAEb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,kBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,yCACb,SAAAD,OAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAkC,qCAAA,EAClFA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,kFAAA,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,8BACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAAS,IAAMuM,EAAiB,SAAS,EACzC,UAAW,8DACTD,IAAkB,UACd,mCACA,mCACN,GACD,SAAA,SAAA,CAED,EACAtM,EAAA,IAAC,SAAA,CACC,QAAS,IAAMuM,EAAiB,aAAa,EAC7C,UAAW,8DACTD,IAAkB,cACd,mCACA,mCACN,GACD,SAAA,aAAA,CAED,EACAtM,EAAA,IAAC,SAAA,CACC,QAAS,IAAMuM,EAAiB,UAAU,EAC1C,UAAW,8DACTD,IAAkB,WACd,mCACA,mCACN,GACD,SAAA,UAAA,CAAA,CAED,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,sCACb,SAACvM,EAAA,KAAA,MAAA,CAAI,UAAU,8CACZ,SAAA,CAAAuM,IAAkB,WACjBvM,OAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC5E,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,2JAA4J,CAAA,CAAA,CACnO,CACF,CAAA,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,oCAAoC,SAAiC,oCAAA,EAClFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAG7B,0MAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGDsM,IAAkB,eAChBvM,OAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC5E,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,yaAA0a,CAAA,CAAA,CACjf,CACF,CAAA,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,oCAAoC,SAAqC,wCAAA,EACtFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAG7B,wLAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGDsM,IAAkB,YAChBvM,OAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC5E,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6IAA8I,CAAA,CAAA,CACrN,CACF,CAAA,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,oCAAoC,SAA4C,+CAAA,EAC7FA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAG7B,qLAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,EAGCA,EAAA,IAAA,MAAA,CAAI,UAAU,OACZ,aACH,QAGC,MAAI,CAAA,UAAU,yBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,8CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAqC,wCAAA,EAC/EA,EAAA,IAAA,IAAA,CAAE,UAAU,kCAAkC,SAK/C,2SAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,mFACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC9D,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,2JAA4J,CAAA,CAAA,CACnO,CACF,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAoB,uBAAA,EACpDA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,sEAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,oFACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC9D,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,yaAA0a,CAAA,CAAA,CACjf,CACF,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAgB,mBAAA,EAChDA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,0EAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,qFACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC9D,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6IAA8I,CAAA,CAAA,CACrN,CACF,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAiB,oBAAA,EACjDA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,uEAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,qCACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAsB,yBAAA,EACvDD,EAAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,qCAAqC,SAAqB,wBAAA,EACzEA,EAAA,IAAA,OAAA,CAAK,UAAU,qCAAqC,SAAiB,oBAAA,EACrEA,EAAA,IAAA,OAAA,CAAK,UAAU,qCAAqC,SAAwB,2BAAA,EAC5EA,EAAA,IAAA,OAAA,CAAK,UAAU,qCAAqC,SAAuB,0BAAA,EAC3EA,EAAA,IAAA,OAAA,CAAK,UAAU,qCAAqC,SAA0B,6BAAA,EAC9EA,EAAA,IAAA,OAAA,CAAK,UAAU,qCAAqC,SAAoB,uBAAA,EACxEA,EAAA,IAAA,OAAA,CAAK,UAAU,qCAAqC,SAAiC,mCAAA,CAAA,CAAA,CACxF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ"}