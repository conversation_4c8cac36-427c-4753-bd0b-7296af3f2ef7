{"version": 3, "file": "CrossCulturalCollaborationPage-CeaGM6MI.js", "sources": ["../../src/services/crossCulturalProjectService.ts", "../../src/components/CrossCulturalProjectDashboard.tsx", "../../src/services/realTimeCommunicationService.ts", "../../src/components/CrossCulturalCommunication.tsx", "../../src/services/culturalEventService.ts", "../../src/components/CulturalEventDashboard.tsx", "../../src/pages/CrossCulturalCollaborationPage.tsx"], "sourcesContent": ["import { \n  CrossCulturalProject, \n  ProjectTeam, \n  ProjectTask, \n  CulturalCollaboration,\n  ImpactMetrics,\n  ProjectMember,\n  CulturalContext,\n  TeamRecommendation,\n  ProjectObjective,\n  CulturalMilestone\n} from '../types/crossCulturalProject';\n\nexport interface ProjectCreationData {\n  title: string;\n  description: string;\n  vision: string;\n  objectives: ProjectObjective[];\n  culturalContext: CulturalContext;\n  requiredSkills: string[];\n  targetCommunities: string[];\n  timeline: {\n    startDate: Date;\n    endDate: Date;\n    culturalMilestones: CulturalMilestone[];\n  };\n}\n\nexport interface TeamFormationRequest {\n  projectId: string;\n  requiredSkills: string[];\n  culturalRepresentation: string[];\n  commitmentLevel: 'full_time' | 'part_time' | 'consulting' | 'advisory';\n}\n\nexport interface TaskCreationData {\n  projectId: string;\n  title: string;\n  description: string;\n  culturalContext: CulturalContext;\n  requiredSkills: string[];\n  culturalRequirements: string[];\n  priority: 'low' | 'medium' | 'high' | 'critical';\n  estimatedHours: number;\n  dueDate: Date;\n}\n\nclass CrossCulturalProjectService {\n  private projects: Map<string, CrossCulturalProject> = new Map();\n  private tasks: Map<string, ProjectTask[]> = new Map();\n  private teams: Map<string, ProjectTeam> = new Map();\n\n  // Project Management\n  async createProject(data: ProjectCreationData, createdBy: string): Promise<CrossCulturalProject> {\n    const project: CrossCulturalProject = {\n      id: this.generateId(),\n      title: data.title,\n      description: data.description,\n      vision: data.vision,\n      objectives: data.objectives,\n      culturalContext: {\n        primaryCultures: data.culturalContext.primaryCultures || [],\n        culturalObjectives: data.culturalContext.culturalObjectives || [],\n        culturalSensitivities: data.culturalContext.culturalSensitivities || [],\n        traditionalKnowledgeIntegration: data.culturalContext.traditionalKnowledgeIntegration || false,\n        languageRequirements: data.culturalContext.languageRequirements || [],\n        culturalMilestones: data.timeline.culturalMilestones,\n        communityEndorsements: []\n      },\n      requiredSkills: data.requiredSkills.map(skill => ({ skill, level: 'intermediate', priority: 'medium' })),\n      targetCommunities: data.targetCommunities.map(community => ({ \n        community, \n        role: 'participant', \n        representation: 'equal' \n      })),\n      timeline: {\n        startDate: data.timeline.startDate,\n        endDate: data.timeline.endDate,\n        phases: [],\n        culturalEvents: data.timeline.culturalMilestones\n      },\n      status: 'planning',\n      impact: this.initializeImpactMetrics(),\n      team: this.initializeTeam(),\n      culturalGuidelines: [],\n      collaborationProtocols: [],\n      createdBy,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    };\n\n    this.projects.set(project.id, project);\n    this.tasks.set(project.id, []);\n    this.teams.set(project.id, project.team);\n\n    return project;\n  }\n\n  async getProject(projectId: string): Promise<CrossCulturalProject | null> {\n    return this.projects.get(projectId) || null;\n  }\n\n  async updateProject(projectId: string, updates: Partial<CrossCulturalProject>): Promise<CrossCulturalProject> {\n    const project = this.projects.get(projectId);\n    if (!project) {\n      throw new Error('Project not found');\n    }\n\n    const updatedProject = {\n      ...project,\n      ...updates,\n      updatedAt: new Date()\n    };\n\n    this.projects.set(projectId, updatedProject);\n    return updatedProject;\n  }\n\n  async deleteProject(projectId: string): Promise<boolean> {\n    const deleted = this.projects.delete(projectId);\n    this.tasks.delete(projectId);\n    this.teams.delete(projectId);\n    return deleted;\n  }\n\n  async getProjectsByUser(userId: string): Promise<CrossCulturalProject[]> {\n    return Array.from(this.projects.values()).filter(project => \n      project.createdBy === userId || \n      project.team.members.some(member => member.userId === userId)\n    );\n  }\n\n  async getProjectsByCulturalContext(culturalContext: string): Promise<CrossCulturalProject[]> {\n    return Array.from(this.projects.values()).filter(project =>\n      project.culturalContext.primaryCultures.includes(culturalContext)\n    );\n  }\n\n  // Team Formation & Management\n  async recommendTeamComposition(projectId: string): Promise<TeamRecommendation[]> {\n    const project = this.projects.get(projectId);\n    if (!project) {\n      throw new Error('Project not found');\n    }\n\n    // Simulate intelligent team composition recommendations\n    const recommendations: TeamRecommendation[] = [\n      {\n        userId: 'user1',\n        culturalBackground: 'Zulu',\n        skills: ['project_management', 'community_engagement'],\n        culturalExpertise: ['traditional_governance', 'ubuntu_philosophy'],\n        recommendationScore: 95,\n        role: 'project_lead',\n        culturalContribution: 'Leadership in Ubuntu principles and traditional decision-making'\n      },\n      {\n        userId: 'user2',\n        culturalBackground: 'Afrikaans',\n        skills: ['technical_development', 'innovation'],\n        culturalExpertise: ['agricultural_innovation', 'sustainable_practices'],\n        recommendationScore: 88,\n        role: 'skill_expert',\n        culturalContribution: 'Technical innovation with cultural sustainability focus'\n      },\n      {\n        userId: 'user3',\n        culturalBackground: 'Xhosa',\n        skills: ['community_liaison', 'cultural_preservation'],\n        culturalExpertise: ['oral_traditions', 'cultural_ceremonies'],\n        recommendationScore: 92,\n        role: 'cultural_representative',\n        culturalContribution: 'Cultural preservation and traditional knowledge integration'\n      }\n    ];\n\n    return recommendations;\n  }\n\n  async addTeamMember(projectId: string, member: ProjectMember): Promise<ProjectTeam> {\n    const team = this.teams.get(projectId);\n    if (!team) {\n      throw new Error('Project team not found');\n    }\n\n    team.members.push({\n      ...member,\n      joinedAt: new Date()\n    });\n\n    // Update cultural diversity score\n    team.culturalDiversityScore = this.calculateCulturalDiversityScore(team.members);\n\n    this.teams.set(projectId, team);\n    return team;\n  }\n\n  async removeTeamMember(projectId: string, userId: string): Promise<ProjectTeam> {\n    const team = this.teams.get(projectId);\n    if (!team) {\n      throw new Error('Project team not found');\n    }\n\n    team.members = team.members.filter(member => member.userId !== userId);\n    team.culturalDiversityScore = this.calculateCulturalDiversityScore(team.members);\n\n    this.teams.set(projectId, team);\n    return team;\n  }\n\n  // Task Management\n  async createTask(data: TaskCreationData): Promise<ProjectTask> {\n    const task: ProjectTask = {\n      id: this.generateId(),\n      projectId: data.projectId,\n      title: data.title,\n      description: data.description,\n      culturalContext: {\n        culturalRequirements: data.culturalRequirements,\n        culturalSensitivities: [],\n        culturalLearningObjectives: []\n      },\n      assignedTo: [],\n      requiredSkills: data.requiredSkills,\n      culturalRequirements: data.culturalRequirements.map(req => ({ \n        requirement: req, \n        priority: 'medium' \n      })),\n      priority: data.priority,\n      status: 'backlog',\n      estimatedHours: data.estimatedHours,\n      culturalLearning: [],\n      dependencies: [],\n      dueDate: data.dueDate,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    };\n\n    const projectTasks = this.tasks.get(data.projectId) || [];\n    projectTasks.push(task);\n    this.tasks.set(data.projectId, projectTasks);\n\n    return task;\n  }\n\n  async getProjectTasks(projectId: string): Promise<ProjectTask[]> {\n    return this.tasks.get(projectId) || [];\n  }\n\n  async updateTask(taskId: string, updates: Partial<ProjectTask>): Promise<ProjectTask> {\n    for (const [projectId, tasks] of this.tasks.entries()) {\n      const taskIndex = tasks.findIndex(task => task.id === taskId);\n      if (taskIndex !== -1) {\n        tasks[taskIndex] = {\n          ...tasks[taskIndex],\n          ...updates,\n          updatedAt: new Date()\n        };\n        this.tasks.set(projectId, tasks);\n        return tasks[taskIndex];\n      }\n    }\n    throw new Error('Task not found');\n  }\n\n  // Impact Tracking\n  async updateImpactMetrics(projectId: string, metrics: Partial<ImpactMetrics>): Promise<ImpactMetrics> {\n    const project = this.projects.get(projectId);\n    if (!project) {\n      throw new Error('Project not found');\n    }\n\n    project.impact = {\n      ...project.impact,\n      ...metrics\n    };\n\n    this.projects.set(projectId, project);\n    return project.impact;\n  }\n\n  async getCulturalImpactAssessment(projectId: string): Promise<any> {\n    const project = this.projects.get(projectId);\n    if (!project) {\n      throw new Error('Project not found');\n    }\n\n    return {\n      culturalPreservation: project.impact.culturalPreservation,\n      crossCulturalLearning: project.impact.learningOutcomes,\n      communityEngagement: project.impact.communityReach,\n      culturalInnovation: project.impact.culturalImpact\n    };\n  }\n\n  // Helper Methods\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n\n  private initializeImpactMetrics(): ImpactMetrics {\n    return {\n      communityReach: { communities: 0, individuals: 0, regions: [] },\n      culturalImpact: { preservationScore: 0, innovationScore: 0, exchangeScore: 0 },\n      socialImpact: { relationshipsFormed: 0, understandingImproved: 0, conflictsResolved: 0 },\n      economicImpact: { opportunitiesCreated: 0, valueGenerated: 0, sustainabilityScore: 0 },\n      learningOutcomes: [],\n      sustainabilityMetrics: [],\n      culturalPreservation: []\n    };\n  }\n\n  private initializeTeam(): ProjectTeam {\n    return {\n      members: [],\n      culturalRepresentatives: [],\n      skillsMatrix: { skills: [], coverage: 0 },\n      culturalDiversityScore: 0,\n      communicationPreferences: [],\n      decisionMakingProcess: { type: 'consensus', culturalConsiderations: [] },\n      conflictResolutionProtocol: { approach: 'mediation', culturalAdaptations: [] }\n    };\n  }\n\n  private calculateCulturalDiversityScore(members: ProjectMember[]): number {\n    const cultures = new Set(members.map(member => member.culturalBackground.primaryCulture));\n    const maxCultures = 11; // South Africa has 11 official languages/cultures\n    return Math.min((cultures.size / maxCultures) * 100, 100);\n  }\n}\n\nexport const crossCulturalProjectService = new CrossCulturalProjectService();\n", "import React, { useState, useEffect } from 'react';\nimport { \n  CrossCulturalProject, \n  ProjectTask, \n  ProjectMember, \n  TeamRecommendation \n} from '../types/crossCulturalProject';\nimport { crossCulturalProjectService } from '../services/crossCulturalProjectService';\n\ninterface CrossCulturalProjectDashboardProps {\n  userId: string;\n}\n\nconst CrossCulturalProjectDashboard: React.FC<CrossCulturalProjectDashboardProps> = ({ userId }) => {\n  const [projects, setProjects] = useState<CrossCulturalProject[]>([]);\n  const [selectedProject, setSelectedProject] = useState<CrossCulturalProject | null>(null);\n  const [projectTasks, setProjectTasks] = useState<ProjectTask[]>([]);\n  const [teamRecommendations, setTeamRecommendations] = useState<TeamRecommendation[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState<'overview' | 'tasks' | 'team' | 'impact'>('overview');\n\n  useEffect(() => {\n    loadUserProjects();\n  }, [userId]);\n\n  useEffect(() => {\n    if (selectedProject) {\n      loadProjectDetails(selectedProject.id);\n    }\n  }, [selectedProject]);\n\n  const loadUserProjects = async () => {\n    try {\n      setLoading(true);\n      const userProjects = await crossCulturalProjectService.getProjectsByUser(userId);\n      setProjects(userProjects);\n      if (userProjects.length > 0) {\n        setSelectedProject(userProjects[0]);\n      }\n    } catch (error) {\n      console.error('Error loading projects:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadProjectDetails = async (projectId: string) => {\n    try {\n      const tasks = await crossCulturalProjectService.getProjectTasks(projectId);\n      const recommendations = await crossCulturalProjectService.recommendTeamComposition(projectId);\n      setProjectTasks(tasks);\n      setTeamRecommendations(recommendations);\n    } catch (error) {\n      console.error('Error loading project details:', error);\n    }\n  };\n\n  const handleCreateProject = async () => {\n    // This would open a project creation modal\n    console.log('Create new cross-cultural project');\n  };\n\n  const handleJoinTeam = async (recommendation: TeamRecommendation) => {\n    if (!selectedProject) return;\n    \n    try {\n      const newMember: ProjectMember = {\n        userId: recommendation.userId,\n        role: recommendation.role as any,\n        culturalBackground: {\n          primaryCulture: recommendation.culturalBackground,\n          secondaryCultures: [],\n          languages: [],\n          culturalExpertise: recommendation.culturalExpertise,\n          traditionalKnowledge: []\n        },\n        skillContributions: recommendation.skills.map(skill => ({\n          skill,\n          level: 'intermediate',\n          experience: '2+ years',\n          culturalContext: recommendation.culturalBackground\n        })),\n        availabilityPattern: {\n          hoursPerWeek: 20,\n          preferredDays: ['Monday', 'Wednesday', 'Friday'],\n          timeZone: 'Africa/Johannesburg',\n          culturalConstraints: []\n        },\n        culturalResponsibilities: [{\n          responsibility: recommendation.culturalContribution,\n          description: 'Cultural guidance and representation',\n          culturalContext: recommendation.culturalBackground,\n          accountability: 'Team and community'\n        }],\n        joinedAt: new Date(),\n        commitmentLevel: 'part_time'\n      };\n\n      await crossCulturalProjectService.addTeamMember(selectedProject.id, newMember);\n      await loadProjectDetails(selectedProject.id);\n    } catch (error) {\n      console.error('Error joining team:', error);\n    }\n  };\n\n  const renderProjectOverview = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Project Vision</h3>\n        <p className=\"text-gray-700 mb-4\">{selectedProject?.vision}</p>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-2\">Cultural Context</h4>\n            <div className=\"flex flex-wrap gap-2\">\n              {selectedProject?.culturalContext.primaryCultures.map((culture, index) => (\n                <span key={index} className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm\">\n                  {culture}\n                </span>\n              ))}\n            </div>\n          </div>\n          \n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-2\">Target Communities</h4>\n            <div className=\"flex flex-wrap gap-2\">\n              {selectedProject?.targetCommunities.map((community, index) => (\n                <span key={index} className=\"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm\">\n                  {community.community}\n                </span>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Cultural Milestones</h3>\n        <div className=\"space-y-3\">\n          {selectedProject?.culturalContext.culturalMilestones.map((milestone, index) => (\n            <div key={index} className=\"border-l-4 border-orange-400 pl-4\">\n              <h4 className=\"font-medium text-gray-900\">{milestone.title}</h4>\n              <p className=\"text-sm text-gray-600\">{milestone.culturalSignificance}</p>\n              <p className=\"text-xs text-gray-500\">Target: {milestone.targetDate.toLocaleDateString()}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderProjectTasks = () => (\n    <div className=\"space-y-4\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Project Tasks</h3>\n        <button className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\">\n          Add Task\n        </button>\n      </div>\n      \n      <div className=\"grid gap-4\">\n        {projectTasks.map((task) => (\n          <div key={task.id} className=\"bg-white rounded-lg shadow p-4\">\n            <div className=\"flex justify-between items-start mb-2\">\n              <h4 className=\"font-medium text-gray-900\">{task.title}</h4>\n              <span className={`px-2 py-1 rounded-full text-xs ${\n                task.status === 'completed' ? 'bg-green-100 text-green-800' :\n                task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :\n                task.status === 'review' ? 'bg-yellow-100 text-yellow-800' :\n                'bg-gray-100 text-gray-800'\n              }`}>\n                {task.status.replace('_', ' ')}\n              </span>\n            </div>\n            \n            <p className=\"text-sm text-gray-600 mb-3\">{task.description}</p>\n            \n            <div className=\"flex flex-wrap gap-2 mb-3\">\n              {task.culturalRequirements.map((req, index) => (\n                <span key={index} className=\"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs\">\n                  {req.requirement}\n                </span>\n              ))}\n            </div>\n            \n            <div className=\"flex justify-between items-center text-sm text-gray-500\">\n              <span>Due: {task.dueDate.toLocaleDateString()}</span>\n              <span>{task.estimatedHours}h estimated</span>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const renderTeamManagement = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Current Team</h3>\n        <div className=\"grid gap-4\">\n          {selectedProject?.team.members.map((member, index) => (\n            <div key={index} className=\"flex items-center justify-between p-4 border rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-gray-900\">{member.role.replace('_', ' ')}</h4>\n                <p className=\"text-sm text-gray-600\">\n                  {member.culturalBackground.primaryCulture} • {member.commitmentLevel.replace('_', ' ')}\n                </p>\n                <div className=\"flex flex-wrap gap-1 mt-2\">\n                  {member.skillContributions.slice(0, 3).map((skill, skillIndex) => (\n                    <span key={skillIndex} className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs\">\n                      {skill.skill}\n                    </span>\n                  ))}\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <p className=\"text-sm text-gray-500\">Joined {member.joinedAt.toLocaleDateString()}</p>\n                <p className=\"text-xs text-gray-400\">{member.availabilityPattern.hoursPerWeek}h/week</p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recommended Team Members</h3>\n        <div className=\"grid gap-4\">\n          {teamRecommendations.map((recommendation, index) => (\n            <div key={index} className=\"p-4 border rounded-lg\">\n              <div className=\"flex justify-between items-start mb-2\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">{recommendation.role.replace('_', ' ')}</h4>\n                  <p className=\"text-sm text-gray-600\">{recommendation.culturalBackground}</p>\n                </div>\n                <div className=\"text-right\">\n                  <span className=\"text-lg font-semibold text-green-600\">{recommendation.recommendationScore}%</span>\n                  <p className=\"text-xs text-gray-500\">match</p>\n                </div>\n              </div>\n              \n              <p className=\"text-sm text-gray-700 mb-3\">{recommendation.culturalContribution}</p>\n              \n              <div className=\"flex flex-wrap gap-2 mb-3\">\n                {recommendation.skills.map((skill, skillIndex) => (\n                  <span key={skillIndex} className=\"px-2 py-1 bg-green-100 text-green-800 rounded text-xs\">\n                    {skill}\n                  </span>\n                ))}\n              </div>\n              \n              <button \n                onClick={() => handleJoinTeam(recommendation)}\n                className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n              >\n                Invite to Team\n              </button>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderImpactTracking = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Community Reach</h3>\n          <p className=\"text-3xl font-bold text-blue-600\">{selectedProject?.impact.communityReach.communities}</p>\n          <p className=\"text-sm text-gray-600\">Communities Engaged</p>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Cultural Impact</h3>\n          <p className=\"text-3xl font-bold text-green-600\">{selectedProject?.impact.culturalImpact.preservationScore}%</p>\n          <p className=\"text-sm text-gray-600\">Preservation Score</p>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Social Impact</h3>\n          <p className=\"text-3xl font-bold text-purple-600\">{selectedProject?.impact.socialImpact.relationshipsFormed}</p>\n          <p className=\"text-sm text-gray-600\">Relationships Formed</p>\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Cultural Learning Outcomes</h3>\n        <div className=\"space-y-3\">\n          {selectedProject?.impact.learningOutcomes.map((outcome, index) => (\n            <div key={index} className=\"border-l-4 border-indigo-400 pl-4\">\n              <h4 className=\"font-medium text-gray-900\">{outcome.outcome}</h4>\n              <p className=\"text-sm text-gray-600\">{outcome.culturalContext}</p>\n              <p className=\"text-xs text-gray-500\">Type: {outcome.learningType}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      <div className=\"flex justify-between items-center mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">Cross-Cultural Projects</h1>\n        <button \n          onClick={handleCreateProject}\n          className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium\"\n        >\n          Create New Project\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n        {/* Project Sidebar */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"p-4 border-b\">\n              <h2 className=\"font-semibold text-gray-900\">Your Projects</h2>\n            </div>\n            <div className=\"p-4 space-y-2\">\n              {projects.map((project) => (\n                <button\n                  key={project.id}\n                  onClick={() => setSelectedProject(project)}\n                  className={`w-full text-left p-3 rounded-lg transition-colors ${\n                    selectedProject?.id === project.id \n                      ? 'bg-blue-100 text-blue-900' \n                      : 'hover:bg-gray-100'\n                  }`}\n                >\n                  <h3 className=\"font-medium truncate\">{project.title}</h3>\n                  <p className=\"text-sm text-gray-600 truncate\">{project.status}</p>\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"lg:col-span-3\">\n          {selectedProject && (\n            <>\n              {/* Project Header */}\n              <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">{selectedProject.title}</h2>\n                <p className=\"text-gray-600 mb-4\">{selectedProject.description}</p>\n                \n                <div className=\"flex items-center space-x-4\">\n                  <span className={`px-3 py-1 rounded-full text-sm ${\n                    selectedProject.status === 'active' ? 'bg-green-100 text-green-800' :\n                    selectedProject.status === 'planning' ? 'bg-yellow-100 text-yellow-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {selectedProject.status}\n                  </span>\n                  <span className=\"text-sm text-gray-500\">\n                    Cultural Diversity: {selectedProject.team.culturalDiversityScore}%\n                  </span>\n                </div>\n              </div>\n\n              {/* Tabs */}\n              <div className=\"bg-white rounded-lg shadow\">\n                <div className=\"border-b border-gray-200\">\n                  <nav className=\"flex space-x-8 px-6\">\n                    {[\n                      { key: 'overview', label: 'Overview' },\n                      { key: 'tasks', label: 'Tasks' },\n                      { key: 'team', label: 'Team' },\n                      { key: 'impact', label: 'Impact' }\n                    ].map((tab) => (\n                      <button\n                        key={tab.key}\n                        onClick={() => setActiveTab(tab.key as any)}\n                        className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                          activeTab === tab.key\n                            ? 'border-blue-500 text-blue-600'\n                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                        }`}\n                      >\n                        {tab.label}\n                      </button>\n                    ))}\n                  </nav>\n                </div>\n\n                <div className=\"p-6\">\n                  {activeTab === 'overview' && renderProjectOverview()}\n                  {activeTab === 'tasks' && renderProjectTasks()}\n                  {activeTab === 'team' && renderTeamManagement()}\n                  {activeTab === 'impact' && renderImpactTracking()}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CrossCulturalProjectDashboard;\n", "import { \n  CrossCulturalConversation,\n  CrossCulturalMessage,\n  MessageTranslation,\n  CulturalContext,\n  VideoConference,\n  CulturalMediaShare,\n  CommunicationAnalytics\n} from '../types/communication';\n\nexport interface ConversationCreationData {\n  participants: string[];\n  conversationType: 'direct_message' | 'group_chat' | 'project_channel' | 'video_call' | 'audio_call';\n  culturalContext: CulturalContext;\n  translationEnabled: boolean;\n}\n\nexport interface MessageData {\n  text: string;\n  language: string;\n  culturalContext?: CulturalContext;\n  messageType: 'text' | 'voice' | 'video' | 'file' | 'cultural_artifact';\n}\n\nexport interface TranslationRequest {\n  text: string;\n  sourceLanguage: string;\n  targetLanguage: string;\n  culturalContext: CulturalContext;\n  preserveCulturalNuance: boolean;\n}\n\nexport interface VideoConferenceData {\n  participants: string[];\n  culturalFeatures: {\n    translationEnabled: boolean;\n    culturalBackgroundSharing: boolean;\n    culturalEtiquetteGuidance: boolean;\n  };\n  scheduledTime?: Date;\n}\n\nclass RealTimeCommunicationService {\n  private conversations: Map<string, CrossCulturalConversation> = new Map();\n  private messages: Map<string, CrossCulturalMessage[]> = new Map();\n  private videoConferences: Map<string, VideoConference> = new Map();\n  private websockets: Map<string, WebSocket> = new Map();\n\n  // Conversation Management\n  async createConversation(data: ConversationCreationData, createdBy: string): Promise<CrossCulturalConversation> {\n    const conversation: CrossCulturalConversation = {\n      id: this.generateId(),\n      participants: data.participants.map(userId => ({\n        userId,\n        culturalBackground: this.getUserCulturalBackground(userId),\n        preferredLanguage: this.getUserPreferredLanguage(userId),\n        communicationPreferences: this.getUserCommunicationPreferences(userId),\n        culturalRole: 'participant',\n        joinedAt: new Date(),\n        lastSeen: new Date(),\n        permissions: { canTranslate: true, canModerate: false, canInvite: true }\n      })),\n      conversationType: data.conversationType,\n      primaryLanguages: this.extractPrimaryLanguages(data.participants),\n      culturalContext: {\n        conversationCultures: data.culturalContext.conversationCultures || [],\n        culturalSensitivities: data.culturalContext.culturalSensitivities || [],\n        communicationProtocols: data.culturalContext.communicationProtocols || [],\n        translationPreferences: data.culturalContext.translationPreferences || {}\n      },\n      translationSettings: {\n        enabled: data.translationEnabled,\n        autoTranslate: true,\n        preserveCulturalContext: true,\n        supportedLanguages: ['en', 'af', 'zu', 'xh', 'st', 'tn', 'ss', 've', 'ts', 'nr', 'nd']\n      },\n      culturalGuidanceEnabled: true,\n      communicationStyle: this.determineCommunicationStyle(data.participants),\n      messages: [],\n      culturalMoments: [],\n      createdAt: new Date(),\n      lastActivity: new Date(),\n      archiveSettings: { autoArchive: false, retentionPeriod: 365 }\n    };\n\n    this.conversations.set(conversation.id, conversation);\n    this.messages.set(conversation.id, []);\n\n    return conversation;\n  }\n\n  async getConversation(conversationId: string): Promise<CrossCulturalConversation | null> {\n    return this.conversations.get(conversationId) || null;\n  }\n\n  async updateConversationSettings(\n    conversationId: string, \n    settings: Partial<CrossCulturalConversation>\n  ): Promise<CrossCulturalConversation> {\n    const conversation = this.conversations.get(conversationId);\n    if (!conversation) {\n      throw new Error('Conversation not found');\n    }\n\n    const updatedConversation = {\n      ...conversation,\n      ...settings,\n      lastActivity: new Date()\n    };\n\n    this.conversations.set(conversationId, updatedConversation);\n    return updatedConversation;\n  }\n\n  // Real-Time Messaging\n  async sendMessage(\n    conversationId: string, \n    senderId: string, \n    messageData: MessageData\n  ): Promise<CrossCulturalMessage> {\n    const conversation = this.conversations.get(conversationId);\n    if (!conversation) {\n      throw new Error('Conversation not found');\n    }\n\n    const message: CrossCulturalMessage = {\n      id: this.generateId(),\n      conversationId,\n      senderId,\n      originalText: messageData.text,\n      originalLanguage: messageData.language,\n      translations: await this.generateTranslations(messageData, conversation),\n      culturalContext: await this.analyzeCulturalContext(messageData.text, messageData.language),\n      messageType: messageData.messageType,\n      culturalAnnotations: await this.generateCulturalAnnotations(messageData.text, messageData.language),\n      reactions: [],\n      timestamp: new Date(),\n      editHistory: [],\n      moderationFlags: []\n    };\n\n    const conversationMessages = this.messages.get(conversationId) || [];\n    conversationMessages.push(message);\n    this.messages.set(conversationId, conversationMessages);\n\n    // Update conversation last activity\n    conversation.lastActivity = new Date();\n    this.conversations.set(conversationId, conversation);\n\n    // Broadcast to all participants via WebSocket\n    await this.broadcastMessage(conversationId, message);\n\n    return message;\n  }\n\n  async getMessages(\n    conversationId: string, \n    language?: string, \n    limit: number = 50\n  ): Promise<CrossCulturalMessage[]> {\n    const messages = this.messages.get(conversationId) || [];\n    \n    if (language) {\n      // Return messages with translations for the specified language\n      return messages.slice(-limit).map(message => ({\n        ...message,\n        displayText: this.getTranslationForLanguage(message, language)\n      }));\n    }\n\n    return messages.slice(-limit);\n  }\n\n  // Translation Services\n  async translateMessage(request: TranslationRequest): Promise<MessageTranslation> {\n    // Simulate advanced translation with cultural context preservation\n    const translation: MessageTranslation = {\n      language: request.targetLanguage,\n      translatedText: await this.performTranslation(request.text, request.sourceLanguage, request.targetLanguage),\n      culturalAdaptation: await this.adaptForCulture(request.text, request.targetLanguage, request.culturalContext),\n      confidenceScore: this.calculateConfidenceScore(request.text, request.sourceLanguage, request.targetLanguage),\n      culturalAccuracyScore: this.calculateCulturalAccuracyScore(request.text, request.culturalContext),\n      translationMethod: 'automatic',\n      culturalNotes: await this.generateCulturalNotes(request.text, request.culturalContext),\n      alternativePhrasings: await this.generateAlternativePhrasings(request.text, request.targetLanguage),\n      reviewedBy: undefined\n    };\n\n    return translation;\n  }\n\n  async improveTranslation(\n    originalTranslation: string,\n    improvedTranslation: string,\n    culturalJustification: string,\n    reviewerId: string\n  ): Promise<void> {\n    // Store community-contributed translation improvements\n    console.log('Translation improvement recorded:', {\n      original: originalTranslation,\n      improved: improvedTranslation,\n      justification: culturalJustification,\n      reviewer: reviewerId\n    });\n  }\n\n  async explainCulturalContext(\n    text: string,\n    culturalReference: string,\n    targetCulture: string\n  ): Promise<any> {\n    // Generate cultural context explanations\n    return {\n      reference: culturalReference,\n      explanation: `Cultural context explanation for \"${culturalReference}\" in ${targetCulture} context`,\n      significance: 'High cultural significance in traditional practices',\n      appropriateResponses: ['Show respect', 'Ask questions respectfully', 'Acknowledge the cultural value'],\n      potentialSensitivities: ['Avoid appropriation', 'Respect sacred aspects', 'Credit cultural origins']\n    };\n  }\n\n  // Video Conferencing\n  async createVideoConference(data: VideoConferenceData): Promise<VideoConference> {\n    const conference: VideoConference = {\n      id: this.generateId(),\n      conversationId: this.generateId(),\n      participants: data.participants.map(userId => ({\n        userId,\n        culturalBackground: this.getUserCulturalBackground(userId),\n        videoEnabled: true,\n        audioEnabled: true,\n        culturalBackgroundVisible: data.culturalFeatures.culturalBackgroundSharing,\n        preferredLanguage: this.getUserPreferredLanguage(userId)\n      })),\n      culturalFeatures: {\n        realTimeTranslation: data.culturalFeatures.translationEnabled,\n        culturalEtiquetteGuidance: data.culturalFeatures.culturalEtiquetteGuidance,\n        culturalBackgroundSharing: data.culturalFeatures.culturalBackgroundSharing,\n        breakoutRoomCulturalGrouping: true\n      },\n      translation: {\n        enabled: data.culturalFeatures.translationEnabled,\n        supportedLanguages: ['en', 'af', 'zu', 'xh', 'st', 'tn', 'ss', 've', 'ts', 'nr', 'nd'],\n        realTimeTranscription: true,\n        culturalContextPreservation: true\n      },\n      recording: {\n        enabled: false,\n        culturalMomentsHighlighted: true,\n        transcriptionWithTranslation: true,\n        culturalContextPreserved: true\n      },\n      breakoutRooms: [],\n      culturalActivities: [],\n      startTime: data.scheduledTime || new Date(),\n      status: data.scheduledTime ? 'scheduled' : 'active'\n    };\n\n    this.videoConferences.set(conference.id, conference);\n    return conference;\n  }\n\n  async getVideoConference(conferenceId: string): Promise<VideoConference | null> {\n    return this.videoConferences.get(conferenceId) || null;\n  }\n\n  // Cultural Media Sharing\n  async shareCulturalMedia(\n    conversationId: string,\n    uploaderId: string,\n    mediaData: {\n      file: File;\n      culturalContext: string;\n      culturalSignificance: string;\n      sharingPermissions: string[];\n    }\n  ): Promise<CulturalMediaShare> {\n    const mediaShare: CulturalMediaShare = {\n      id: this.generateId(),\n      conversationId,\n      uploaderId,\n      mediaType: this.determineMediaType(mediaData.file),\n      fileName: mediaData.file.name,\n      fileUrl: await this.uploadFile(mediaData.file),\n      culturalContext: {\n        culture: mediaData.culturalContext,\n        significance: mediaData.culturalSignificance,\n        traditionalKnowledge: false,\n        sharingRestrictions: []\n      },\n      culturalSignificance: mediaData.culturalSignificance,\n      sharingPermissions: {\n        allowedUsers: mediaData.sharingPermissions,\n        publicAccess: false,\n        culturalCommunityAccess: true,\n        commercialUse: false\n      },\n      culturalAttribution: {\n        originalCulture: mediaData.culturalContext,\n        knowledgeHolders: [uploaderId],\n        communityPermissions: true,\n        attributionRequired: true\n      },\n      annotations: [],\n      accessLog: [],\n      uploadedAt: new Date()\n    };\n\n    return mediaShare;\n  }\n\n  // Communication Analytics\n  async getConversationAnalytics(conversationId: string): Promise<CommunicationAnalytics> {\n    const messages = this.messages.get(conversationId) || [];\n    const conversation = this.conversations.get(conversationId);\n\n    if (!conversation) {\n      throw new Error('Conversation not found');\n    }\n\n    return {\n      conversationId,\n      culturalEngagement: this.calculateCulturalEngagement(messages, conversation),\n      translationQuality: this.calculateTranslationQuality(messages),\n      crossCulturalUnderstanding: this.calculateCrossCulturalUnderstanding(messages, conversation),\n      communicationEffectiveness: this.calculateCommunicationEffectiveness(messages),\n      relationshipBuilding: this.calculateRelationshipBuilding(messages, conversation),\n      culturalLearning: this.calculateCulturalLearning(messages, conversation)\n    };\n  }\n\n  // WebSocket Management\n  async connectToConversation(conversationId: string, userId: string): Promise<void> {\n    // Simulate WebSocket connection\n    const wsKey = `${conversationId}-${userId}`;\n    console.log(`WebSocket connected for user ${userId} in conversation ${conversationId}`);\n    \n    // In a real implementation, this would establish an actual WebSocket connection\n    this.websockets.set(wsKey, {} as WebSocket);\n  }\n\n  async disconnectFromConversation(conversationId: string, userId: string): Promise<void> {\n    const wsKey = `${conversationId}-${userId}`;\n    this.websockets.delete(wsKey);\n    console.log(`WebSocket disconnected for user ${userId} in conversation ${conversationId}`);\n  }\n\n  // Helper Methods\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n\n  private async generateTranslations(\n    messageData: MessageData, \n    conversation: CrossCulturalConversation\n  ): Promise<MessageTranslation[]> {\n    const translations: MessageTranslation[] = [];\n    \n    for (const participant of conversation.participants) {\n      if (participant.preferredLanguage !== messageData.language) {\n        const translation = await this.translateMessage({\n          text: messageData.text,\n          sourceLanguage: messageData.language,\n          targetLanguage: participant.preferredLanguage,\n          culturalContext: messageData.culturalContext || {},\n          preserveCulturalNuance: true\n        });\n        translations.push(translation);\n      }\n    }\n\n    return translations;\n  }\n\n  private async performTranslation(text: string, source: string, target: string): Promise<string> {\n    // Simulate translation - in real implementation, integrate with translation APIs\n    return `[${target.toUpperCase()}] ${text}`;\n  }\n\n  private async adaptForCulture(text: string, targetLanguage: string, culturalContext: CulturalContext): Promise<string> {\n    // Simulate cultural adaptation\n    return `[Culturally adapted for ${targetLanguage}] ${text}`;\n  }\n\n  private calculateConfidenceScore(text: string, source: string, target: string): number {\n    // Simulate confidence calculation\n    return Math.floor(Math.random() * 20) + 80; // 80-100\n  }\n\n  private calculateCulturalAccuracyScore(text: string, culturalContext: CulturalContext): number {\n    // Simulate cultural accuracy calculation\n    return Math.floor(Math.random() * 15) + 85; // 85-100\n  }\n\n  private async generateCulturalNotes(text: string, culturalContext: CulturalContext): Promise<string[]> {\n    return ['Cultural note: This expression has special significance in traditional contexts'];\n  }\n\n  private async generateAlternativePhrasings(text: string, targetLanguage: string): Promise<string[]> {\n    return [`Alternative phrasing in ${targetLanguage}`, `More formal version in ${targetLanguage}`];\n  }\n\n  private async analyzeCulturalContext(text: string, language: string): Promise<any> {\n    return {\n      culturalReferences: [],\n      idioms: [],\n      formalityLevel: 'neutral',\n      emotionalTone: 'neutral'\n    };\n  }\n\n  private async generateCulturalAnnotations(text: string, language: string): Promise<any[]> {\n    return [];\n  }\n\n  private async broadcastMessage(conversationId: string, message: CrossCulturalMessage): Promise<void> {\n    // Simulate WebSocket broadcast\n    console.log(`Broadcasting message ${message.id} to conversation ${conversationId}`);\n  }\n\n  private getTranslationForLanguage(message: CrossCulturalMessage, language: string): string {\n    const translation = message.translations.find(t => t.language === language);\n    return translation ? translation.translatedText : message.originalText;\n  }\n\n  private getUserCulturalBackground(userId: string): any {\n    // Simulate getting user cultural background\n    return { primaryCulture: 'Zulu', secondaryCultures: ['English'], languages: ['zu', 'en'] };\n  }\n\n  private getUserPreferredLanguage(userId: string): string {\n    // Simulate getting user preferred language\n    return 'en';\n  }\n\n  private getUserCommunicationPreferences(userId: string): any {\n    return { style: 'direct', formality: 'informal', culturalProtocols: [] };\n  }\n\n  private extractPrimaryLanguages(participants: string[]): string[] {\n    return ['en', 'zu', 'af']; // Simulate extracting languages\n  }\n\n  private determineCommunicationStyle(participants: string[]): any {\n    return { primary: 'collaborative', cultural: 'ubuntu', formality: 'respectful' };\n  }\n\n  private determineMediaType(file: File): 'image' | 'video' | 'audio' | 'document' | 'artifact' {\n    if (file.type.startsWith('image/')) return 'image';\n    if (file.type.startsWith('video/')) return 'video';\n    if (file.type.startsWith('audio/')) return 'audio';\n    return 'document';\n  }\n\n  private async uploadFile(file: File): Promise<string> {\n    // Simulate file upload\n    return `https://storage.example.com/${file.name}`;\n  }\n\n  private calculateCulturalEngagement(messages: CrossCulturalMessage[], conversation: CrossCulturalConversation): any {\n    return { score: 85, culturalReferencesShared: 12, culturalLearningMoments: 8 };\n  }\n\n  private calculateTranslationQuality(messages: CrossCulturalMessage[]): any {\n    return { averageConfidence: 88, culturalAccuracy: 92, userSatisfaction: 87 };\n  }\n\n  private calculateCrossCulturalUnderstanding(messages: CrossCulturalMessage[], conversation: CrossCulturalConversation): any {\n    return { understandingScore: 78, misunderstandingsResolved: 3, culturalBridgesMade: 5 };\n  }\n\n  private calculateCommunicationEffectiveness(messages: CrossCulturalMessage[]): any {\n    return { responseRate: 94, clarificationRequests: 2, successfulExchanges: 45 };\n  }\n\n  private calculateRelationshipBuilding(messages: CrossCulturalMessage[], conversation: CrossCulturalConversation): any {\n    return { relationshipStrength: 72, culturalBonding: 68, trustBuilding: 75 };\n  }\n\n  private calculateCulturalLearning(messages: CrossCulturalMessage[], conversation: CrossCulturalConversation): any {\n    return { learningMoments: 15, culturalKnowledgeGained: 8, appreciationGrowth: 82 };\n  }\n}\n\nexport const realTimeCommunicationService = new RealTimeCommunicationService();\n", "import React, { useState, useEffect, useRef } from 'react';\nimport { \n  CrossCulturalConversation, \n  CrossCulturalMessage, \n  MessageTranslation,\n  CommunicationAnalytics \n} from '../types/communication';\nimport { realTimeCommunicationService } from '../services/realTimeCommunicationService';\n\ninterface CrossCulturalCommunicationProps {\n  userId: string;\n  conversationId?: string;\n}\n\nconst CrossCulturalCommunication: React.FC<CrossCulturalCommunicationProps> = ({ \n  userId, \n  conversationId \n}) => {\n  const [conversations, setConversations] = useState<CrossCulturalConversation[]>([]);\n  const [activeConversation, setActiveConversation] = useState<CrossCulturalConversation | null>(null);\n  const [messages, setMessages] = useState<CrossCulturalMessage[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [selectedLanguage, setSelectedLanguage] = useState('en');\n  const [translationEnabled, setTranslationEnabled] = useState(true);\n  const [culturalGuidanceEnabled, setCulturalGuidanceEnabled] = useState(true);\n  const [analytics, setAnalytics] = useState<CommunicationAnalytics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const supportedLanguages = [\n    { code: 'en', name: 'English' },\n    { code: 'af', name: 'Afrikaans' },\n    { code: 'zu', name: 'Zulu' },\n    { code: 'xh', name: 'Xhosa' },\n    { code: 'st', name: 'Sesotho' },\n    { code: 'tn', name: 'Setswana' },\n    { code: 'ss', name: 'Siswati' },\n    { code: 've', name: 'Venda' },\n    { code: 'ts', name: 'Tsonga' },\n    { code: 'nr', name: 'Ndebele (South)' },\n    { code: 'nd', name: 'Ndebele (North)' }\n  ];\n\n  useEffect(() => {\n    loadConversations();\n    if (conversationId) {\n      loadConversation(conversationId);\n    }\n  }, [userId, conversationId]);\n\n  useEffect(() => {\n    if (activeConversation) {\n      loadMessages(activeConversation.id);\n      loadAnalytics(activeConversation.id);\n      // Connect to WebSocket for real-time updates\n      realTimeCommunicationService.connectToConversation(activeConversation.id, userId);\n    }\n  }, [activeConversation]);\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const loadConversations = async () => {\n    try {\n      setLoading(true);\n      // In a real implementation, this would fetch user's conversations\n      setConversations([]);\n    } catch (error) {\n      console.error('Error loading conversations:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadConversation = async (convId: string) => {\n    try {\n      const conversation = await realTimeCommunicationService.getConversation(convId);\n      if (conversation) {\n        setActiveConversation(conversation);\n      }\n    } catch (error) {\n      console.error('Error loading conversation:', error);\n    }\n  };\n\n  const loadMessages = async (convId: string) => {\n    try {\n      const conversationMessages = await realTimeCommunicationService.getMessages(\n        convId, \n        translationEnabled ? selectedLanguage : undefined\n      );\n      setMessages(conversationMessages);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    }\n  };\n\n  const loadAnalytics = async (convId: string) => {\n    try {\n      const conversationAnalytics = await realTimeCommunicationService.getConversationAnalytics(convId);\n      setAnalytics(conversationAnalytics);\n    } catch (error) {\n      console.error('Error loading analytics:', error);\n    }\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleSendMessage = async () => {\n    if (!newMessage.trim() || !activeConversation) return;\n\n    try {\n      const messageData = {\n        text: newMessage,\n        language: selectedLanguage,\n        messageType: 'text' as const\n      };\n\n      const sentMessage = await realTimeCommunicationService.sendMessage(\n        activeConversation.id,\n        userId,\n        messageData\n      );\n\n      setMessages(prev => [...prev, sentMessage]);\n      setNewMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const createNewConversation = async () => {\n    try {\n      const conversationData = {\n        participants: [userId], // In real implementation, would select participants\n        conversationType: 'group_chat' as const,\n        culturalContext: {\n          conversationCultures: ['Zulu', 'English', 'Afrikaans'],\n          culturalSensitivities: [],\n          communicationProtocols: [],\n          translationPreferences: {}\n        },\n        translationEnabled: true\n      };\n\n      const newConversation = await realTimeCommunicationService.createConversation(\n        conversationData,\n        userId\n      );\n\n      setConversations(prev => [...prev, newConversation]);\n      setActiveConversation(newConversation);\n    } catch (error) {\n      console.error('Error creating conversation:', error);\n    }\n  };\n\n  const getMessageTranslation = (message: CrossCulturalMessage): string => {\n    if (!translationEnabled || message.originalLanguage === selectedLanguage) {\n      return message.originalText;\n    }\n\n    const translation = message.translations.find(t => t.language === selectedLanguage);\n    return translation ? translation.translatedText : message.originalText;\n  };\n\n  const renderMessage = (message: CrossCulturalMessage) => {\n    const isOwnMessage = message.senderId === userId;\n    const displayText = getMessageTranslation(message);\n    const translation = message.translations.find(t => t.language === selectedLanguage);\n\n    return (\n      <div\n        key={message.id}\n        className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-4`}\n      >\n        <div\n          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n            isOwnMessage\n              ? 'bg-blue-600 text-white'\n              : 'bg-gray-200 text-gray-900'\n          }`}\n        >\n          <p className=\"text-sm\">{displayText}</p>\n          \n          {translation && translation.culturalNotes && translation.culturalNotes.length > 0 && (\n            <div className=\"mt-2 p-2 bg-yellow-100 rounded text-xs text-gray-800\">\n              <strong>Cultural Note:</strong> {translation.culturalNotes[0]}\n            </div>\n          )}\n          \n          {message.culturalAnnotations.length > 0 && (\n            <div className=\"mt-2 space-y-1\">\n              {message.culturalAnnotations.map((annotation, index) => (\n                <div key={index} className=\"p-2 bg-purple-100 rounded text-xs text-gray-800\">\n                  <strong>{annotation.annotationType}:</strong> {annotation.content}\n                </div>\n              ))}\n            </div>\n          )}\n          \n          <div className=\"flex justify-between items-center mt-2 text-xs opacity-75\">\n            <span>{message.timestamp.toLocaleTimeString()}</span>\n            {message.originalLanguage !== selectedLanguage && (\n              <span className=\"ml-2\">\n                Translated from {message.originalLanguage.toUpperCase()}\n              </span>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderCulturalGuidance = () => {\n    if (!culturalGuidanceEnabled || !activeConversation) return null;\n\n    return (\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\">\n        <h4 className=\"font-medium text-blue-900 mb-2\">Cultural Guidance</h4>\n        <div className=\"space-y-2 text-sm text-blue-800\">\n          <p>• This conversation includes participants from {activeConversation.culturalContext.conversationCultures?.join(', ')} cultures</p>\n          <p>• Consider using respectful greetings and showing appreciation for cultural sharing</p>\n          <p>• Ask questions about cultural references to show genuine interest</p>\n        </div>\n      </div>\n    );\n  };\n\n  const renderAnalytics = () => {\n    if (!analytics) return null;\n\n    return (\n      <div className=\"bg-white rounded-lg shadow p-4 mb-4\">\n        <h4 className=\"font-medium text-gray-900 mb-3\">Communication Insights</h4>\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          <div>\n            <p className=\"text-gray-600\">Cultural Engagement</p>\n            <p className=\"font-semibold text-green-600\">{analytics.culturalEngagement.score}%</p>\n          </div>\n          <div>\n            <p className=\"text-gray-600\">Translation Quality</p>\n            <p className=\"font-semibold text-blue-600\">{analytics.translationQuality.averageConfidence}%</p>\n          </div>\n          <div>\n            <p className=\"text-gray-600\">Understanding Score</p>\n            <p className=\"font-semibold text-purple-600\">{analytics.crossCulturalUnderstanding.understandingScore}%</p>\n          </div>\n          <div>\n            <p className=\"text-gray-600\">Relationship Building</p>\n            <p className=\"font-semibold text-orange-600\">{analytics.relationshipBuilding.relationshipStrength}%</p>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      <div className=\"flex justify-between items-center mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">Cross-Cultural Communication</h1>\n        <button \n          onClick={createNewConversation}\n          className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium\"\n        >\n          New Conversation\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Sidebar */}\n        <div className=\"lg:col-span-1 space-y-4\">\n          {/* Language Selection */}\n          <div className=\"bg-white rounded-lg shadow p-4\">\n            <h3 className=\"font-medium text-gray-900 mb-3\">Language Settings</h3>\n            <select\n              value={selectedLanguage}\n              onChange={(e) => setSelectedLanguage(e.target.value)}\n              className=\"w-full p-2 border border-gray-300 rounded-md\"\n            >\n              {supportedLanguages.map((lang) => (\n                <option key={lang.code} value={lang.code}>\n                  {lang.name}\n                </option>\n              ))}\n            </select>\n            \n            <div className=\"mt-3 space-y-2\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={translationEnabled}\n                  onChange={(e) => setTranslationEnabled(e.target.checked)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm\">Enable Translation</span>\n              </label>\n              \n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={culturalGuidanceEnabled}\n                  onChange={(e) => setCulturalGuidanceEnabled(e.target.checked)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm\">Cultural Guidance</span>\n              </label>\n            </div>\n          </div>\n\n          {/* Analytics */}\n          {renderAnalytics()}\n        </div>\n\n        {/* Main Chat Area */}\n        <div className=\"lg:col-span-3\">\n          {activeConversation ? (\n            <div className=\"bg-white rounded-lg shadow h-96 flex flex-col\">\n              {/* Chat Header */}\n              <div className=\"p-4 border-b border-gray-200\">\n                <h3 className=\"font-medium text-gray-900\">\n                  Cross-Cultural Conversation\n                </h3>\n                <p className=\"text-sm text-gray-600\">\n                  {activeConversation.participants.length} participants • \n                  {activeConversation.primaryLanguages.join(', ')} languages\n                </p>\n              </div>\n\n              {/* Cultural Guidance */}\n              <div className=\"p-4\">\n                {renderCulturalGuidance()}\n              </div>\n\n              {/* Messages */}\n              <div className=\"flex-1 overflow-y-auto p-4\">\n                {messages.map(renderMessage)}\n                <div ref={messagesEndRef} />\n              </div>\n\n              {/* Message Input */}\n              <div className=\"p-4 border-t border-gray-200\">\n                <div className=\"flex space-x-2\">\n                  <textarea\n                    value={newMessage}\n                    onChange={(e) => setNewMessage(e.target.value)}\n                    onKeyPress={handleKeyPress}\n                    placeholder={`Type your message in ${supportedLanguages.find(l => l.code === selectedLanguage)?.name}...`}\n                    className=\"flex-1 p-2 border border-gray-300 rounded-md resize-none\"\n                    rows={2}\n                  />\n                  <button\n                    onClick={handleSendMessage}\n                    disabled={!newMessage.trim()}\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    Send\n                  </button>\n                </div>\n                \n                <div className=\"mt-2 text-xs text-gray-500\">\n                  {translationEnabled && (\n                    <span>Messages will be automatically translated for all participants</span>\n                  )}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"bg-white rounded-lg shadow p-8 text-center\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                Welcome to Cross-Cultural Communication\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Start a conversation to connect with people from different cultural backgrounds.\n                Our platform provides real-time translation and cultural context to help you\n                communicate effectively and build meaningful relationships.\n              </p>\n              <button \n                onClick={createNewConversation}\n                className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium\"\n              >\n                Start Your First Conversation\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CrossCulturalCommunication;\n", "import {\n  CulturalEvent,\n  EventProgramming,\n  EventAttendee,\n  CulturalPerformance,\n  CulturalWorkshop,\n  EventPromotion,\n  EventImpact,\n  EventCoordination,\n  CulturalDocumentation\n} from '../types/culturalEvent';\n\nexport interface EventCreationData {\n  title: string;\n  description: string;\n  eventType: 'celebration' | 'workshop' | 'festival' | 'educational' | 'performance' | 'exhibition' | 'ceremony';\n  format: 'in_person' | 'virtual' | 'hybrid';\n  location: {\n    venue?: string;\n    address?: string;\n    virtualPlatform?: string;\n    culturalSignificance?: string;\n  };\n  dateTime: {\n    startDate: Date;\n    endDate: Date;\n    culturalCalendarConsiderations: string[];\n  };\n  culturalContext: {\n    primaryCultures: string[];\n    culturalSignificance: string;\n    traditionalElements: string[];\n    culturalLearningObjectives: string[];\n  };\n  targetCommunities: string[];\n  accessibility: {\n    wheelchairAccessible: boolean;\n    languageSupport: string[];\n    culturalAccommodations: string[];\n  };\n}\n\nexport interface RSVPData {\n  eventId: string;\n  userId: string;\n  attendanceType: 'full_event' | 'specific_sessions' | 'performer' | 'volunteer' | 'vendor';\n  culturalBackground: string;\n  culturalContributions: string[];\n  dietaryRequirements: string[];\n  accessibilityNeeds: string[];\n  culturalInterests: string[];\n}\n\nexport interface PerformanceData {\n  eventId: string;\n  performanceName: string;\n  culturalOrigin: string;\n  performers: string[];\n  culturalSignificance: string;\n  performanceType: 'music' | 'dance' | 'storytelling' | 'poetry' | 'theater' | 'ceremony';\n  duration: number;\n  culturalContext: string;\n  audience: 'all_ages' | 'adults' | 'children' | 'specific_culture';\n  requirements: string[];\n  scheduledTime: Date;\n}\n\nexport interface WorkshopData {\n  eventId: string;\n  workshopTitle: string;\n  culturalFocus: string;\n  facilitator: string;\n  learningObjectives: string[];\n  culturalSkills: string[];\n  participantLimit: number;\n  materials: string[];\n  culturalPrerequisites?: string[];\n  duration: number;\n  difficulty: 'beginner' | 'intermediate' | 'advanced';\n}\n\nclass CulturalEventService {\n  private events: Map<string, CulturalEvent> = new Map();\n  private attendees: Map<string, EventAttendee[]> = new Map();\n  private performances: Map<string, CulturalPerformance[]> = new Map();\n  private workshops: Map<string, CulturalWorkshop[]> = new Map();\n  private promotions: Map<string, EventPromotion> = new Map();\n\n  // Event Management\n  async createEvent(data: EventCreationData, organizerId: string): Promise<CulturalEvent> {\n    const event: CulturalEvent = {\n      id: this.generateId(),\n      title: data.title,\n      description: data.description,\n      culturalContext: {\n        primaryCultures: data.culturalContext.primaryCultures,\n        culturalSignificance: data.culturalContext.culturalSignificance,\n        traditionalElements: data.culturalContext.traditionalElements.map(element => ({\n          element,\n          significance: 'Traditional cultural practice',\n          authenticity: 'verified'\n        })),\n        culturalLearningObjectives: data.culturalContext.culturalLearningObjectives.map(objective => ({\n          objective,\n          culturalContext: data.culturalContext.primaryCultures.join(', '),\n          expectedOutcome: 'Enhanced cultural understanding'\n        })),\n        culturalSensitivities: [],\n        culturalProtocols: [],\n        heritageConnections: [],\n        crossCulturalGoals: []\n      },\n      eventType: data.eventType,\n      format: data.format,\n      location: {\n        venue: data.location.venue,\n        address: data.location.address,\n        virtualPlatform: data.location.virtualPlatform,\n        culturalSignificance: data.location.culturalSignificance,\n        accessibility: data.accessibility,\n        culturalConsiderations: []\n      },\n      dateTime: {\n        startDate: data.dateTime.startDate,\n        endDate: data.dateTime.endDate,\n        timeZone: 'Africa/Johannesburg',\n        culturalCalendarAlignment: data.dateTime.culturalCalendarConsiderations,\n        flexibilityOptions: []\n      },\n      organizers: [{\n        userId: organizerId,\n        role: 'primary_organizer',\n        culturalBackground: this.getUserCulturalBackground(organizerId),\n        responsibilities: ['overall_coordination', 'cultural_oversight'],\n        permissions: ['edit_event', 'manage_attendees', 'approve_content']\n      }],\n      targetCommunities: data.targetCommunities.map(community => ({\n        community,\n        targetParticipation: 'balanced',\n        outreachStrategy: 'community_leaders',\n        culturalApproach: 'respectful_invitation'\n      })),\n      culturalRequirements: [],\n      programming: this.initializeEventProgramming(),\n      ticketing: {\n        enabled: false,\n        pricing: { free: true, tiers: [] },\n        accessibility: { scholarships: true, communitySupport: true },\n        culturalConsiderations: []\n      },\n      accessibility: data.accessibility,\n      promotion: this.initializePromotionSettings(),\n      status: 'planning',\n      attendees: [],\n      impact: this.initializeEventImpact(),\n      culturalDocumentation: this.initializeCulturalDocumentation(),\n      createdAt: new Date(),\n      updatedAt: new Date()\n    };\n\n    this.events.set(event.id, event);\n    this.attendees.set(event.id, []);\n    this.performances.set(event.id, []);\n    this.workshops.set(event.id, []);\n\n    return event;\n  }\n\n  async getEvent(eventId: string): Promise<CulturalEvent | null> {\n    return this.events.get(eventId) || null;\n  }\n\n  async updateEvent(eventId: string, updates: Partial<CulturalEvent>): Promise<CulturalEvent> {\n    const event = this.events.get(eventId);\n    if (!event) {\n      throw new Error('Event not found');\n    }\n\n    const updatedEvent = {\n      ...event,\n      ...updates,\n      updatedAt: new Date()\n    };\n\n    this.events.set(eventId, updatedEvent);\n    return updatedEvent;\n  }\n\n  async deleteEvent(eventId: string): Promise<boolean> {\n    const deleted = this.events.delete(eventId);\n    this.attendees.delete(eventId);\n    this.performances.delete(eventId);\n    this.workshops.delete(eventId);\n    this.promotions.delete(eventId);\n    return deleted;\n  }\n\n  async getEventsByCulturalFocus(culturalFocus: string): Promise<CulturalEvent[]> {\n    return Array.from(this.events.values()).filter(event =>\n      event.culturalContext.primaryCultures.includes(culturalFocus)\n    );\n  }\n\n  async getEventsByLocation(location: string): Promise<CulturalEvent[]> {\n    return Array.from(this.events.values()).filter(event =>\n      event.location.address?.includes(location) || event.location.venue?.includes(location)\n    );\n  }\n\n  async getEventsByDateRange(startDate: Date, endDate: Date): Promise<CulturalEvent[]> {\n    return Array.from(this.events.values()).filter(event =>\n      event.dateTime.startDate >= startDate && event.dateTime.endDate <= endDate\n    );\n  }\n\n  // RSVP & Attendee Management\n  async rsvpToEvent(data: RSVPData): Promise<EventAttendee> {\n    const event = this.events.get(data.eventId);\n    if (!event) {\n      throw new Error('Event not found');\n    }\n\n    const attendee: EventAttendee = {\n      userId: data.userId,\n      registrationDate: new Date(),\n      culturalBackground: {\n        primaryCulture: data.culturalBackground,\n        secondaryCultures: [],\n        languages: [],\n        culturalExpertise: [],\n        traditionalKnowledge: []\n      },\n      attendanceType: data.attendanceType,\n      culturalContributions: data.culturalContributions.map(contribution => ({\n        contributionType: 'cultural_knowledge',\n        description: contribution,\n        culturalContext: data.culturalBackground,\n        sharingPermissions: ['event_participants']\n      })),\n      dietaryRequirements: data.dietaryRequirements.map(req => ({\n        requirement: req,\n        culturalContext: data.culturalBackground,\n        severity: 'preference'\n      })),\n      accessibilityNeeds: data.accessibilityNeeds.map(need => ({\n        need,\n        accommodation: 'required',\n        culturalContext: ''\n      })),\n      culturalInterests: data.culturalInterests.map(interest => ({\n        interest,\n        level: 'interested',\n        culturalContext: data.culturalBackground\n      })),\n      networkingPreferences: [],\n      checkInStatus: {\n        checkedIn: false,\n        checkInTime: undefined,\n        culturalWelcome: false\n      }\n    };\n\n    const eventAttendees = this.attendees.get(data.eventId) || [];\n    eventAttendees.push(attendee);\n    this.attendees.set(data.eventId, eventAttendees);\n\n    // Update event attendee count\n    event.attendees.push(attendee);\n    this.events.set(data.eventId, event);\n\n    return attendee;\n  }\n\n  async getEventAttendees(eventId: string): Promise<EventAttendee[]> {\n    return this.attendees.get(eventId) || [];\n  }\n\n  async updateAttendee(eventId: string, userId: string, updates: Partial<EventAttendee>): Promise<EventAttendee> {\n    const attendees = this.attendees.get(eventId) || [];\n    const attendeeIndex = attendees.findIndex(attendee => attendee.userId === userId);\n    \n    if (attendeeIndex === -1) {\n      throw new Error('Attendee not found');\n    }\n\n    attendees[attendeeIndex] = {\n      ...attendees[attendeeIndex],\n      ...updates\n    };\n\n    this.attendees.set(eventId, attendees);\n    return attendees[attendeeIndex];\n  }\n\n  // Programming Management\n  async addPerformance(data: PerformanceData): Promise<CulturalPerformance> {\n    const performance: CulturalPerformance = {\n      id: this.generateId(),\n      eventId: data.eventId,\n      performanceName: data.performanceName,\n      culturalOrigin: data.culturalOrigin,\n      performers: data.performers.map(performer => ({\n        performerId: performer,\n        role: 'performer',\n        culturalBackground: data.culturalOrigin,\n        expertise: []\n      })),\n      culturalSignificance: data.culturalSignificance,\n      performanceType: data.performanceType,\n      duration: data.duration,\n      culturalContext: data.culturalContext,\n      audience: data.audience,\n      requirements: data.requirements.map(req => ({\n        requirement: req,\n        type: 'technical',\n        culturalImportance: 'medium'\n      })),\n      scheduledTime: data.scheduledTime,\n      status: 'scheduled',\n      culturalApprovals: [],\n      documentation: {\n        recordingPermitted: false,\n        culturalSensitivities: [],\n        sharingRestrictions: []\n      }\n    };\n\n    const eventPerformances = this.performances.get(data.eventId) || [];\n    eventPerformances.push(performance);\n    this.performances.set(data.eventId, eventPerformances);\n\n    return performance;\n  }\n\n  async addWorkshop(data: WorkshopData): Promise<CulturalWorkshop> {\n    const workshop: CulturalWorkshop = {\n      id: this.generateId(),\n      eventId: data.eventId,\n      workshopTitle: data.workshopTitle,\n      culturalFocus: data.culturalFocus,\n      facilitator: {\n        facilitatorId: data.facilitator,\n        culturalBackground: data.culturalFocus,\n        expertise: data.culturalSkills,\n        credentials: []\n      },\n      learningObjectives: data.learningObjectives,\n      culturalSkills: data.culturalSkills.map(skill => ({\n        skill,\n        culturalContext: data.culturalFocus,\n        difficulty: data.difficulty,\n        traditionalKnowledge: false\n      })),\n      participantLimit: data.participantLimit,\n      materials: data.materials.map(material => ({\n        material,\n        culturalSignificance: '',\n        source: 'provided',\n        culturalConsiderations: []\n      })),\n      culturalPrerequisites: data.culturalPrerequisites || [],\n      duration: data.duration,\n      difficulty: data.difficulty,\n      registeredParticipants: [],\n      culturalGuidelines: [],\n      learningOutcomes: []\n    };\n\n    const eventWorkshops = this.workshops.get(data.eventId) || [];\n    eventWorkshops.push(workshop);\n    this.workshops.set(data.eventId, eventWorkshops);\n\n    return workshop;\n  }\n\n  async getEventProgramming(eventId: string): Promise<EventProgramming> {\n    const performances = this.performances.get(eventId) || [];\n    const workshops = this.workshops.get(eventId) || [];\n\n    return {\n      schedule: this.generateSchedule(performances, workshops),\n      culturalPerformances: performances,\n      workshops,\n      exhibitions: [],\n      speakerSessions: [],\n      interactiveActivities: [],\n      culturalExperiences: [],\n      networking: []\n    };\n  }\n\n  // Event Promotion\n  async launchPromotion(eventId: string, strategy: any): Promise<EventPromotion> {\n    const event = this.events.get(eventId);\n    if (!event) {\n      throw new Error('Event not found');\n    }\n\n    const promotion: EventPromotion = {\n      eventId,\n      promotionChannels: [\n        {\n          channel: 'community_networks',\n          culturalApproach: 'respectful_invitation',\n          targetCommunities: event.targetCommunities.map(tc => tc.community),\n          status: 'active'\n        },\n        {\n          channel: 'social_media',\n          culturalApproach: 'inclusive_messaging',\n          targetCommunities: event.targetCommunities.map(tc => tc.community),\n          status: 'active'\n        }\n      ],\n      targetCommunities: event.targetCommunities.map(tc => ({\n        community: tc.community,\n        outreachStrategy: 'community_leaders',\n        culturalMessaging: 'authentic_invitation',\n        responseTracking: { reached: 0, engaged: 0, registered: 0 }\n      })),\n      culturalInfluencers: [],\n      partnerOrganizations: [],\n      socialMediaCampaign: {\n        platforms: ['facebook', 'instagram', 'twitter'],\n        culturalHashtags: event.culturalContext.primaryCultures.map(culture => `#${culture}Culture`),\n        contentStrategy: 'authentic_representation',\n        engagementGoals: { reach: 1000, engagement: 100, registrations: 50 }\n      },\n      communityEndorsements: [],\n      mediaKit: {\n        culturallyAppropriateImages: [],\n        culturalContextDescriptions: [],\n        communityQuotes: [],\n        culturalSignificanceExplanations: []\n      },\n      promotionAnalytics: {\n        reach: { total: 0, byCommunity: {} },\n        engagement: { total: 0, byCommunity: {} },\n        registrations: { total: 0, byCommunity: {} },\n        culturalResonance: { score: 0, feedback: [] }\n      }\n    };\n\n    this.promotions.set(eventId, promotion);\n    return promotion;\n  }\n\n  async getPromotionAnalytics(eventId: string): Promise<any> {\n    const promotion = this.promotions.get(eventId);\n    if (!promotion) {\n      throw new Error('Promotion not found');\n    }\n\n    return promotion.promotionAnalytics;\n  }\n\n  // Event Coordination\n  async checkInAttendee(eventId: string, attendeeId: string): Promise<any> {\n    const attendees = this.attendees.get(eventId) || [];\n    const attendeeIndex = attendees.findIndex(attendee => attendee.userId === attendeeId);\n    \n    if (attendeeIndex === -1) {\n      throw new Error('Attendee not found');\n    }\n\n    attendees[attendeeIndex].checkInStatus = {\n      checkedIn: true,\n      checkInTime: new Date(),\n      culturalWelcome: true\n    };\n\n    this.attendees.set(eventId, attendees);\n\n    return {\n      success: true,\n      attendee: attendees[attendeeIndex],\n      culturalWelcome: 'Welcome! We honor your presence at this cultural gathering.'\n    };\n  }\n\n  async getRealTimeCoordination(eventId: string): Promise<any> {\n    const event = this.events.get(eventId);\n    const attendees = this.attendees.get(eventId) || [];\n    \n    if (!event) {\n      throw new Error('Event not found');\n    }\n\n    return {\n      eventStatus: event.status,\n      totalAttendees: attendees.length,\n      checkedInAttendees: attendees.filter(a => a.checkInStatus.checkedIn).length,\n      currentActivities: this.getCurrentActivities(eventId),\n      culturalMoments: this.getCulturalMoments(eventId),\n      emergencyContacts: [],\n      culturalLiaisons: []\n    };\n  }\n\n  // Impact Measurement\n  async generateImpactReport(eventId: string): Promise<EventImpact> {\n    const event = this.events.get(eventId);\n    const attendees = this.attendees.get(eventId) || [];\n    \n    if (!event) {\n      throw new Error('Event not found');\n    }\n\n    const impact: EventImpact = {\n      attendanceMetrics: {\n        totalAttendees: attendees.length,\n        culturalDiversity: this.calculateCulturalDiversity(attendees),\n        communityRepresentation: this.calculateCommunityRepresentation(attendees),\n        demographicBreakdown: this.calculateDemographicBreakdown(attendees)\n      },\n      culturalExchange: {\n        culturalInteractions: 25,\n        knowledgeSharing: 18,\n        traditionalPracticesShared: 8,\n        crossCulturalConnections: 32\n      },\n      communityEngagement: {\n        communityParticipation: 85,\n        leadershipInvolvement: 12,\n        volunteerContributions: 15,\n        communityFeedback: 4.2\n      },\n      culturalLearning: {\n        learningObjectivesAchieved: 90,\n        culturalCompetencyIncrease: 78,\n        appreciationGrowth: 88,\n        knowledgeRetention: 82\n      },\n      economicImpact: {\n        localEconomicBenefit: 5000,\n        culturalEntrepreneurSupport: 8,\n        artisanSales: 2500,\n        communityInvestment: 1500\n      },\n      socialCohesion: {\n        relationshipsFormed: 45,\n        communityBonding: 82,\n        culturalPrideBoosted: 91,\n        inclusionImproved: 87\n      },\n      culturalPreservation: {\n        traditionalKnowledgeDocumented: 12,\n        culturalPracticesPreserved: 8,\n        intergenerationalTransfer: 15,\n        culturalArtifactsShared: 6\n      },\n      followUpActivities: []\n    };\n\n    return impact;\n  }\n\n  // Cultural Documentation\n  async documentCulturalMoment(eventId: string, momentData: any): Promise<any> {\n    const event = this.events.get(eventId);\n    if (!event) {\n      throw new Error('Event not found');\n    }\n\n    const culturalMoment = {\n      id: this.generateId(),\n      eventId,\n      momentType: momentData.momentType,\n      description: momentData.description,\n      culturalSignificance: momentData.culturalSignificance,\n      participants: momentData.participants,\n      timestamp: new Date(),\n      preservationValue: momentData.preservationValue || 8,\n      documentation: {\n        photos: [],\n        videos: [],\n        audioRecordings: [],\n        writtenAccounts: []\n      }\n    };\n\n    // In a real implementation, this would be stored in the cultural documentation\n    console.log('Cultural moment documented:', culturalMoment);\n\n    return culturalMoment;\n  }\n\n  // Helper Methods\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n\n  private getUserCulturalBackground(userId: string): any {\n    // Simulate getting user cultural background\n    return {\n      primaryCulture: 'Zulu',\n      secondaryCultures: ['English'],\n      languages: ['zu', 'en'],\n      culturalExpertise: ['traditional_music', 'ubuntu_philosophy'],\n      traditionalKnowledge: ['oral_traditions']\n    };\n  }\n\n  private initializeEventProgramming(): EventProgramming {\n    return {\n      schedule: [],\n      culturalPerformances: [],\n      workshops: [],\n      exhibitions: [],\n      speakerSessions: [],\n      interactiveActivities: [],\n      culturalExperiences: [],\n      networking: []\n    };\n  }\n\n  private initializePromotionSettings(): any {\n    return {\n      enabled: true,\n      culturalApproach: 'respectful_invitation',\n      targetChannels: ['community_networks', 'social_media'],\n      culturalMessaging: 'authentic_representation'\n    };\n  }\n\n  private initializeEventImpact(): EventImpact {\n    return {\n      attendanceMetrics: { totalAttendees: 0, culturalDiversity: 0, communityRepresentation: {}, demographicBreakdown: {} },\n      culturalExchange: { culturalInteractions: 0, knowledgeSharing: 0, traditionalPracticesShared: 0, crossCulturalConnections: 0 },\n      communityEngagement: { communityParticipation: 0, leadershipInvolvement: 0, volunteerContributions: 0, communityFeedback: 0 },\n      culturalLearning: { learningObjectivesAchieved: 0, culturalCompetencyIncrease: 0, appreciationGrowth: 0, knowledgeRetention: 0 },\n      economicImpact: { localEconomicBenefit: 0, culturalEntrepreneurSupport: 0, artisanSales: 0, communityInvestment: 0 },\n      socialCohesion: { relationshipsFormed: 0, communityBonding: 0, culturalPrideBoosted: 0, inclusionImproved: 0 },\n      culturalPreservation: { traditionalKnowledgeDocumented: 0, culturalPracticesPreserved: 0, intergenerationalTransfer: 0, culturalArtifactsShared: 0 },\n      followUpActivities: []\n    };\n  }\n\n  private initializeCulturalDocumentation(): CulturalDocumentation {\n    return {\n      eventId: '',\n      culturalMoments: [],\n      traditionalKnowledge: [],\n      culturalPerformanceRecordings: [],\n      participantStories: [],\n      culturalArtifacts: [],\n      communityTestimonials: [],\n      preservationOutcomes: []\n    };\n  }\n\n  private generateSchedule(performances: CulturalPerformance[], workshops: CulturalWorkshop[]): any[] {\n    const schedule = [];\n    \n    performances.forEach(performance => {\n      schedule.push({\n        id: performance.id,\n        type: 'performance',\n        title: performance.performanceName,\n        startTime: performance.scheduledTime,\n        duration: performance.duration,\n        culturalContext: performance.culturalOrigin\n      });\n    });\n\n    workshops.forEach(workshop => {\n      schedule.push({\n        id: workshop.id,\n        type: 'workshop',\n        title: workshop.workshopTitle,\n        duration: workshop.duration,\n        culturalContext: workshop.culturalFocus\n      });\n    });\n\n    return schedule.sort((a, b) => a.startTime - b.startTime);\n  }\n\n  private getCurrentActivities(eventId: string): any[] {\n    // Simulate getting current activities\n    return [\n      { activity: 'Traditional Zulu Dance Performance', status: 'active', culturalContext: 'Zulu' },\n      { activity: 'Ubuntu Philosophy Workshop', status: 'upcoming', culturalContext: 'Pan-African' }\n    ];\n  }\n\n  private getCulturalMoments(eventId: string): any[] {\n    // Simulate getting cultural moments\n    return [\n      { moment: 'Intergenerational knowledge sharing', significance: 'High', timestamp: new Date() },\n      { moment: 'Cross-cultural collaboration breakthrough', significance: 'Medium', timestamp: new Date() }\n    ];\n  }\n\n  private calculateCulturalDiversity(attendees: EventAttendee[]): number {\n    const cultures = new Set(attendees.map(attendee => attendee.culturalBackground.primaryCulture));\n    return (cultures.size / 11) * 100; // 11 official cultures in South Africa\n  }\n\n  private calculateCommunityRepresentation(attendees: EventAttendee[]): any {\n    const representation: any = {};\n    attendees.forEach(attendee => {\n      const culture = attendee.culturalBackground.primaryCulture;\n      representation[culture] = (representation[culture] || 0) + 1;\n    });\n    return representation;\n  }\n\n  private calculateDemographicBreakdown(attendees: EventAttendee[]): any {\n    return {\n      totalAttendees: attendees.length,\n      byAttendanceType: this.groupBy(attendees, 'attendanceType'),\n      byCulturalBackground: this.groupBy(attendees, a => a.culturalBackground.primaryCulture)\n    };\n  }\n\n  private groupBy(array: any[], key: string | ((item: any) => string)): any {\n    return array.reduce((result, item) => {\n      const group = typeof key === 'function' ? key(item) : item[key];\n      result[group] = (result[group] || 0) + 1;\n      return result;\n    }, {});\n  }\n}\n\nexport const culturalEventService = new CulturalEventService();\n", "import React, { useState, useEffect } from 'react';\nimport { \n  CulturalEvent, \n  EventAttendee, \n  CulturalPerformance, \n  CulturalWorkshop,\n  EventImpact \n} from '../types/culturalEvent';\nimport { culturalEventService } from '../services/culturalEventService';\n\ninterface CulturalEventDashboardProps {\n  userId: string;\n}\n\nconst CulturalEventDashboard: React.FC<CulturalEventDashboardProps> = ({ userId }) => {\n  const [events, setEvents] = useState<CulturalEvent[]>([]);\n  const [selectedEvent, setSelectedEvent] = useState<CulturalEvent | null>(null);\n  const [attendees, setAttendees] = useState<EventAttendee[]>([]);\n  const [eventImpact, setEventImpact] = useState<EventImpact | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState<'overview' | 'programming' | 'attendees' | 'impact'>('overview');\n\n  useEffect(() => {\n    loadEvents();\n  }, []);\n\n  useEffect(() => {\n    if (selectedEvent) {\n      loadEventDetails(selectedEvent.id);\n    }\n  }, [selectedEvent]);\n\n  const loadEvents = async () => {\n    try {\n      setLoading(true);\n      // In a real implementation, this would fetch user's events\n      const sampleEvents = await createSampleEvents();\n      setEvents(sampleEvents);\n      if (sampleEvents.length > 0) {\n        setSelectedEvent(sampleEvents[0]);\n      }\n    } catch (error) {\n      console.error('Error loading events:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const createSampleEvents = async (): Promise<CulturalEvent[]> => {\n    const eventData = {\n      title: 'Ubuntu Cultural Festival',\n      description: 'A celebration of South African cultural diversity through music, dance, and storytelling',\n      eventType: 'festival' as const,\n      format: 'hybrid' as const,\n      location: {\n        venue: 'Community Cultural Center',\n        address: 'Cape Town, South Africa',\n        culturalSignificance: 'Historic gathering place for multiple communities'\n      },\n      dateTime: {\n        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now\n        endDate: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000), // 1 week + 1 day from now\n        culturalCalendarConsiderations: ['Heritage Month', 'Traditional harvest season']\n      },\n      culturalContext: {\n        primaryCultures: ['Zulu', 'Xhosa', 'Afrikaans', 'English'],\n        culturalSignificance: 'Celebrating unity in diversity through Ubuntu philosophy',\n        traditionalElements: ['Traditional music', 'Storytelling', 'Cultural dances', 'Craft demonstrations'],\n        culturalLearningObjectives: [\n          'Understanding Ubuntu philosophy',\n          'Appreciating cultural diversity',\n          'Learning traditional practices'\n        ]\n      },\n      targetCommunities: ['Zulu', 'Xhosa', 'Afrikaans', 'English', 'Sotho'],\n      accessibility: {\n        wheelchairAccessible: true,\n        languageSupport: ['en', 'af', 'zu', 'xh', 'st'],\n        culturalAccommodations: ['Prayer spaces', 'Dietary considerations', 'Cultural dress areas']\n      }\n    };\n\n    const event = await culturalEventService.createEvent(eventData, userId);\n    return [event];\n  };\n\n  const loadEventDetails = async (eventId: string) => {\n    try {\n      const eventAttendees = await culturalEventService.getEventAttendees(eventId);\n      const impact = await culturalEventService.generateImpactReport(eventId);\n      setAttendees(eventAttendees);\n      setEventImpact(impact);\n    } catch (error) {\n      console.error('Error loading event details:', error);\n    }\n  };\n\n  const handleCreateEvent = async () => {\n    // This would open an event creation modal\n    console.log('Create new cultural event');\n  };\n\n  const handleRSVP = async () => {\n    if (!selectedEvent) return;\n\n    try {\n      const rsvpData = {\n        eventId: selectedEvent.id,\n        userId,\n        attendanceType: 'full_event' as const,\n        culturalBackground: 'Zulu',\n        culturalContributions: ['Traditional music knowledge', 'Ubuntu philosophy'],\n        dietaryRequirements: ['Vegetarian'],\n        accessibilityNeeds: [],\n        culturalInterests: ['Traditional music', 'Storytelling', 'Cultural dances']\n      };\n\n      const attendee = await culturalEventService.rsvpToEvent(rsvpData);\n      setAttendees(prev => [...prev, attendee]);\n    } catch (error) {\n      console.error('Error RSVPing to event:', error);\n    }\n  };\n\n  const renderEventOverview = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Event Details</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-2\">Cultural Context</h4>\n            <p className=\"text-gray-700 mb-3\">{selectedEvent?.culturalContext.culturalSignificance}</p>\n            <div className=\"flex flex-wrap gap-2\">\n              {selectedEvent?.culturalContext.primaryCultures.map((culture, index) => (\n                <span key={index} className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm\">\n                  {culture}\n                </span>\n              ))}\n            </div>\n          </div>\n          \n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-2\">Event Information</h4>\n            <div className=\"space-y-2 text-sm text-gray-600\">\n              <p><strong>Format:</strong> {selectedEvent?.format.replace('_', ' ')}</p>\n              <p><strong>Venue:</strong> {selectedEvent?.location.venue}</p>\n              <p><strong>Date:</strong> {selectedEvent?.dateTime.startDate.toLocaleDateString()}</p>\n              <p><strong>Status:</strong> {selectedEvent?.status}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Traditional Elements</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {selectedEvent?.culturalContext.traditionalElements.map((element, index) => (\n            <div key={index} className=\"border-l-4 border-green-400 pl-4\">\n              <h4 className=\"font-medium text-gray-900\">{element.element}</h4>\n              <p className=\"text-sm text-gray-600\">{element.significance}</p>\n              <span className=\"text-xs text-green-600\">{element.authenticity}</span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Cultural Learning Objectives</h3>\n        <div className=\"space-y-3\">\n          {selectedEvent?.culturalContext.culturalLearningObjectives.map((objective, index) => (\n            <div key={index} className=\"flex items-start space-x-3\">\n              <div className=\"flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-purple-600 text-sm font-medium\">{index + 1}</span>\n              </div>\n              <div>\n                <h4 className=\"font-medium text-gray-900\">{objective.objective}</h4>\n                <p className=\"text-sm text-gray-600\">{objective.expectedOutcome}</p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderEventProgramming = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Event Programming</h3>\n        <div className=\"space-x-2\">\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\">\n            Add Performance\n          </button>\n          <button className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\">\n            Add Workshop\n          </button>\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h4 className=\"font-medium text-gray-900 mb-4\">Cultural Performances</h4>\n        <div className=\"space-y-4\">\n          <div className=\"border rounded-lg p-4\">\n            <div className=\"flex justify-between items-start mb-2\">\n              <h5 className=\"font-medium text-gray-900\">Traditional Zulu Dance</h5>\n              <span className=\"px-2 py-1 bg-green-100 text-green-800 rounded text-sm\">Scheduled</span>\n            </div>\n            <p className=\"text-sm text-gray-600 mb-2\">\n              Authentic Zulu dance performance showcasing traditional movements and cultural significance\n            </p>\n            <div className=\"flex justify-between items-center text-sm text-gray-500\">\n              <span>Cultural Origin: Zulu</span>\n              <span>Duration: 30 minutes</span>\n              <span>Audience: All ages</span>\n            </div>\n          </div>\n\n          <div className=\"border rounded-lg p-4\">\n            <div className=\"flex justify-between items-start mb-2\">\n              <h5 className=\"font-medium text-gray-900\">Ubuntu Storytelling Circle</h5>\n              <span className=\"px-2 py-1 bg-green-100 text-green-800 rounded text-sm\">Scheduled</span>\n            </div>\n            <p className=\"text-sm text-gray-600 mb-2\">\n              Interactive storytelling session sharing Ubuntu philosophy and traditional wisdom\n            </p>\n            <div className=\"flex justify-between items-center text-sm text-gray-500\">\n              <span>Cultural Origin: Pan-African</span>\n              <span>Duration: 45 minutes</span>\n              <span>Audience: All ages</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h4 className=\"font-medium text-gray-900 mb-4\">Cultural Workshops</h4>\n        <div className=\"space-y-4\">\n          <div className=\"border rounded-lg p-4\">\n            <div className=\"flex justify-between items-start mb-2\">\n              <h5 className=\"font-medium text-gray-900\">Traditional Beadwork</h5>\n              <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm\">Beginner</span>\n            </div>\n            <p className=\"text-sm text-gray-600 mb-2\">\n              Learn the art of traditional South African beadwork and its cultural meanings\n            </p>\n            <div className=\"flex justify-between items-center text-sm text-gray-500\">\n              <span>Cultural Focus: Zulu/Xhosa</span>\n              <span>Duration: 90 minutes</span>\n              <span>Limit: 20 participants</span>\n            </div>\n          </div>\n\n          <div className=\"border rounded-lg p-4\">\n            <div className=\"flex justify-between items-start mb-2\">\n              <h5 className=\"font-medium text-gray-900\">Ubuntu Philosophy Discussion</h5>\n              <span className=\"px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-sm\">Intermediate</span>\n            </div>\n            <p className=\"text-sm text-gray-600 mb-2\">\n              Deep dive into Ubuntu philosophy and its application in modern community building\n            </p>\n            <div className=\"flex justify-between items-center text-sm text-gray-500\">\n              <span>Cultural Focus: Pan-African</span>\n              <span>Duration: 60 minutes</span>\n              <span>Limit: 30 participants</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderAttendees = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Event Attendees</h3>\n        <button \n          onClick={handleRSVP}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n        >\n          RSVP to Event\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Total Attendees</h4>\n          <p className=\"text-3xl font-bold text-blue-600\">{attendees.length}</p>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Cultural Diversity</h4>\n          <p className=\"text-3xl font-bold text-green-600\">\n            {new Set(attendees.map(a => a.culturalBackground.primaryCulture)).size}\n          </p>\n          <p className=\"text-sm text-gray-600\">Different cultures</p>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Volunteers</h4>\n          <p className=\"text-3xl font-bold text-purple-600\">\n            {attendees.filter(a => a.attendanceType === 'volunteer').length}\n          </p>\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h4 className=\"font-medium text-gray-900 mb-4\">Attendee List</h4>\n        <div className=\"space-y-3\">\n          {attendees.map((attendee, index) => (\n            <div key={index} className=\"flex items-center justify-between p-3 border rounded-lg\">\n              <div>\n                <h5 className=\"font-medium text-gray-900\">\n                  {attendee.attendanceType.replace('_', ' ')}\n                </h5>\n                <p className=\"text-sm text-gray-600\">\n                  {attendee.culturalBackground.primaryCulture} • \n                  Registered {attendee.registrationDate.toLocaleDateString()}\n                </p>\n                {attendee.culturalContributions.length > 0 && (\n                  <div className=\"flex flex-wrap gap-1 mt-2\">\n                    {attendee.culturalContributions.slice(0, 2).map((contribution, contribIndex) => (\n                      <span key={contribIndex} className=\"px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs\">\n                        {contribution.description}\n                      </span>\n                    ))}\n                  </div>\n                )}\n              </div>\n              <div className=\"text-right\">\n                <span className={`px-2 py-1 rounded text-xs ${\n                  attendee.checkInStatus.checkedIn \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-gray-100 text-gray-800'\n                }`}>\n                  {attendee.checkInStatus.checkedIn ? 'Checked In' : 'Registered'}\n                </span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderImpact = () => (\n    <div className=\"space-y-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900\">Event Impact Report</h3>\n      \n      {eventImpact && (\n        <>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h4 className=\"font-medium text-gray-900 mb-2\">Cultural Diversity</h4>\n              <p className=\"text-2xl font-bold text-blue-600\">{eventImpact.attendanceMetrics.culturalDiversity}%</p>\n            </div>\n            \n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h4 className=\"font-medium text-gray-900 mb-2\">Cultural Interactions</h4>\n              <p className=\"text-2xl font-bold text-green-600\">{eventImpact.culturalExchange.culturalInteractions}</p>\n            </div>\n            \n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h4 className=\"font-medium text-gray-900 mb-2\">Relationships Formed</h4>\n              <p className=\"text-2xl font-bold text-purple-600\">{eventImpact.socialCohesion.relationshipsFormed}</p>\n            </div>\n            \n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h4 className=\"font-medium text-gray-900 mb-2\">Economic Benefit</h4>\n              <p className=\"text-2xl font-bold text-orange-600\">R{eventImpact.economicImpact.localEconomicBenefit}</p>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h4 className=\"font-medium text-gray-900 mb-4\">Cultural Learning</h4>\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Learning Objectives Achieved</span>\n                  <span className=\"font-medium\">{eventImpact.culturalLearning.learningObjectivesAchieved}%</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Cultural Competency Increase</span>\n                  <span className=\"font-medium\">{eventImpact.culturalLearning.culturalCompetencyIncrease}%</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Appreciation Growth</span>\n                  <span className=\"font-medium\">{eventImpact.culturalLearning.appreciationGrowth}%</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h4 className=\"font-medium text-gray-900 mb-4\">Cultural Preservation</h4>\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Knowledge Documented</span>\n                  <span className=\"font-medium\">{eventImpact.culturalPreservation.traditionalKnowledgeDocumented}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Practices Preserved</span>\n                  <span className=\"font-medium\">{eventImpact.culturalPreservation.culturalPracticesPreserved}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Intergenerational Transfer</span>\n                  <span className=\"font-medium\">{eventImpact.culturalPreservation.intergenerationalTransfer}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      <div className=\"flex justify-between items-center mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">Cultural Events</h1>\n        <button \n          onClick={handleCreateEvent}\n          className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium\"\n        >\n          Create New Event\n        </button>\n      </div>\n\n      {selectedEvent && (\n        <>\n          {/* Event Header */}\n          <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">{selectedEvent.title}</h2>\n            <p className=\"text-gray-600 mb-4\">{selectedEvent.description}</p>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className={`px-3 py-1 rounded-full text-sm ${\n                selectedEvent.status === 'active' ? 'bg-green-100 text-green-800' :\n                selectedEvent.status === 'planning' ? 'bg-yellow-100 text-yellow-800' :\n                'bg-gray-100 text-gray-800'\n              }`}>\n                {selectedEvent.status}\n              </span>\n              <span className=\"text-sm text-gray-500\">\n                {selectedEvent.format} • {selectedEvent.eventType}\n              </span>\n              <span className=\"text-sm text-gray-500\">\n                {selectedEvent.dateTime.startDate.toLocaleDateString()}\n              </span>\n            </div>\n          </div>\n\n          {/* Tabs */}\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"border-b border-gray-200\">\n              <nav className=\"flex space-x-8 px-6\">\n                {[\n                  { key: 'overview', label: 'Overview' },\n                  { key: 'programming', label: 'Programming' },\n                  { key: 'attendees', label: 'Attendees' },\n                  { key: 'impact', label: 'Impact' }\n                ].map((tab) => (\n                  <button\n                    key={tab.key}\n                    onClick={() => setActiveTab(tab.key as any)}\n                    className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                      activeTab === tab.key\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    {tab.label}\n                  </button>\n                ))}\n              </nav>\n            </div>\n\n            <div className=\"p-6\">\n              {activeTab === 'overview' && renderEventOverview()}\n              {activeTab === 'programming' && renderEventProgramming()}\n              {activeTab === 'attendees' && renderAttendees()}\n              {activeTab === 'impact' && renderImpact()}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default CulturalEventDashboard;\n", "import React, { useState } from 'react';\nimport CrossCulturalProjectDashboard from '../components/CrossCulturalProjectDashboard';\nimport CrossCulturalCommunication from '../components/CrossCulturalCommunication';\nimport CulturalEventDashboard from '../components/CulturalEventDashboard';\n\ninterface CrossCulturalCollaborationPageProps {\n  userId: string;\n}\n\nconst CrossCulturalCollaborationPage: React.FC<CrossCulturalCollaborationPageProps> = ({ userId }) => {\n  const [activeSection, setActiveSection] = useState<'projects' | 'communication' | 'events'>('projects');\n\n  const renderSectionContent = () => {\n    switch (activeSection) {\n      case 'projects':\n        return <CrossCulturalProjectDashboard userId={userId} />;\n      case 'communication':\n        return <CrossCulturalCommunication userId={userId} />;\n      case 'events':\n        return <CulturalEventDashboard userId={userId} />;\n      default:\n        return <CrossCulturalProjectDashboard userId={userId} />;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Cross-Cultural Collaboration</h1>\n              <p className=\"text-gray-600 mt-1\">\n                Connect, collaborate, and celebrate South Africa's cultural diversity\n              </p>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                <button\n                  onClick={() => setActiveSection('projects')}\n                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                    activeSection === 'projects'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  Projects\n                </button>\n                <button\n                  onClick={() => setActiveSection('communication')}\n                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                    activeSection === 'communication'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  Communication\n                </button>\n                <button\n                  onClick={() => setActiveSection('events')}\n                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                    activeSection === 'events'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  Events\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Section Description */}\n      <div className=\"bg-blue-50 border-b border-blue-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          {activeSection === 'projects' && (\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-blue-900\">Cross-Cultural Project Management</h3>\n                <p className=\"text-blue-700\">\n                  Create and manage projects that bring together diverse communities to solve challenges \n                  and build understanding through collaborative action.\n                </p>\n              </div>\n            </div>\n          )}\n          \n          {activeSection === 'communication' && (\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-blue-900\">Real-Time Cross-Cultural Communication</h3>\n                <p className=\"text-blue-700\">\n                  Bridge language barriers and preserve cultural context with intelligent translation \n                  and cultural guidance for meaningful cross-cultural conversations.\n                </p>\n              </div>\n            </div>\n          )}\n          \n          {activeSection === 'events' && (\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-blue-900\">Cultural Event Organization & Coordination</h3>\n                <p className=\"text-blue-700\">\n                  Organize and coordinate cultural events that celebrate heritage, facilitate exchange, \n                  and create inclusive celebrations that unite diverse communities.\n                </p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"py-8\">\n        {renderSectionContent()}\n      </div>\n\n      {/* Ubuntu Philosophy Footer */}\n      <div className=\"bg-gray-900 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-xl font-semibold mb-4\">Ubuntu: \"I am because we are\"</h3>\n            <p className=\"text-gray-300 max-w-3xl mx-auto\">\n              Our cross-cultural collaboration tools are built on the Ubuntu philosophy, \n              recognizing that our individual growth and success are interconnected with \n              the wellbeing and prosperity of our communities. Through respectful collaboration, \n              cultural exchange, and shared learning, we build bridges that strengthen \n              the beautiful tapestry of South African diversity.\n            </p>\n          </div>\n          \n          <div className=\"mt-8 grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"bg-blue-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-medium mb-2\">Respect & Understanding</h4>\n              <p className=\"text-gray-400 text-sm\">\n                Honor all cultural traditions and approaches to collaboration\n              </p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"bg-green-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-medium mb-2\">Inclusive Collaboration</h4>\n              <p className=\"text-gray-400 text-sm\">\n                Ensure all voices are heard and valued in decision-making processes\n              </p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"bg-purple-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-medium mb-2\">Cultural Preservation</h4>\n              <p className=\"text-gray-400 text-sm\">\n                Document and preserve cultural knowledge for future generations\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CrossCulturalCollaborationPage;\n"], "names": ["CrossCulturalProjectService", "data", "created<PERSON>y", "project", "skill", "community", "projectId", "updates", "updatedProject", "deleted", "userId", "member", "culturalContext", "team", "task", "req", "projectTasks", "taskId", "tasks", "taskIndex", "metrics", "members", "cultures", "crossCulturalProjectService", "CrossCulturalProjectDashboard", "projects", "setProjects", "useState", "selectedProject", "setSelectedProject", "setProjectTasks", "teamRecommendations", "setTeamRecommendations", "loading", "setLoading", "activeTab", "setActiveTab", "useEffect", "loadUserProjects", "loadProjectDetails", "userProjects", "error", "recommendations", "handleCreateProject", "handleJoinTeam", "recommendation", "newMember", "renderProjectOverview", "jsxs", "jsx", "culture", "index", "milestone", "renderProjectTasks", "renderTeamManagement", "skillIndex", "renderImpactTracking", "outcome", "Fragment", "tab", "RealTimeCommunicationService", "conversation", "conversationId", "settings", "updatedConversation", "senderId", "messageData", "message", "conversationMessages", "language", "limit", "messages", "request", "originalTranslation", "improvedTranslation", "culturalJustification", "reviewerId", "text", "culturalReference", "targetCulture", "conference", "conferenceId", "uploaderId", "mediaData", "ws<PERSON>ey", "translations", "participant", "translation", "source", "target", "targetLanguage", "t", "participants", "file", "realTimeCommunicationService", "CrossCulturalCommunication", "conversations", "setConversations", "activeConversation", "setActiveConversation", "setMessages", "newMessage", "setNewMessage", "selectedLanguage", "setSelectedLanguage", "translationEnabled", "setTranslationEnabled", "culturalGuidanceEnabled", "setCulturalGuidanceEnabled", "analytics", "setAnalytics", "messagesEndRef", "useRef", "supportedLanguages", "loadConversations", "loadConversation", "loadMessages", "loadAnalytics", "scrollToBottom", "convId", "conversationAnalytics", "handleSendMessage", "sentMessage", "prev", "handleKeyPress", "e", "createNewConversation", "conversationData", "newConversation", "getMessageTranslation", "renderMessage", "isOwnMessage", "displayText", "annotation", "renderCulturalGuidance", "renderAnalytics", "lang", "l", "CulturalEventService", "organizerId", "event", "element", "objective", "eventId", "updatedEvent", "culturalFocus", "location", "startDate", "endDate", "attendee", "contribution", "need", "interest", "eventAtten<PERSON>es", "attendees", "attendeeIndex", "performance", "performer", "eventPerformances", "workshop", "material", "eventWorkshops", "performances", "workshops", "strategy", "promotion", "tc", "attendeeId", "momentData", "culturalMoment", "schedule", "b", "representation", "a", "array", "key", "result", "item", "group", "culturalEventService", "CulturalEventDashboard", "events", "setEvents", "selectedEvent", "setSelectedEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventImpact", "setEventImpact", "loadEvents", "loadEventDetails", "sampleEvents", "createSampleEvents", "eventData", "impact", "handleCreateEvent", "handleRSVP", "rsvpData", "renderEventOverview", "renderEventProgramming", "renderAttendees", "contribIndex", "renderImpact", "CrossCulturalCollaborationPage", "activeSection", "setActiveSection", "renderSectionContent"], "mappings": "gHA+CA,MAAMA,CAA4B,CACxB,aAAkD,IAClD,UAAwC,IACxC,UAAsC,IAG9C,MAAM,cAAcC,EAA2BC,EAAkD,CAC/F,MAAMC,EAAgC,CACpC,GAAI,KAAK,WAAW,EACpB,MAAOF,EAAK,MACZ,YAAaA,EAAK,YAClB,OAAQA,EAAK,OACb,WAAYA,EAAK,WACjB,gBAAiB,CACf,gBAAiBA,EAAK,gBAAgB,iBAAmB,CAAC,EAC1D,mBAAoBA,EAAK,gBAAgB,oBAAsB,CAAC,EAChE,sBAAuBA,EAAK,gBAAgB,uBAAyB,CAAC,EACtE,gCAAiCA,EAAK,gBAAgB,iCAAmC,GACzF,qBAAsBA,EAAK,gBAAgB,sBAAwB,CAAC,EACpE,mBAAoBA,EAAK,SAAS,mBAClC,sBAAuB,CAAA,CACzB,EACA,eAAgBA,EAAK,eAAe,IAAIG,IAAU,CAAE,MAAAA,EAAO,MAAO,eAAgB,SAAU,QAAW,EAAA,EACvG,kBAAmBH,EAAK,kBAAkB,IAAkBI,IAAA,CAC1D,UAAAA,EACA,KAAM,cACN,eAAgB,OAAA,EAChB,EACF,SAAU,CACR,UAAWJ,EAAK,SAAS,UACzB,QAASA,EAAK,SAAS,QACvB,OAAQ,CAAC,EACT,eAAgBA,EAAK,SAAS,kBAChC,EACA,OAAQ,WACR,OAAQ,KAAK,wBAAwB,EACrC,KAAM,KAAK,eAAe,EAC1B,mBAAoB,CAAC,EACrB,uBAAwB,CAAC,EACzB,UAAAC,EACA,cAAe,KACf,cAAe,IACjB,EAEA,YAAK,SAAS,IAAIC,EAAQ,GAAIA,CAAO,EACrC,KAAK,MAAM,IAAIA,EAAQ,GAAI,CAAA,CAAE,EAC7B,KAAK,MAAM,IAAIA,EAAQ,GAAIA,EAAQ,IAAI,EAEhCA,CAAA,CAGT,MAAM,WAAWG,EAAyD,CACxE,OAAO,KAAK,SAAS,IAAIA,CAAS,GAAK,IAAA,CAGzC,MAAM,cAAcA,EAAmBC,EAAuE,CAC5G,MAAMJ,EAAU,KAAK,SAAS,IAAIG,CAAS,EAC3C,GAAI,CAACH,EACG,MAAA,IAAI,MAAM,mBAAmB,EAGrC,MAAMK,EAAiB,CACrB,GAAGL,EACH,GAAGI,EACH,cAAe,IACjB,EAEK,YAAA,SAAS,IAAID,EAAWE,CAAc,EACpCA,CAAA,CAGT,MAAM,cAAcF,EAAqC,CACvD,MAAMG,EAAU,KAAK,SAAS,OAAOH,CAAS,EACzC,YAAA,MAAM,OAAOA,CAAS,EACtB,KAAA,MAAM,OAAOA,CAAS,EACpBG,CAAA,CAGT,MAAM,kBAAkBC,EAAiD,CACvE,OAAO,MAAM,KAAK,KAAK,SAAS,OAAQ,CAAA,EAAE,OAAOP,GAC/CA,EAAQ,YAAcO,GACtBP,EAAQ,KAAK,QAAQ,KAAKQ,GAAUA,EAAO,SAAWD,CAAM,CAC9D,CAAA,CAGF,MAAM,6BAA6BE,EAA0D,CAC3F,OAAO,MAAM,KAAK,KAAK,SAAS,OAAQ,CAAA,EAAE,OACxCT,GAAAA,EAAQ,gBAAgB,gBAAgB,SAASS,CAAe,CAClE,CAAA,CAIF,MAAM,yBAAyBN,EAAkD,CAE/E,GAAI,CADY,KAAK,SAAS,IAAIA,CAAS,EAEnC,MAAA,IAAI,MAAM,mBAAmB,EAkC9B,MA9BuC,CAC5C,CACE,OAAQ,QACR,mBAAoB,OACpB,OAAQ,CAAC,qBAAsB,sBAAsB,EACrD,kBAAmB,CAAC,yBAA0B,mBAAmB,EACjE,oBAAqB,GACrB,KAAM,eACN,qBAAsB,iEACxB,EACA,CACE,OAAQ,QACR,mBAAoB,YACpB,OAAQ,CAAC,wBAAyB,YAAY,EAC9C,kBAAmB,CAAC,0BAA2B,uBAAuB,EACtE,oBAAqB,GACrB,KAAM,eACN,qBAAsB,yDACxB,EACA,CACE,OAAQ,QACR,mBAAoB,QACpB,OAAQ,CAAC,oBAAqB,uBAAuB,EACrD,kBAAmB,CAAC,kBAAmB,qBAAqB,EAC5D,oBAAqB,GACrB,KAAM,0BACN,qBAAsB,6DAAA,CAE1B,CAEO,CAGT,MAAM,cAAcA,EAAmBK,EAA6C,CAClF,MAAME,EAAO,KAAK,MAAM,IAAIP,CAAS,EACrC,GAAI,CAACO,EACG,MAAA,IAAI,MAAM,wBAAwB,EAG1C,OAAAA,EAAK,QAAQ,KAAK,CAChB,GAAGF,EACH,aAAc,IAAK,CACpB,EAGDE,EAAK,uBAAyB,KAAK,gCAAgCA,EAAK,OAAO,EAE1E,KAAA,MAAM,IAAIP,EAAWO,CAAI,EACvBA,CAAA,CAGT,MAAM,iBAAiBP,EAAmBI,EAAsC,CAC9E,MAAMG,EAAO,KAAK,MAAM,IAAIP,CAAS,EACrC,GAAI,CAACO,EACG,MAAA,IAAI,MAAM,wBAAwB,EAG1C,OAAAA,EAAK,QAAUA,EAAK,QAAQ,OAAiBF,GAAAA,EAAO,SAAWD,CAAM,EACrEG,EAAK,uBAAyB,KAAK,gCAAgCA,EAAK,OAAO,EAE1E,KAAA,MAAM,IAAIP,EAAWO,CAAI,EACvBA,CAAA,CAIT,MAAM,WAAWZ,EAA8C,CAC7D,MAAMa,EAAoB,CACxB,GAAI,KAAK,WAAW,EACpB,UAAWb,EAAK,UAChB,MAAOA,EAAK,MACZ,YAAaA,EAAK,YAClB,gBAAiB,CACf,qBAAsBA,EAAK,qBAC3B,sBAAuB,CAAC,EACxB,2BAA4B,CAAA,CAC9B,EACA,WAAY,CAAC,EACb,eAAgBA,EAAK,eACrB,qBAAsBA,EAAK,qBAAqB,IAAYc,IAAA,CAC1D,YAAaA,EACb,SAAU,QAAA,EACV,EACF,SAAUd,EAAK,SACf,OAAQ,UACR,eAAgBA,EAAK,eACrB,iBAAkB,CAAC,EACnB,aAAc,CAAC,EACf,QAASA,EAAK,QACd,cAAe,KACf,cAAe,IACjB,EAEMe,EAAe,KAAK,MAAM,IAAIf,EAAK,SAAS,GAAK,CAAC,EACxD,OAAAe,EAAa,KAAKF,CAAI,EACtB,KAAK,MAAM,IAAIb,EAAK,UAAWe,CAAY,EAEpCF,CAAA,CAGT,MAAM,gBAAgBR,EAA2C,CAC/D,OAAO,KAAK,MAAM,IAAIA,CAAS,GAAK,CAAC,CAAA,CAGvC,MAAM,WAAWW,EAAgBV,EAAqD,CACpF,SAAW,CAACD,EAAWY,CAAK,IAAK,KAAK,MAAM,UAAW,CACrD,MAAMC,EAAYD,EAAM,UAAkBJ,GAAAA,EAAK,KAAOG,CAAM,EAC5D,GAAIE,IAAc,GAChB,OAAAD,EAAMC,CAAS,EAAI,CACjB,GAAGD,EAAMC,CAAS,EAClB,GAAGZ,EACH,cAAe,IACjB,EACK,KAAA,MAAM,IAAID,EAAWY,CAAK,EACxBA,EAAMC,CAAS,CACxB,CAEI,MAAA,IAAI,MAAM,gBAAgB,CAAA,CAIlC,MAAM,oBAAoBb,EAAmBc,EAAyD,CACpG,MAAMjB,EAAU,KAAK,SAAS,IAAIG,CAAS,EAC3C,GAAI,CAACH,EACG,MAAA,IAAI,MAAM,mBAAmB,EAGrC,OAAAA,EAAQ,OAAS,CACf,GAAGA,EAAQ,OACX,GAAGiB,CACL,EAEK,KAAA,SAAS,IAAId,EAAWH,CAAO,EAC7BA,EAAQ,MAAA,CAGjB,MAAM,4BAA4BG,EAAiC,CACjE,MAAMH,EAAU,KAAK,SAAS,IAAIG,CAAS,EAC3C,GAAI,CAACH,EACG,MAAA,IAAI,MAAM,mBAAmB,EAG9B,MAAA,CACL,qBAAsBA,EAAQ,OAAO,qBACrC,sBAAuBA,EAAQ,OAAO,iBACtC,oBAAqBA,EAAQ,OAAO,eACpC,mBAAoBA,EAAQ,OAAO,cACrC,CAAA,CAIM,YAAqB,CACpB,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAA,CAGvC,yBAAyC,CACxC,MAAA,CACL,eAAgB,CAAE,YAAa,EAAG,YAAa,EAAG,QAAS,EAAG,EAC9D,eAAgB,CAAE,kBAAmB,EAAG,gBAAiB,EAAG,cAAe,CAAE,EAC7E,aAAc,CAAE,oBAAqB,EAAG,sBAAuB,EAAG,kBAAmB,CAAE,EACvF,eAAgB,CAAE,qBAAsB,EAAG,eAAgB,EAAG,oBAAqB,CAAE,EACrF,iBAAkB,CAAC,EACnB,sBAAuB,CAAC,EACxB,qBAAsB,CAAA,CACxB,CAAA,CAGM,gBAA8B,CAC7B,MAAA,CACL,QAAS,CAAC,EACV,wBAAyB,CAAC,EAC1B,aAAc,CAAE,OAAQ,GAAI,SAAU,CAAE,EACxC,uBAAwB,EACxB,yBAA0B,CAAC,EAC3B,sBAAuB,CAAE,KAAM,YAAa,uBAAwB,CAAA,CAAG,EACvE,2BAA4B,CAAE,SAAU,YAAa,oBAAqB,CAAG,CAAA,CAC/E,CAAA,CAGM,gCAAgCkB,EAAkC,CAClE,MAAAC,EAAW,IAAI,IAAID,EAAQ,IAAcV,GAAAA,EAAO,mBAAmB,cAAc,CAAC,EAExF,OAAO,KAAK,IAAKW,EAAS,KADN,GAC4B,IAAK,GAAG,CAAA,CAE5D,CAEa,MAAAC,EAA8B,IAAIvB,EC9TzCwB,EAA8E,CAAC,CAAE,OAAAd,KAAa,CAClG,KAAM,CAACe,EAAUC,CAAW,EAAIC,EAAAA,SAAiC,CAAA,CAAE,EAC7D,CAACC,EAAiBC,CAAkB,EAAIF,EAAAA,SAAsC,IAAI,EAClF,CAACX,EAAcc,CAAe,EAAIH,EAAAA,SAAwB,CAAA,CAAE,EAC5D,CAACI,EAAqBC,CAAsB,EAAIL,EAAAA,SAA+B,CAAA,CAAE,EACjF,CAACM,EAASC,CAAU,EAAIP,EAAAA,SAAS,EAAI,EACrC,CAACQ,EAAWC,CAAY,EAAIT,EAAAA,SAAmD,UAAU,EAE/FU,EAAAA,UAAU,IAAM,CACGC,EAAA,CAAA,EAChB,CAAC5B,CAAM,CAAC,EAEX2B,EAAAA,UAAU,IAAM,CACVT,GACFW,EAAmBX,EAAgB,EAAE,CACvC,EACC,CAACA,CAAe,CAAC,EAEpB,MAAMU,EAAmB,SAAY,CAC/B,GAAA,CACFJ,EAAW,EAAI,EACf,MAAMM,EAAe,MAAMjB,EAA4B,kBAAkBb,CAAM,EAC/EgB,EAAYc,CAAY,EACpBA,EAAa,OAAS,GACLX,EAAAW,EAAa,CAAC,CAAC,QAE7BC,EAAO,CACN,QAAA,MAAM,0BAA2BA,CAAK,CAAA,QAC9C,CACAP,EAAW,EAAK,CAAA,CAEpB,EAEMK,EAAqB,MAAOjC,GAAsB,CAClD,GAAA,CACF,MAAMY,EAAQ,MAAMK,EAA4B,gBAAgBjB,CAAS,EACnEoC,EAAkB,MAAMnB,EAA4B,yBAAyBjB,CAAS,EAC5FwB,EAAgBZ,CAAK,EACrBc,EAAuBU,CAAe,QAC/BD,EAAO,CACN,QAAA,MAAM,iCAAkCA,CAAK,CAAA,CAEzD,EAEME,EAAsB,SAAY,CAEtC,QAAQ,IAAI,mCAAmC,CACjD,EAEMC,EAAiB,MAAOC,GAAuC,CACnE,GAAKjB,EAED,GAAA,CACF,MAAMkB,EAA2B,CAC/B,OAAQD,EAAe,OACvB,KAAMA,EAAe,KACrB,mBAAoB,CAClB,eAAgBA,EAAe,mBAC/B,kBAAmB,CAAC,EACpB,UAAW,CAAC,EACZ,kBAAmBA,EAAe,kBAClC,qBAAsB,CAAA,CACxB,EACA,mBAAoBA,EAAe,OAAO,IAAczC,IAAA,CACtD,MAAAA,EACA,MAAO,eACP,WAAY,WACZ,gBAAiByC,EAAe,kBAAA,EAChC,EACF,oBAAqB,CACnB,aAAc,GACd,cAAe,CAAC,SAAU,YAAa,QAAQ,EAC/C,SAAU,sBACV,oBAAqB,CAAA,CACvB,EACA,yBAA0B,CAAC,CACzB,eAAgBA,EAAe,qBAC/B,YAAa,uCACb,gBAAiBA,EAAe,mBAChC,eAAgB,oBAAA,CACjB,EACD,aAAc,KACd,gBAAiB,WACnB,EAEA,MAAMtB,EAA4B,cAAcK,EAAgB,GAAIkB,CAAS,EACvE,MAAAP,EAAmBX,EAAgB,EAAE,QACpCa,EAAO,CACN,QAAA,MAAM,sBAAuBA,CAAK,CAAA,CAE9C,EAEMM,EAAwB,IAC3BC,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAc,iBAAA,EACtEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAsB,YAAiB,OAAO,EAE3DD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAgB,mBAAA,QAC9D,MAAI,CAAA,UAAU,uBACZ,SAAiBrB,GAAA,gBAAgB,gBAAgB,IAAI,CAACsB,EAASC,UAC7D,OAAiB,CAAA,UAAU,2DACzB,SADQD,CAAA,EAAAC,CAEX,CACD,CACH,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAkB,qBAAA,QAChE,MAAI,CAAA,UAAU,uBACZ,SAAiBrB,GAAA,kBAAkB,IAAI,CAACvB,EAAW8C,IAClDF,MAAC,QAAiB,UAAU,6DACzB,WAAU,SADF,EAAAE,CAEX,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAH,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAmB,sBAAA,EAC3EA,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,YAAiB,gBAAgB,mBAAmB,IAAI,CAACG,EAAWD,IAClEH,EAAA,KAAA,MAAA,CAAgB,UAAU,oCACzB,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAG,EAAU,MAAM,EAC1DH,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAyB,WAAU,qBAAqB,EACrED,EAAAA,KAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,CAAA,WAASI,EAAU,WAAW,mBAAmB,CAAA,CAAE,CAAA,CAAA,CAHhF,EAAAD,CAIV,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIE,EAAqB,IACxBL,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAa,gBAAA,EAChEA,EAAA,IAAA,SAAA,CAAO,UAAU,gEAAgE,SAElF,UAAA,CAAA,CAAA,EACF,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAU,aACZ,SAAAjC,EAAa,IAAKF,GACjBkC,EAAA,KAAC,MAAkB,CAAA,UAAU,iCAC3B,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAnC,EAAK,MAAM,EACtDmC,EAAAA,IAAC,QAAK,UAAW,kCACfnC,EAAK,SAAW,YAAc,8BAC9BA,EAAK,SAAW,cAAgB,4BAChCA,EAAK,SAAW,SAAW,gCAC3B,2BACF,GACG,WAAK,OAAO,QAAQ,IAAK,GAAG,CAC/B,CAAA,CAAA,EACF,EAECmC,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA8B,WAAK,YAAY,QAE3D,MAAI,CAAA,UAAU,4BACZ,SAAKnC,EAAA,qBAAqB,IAAI,CAACC,EAAKoC,IACnCF,EAAAA,IAAC,QAAiB,UAAU,0DACzB,WAAI,WADI,EAAAE,CAEX,CACD,EACH,EAEAH,EAAAA,KAAC,MAAI,CAAA,UAAU,0DACb,SAAA,CAAAA,OAAC,OAAK,CAAA,SAAA,CAAA,QAAMlC,EAAK,QAAQ,mBAAmB,CAAA,EAAE,SAC7C,OAAM,CAAA,SAAA,CAAKA,EAAA,eAAe,aAAA,CAAW,CAAA,CAAA,CACxC,CAAA,CAAA,GA1BQA,EAAK,EA2Bf,CACD,CACH,CAAA,CAAA,EACF,EAGIwC,EAAuB,IAC1BN,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAY,eAAA,EACpEA,EAAA,IAAA,MAAA,CAAI,UAAU,aACZ,YAAiB,KAAK,QAAQ,IAAI,CAACtC,EAAQwC,IACzCH,EAAA,KAAA,MAAA,CAAgB,UAAU,0DACzB,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,4BAA6B,SAAAtC,EAAO,KAAK,QAAQ,IAAK,GAAG,CAAE,CAAA,EACzEqC,EAAAA,KAAC,IAAE,CAAA,UAAU,wBACV,SAAA,CAAArC,EAAO,mBAAmB,eAAe,MAAIA,EAAO,gBAAgB,QAAQ,IAAK,GAAG,CAAA,EACvF,EACAsC,EAAAA,IAAC,OAAI,UAAU,4BACZ,WAAO,mBAAmB,MAAM,EAAG,CAAC,EAAE,IAAI,CAAC7C,EAAOmD,UAChD,OAAsB,CAAA,UAAU,sDAC9B,SAAMnD,EAAA,OADEmD,CAEX,CACD,CACH,CAAA,CAAA,EACF,EACAP,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACA,EAAAA,KAAA,IAAA,CAAE,UAAU,wBAAwB,SAAA,CAAA,UAAQrC,EAAO,SAAS,mBAAmB,CAAA,EAAE,EAClFqC,EAAAA,KAAC,IAAE,CAAA,UAAU,wBAAyB,SAAA,CAAArC,EAAO,oBAAoB,aAAa,QAAA,CAAM,CAAA,CAAA,CACtF,CAAA,CAAA,CAjBQ,EAAAwC,CAkBV,CACD,CACH,CAAA,CAAA,EACF,EAEAH,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAwB,2BAAA,EAChFA,EAAA,IAAA,MAAA,CAAI,UAAU,aACZ,SAAoBlB,EAAA,IAAI,CAACc,EAAgBM,IACxCH,EAAAA,KAAC,MAAgB,CAAA,UAAU,wBACzB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,4BAA6B,SAAAJ,EAAe,KAAK,QAAQ,IAAK,GAAG,CAAE,CAAA,EAChFI,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAyB,WAAe,kBAAmB,CAAA,CAAA,EAC1E,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,uCAAwC,SAAA,CAAeH,EAAA,oBAAoB,GAAA,EAAC,EAC3FI,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAK,OAAA,CAAA,CAAA,CAC5C,CAAA,CAAA,EACF,EAECA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA8B,WAAe,qBAAqB,QAE9E,MAAI,CAAA,UAAU,4BACZ,SAAAJ,EAAe,OAAO,IAAI,CAACzC,EAAOmD,UAChC,OAAsB,CAAA,UAAU,wDAC9B,SADQnD,CAAA,EAAAmD,CAEX,CACD,EACH,EAEAN,EAAA,IAAC,SAAA,CACC,QAAS,IAAML,EAAeC,CAAc,EAC5C,UAAU,uEACX,SAAA,gBAAA,CAAA,CAED,CA3BQ,EAAAM,CA4BV,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIK,EAAuB,IAC1BR,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAe,kBAAA,QACvE,IAAE,CAAA,UAAU,mCAAoC,SAAiBrB,GAAA,OAAO,eAAe,YAAY,EACnGqB,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAmB,qBAAA,CAAA,CAAA,EAC1D,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAe,kBAAA,EACxED,EAAAA,KAAC,IAAE,CAAA,UAAU,oCAAqC,SAAA,CAAApB,GAAiB,OAAO,eAAe,kBAAkB,GAAA,EAAC,EAC3GqB,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAkB,oBAAA,CAAA,CAAA,EACzD,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAa,gBAAA,QACrE,IAAE,CAAA,UAAU,qCAAsC,SAAiBrB,GAAA,OAAO,aAAa,oBAAoB,EAC3GqB,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAoB,sBAAA,CAAA,CAAA,CAC3D,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAA0B,6BAAA,EAClFA,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,YAAiB,OAAO,iBAAiB,IAAI,CAACQ,EAASN,IACrDH,EAAA,KAAA,MAAA,CAAgB,UAAU,oCACzB,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAQ,EAAQ,QAAQ,EAC1DR,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAyB,WAAQ,gBAAgB,EAC9DD,EAAAA,KAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,CAAA,SAAOS,EAAQ,YAAA,CAAa,CAAA,CAAA,CAHzD,EAAAN,CAIV,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGF,OAAIlB,EAEAgB,MAAC,OAAI,UAAU,wCACb,eAAC,MAAI,CAAA,UAAU,iEAAiE,CAClF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAuB,0BAAA,EACxEA,EAAA,IAAC,SAAA,CACC,QAASN,EACT,UAAU,4EACX,SAAA,oBAAA,CAAA,CAED,EACF,EAEAK,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAEb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,gBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,6BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,eACb,SAAAA,EAAA,IAAC,MAAG,UAAU,8BAA8B,yBAAa,CAC3D,CAAA,QACC,MAAI,CAAA,UAAU,gBACZ,SAASxB,EAAA,IAAKtB,GACb6C,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMnB,EAAmB1B,CAAO,EACzC,UAAW,qDACTyB,GAAiB,KAAOzB,EAAQ,GAC5B,4BACA,mBACN,GAEA,SAAA,CAAA8C,EAAA,IAAC,KAAG,CAAA,UAAU,uBAAwB,SAAA9C,EAAQ,MAAM,EACnD8C,EAAA,IAAA,IAAA,CAAE,UAAU,iCAAkC,WAAQ,MAAO,CAAA,CAAA,CAAA,EATzD9C,EAAQ,EAAA,CAWhB,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGC8C,MAAA,MAAA,CAAI,UAAU,gBACZ,YAGGD,EAAA,KAAAU,WAAA,CAAA,SAAA,CAACV,EAAAA,KAAA,MAAA,CAAI,UAAU,sCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,wCAAyC,SAAArB,EAAgB,MAAM,EAC5EqB,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAsB,WAAgB,YAAY,EAE/DD,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAW,kCACfrB,EAAgB,SAAW,SAAW,8BACtCA,EAAgB,SAAW,WAAa,gCACxC,2BACF,GACG,WAAgB,OACnB,EACAoB,EAAAA,KAAC,OAAK,CAAA,UAAU,wBAAwB,SAAA,CAAA,uBACjBpB,EAAgB,KAAK,uBAAuB,GAAA,CACnE,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAoB,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,2BACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,sBACZ,SAAA,CACC,CAAE,IAAK,WAAY,MAAO,UAAW,EACrC,CAAE,IAAK,QAAS,MAAO,OAAQ,EAC/B,CAAE,IAAK,OAAQ,MAAO,MAAO,EAC7B,CAAE,IAAK,SAAU,MAAO,QAAS,CAAA,EACjC,IAAKU,GACLV,EAAA,IAAC,SAAA,CAEC,QAAS,IAAMb,EAAauB,EAAI,GAAU,EAC1C,UAAW,4CACTxB,IAAcwB,EAAI,IACd,gCACA,4EACN,GAEC,SAAIA,EAAA,KAAA,EARAA,EAAI,GAAA,CAUZ,EACH,CACF,CAAA,EAEAX,EAAAA,KAAC,MAAI,CAAA,UAAU,MACZ,SAAA,CAAAb,IAAc,YAAcY,EAAsB,EAClDZ,IAAc,SAAWkB,EAAmB,EAC5ClB,IAAc,QAAUmB,EAAqB,EAC7CnB,IAAc,UAAYqB,EAAqB,CAAA,CAClD,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEJ,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,EC7WA,MAAMI,CAA6B,CACzB,kBAA4D,IAC5D,aAAoD,IACpD,qBAAqD,IACrD,eAAyC,IAGjD,MAAM,mBAAmB3D,EAAgCC,EAAuD,CAC9G,MAAM2D,EAA0C,CAC9C,GAAI,KAAK,WAAW,EACpB,aAAc5D,EAAK,aAAa,IAAeS,IAAA,CAC7C,OAAAA,EACA,mBAAoB,KAAK,0BAA0BA,CAAM,EACzD,kBAAmB,KAAK,yBAAyBA,CAAM,EACvD,yBAA0B,KAAK,gCAAgCA,CAAM,EACrE,aAAc,cACd,aAAc,KACd,aAAc,KACd,YAAa,CAAE,aAAc,GAAM,YAAa,GAAO,UAAW,EAAK,CAAA,EACvE,EACF,iBAAkBT,EAAK,iBACvB,iBAAkB,KAAK,wBAAwBA,EAAK,YAAY,EAChE,gBAAiB,CACf,qBAAsBA,EAAK,gBAAgB,sBAAwB,CAAC,EACpE,sBAAuBA,EAAK,gBAAgB,uBAAyB,CAAC,EACtE,uBAAwBA,EAAK,gBAAgB,wBAA0B,CAAC,EACxE,uBAAwBA,EAAK,gBAAgB,wBAA0B,CAAA,CACzE,EACA,oBAAqB,CACnB,QAASA,EAAK,mBACd,cAAe,GACf,wBAAyB,GACzB,mBAAoB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,CACvF,EACA,wBAAyB,GACzB,mBAAoB,KAAK,4BAA4BA,EAAK,YAAY,EACtE,SAAU,CAAC,EACX,gBAAiB,CAAC,EAClB,cAAe,KACf,iBAAkB,KAClB,gBAAiB,CAAE,YAAa,GAAO,gBAAiB,GAAI,CAC9D,EAEA,YAAK,cAAc,IAAI4D,EAAa,GAAIA,CAAY,EACpD,KAAK,SAAS,IAAIA,EAAa,GAAI,CAAA,CAAE,EAE9BA,CAAA,CAGT,MAAM,gBAAgBC,EAAmE,CACvF,OAAO,KAAK,cAAc,IAAIA,CAAc,GAAK,IAAA,CAGnD,MAAM,2BACJA,EACAC,EACoC,CACpC,MAAMF,EAAe,KAAK,cAAc,IAAIC,CAAc,EAC1D,GAAI,CAACD,EACG,MAAA,IAAI,MAAM,wBAAwB,EAG1C,MAAMG,EAAsB,CAC1B,GAAGH,EACH,GAAGE,EACH,iBAAkB,IACpB,EAEK,YAAA,cAAc,IAAID,EAAgBE,CAAmB,EACnDA,CAAA,CAIT,MAAM,YACJF,EACAG,EACAC,EAC+B,CAC/B,MAAML,EAAe,KAAK,cAAc,IAAIC,CAAc,EAC1D,GAAI,CAACD,EACG,MAAA,IAAI,MAAM,wBAAwB,EAG1C,MAAMM,EAAgC,CACpC,GAAI,KAAK,WAAW,EACpB,eAAAL,EACA,SAAAG,EACA,aAAcC,EAAY,KAC1B,iBAAkBA,EAAY,SAC9B,aAAc,MAAM,KAAK,qBAAqBA,EAAaL,CAAY,EACvE,gBAAiB,MAAM,KAAK,uBAAuBK,EAAY,KAAMA,EAAY,QAAQ,EACzF,YAAaA,EAAY,YACzB,oBAAqB,MAAM,KAAK,4BAA4BA,EAAY,KAAMA,EAAY,QAAQ,EAClG,UAAW,CAAC,EACZ,cAAe,KACf,YAAa,CAAC,EACd,gBAAiB,CAAA,CACnB,EAEME,EAAuB,KAAK,SAAS,IAAIN,CAAc,GAAK,CAAC,EACnE,OAAAM,EAAqB,KAAKD,CAAO,EAC5B,KAAA,SAAS,IAAIL,EAAgBM,CAAoB,EAGzCP,EAAA,iBAAmB,KAC3B,KAAA,cAAc,IAAIC,EAAgBD,CAAY,EAG7C,MAAA,KAAK,iBAAiBC,EAAgBK,CAAO,EAE5CA,CAAA,CAGT,MAAM,YACJL,EACAO,EACAC,EAAgB,GACiB,CACjC,MAAMC,EAAW,KAAK,SAAS,IAAIT,CAAc,GAAK,CAAC,EAEvD,OAAIO,EAEKE,EAAS,MAAM,CAACD,CAAK,EAAE,IAAgBH,IAAA,CAC5C,GAAGA,EACH,YAAa,KAAK,0BAA0BA,EAASE,CAAQ,CAAA,EAC7D,EAGGE,EAAS,MAAM,CAACD,CAAK,CAAA,CAI9B,MAAM,iBAAiBE,EAA0D,CAcxE,MAZiC,CACtC,SAAUA,EAAQ,eAClB,eAAgB,MAAM,KAAK,mBAAmBA,EAAQ,KAAMA,EAAQ,eAAgBA,EAAQ,cAAc,EAC1G,mBAAoB,MAAM,KAAK,gBAAgBA,EAAQ,KAAMA,EAAQ,eAAgBA,EAAQ,eAAe,EAC5G,gBAAiB,KAAK,yBAAyBA,EAAQ,KAAMA,EAAQ,eAAgBA,EAAQ,cAAc,EAC3G,sBAAuB,KAAK,+BAA+BA,EAAQ,KAAMA,EAAQ,eAAe,EAChG,kBAAmB,YACnB,cAAe,MAAM,KAAK,sBAAsBA,EAAQ,KAAMA,EAAQ,eAAe,EACrF,qBAAsB,MAAM,KAAK,6BAA6BA,EAAQ,KAAMA,EAAQ,cAAc,EAClG,WAAY,MACd,CAEO,CAGT,MAAM,mBACJC,EACAC,EACAC,EACAC,EACe,CAEf,QAAQ,IAAI,oCAAqC,CAC/C,SAAUH,EACV,SAAUC,EACV,cAAeC,EACf,SAAUC,CAAA,CACX,CAAA,CAGH,MAAM,uBACJC,EACAC,EACAC,EACc,CAEP,MAAA,CACL,UAAWD,EACX,YAAa,qCAAqCA,CAAiB,QAAQC,CAAa,WACxF,aAAc,sDACd,qBAAsB,CAAC,eAAgB,6BAA8B,gCAAgC,EACrG,uBAAwB,CAAC,sBAAuB,yBAA0B,yBAAyB,CACrG,CAAA,CAIF,MAAM,sBAAsB9E,EAAqD,CAC/E,MAAM+E,EAA8B,CAClC,GAAI,KAAK,WAAW,EACpB,eAAgB,KAAK,WAAW,EAChC,aAAc/E,EAAK,aAAa,IAAeS,IAAA,CAC7C,OAAAA,EACA,mBAAoB,KAAK,0BAA0BA,CAAM,EACzD,aAAc,GACd,aAAc,GACd,0BAA2BT,EAAK,iBAAiB,0BACjD,kBAAmB,KAAK,yBAAyBS,CAAM,CAAA,EACvD,EACF,iBAAkB,CAChB,oBAAqBT,EAAK,iBAAiB,mBAC3C,0BAA2BA,EAAK,iBAAiB,0BACjD,0BAA2BA,EAAK,iBAAiB,0BACjD,6BAA8B,EAChC,EACA,YAAa,CACX,QAASA,EAAK,iBAAiB,mBAC/B,mBAAoB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACrF,sBAAuB,GACvB,4BAA6B,EAC/B,EACA,UAAW,CACT,QAAS,GACT,2BAA4B,GAC5B,6BAA8B,GAC9B,yBAA0B,EAC5B,EACA,cAAe,CAAC,EAChB,mBAAoB,CAAC,EACrB,UAAWA,EAAK,eAAiB,IAAI,KACrC,OAAQA,EAAK,cAAgB,YAAc,QAC7C,EAEA,YAAK,iBAAiB,IAAI+E,EAAW,GAAIA,CAAU,EAC5CA,CAAA,CAGT,MAAM,mBAAmBC,EAAuD,CAC9E,OAAO,KAAK,iBAAiB,IAAIA,CAAY,GAAK,IAAA,CAIpD,MAAM,mBACJnB,EACAoB,EACAC,EAM6B,CAgCtB,MA/BgC,CACrC,GAAI,KAAK,WAAW,EACpB,eAAArB,EACA,WAAAoB,EACA,UAAW,KAAK,mBAAmBC,EAAU,IAAI,EACjD,SAAUA,EAAU,KAAK,KACzB,QAAS,MAAM,KAAK,WAAWA,EAAU,IAAI,EAC7C,gBAAiB,CACf,QAASA,EAAU,gBACnB,aAAcA,EAAU,qBACxB,qBAAsB,GACtB,oBAAqB,CAAA,CACvB,EACA,qBAAsBA,EAAU,qBAChC,mBAAoB,CAClB,aAAcA,EAAU,mBACxB,aAAc,GACd,wBAAyB,GACzB,cAAe,EACjB,EACA,oBAAqB,CACnB,gBAAiBA,EAAU,gBAC3B,iBAAkB,CAACD,CAAU,EAC7B,qBAAsB,GACtB,oBAAqB,EACvB,EACA,YAAa,CAAC,EACd,UAAW,CAAC,EACZ,eAAgB,IAClB,CAEO,CAIT,MAAM,yBAAyBpB,EAAyD,CACtF,MAAMS,EAAW,KAAK,SAAS,IAAIT,CAAc,GAAK,CAAC,EACjDD,EAAe,KAAK,cAAc,IAAIC,CAAc,EAE1D,GAAI,CAACD,EACG,MAAA,IAAI,MAAM,wBAAwB,EAGnC,MAAA,CACL,eAAAC,EACA,mBAAoB,KAAK,4BAA4BS,EAAUV,CAAY,EAC3E,mBAAoB,KAAK,4BAA4BU,CAAQ,EAC7D,2BAA4B,KAAK,oCAAoCA,EAAUV,CAAY,EAC3F,2BAA4B,KAAK,oCAAoCU,CAAQ,EAC7E,qBAAsB,KAAK,8BAA8BA,EAAUV,CAAY,EAC/E,iBAAkB,KAAK,0BAA0BU,EAAUV,CAAY,CACzE,CAAA,CAIF,MAAM,sBAAsBC,EAAwBpD,EAA+B,CAEjF,MAAM0E,EAAQ,GAAGtB,CAAc,IAAIpD,CAAM,GACzC,QAAQ,IAAI,gCAAgCA,CAAM,oBAAoBoD,CAAc,EAAE,EAGtF,KAAK,WAAW,IAAIsB,EAAO,CAAA,CAAe,CAAA,CAG5C,MAAM,2BAA2BtB,EAAwBpD,EAA+B,CACtF,MAAM0E,EAAQ,GAAGtB,CAAc,IAAIpD,CAAM,GACpC,KAAA,WAAW,OAAO0E,CAAK,EAC5B,QAAQ,IAAI,mCAAmC1E,CAAM,oBAAoBoD,CAAc,EAAE,CAAA,CAInF,YAAqB,CACpB,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAA,CAG/C,MAAc,qBACZI,EACAL,EAC+B,CAC/B,MAAMwB,EAAqC,CAAC,EAEjC,UAAAC,KAAezB,EAAa,aACjC,GAAAyB,EAAY,oBAAsBpB,EAAY,SAAU,CACpD,MAAAqB,EAAc,MAAM,KAAK,iBAAiB,CAC9C,KAAMrB,EAAY,KAClB,eAAgBA,EAAY,SAC5B,eAAgBoB,EAAY,kBAC5B,gBAAiBpB,EAAY,iBAAmB,CAAC,EACjD,uBAAwB,EAAA,CACzB,EACDmB,EAAa,KAAKE,CAAW,CAAA,CAI1B,OAAAF,CAAA,CAGT,MAAc,mBAAmBR,EAAcW,EAAgBC,EAAiC,CAE9F,MAAO,IAAIA,EAAO,YAAY,CAAC,KAAKZ,CAAI,EAAA,CAG1C,MAAc,gBAAgBA,EAAca,EAAwB9E,EAAmD,CAE9G,MAAA,2BAA2B8E,CAAc,KAAKb,CAAI,EAAA,CAGnD,yBAAyBA,EAAcW,EAAgBC,EAAwB,CAErF,OAAO,KAAK,MAAM,KAAK,OAAO,EAAI,EAAE,EAAI,EAAA,CAGlC,+BAA+BZ,EAAcjE,EAA0C,CAE7F,OAAO,KAAK,MAAM,KAAK,OAAO,EAAI,EAAE,EAAI,EAAA,CAG1C,MAAc,sBAAsBiE,EAAcjE,EAAqD,CACrG,MAAO,CAAC,iFAAiF,CAAA,CAG3F,MAAc,6BAA6BiE,EAAca,EAA2C,CAClG,MAAO,CAAC,2BAA2BA,CAAc,GAAI,0BAA0BA,CAAc,EAAE,CAAA,CAGjG,MAAc,uBAAuBb,EAAcR,EAAgC,CAC1E,MAAA,CACL,mBAAoB,CAAC,EACrB,OAAQ,CAAC,EACT,eAAgB,UAChB,cAAe,SACjB,CAAA,CAGF,MAAc,4BAA4BQ,EAAcR,EAAkC,CACxF,MAAO,CAAC,CAAA,CAGV,MAAc,iBAAiBP,EAAwBK,EAA8C,CAEnG,QAAQ,IAAI,wBAAwBA,EAAQ,EAAE,oBAAoBL,CAAc,EAAE,CAAA,CAG5E,0BAA0BK,EAA+BE,EAA0B,CACzF,MAAMkB,EAAcpB,EAAQ,aAAa,KAAUwB,GAAAA,EAAE,WAAatB,CAAQ,EACnE,OAAAkB,EAAcA,EAAY,eAAiBpB,EAAQ,YAAA,CAGpD,0BAA0BzD,EAAqB,CAE9C,MAAA,CAAE,eAAgB,OAAQ,kBAAmB,CAAC,SAAS,EAAG,UAAW,CAAC,KAAM,IAAI,CAAE,CAAA,CAGnF,yBAAyBA,EAAwB,CAEhD,MAAA,IAAA,CAGD,gCAAgCA,EAAqB,CAC3D,MAAO,CAAE,MAAO,SAAU,UAAW,WAAY,kBAAmB,EAAG,CAAA,CAGjE,wBAAwBkF,EAAkC,CACzD,MAAA,CAAC,KAAM,KAAM,IAAI,CAAA,CAGlB,4BAA4BA,EAA6B,CAC/D,MAAO,CAAE,QAAS,gBAAiB,SAAU,SAAU,UAAW,YAAa,CAAA,CAGzE,mBAAmBC,EAAmE,CAC5F,OAAIA,EAAK,KAAK,WAAW,QAAQ,EAAU,QACvCA,EAAK,KAAK,WAAW,QAAQ,EAAU,QACvCA,EAAK,KAAK,WAAW,QAAQ,EAAU,QACpC,UAAA,CAGT,MAAc,WAAWA,EAA6B,CAE7C,MAAA,+BAA+BA,EAAK,IAAI,EAAA,CAGzC,4BAA4BtB,EAAkCV,EAA8C,CAClH,MAAO,CAAE,MAAO,GAAI,yBAA0B,GAAI,wBAAyB,CAAE,CAAA,CAGvE,4BAA4BU,EAAuC,CACzE,MAAO,CAAE,kBAAmB,GAAI,iBAAkB,GAAI,iBAAkB,EAAG,CAAA,CAGrE,oCAAoCA,EAAkCV,EAA8C,CAC1H,MAAO,CAAE,mBAAoB,GAAI,0BAA2B,EAAG,oBAAqB,CAAE,CAAA,CAGhF,oCAAoCU,EAAuC,CACjF,MAAO,CAAE,aAAc,GAAI,sBAAuB,EAAG,oBAAqB,EAAG,CAAA,CAGvE,8BAA8BA,EAAkCV,EAA8C,CACpH,MAAO,CAAE,qBAAsB,GAAI,gBAAiB,GAAI,cAAe,EAAG,CAAA,CAGpE,0BAA0BU,EAAkCV,EAA8C,CAChH,MAAO,CAAE,gBAAiB,GAAI,wBAAyB,EAAG,mBAAoB,EAAG,CAAA,CAErF,CAEa,MAAAiC,EAA+B,IAAIlC,ECtd1CmC,EAAwE,CAAC,CAC7E,OAAArF,EACA,eAAAoD,CACF,IAAM,CACJ,KAAM,CAACkC,EAAeC,CAAgB,EAAItE,EAAAA,SAAsC,CAAA,CAAE,EAC5E,CAACuE,EAAoBC,CAAqB,EAAIxE,EAAAA,SAA2C,IAAI,EAC7F,CAAC4C,EAAU6B,CAAW,EAAIzE,EAAAA,SAAiC,CAAA,CAAE,EAC7D,CAAC0E,EAAYC,CAAa,EAAI3E,EAAAA,SAAS,EAAE,EACzC,CAAC4E,EAAkBC,CAAmB,EAAI7E,EAAAA,SAAS,IAAI,EACvD,CAAC8E,EAAoBC,CAAqB,EAAI/E,EAAAA,SAAS,EAAI,EAC3D,CAACgF,EAAyBC,CAA0B,EAAIjF,EAAAA,SAAS,EAAI,EACrE,CAACkF,EAAWC,CAAY,EAAInF,EAAAA,SAAwC,IAAI,EACxE,CAACM,EAASC,CAAU,EAAIP,EAAAA,SAAS,EAAI,EACrCoF,EAAiBC,SAAuB,IAAI,EAE5CC,EAAqB,CACzB,CAAE,KAAM,KAAM,KAAM,SAAU,EAC9B,CAAE,KAAM,KAAM,KAAM,WAAY,EAChC,CAAE,KAAM,KAAM,KAAM,MAAO,EAC3B,CAAE,KAAM,KAAM,KAAM,OAAQ,EAC5B,CAAE,KAAM,KAAM,KAAM,SAAU,EAC9B,CAAE,KAAM,KAAM,KAAM,UAAW,EAC/B,CAAE,KAAM,KAAM,KAAM,SAAU,EAC9B,CAAE,KAAM,KAAM,KAAM,OAAQ,EAC5B,CAAE,KAAM,KAAM,KAAM,QAAS,EAC7B,CAAE,KAAM,KAAM,KAAM,iBAAkB,EACtC,CAAE,KAAM,KAAM,KAAM,iBAAkB,CACxC,EAEA5E,EAAAA,UAAU,IAAM,CACI6E,EAAA,EACdpD,GACFqD,EAAiBrD,CAAc,CACjC,EACC,CAACpD,EAAQoD,CAAc,CAAC,EAE3BzB,EAAAA,UAAU,IAAM,CACV6D,IACFkB,EAAalB,EAAmB,EAAE,EAClCmB,EAAcnB,EAAmB,EAAE,EAENJ,EAAA,sBAAsBI,EAAmB,GAAIxF,CAAM,EAClF,EACC,CAACwF,CAAkB,CAAC,EAEvB7D,EAAAA,UAAU,IAAM,CACCiF,EAAA,CAAA,EACd,CAAC/C,CAAQ,CAAC,EAEb,MAAM2C,EAAoB,SAAY,CAChC,GAAA,CACFhF,EAAW,EAAI,EAEf+D,EAAiB,CAAA,CAAE,QACZxD,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,CAAA,QACnD,CACAP,EAAW,EAAK,CAAA,CAEpB,EAEMiF,EAAmB,MAAOI,GAAmB,CAC7C,GAAA,CACF,MAAM1D,EAAe,MAAMiC,EAA6B,gBAAgByB,CAAM,EAC1E1D,GACFsC,EAAsBtC,CAAY,QAE7BpB,EAAO,CACN,QAAA,MAAM,8BAA+BA,CAAK,CAAA,CAEtD,EAEM2E,EAAe,MAAOG,GAAmB,CACzC,GAAA,CACI,MAAAnD,EAAuB,MAAM0B,EAA6B,YAC9DyB,EACAd,EAAqBF,EAAmB,MAC1C,EACAH,EAAYhC,CAAoB,QACzB3B,EAAO,CACN,QAAA,MAAM,0BAA2BA,CAAK,CAAA,CAElD,EAEM4E,EAAgB,MAAOE,GAAmB,CAC1C,GAAA,CACF,MAAMC,EAAwB,MAAM1B,EAA6B,yBAAyByB,CAAM,EAChGT,EAAaU,CAAqB,QAC3B/E,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,CAAA,CAEnD,EAEM6E,EAAiB,IAAM,CAC3BP,EAAe,SAAS,eAAe,CAAE,SAAU,SAAU,CAC/D,EAEMU,EAAoB,SAAY,CACpC,GAAI,GAACpB,EAAW,KAAK,GAAK,CAACH,GAEvB,GAAA,CACF,MAAMhC,EAAc,CAClB,KAAMmC,EACN,SAAUE,EACV,YAAa,MACf,EAEMmB,EAAc,MAAM5B,EAA6B,YACrDI,EAAmB,GACnBxF,EACAwD,CACF,EAEAkC,EAAoBuB,GAAA,CAAC,GAAGA,EAAMD,CAAW,CAAC,EAC1CpB,EAAc,EAAE,QACT7D,EAAO,CACN,QAAA,MAAM,yBAA0BA,CAAK,CAAA,CAEjD,EAEMmF,EAAkBC,GAA2B,CAC7CA,EAAE,MAAQ,SAAW,CAACA,EAAE,WAC1BA,EAAE,eAAe,EACCJ,EAAA,EAEtB,EAEMK,EAAwB,SAAY,CACpC,GAAA,CACF,MAAMC,EAAmB,CACvB,aAAc,CAACrH,CAAM,EACrB,iBAAkB,aAClB,gBAAiB,CACf,qBAAsB,CAAC,OAAQ,UAAW,WAAW,EACrD,sBAAuB,CAAC,EACxB,uBAAwB,CAAC,EACzB,uBAAwB,CAAA,CAC1B,EACA,mBAAoB,EACtB,EAEMsH,EAAkB,MAAMlC,EAA6B,mBACzDiC,EACArH,CACF,EAEAuF,EAAyB0B,GAAA,CAAC,GAAGA,EAAMK,CAAe,CAAC,EACnD7B,EAAsB6B,CAAe,QAC9BvF,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,CAAA,CAEvD,EAEMwF,EAAyB9D,GAA0C,CACvE,GAAI,CAACsC,GAAsBtC,EAAQ,mBAAqBoC,EACtD,OAAOpC,EAAQ,aAGjB,MAAMoB,EAAcpB,EAAQ,aAAa,KAAUwB,GAAAA,EAAE,WAAaY,CAAgB,EAC3E,OAAAhB,EAAcA,EAAY,eAAiBpB,EAAQ,YAC5D,EAEM+D,EAAiB/D,GAAkC,CACjD,MAAAgE,EAAehE,EAAQ,WAAazD,EACpC0H,EAAcH,EAAsB9D,CAAO,EAC3CoB,EAAcpB,EAAQ,aAAa,KAAUwB,GAAAA,EAAE,WAAaY,CAAgB,EAGhF,OAAAtD,EAAA,IAAC,MAAA,CAEC,UAAW,QAAQkF,EAAe,cAAgB,eAAe,QAEjE,SAAAnF,EAAA,KAAC,MAAA,CACC,UAAW,6CACTmF,EACI,yBACA,2BACN,GAEA,SAAA,CAAClF,EAAA,IAAA,IAAA,CAAE,UAAU,UAAW,SAAYmF,EAAA,EAEnC7C,GAAeA,EAAY,eAAiBA,EAAY,cAAc,OAAS,GAC9EvC,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAc,gBAAA,CAAA,EAAS,IAAEsC,EAAY,cAAc,CAAC,CAAA,EAC9D,EAGDpB,EAAQ,oBAAoB,OAAS,GACpClB,EAAAA,IAAC,OAAI,UAAU,iBACZ,SAAQkB,EAAA,oBAAoB,IAAI,CAACkE,EAAYlF,IAC3CH,EAAAA,KAAA,MAAA,CAAgB,UAAU,kDACzB,SAAA,CAAAA,OAAC,SAAQ,CAAA,SAAA,CAAWqF,EAAA,eAAe,GAAA,EAAC,EAAS,IAAEA,EAAW,OAAA,GADlDlF,CAEV,CACD,EACH,EAGFH,EAAAA,KAAC,MAAI,CAAA,UAAU,4DACb,SAAA,CAAAC,EAAA,IAAC,OAAM,CAAA,SAAAkB,EAAQ,UAAU,mBAAA,EAAqB,EAC7CA,EAAQ,mBAAqBoC,GAC3BvD,EAAA,KAAA,OAAA,CAAK,UAAU,OAAO,SAAA,CAAA,mBACJmB,EAAQ,iBAAiB,YAAY,CAAA,CACxD,CAAA,CAAA,CAEJ,CAAA,CAAA,CAAA,CAAA,CACF,EApCKA,EAAQ,EAqCf,CAEJ,EAEMmE,EAAyB,IACzB,CAAC3B,GAA2B,CAACT,EAA2B,KAG1DlD,EAAA,KAAC,MAAI,CAAA,UAAU,wDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAiB,oBAAA,EAChED,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAA,OAAC,IAAE,CAAA,SAAA,CAAA,kDAAgDkD,EAAmB,gBAAgB,sBAAsB,KAAK,IAAI,EAAE,WAAA,EAAS,EAChIjD,EAAAA,IAAC,KAAE,SAAmF,qFAAA,CAAA,EACtFA,EAAAA,IAAC,KAAE,SAAkE,oEAAA,CAAA,CAAA,CACvE,CAAA,CAAA,EACF,EAIEsF,EAAkB,IACjB1B,EAGH7D,EAAA,KAAC,MAAI,CAAA,UAAU,sCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAsB,yBAAA,EACrED,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAAmB,sBAAA,EAChDD,EAAAA,KAAC,IAAE,CAAA,UAAU,+BAAgC,SAAA,CAAA6D,EAAU,mBAAmB,MAAM,GAAA,CAAC,CAAA,CAAA,EACnF,SACC,MACC,CAAA,SAAA,CAAC5D,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAAmB,sBAAA,EAChDD,EAAAA,KAAC,IAAE,CAAA,UAAU,8BAA+B,SAAA,CAAA6D,EAAU,mBAAmB,kBAAkB,GAAA,CAAC,CAAA,CAAA,EAC9F,SACC,MACC,CAAA,SAAA,CAAC5D,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAAmB,sBAAA,EAChDD,EAAAA,KAAC,IAAE,CAAA,UAAU,gCAAiC,SAAA,CAAA6D,EAAU,2BAA2B,mBAAmB,GAAA,CAAC,CAAA,CAAA,EACzG,SACC,MACC,CAAA,SAAA,CAAC5D,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAAqB,wBAAA,EAClDD,EAAAA,KAAC,IAAE,CAAA,UAAU,gCAAiC,SAAA,CAAA6D,EAAU,qBAAqB,qBAAqB,GAAA,CAAC,CAAA,CAAA,CACrG,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAvBqB,KA2BzB,OAAI5E,EAEAgB,MAAC,OAAI,UAAU,wCACb,eAAC,MAAI,CAAA,UAAU,iEAAiE,CAClF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAA4B,+BAAA,EAC7EA,EAAA,IAAC,SAAA,CACC,QAAS6E,EACT,UAAU,4EACX,SAAA,kBAAA,CAAA,CAED,EACF,EAEA9E,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0BAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAiB,oBAAA,EAChEA,EAAA,IAAC,SAAA,CACC,MAAOsD,EACP,SAAWsB,GAAMrB,EAAoBqB,EAAE,OAAO,KAAK,EACnD,UAAU,+CAET,SAAmBZ,EAAA,IAAKuB,GACtBvF,EAAAA,IAAA,SAAA,CAAuB,MAAOuF,EAAK,KACjC,SAAAA,EAAK,IADK,EAAAA,EAAK,IAElB,CACD,CAAA,CACH,EAEAxF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,oBACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASwD,EACT,SAAWoB,GAAMnB,EAAsBmB,EAAE,OAAO,OAAO,EACvD,UAAU,MAAA,CACZ,EACC5E,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAkB,oBAAA,CAAA,CAAA,EAC9C,EAEAD,EAAAA,KAAC,QAAM,CAAA,UAAU,oBACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS0D,EACT,SAAWkB,GAAMjB,EAA2BiB,EAAE,OAAO,OAAO,EAC5D,UAAU,MAAA,CACZ,EACC5E,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAiB,mBAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGCsF,EAAgB,CAAA,EACnB,EAGAtF,EAAAA,IAAC,OAAI,UAAU,gBACZ,WACED,EAAAA,KAAA,MAAA,CAAI,UAAU,gDAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4BAA4B,SAE1C,8BAAA,EACAD,EAAAA,KAAC,IAAE,CAAA,UAAU,wBACV,SAAA,CAAAkD,EAAmB,aAAa,OAAO,kBACvCA,EAAmB,iBAAiB,KAAK,IAAI,EAAE,YAAA,CAClD,CAAA,CAAA,EACF,EAGCjD,EAAA,IAAA,MAAA,CAAI,UAAU,MACZ,aACH,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACZ,SAAA,CAAAuB,EAAS,IAAI2D,CAAa,EAC3BjF,EAAAA,IAAC,MAAI,CAAA,IAAK8D,CAAgB,CAAA,CAAA,EAC5B,EAGA/D,EAAAA,KAAC,MAAI,CAAA,UAAU,+BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAAAC,EAAA,IAAC,WAAA,CACC,MAAOoD,EACP,SAAWwB,GAAMvB,EAAcuB,EAAE,OAAO,KAAK,EAC7C,WAAYD,EACZ,YAAa,wBAAwBX,EAAmB,QAAUwB,EAAE,OAASlC,CAAgB,GAAG,IAAI,MACpG,UAAU,2DACV,KAAM,CAAA,CACR,EACAtD,EAAA,IAAC,SAAA,CACC,QAASwE,EACT,SAAU,CAACpB,EAAW,KAAK,EAC3B,UAAU,gHACX,SAAA,MAAA,CAAA,CAED,EACF,EAEApD,EAAAA,IAAC,OAAI,UAAU,6BACZ,YACEA,EAAAA,IAAA,OAAA,CAAK,yEAA8D,CAAA,CAExE,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAEvD,0CAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAIlC,4NAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAAS6E,EACT,UAAU,4EACX,SAAA,+BAAA,CAAA,CAED,CAAA,CACF,CAEJ,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECtUA,MAAMY,CAAqB,CACjB,WAAyC,IACzC,cAA8C,IAC9C,iBAAuD,IACvD,cAAiD,IACjD,eAA8C,IAGtD,MAAM,YAAYzI,EAAyB0I,EAA6C,CACtF,MAAMC,EAAuB,CAC3B,GAAI,KAAK,WAAW,EACpB,MAAO3I,EAAK,MACZ,YAAaA,EAAK,YAClB,gBAAiB,CACf,gBAAiBA,EAAK,gBAAgB,gBACtC,qBAAsBA,EAAK,gBAAgB,qBAC3C,oBAAqBA,EAAK,gBAAgB,oBAAoB,IAAgB4I,IAAA,CAC5E,QAAAA,EACA,aAAc,gCACd,aAAc,UAAA,EACd,EACF,2BAA4B5I,EAAK,gBAAgB,2BAA2B,IAAkB6I,IAAA,CAC5F,UAAAA,EACA,gBAAiB7I,EAAK,gBAAgB,gBAAgB,KAAK,IAAI,EAC/D,gBAAiB,iCAAA,EACjB,EACF,sBAAuB,CAAC,EACxB,kBAAmB,CAAC,EACpB,oBAAqB,CAAC,EACtB,mBAAoB,CAAA,CACtB,EACA,UAAWA,EAAK,UAChB,OAAQA,EAAK,OACb,SAAU,CACR,MAAOA,EAAK,SAAS,MACrB,QAASA,EAAK,SAAS,QACvB,gBAAiBA,EAAK,SAAS,gBAC/B,qBAAsBA,EAAK,SAAS,qBACpC,cAAeA,EAAK,cACpB,uBAAwB,CAAA,CAC1B,EACA,SAAU,CACR,UAAWA,EAAK,SAAS,UACzB,QAASA,EAAK,SAAS,QACvB,SAAU,sBACV,0BAA2BA,EAAK,SAAS,+BACzC,mBAAoB,CAAA,CACtB,EACA,WAAY,CAAC,CACX,OAAQ0I,EACR,KAAM,oBACN,mBAAoB,KAAK,0BAA0BA,CAAW,EAC9D,iBAAkB,CAAC,uBAAwB,oBAAoB,EAC/D,YAAa,CAAC,aAAc,mBAAoB,iBAAiB,CAAA,CAClE,EACD,kBAAmB1I,EAAK,kBAAkB,IAAkBI,IAAA,CAC1D,UAAAA,EACA,oBAAqB,WACrB,iBAAkB,oBAClB,iBAAkB,uBAAA,EAClB,EACF,qBAAsB,CAAC,EACvB,YAAa,KAAK,2BAA2B,EAC7C,UAAW,CACT,QAAS,GACT,QAAS,CAAE,KAAM,GAAM,MAAO,CAAA,CAAG,EACjC,cAAe,CAAE,aAAc,GAAM,iBAAkB,EAAK,EAC5D,uBAAwB,CAAA,CAC1B,EACA,cAAeJ,EAAK,cACpB,UAAW,KAAK,4BAA4B,EAC5C,OAAQ,WACR,UAAW,CAAC,EACZ,OAAQ,KAAK,sBAAsB,EACnC,sBAAuB,KAAK,gCAAgC,EAC5D,cAAe,KACf,cAAe,IACjB,EAEA,YAAK,OAAO,IAAI2I,EAAM,GAAIA,CAAK,EAC/B,KAAK,UAAU,IAAIA,EAAM,GAAI,CAAA,CAAE,EAC/B,KAAK,aAAa,IAAIA,EAAM,GAAI,CAAA,CAAE,EAClC,KAAK,UAAU,IAAIA,EAAM,GAAI,CAAA,CAAE,EAExBA,CAAA,CAGT,MAAM,SAASG,EAAgD,CAC7D,OAAO,KAAK,OAAO,IAAIA,CAAO,GAAK,IAAA,CAGrC,MAAM,YAAYA,EAAiBxI,EAAyD,CAC1F,MAAMqI,EAAQ,KAAK,OAAO,IAAIG,CAAO,EACrC,GAAI,CAACH,EACG,MAAA,IAAI,MAAM,iBAAiB,EAGnC,MAAMI,EAAe,CACnB,GAAGJ,EACH,GAAGrI,EACH,cAAe,IACjB,EAEK,YAAA,OAAO,IAAIwI,EAASC,CAAY,EAC9BA,CAAA,CAGT,MAAM,YAAYD,EAAmC,CACnD,MAAMtI,EAAU,KAAK,OAAO,OAAOsI,CAAO,EACrC,YAAA,UAAU,OAAOA,CAAO,EACxB,KAAA,aAAa,OAAOA,CAAO,EAC3B,KAAA,UAAU,OAAOA,CAAO,EACxB,KAAA,WAAW,OAAOA,CAAO,EACvBtI,CAAA,CAGT,MAAM,yBAAyBwI,EAAiD,CAC9E,OAAO,MAAM,KAAK,KAAK,OAAO,OAAQ,CAAA,EAAE,OACtCL,GAAAA,EAAM,gBAAgB,gBAAgB,SAASK,CAAa,CAC9D,CAAA,CAGF,MAAM,oBAAoBC,EAA4C,CACpE,OAAO,MAAM,KAAK,KAAK,OAAO,OAAQ,CAAA,EAAE,OAAON,GAC7CA,EAAM,SAAS,SAAS,SAASM,CAAQ,GAAKN,EAAM,SAAS,OAAO,SAASM,CAAQ,CACvF,CAAA,CAGF,MAAM,qBAAqBC,EAAiBC,EAAyC,CACnF,OAAO,MAAM,KAAK,KAAK,OAAO,OAAQ,CAAA,EAAE,UACtCR,EAAM,SAAS,WAAaO,GAAaP,EAAM,SAAS,SAAWQ,CACrE,CAAA,CAIF,MAAM,YAAYnJ,EAAwC,CACxD,MAAM2I,EAAQ,KAAK,OAAO,IAAI3I,EAAK,OAAO,EAC1C,GAAI,CAAC2I,EACG,MAAA,IAAI,MAAM,iBAAiB,EAGnC,MAAMS,EAA0B,CAC9B,OAAQpJ,EAAK,OACb,qBAAsB,KACtB,mBAAoB,CAClB,eAAgBA,EAAK,mBACrB,kBAAmB,CAAC,EACpB,UAAW,CAAC,EACZ,kBAAmB,CAAC,EACpB,qBAAsB,CAAA,CACxB,EACA,eAAgBA,EAAK,eACrB,sBAAuBA,EAAK,sBAAsB,IAAqBqJ,IAAA,CACrE,iBAAkB,qBAClB,YAAaA,EACb,gBAAiBrJ,EAAK,mBACtB,mBAAoB,CAAC,oBAAoB,CAAA,EACzC,EACF,oBAAqBA,EAAK,oBAAoB,IAAYc,IAAA,CACxD,YAAaA,EACb,gBAAiBd,EAAK,mBACtB,SAAU,YAAA,EACV,EACF,mBAAoBA,EAAK,mBAAmB,IAAasJ,IAAA,CACvD,KAAAA,EACA,cAAe,WACf,gBAAiB,EAAA,EACjB,EACF,kBAAmBtJ,EAAK,kBAAkB,IAAiBuJ,IAAA,CACzD,SAAAA,EACA,MAAO,aACP,gBAAiBvJ,EAAK,kBAAA,EACtB,EACF,sBAAuB,CAAC,EACxB,cAAe,CACb,UAAW,GACX,YAAa,OACb,gBAAiB,EAAA,CAErB,EAEMwJ,EAAiB,KAAK,UAAU,IAAIxJ,EAAK,OAAO,GAAK,CAAC,EAC5D,OAAAwJ,EAAe,KAAKJ,CAAQ,EAC5B,KAAK,UAAU,IAAIpJ,EAAK,QAASwJ,CAAc,EAGzCb,EAAA,UAAU,KAAKS,CAAQ,EAC7B,KAAK,OAAO,IAAIpJ,EAAK,QAAS2I,CAAK,EAE5BS,CAAA,CAGT,MAAM,kBAAkBN,EAA2C,CACjE,OAAO,KAAK,UAAU,IAAIA,CAAO,GAAK,CAAC,CAAA,CAGzC,MAAM,eAAeA,EAAiBrI,EAAgBH,EAAyD,CAC7G,MAAMmJ,EAAY,KAAK,UAAU,IAAIX,CAAO,GAAK,CAAC,EAC5CY,EAAgBD,EAAU,UAAsBL,GAAAA,EAAS,SAAW3I,CAAM,EAEhF,GAAIiJ,IAAkB,GACd,MAAA,IAAI,MAAM,oBAAoB,EAGtC,OAAAD,EAAUC,CAAa,EAAI,CACzB,GAAGD,EAAUC,CAAa,EAC1B,GAAGpJ,CACL,EAEK,KAAA,UAAU,IAAIwI,EAASW,CAAS,EAC9BA,EAAUC,CAAa,CAAA,CAIhC,MAAM,eAAe1J,EAAqD,CACxE,MAAM2J,EAAmC,CACvC,GAAI,KAAK,WAAW,EACpB,QAAS3J,EAAK,QACd,gBAAiBA,EAAK,gBACtB,eAAgBA,EAAK,eACrB,WAAYA,EAAK,WAAW,IAAkB4J,IAAA,CAC5C,YAAaA,EACb,KAAM,YACN,mBAAoB5J,EAAK,eACzB,UAAW,CAAA,CAAC,EACZ,EACF,qBAAsBA,EAAK,qBAC3B,gBAAiBA,EAAK,gBACtB,SAAUA,EAAK,SACf,gBAAiBA,EAAK,gBACtB,SAAUA,EAAK,SACf,aAAcA,EAAK,aAAa,IAAYc,IAAA,CAC1C,YAAaA,EACb,KAAM,YACN,mBAAoB,QAAA,EACpB,EACF,cAAed,EAAK,cACpB,OAAQ,YACR,kBAAmB,CAAC,EACpB,cAAe,CACb,mBAAoB,GACpB,sBAAuB,CAAC,EACxB,oBAAqB,CAAA,CAAC,CAE1B,EAEM6J,EAAoB,KAAK,aAAa,IAAI7J,EAAK,OAAO,GAAK,CAAC,EAClE,OAAA6J,EAAkB,KAAKF,CAAW,EAClC,KAAK,aAAa,IAAI3J,EAAK,QAAS6J,CAAiB,EAE9CF,CAAA,CAGT,MAAM,YAAY3J,EAA+C,CAC/D,MAAM8J,EAA6B,CACjC,GAAI,KAAK,WAAW,EACpB,QAAS9J,EAAK,QACd,cAAeA,EAAK,cACpB,cAAeA,EAAK,cACpB,YAAa,CACX,cAAeA,EAAK,YACpB,mBAAoBA,EAAK,cACzB,UAAWA,EAAK,eAChB,YAAa,CAAA,CACf,EACA,mBAAoBA,EAAK,mBACzB,eAAgBA,EAAK,eAAe,IAAcG,IAAA,CAChD,MAAAA,EACA,gBAAiBH,EAAK,cACtB,WAAYA,EAAK,WACjB,qBAAsB,EAAA,EACtB,EACF,iBAAkBA,EAAK,iBACvB,UAAWA,EAAK,UAAU,IAAiB+J,IAAA,CACzC,SAAAA,EACA,qBAAsB,GACtB,OAAQ,WACR,uBAAwB,CAAA,CAAC,EACzB,EACF,sBAAuB/J,EAAK,uBAAyB,CAAC,EACtD,SAAUA,EAAK,SACf,WAAYA,EAAK,WACjB,uBAAwB,CAAC,EACzB,mBAAoB,CAAC,EACrB,iBAAkB,CAAA,CACpB,EAEMgK,EAAiB,KAAK,UAAU,IAAIhK,EAAK,OAAO,GAAK,CAAC,EAC5D,OAAAgK,EAAe,KAAKF,CAAQ,EAC5B,KAAK,UAAU,IAAI9J,EAAK,QAASgK,CAAc,EAExCF,CAAA,CAGT,MAAM,oBAAoBhB,EAA4C,CACpE,MAAMmB,EAAe,KAAK,aAAa,IAAInB,CAAO,GAAK,CAAC,EAClDoB,EAAY,KAAK,UAAU,IAAIpB,CAAO,GAAK,CAAC,EAE3C,MAAA,CACL,SAAU,KAAK,iBAAiBmB,EAAcC,CAAS,EACvD,qBAAsBD,EACtB,UAAAC,EACA,YAAa,CAAC,EACd,gBAAiB,CAAC,EAClB,sBAAuB,CAAC,EACxB,oBAAqB,CAAC,EACtB,WAAY,CAAA,CACd,CAAA,CAIF,MAAM,gBAAgBpB,EAAiBqB,EAAwC,CAC7E,MAAMxB,EAAQ,KAAK,OAAO,IAAIG,CAAO,EACrC,GAAI,CAACH,EACG,MAAA,IAAI,MAAM,iBAAiB,EAGnC,MAAMyB,EAA4B,CAChC,QAAAtB,EACA,kBAAmB,CACjB,CACE,QAAS,qBACT,iBAAkB,wBAClB,kBAAmBH,EAAM,kBAAkB,IAAI0B,GAAMA,EAAG,SAAS,EACjE,OAAQ,QACV,EACA,CACE,QAAS,eACT,iBAAkB,sBAClB,kBAAmB1B,EAAM,kBAAkB,IAAI0B,GAAMA,EAAG,SAAS,EACjE,OAAQ,QAAA,CAEZ,EACA,kBAAmB1B,EAAM,kBAAkB,IAAW0B,IAAA,CACpD,UAAWA,EAAG,UACd,iBAAkB,oBAClB,kBAAmB,uBACnB,iBAAkB,CAAE,QAAS,EAAG,QAAS,EAAG,WAAY,CAAE,CAAA,EAC1D,EACF,oBAAqB,CAAC,EACtB,qBAAsB,CAAC,EACvB,oBAAqB,CACnB,UAAW,CAAC,WAAY,YAAa,SAAS,EAC9C,iBAAkB1B,EAAM,gBAAgB,gBAAgB,IAAe1F,GAAA,IAAIA,CAAO,SAAS,EAC3F,gBAAiB,2BACjB,gBAAiB,CAAE,MAAO,IAAM,WAAY,IAAK,cAAe,EAAG,CACrE,EACA,sBAAuB,CAAC,EACxB,SAAU,CACR,4BAA6B,CAAC,EAC9B,4BAA6B,CAAC,EAC9B,gBAAiB,CAAC,EAClB,iCAAkC,CAAA,CACpC,EACA,mBAAoB,CAClB,MAAO,CAAE,MAAO,EAAG,YAAa,CAAA,CAAG,EACnC,WAAY,CAAE,MAAO,EAAG,YAAa,CAAA,CAAG,EACxC,cAAe,CAAE,MAAO,EAAG,YAAa,CAAA,CAAG,EAC3C,kBAAmB,CAAE,MAAO,EAAG,SAAU,CAAG,CAAA,CAAA,CAEhD,EAEK,YAAA,WAAW,IAAI6F,EAASsB,CAAS,EAC/BA,CAAA,CAGT,MAAM,sBAAsBtB,EAA+B,CACzD,MAAMsB,EAAY,KAAK,WAAW,IAAItB,CAAO,EAC7C,GAAI,CAACsB,EACG,MAAA,IAAI,MAAM,qBAAqB,EAGvC,OAAOA,EAAU,kBAAA,CAInB,MAAM,gBAAgBtB,EAAiBwB,EAAkC,CACvE,MAAMb,EAAY,KAAK,UAAU,IAAIX,CAAO,GAAK,CAAC,EAC5CY,EAAgBD,EAAU,UAAsBL,GAAAA,EAAS,SAAWkB,CAAU,EAEpF,GAAIZ,IAAkB,GACd,MAAA,IAAI,MAAM,oBAAoB,EAG5B,OAAAD,EAAAC,CAAa,EAAE,cAAgB,CACvC,UAAW,GACX,gBAAiB,KACjB,gBAAiB,EACnB,EAEK,KAAA,UAAU,IAAIZ,EAASW,CAAS,EAE9B,CACL,QAAS,GACT,SAAUA,EAAUC,CAAa,EACjC,gBAAiB,6DACnB,CAAA,CAGF,MAAM,wBAAwBZ,EAA+B,CAC3D,MAAMH,EAAQ,KAAK,OAAO,IAAIG,CAAO,EAC/BW,EAAY,KAAK,UAAU,IAAIX,CAAO,GAAK,CAAC,EAElD,GAAI,CAACH,EACG,MAAA,IAAI,MAAM,iBAAiB,EAG5B,MAAA,CACL,YAAaA,EAAM,OACnB,eAAgBc,EAAU,OAC1B,mBAAoBA,EAAU,UAAY,EAAE,cAAc,SAAS,EAAE,OACrE,kBAAmB,KAAK,qBAAqBX,CAAO,EACpD,gBAAiB,KAAK,mBAAmBA,CAAO,EAChD,kBAAmB,CAAC,EACpB,iBAAkB,CAAA,CACpB,CAAA,CAIF,MAAM,qBAAqBA,EAAuC,CAChE,MAAMH,EAAQ,KAAK,OAAO,IAAIG,CAAO,EAC/BW,EAAY,KAAK,UAAU,IAAIX,CAAO,GAAK,CAAC,EAElD,GAAI,CAACH,EACG,MAAA,IAAI,MAAM,iBAAiB,EAiD5B,MA9CqB,CAC1B,kBAAmB,CACjB,eAAgBc,EAAU,OAC1B,kBAAmB,KAAK,2BAA2BA,CAAS,EAC5D,wBAAyB,KAAK,iCAAiCA,CAAS,EACxE,qBAAsB,KAAK,8BAA8BA,CAAS,CACpE,EACA,iBAAkB,CAChB,qBAAsB,GACtB,iBAAkB,GAClB,2BAA4B,EAC5B,yBAA0B,EAC5B,EACA,oBAAqB,CACnB,uBAAwB,GACxB,sBAAuB,GACvB,uBAAwB,GACxB,kBAAmB,GACrB,EACA,iBAAkB,CAChB,2BAA4B,GAC5B,2BAA4B,GAC5B,mBAAoB,GACpB,mBAAoB,EACtB,EACA,eAAgB,CACd,qBAAsB,IACtB,4BAA6B,EAC7B,aAAc,KACd,oBAAqB,IACvB,EACA,eAAgB,CACd,oBAAqB,GACrB,iBAAkB,GAClB,qBAAsB,GACtB,kBAAmB,EACrB,EACA,qBAAsB,CACpB,+BAAgC,GAChC,2BAA4B,EAC5B,0BAA2B,GAC3B,wBAAyB,CAC3B,EACA,mBAAoB,CAAA,CACtB,CAEO,CAIT,MAAM,uBAAuBX,EAAiByB,EAA+B,CAE3E,GAAI,CADU,KAAK,OAAO,IAAIzB,CAAO,EAE7B,MAAA,IAAI,MAAM,iBAAiB,EAGnC,MAAM0B,EAAiB,CACrB,GAAI,KAAK,WAAW,EACpB,QAAA1B,EACA,WAAYyB,EAAW,WACvB,YAAaA,EAAW,YACxB,qBAAsBA,EAAW,qBACjC,aAAcA,EAAW,aACzB,cAAe,KACf,kBAAmBA,EAAW,mBAAqB,EACnD,cAAe,CACb,OAAQ,CAAC,EACT,OAAQ,CAAC,EACT,gBAAiB,CAAC,EAClB,gBAAiB,CAAA,CAAC,CAEtB,EAGQ,eAAA,IAAI,8BAA+BC,CAAc,EAElDA,CAAA,CAID,YAAqB,CACpB,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAA,CAGvC,0BAA0B/J,EAAqB,CAE9C,MAAA,CACL,eAAgB,OAChB,kBAAmB,CAAC,SAAS,EAC7B,UAAW,CAAC,KAAM,IAAI,EACtB,kBAAmB,CAAC,oBAAqB,mBAAmB,EAC5D,qBAAsB,CAAC,iBAAiB,CAC1C,CAAA,CAGM,4BAA+C,CAC9C,MAAA,CACL,SAAU,CAAC,EACX,qBAAsB,CAAC,EACvB,UAAW,CAAC,EACZ,YAAa,CAAC,EACd,gBAAiB,CAAC,EAClB,sBAAuB,CAAC,EACxB,oBAAqB,CAAC,EACtB,WAAY,CAAA,CACd,CAAA,CAGM,6BAAmC,CAClC,MAAA,CACL,QAAS,GACT,iBAAkB,wBAClB,eAAgB,CAAC,qBAAsB,cAAc,EACrD,kBAAmB,0BACrB,CAAA,CAGM,uBAAqC,CACpC,MAAA,CACL,kBAAmB,CAAE,eAAgB,EAAG,kBAAmB,EAAG,wBAAyB,CAAA,EAAI,qBAAsB,EAAG,EACpH,iBAAkB,CAAE,qBAAsB,EAAG,iBAAkB,EAAG,2BAA4B,EAAG,yBAA0B,CAAE,EAC7H,oBAAqB,CAAE,uBAAwB,EAAG,sBAAuB,EAAG,uBAAwB,EAAG,kBAAmB,CAAE,EAC5H,iBAAkB,CAAE,2BAA4B,EAAG,2BAA4B,EAAG,mBAAoB,EAAG,mBAAoB,CAAE,EAC/H,eAAgB,CAAE,qBAAsB,EAAG,4BAA6B,EAAG,aAAc,EAAG,oBAAqB,CAAE,EACnH,eAAgB,CAAE,oBAAqB,EAAG,iBAAkB,EAAG,qBAAsB,EAAG,kBAAmB,CAAE,EAC7G,qBAAsB,CAAE,+BAAgC,EAAG,2BAA4B,EAAG,0BAA2B,EAAG,wBAAyB,CAAE,EACnJ,mBAAoB,CAAA,CACtB,CAAA,CAGM,iCAAyD,CACxD,MAAA,CACL,QAAS,GACT,gBAAiB,CAAC,EAClB,qBAAsB,CAAC,EACvB,8BAA+B,CAAC,EAChC,mBAAoB,CAAC,EACrB,kBAAmB,CAAC,EACpB,sBAAuB,CAAC,EACxB,qBAAsB,CAAA,CACxB,CAAA,CAGM,iBAAiBwJ,EAAqCC,EAAsC,CAClG,MAAMO,EAAW,CAAC,EAElB,OAAAR,EAAa,QAAuBN,GAAA,CAClCc,EAAS,KAAK,CACZ,GAAId,EAAY,GAChB,KAAM,cACN,MAAOA,EAAY,gBACnB,UAAWA,EAAY,cACvB,SAAUA,EAAY,SACtB,gBAAiBA,EAAY,cAAA,CAC9B,CAAA,CACF,EAEDO,EAAU,QAAoBJ,GAAA,CAC5BW,EAAS,KAAK,CACZ,GAAIX,EAAS,GACb,KAAM,WACN,MAAOA,EAAS,cAChB,SAAUA,EAAS,SACnB,gBAAiBA,EAAS,aAAA,CAC3B,CAAA,CACF,EAEMW,EAAS,KAAK,CAAC,EAAGC,IAAM,EAAE,UAAYA,EAAE,SAAS,CAAA,CAGlD,qBAAqB5B,EAAwB,CAE5C,MAAA,CACL,CAAE,SAAU,qCAAsC,OAAQ,SAAU,gBAAiB,MAAO,EAC5F,CAAE,SAAU,6BAA8B,OAAQ,WAAY,gBAAiB,aAAc,CAC/F,CAAA,CAGM,mBAAmBA,EAAwB,CAE1C,MAAA,CACL,CAAE,OAAQ,sCAAuC,aAAc,OAAQ,UAAW,IAAI,IAAO,EAC7F,CAAE,OAAQ,4CAA6C,aAAc,SAAU,UAAW,IAAI,IAAO,CACvG,CAAA,CAGM,2BAA2BW,EAAoC,CAE7D,OADS,IAAI,IAAIA,EAAU,IAAgBL,GAAAA,EAAS,mBAAmB,cAAc,CAAC,EAC7E,KAAO,GAAM,GAAA,CAGxB,iCAAiCK,EAAiC,CACxE,MAAMkB,EAAsB,CAAC,EAC7B,OAAAlB,EAAU,QAAoBL,GAAA,CACtB,MAAAnG,EAAUmG,EAAS,mBAAmB,eAC5CuB,EAAe1H,CAAO,GAAK0H,EAAe1H,CAAO,GAAK,GAAK,CAAA,CAC5D,EACM0H,CAAA,CAGD,8BAA8BlB,EAAiC,CAC9D,MAAA,CACL,eAAgBA,EAAU,OAC1B,iBAAkB,KAAK,QAAQA,EAAW,gBAAgB,EAC1D,qBAAsB,KAAK,QAAQA,EAAgBmB,GAAAA,EAAE,mBAAmB,cAAc,CACxF,CAAA,CAGM,QAAQC,EAAcC,EAA4C,CACxE,OAAOD,EAAM,OAAO,CAACE,EAAQC,IAAS,CAC9B,MAAAC,EAAQ,OAAOH,GAAQ,WAAaA,EAAIE,CAAI,EAAIA,EAAKF,CAAG,EAC9D,OAAAC,EAAOE,CAAK,GAAKF,EAAOE,CAAK,GAAK,GAAK,EAChCF,CACT,EAAG,EAAE,CAAA,CAET,CAEa,MAAAG,EAAuB,IAAIzC,ECvsBlC0C,EAAgE,CAAC,CAAE,OAAA1K,KAAa,CACpF,KAAM,CAAC2K,EAAQC,CAAS,EAAI3J,EAAAA,SAA0B,CAAA,CAAE,EAClD,CAAC4J,EAAeC,CAAgB,EAAI7J,EAAAA,SAA+B,IAAI,EACvE,CAAC+H,EAAW+B,CAAY,EAAI9J,EAAAA,SAA0B,CAAA,CAAE,EACxD,CAAC+J,EAAaC,CAAc,EAAIhK,EAAAA,SAA6B,IAAI,EACjE,CAACM,EAASC,CAAU,EAAIP,EAAAA,SAAS,EAAI,EACrC,CAACQ,EAAWC,CAAY,EAAIT,EAAAA,SAA8D,UAAU,EAE1GU,EAAAA,UAAU,IAAM,CACHuJ,EAAA,CACb,EAAG,EAAE,EAELvJ,EAAAA,UAAU,IAAM,CACVkJ,GACFM,EAAiBN,EAAc,EAAE,CACnC,EACC,CAACA,CAAa,CAAC,EAElB,MAAMK,EAAa,SAAY,CACzB,GAAA,CACF1J,EAAW,EAAI,EAET,MAAA4J,EAAe,MAAMC,EAAmB,EAC9CT,EAAUQ,CAAY,EAClBA,EAAa,OAAS,GACPN,EAAAM,EAAa,CAAC,CAAC,QAE3BrJ,EAAO,CACN,QAAA,MAAM,wBAAyBA,CAAK,CAAA,QAC5C,CACAP,EAAW,EAAK,CAAA,CAEpB,EAEM6J,EAAqB,SAAsC,CAC/D,MAAMC,EAAY,CAChB,MAAO,2BACP,YAAa,2FACb,UAAW,WACX,OAAQ,SACR,SAAU,CACR,MAAO,4BACP,QAAS,0BACT,qBAAsB,mDACxB,EACA,SAAU,CACR,UAAW,IAAI,KAAK,KAAK,IAAA,EAAQ,MAAuB,EACxD,QAAS,IAAI,KAAK,KAAK,IAAA,EAAQ,MAAuB,EACtD,+BAAgC,CAAC,iBAAkB,4BAA4B,CACjF,EACA,gBAAiB,CACf,gBAAiB,CAAC,OAAQ,QAAS,YAAa,SAAS,EACzD,qBAAsB,2DACtB,oBAAqB,CAAC,oBAAqB,eAAgB,kBAAmB,sBAAsB,EACpG,2BAA4B,CAC1B,kCACA,kCACA,gCAAA,CAEJ,EACA,kBAAmB,CAAC,OAAQ,QAAS,YAAa,UAAW,OAAO,EACpE,cAAe,CACb,qBAAsB,GACtB,gBAAiB,CAAC,KAAM,KAAM,KAAM,KAAM,IAAI,EAC9C,uBAAwB,CAAC,gBAAiB,yBAA0B,sBAAsB,CAAA,CAE9F,EAGA,MAAO,CADO,MAAMb,EAAqB,YAAYa,EAAWtL,CAAM,CACzD,CACf,EAEMmL,EAAmB,MAAO9C,GAAoB,CAC9C,GAAA,CACF,MAAMU,EAAiB,MAAM0B,EAAqB,kBAAkBpC,CAAO,EACrEkD,EAAS,MAAMd,EAAqB,qBAAqBpC,CAAO,EACtE0C,EAAahC,CAAc,EAC3BkC,EAAeM,CAAM,QACdxJ,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,CAAA,CAEvD,EAEMyJ,EAAoB,SAAY,CAEpC,QAAQ,IAAI,2BAA2B,CACzC,EAEMC,EAAa,SAAY,CAC7B,GAAKZ,EAED,GAAA,CACF,MAAMa,EAAW,CACf,QAASb,EAAc,GACvB,OAAA7K,EACA,eAAgB,aAChB,mBAAoB,OACpB,sBAAuB,CAAC,8BAA+B,mBAAmB,EAC1E,oBAAqB,CAAC,YAAY,EAClC,mBAAoB,CAAC,EACrB,kBAAmB,CAAC,oBAAqB,eAAgB,iBAAiB,CAC5E,EAEM2I,EAAW,MAAM8B,EAAqB,YAAYiB,CAAQ,EAChEX,EAAqB9D,GAAA,CAAC,GAAGA,EAAM0B,CAAQ,CAAC,QACjC5G,EAAO,CACN,QAAA,MAAM,0BAA2BA,CAAK,CAAA,CAElD,EAEM4J,EAAsB,IACzBrJ,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAa,gBAAA,EACtED,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAgB,mBAAA,QAC9D,IAAE,CAAA,UAAU,qBAAsB,SAAAsI,GAAe,gBAAgB,qBAAqB,QACtF,MAAI,CAAA,UAAU,uBACZ,SAAeA,GAAA,gBAAgB,gBAAgB,IAAI,CAACrI,EAASC,UAC3D,OAAiB,CAAA,UAAU,2DACzB,SADQD,CAAA,EAAAC,CAEX,CACD,CACH,CAAA,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAiB,oBAAA,EAChED,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAA,OAAC,IAAE,CAAA,SAAA,CAAAC,EAAAA,IAAC,UAAO,SAAO,SAAA,CAAA,EAAS,IAAEsI,GAAe,OAAO,QAAQ,IAAK,GAAG,CAAA,EAAE,SACpE,IAAE,CAAA,SAAA,CAAAtI,EAAAA,IAAC,UAAO,SAAM,QAAA,CAAA,EAAS,IAAEsI,GAAe,SAAS,KAAA,EAAM,SACzD,IAAE,CAAA,SAAA,CAAAtI,EAAAA,IAAC,UAAO,SAAK,OAAA,CAAA,EAAS,IAAEsI,GAAe,SAAS,UAAU,mBAAmB,CAAA,EAAE,SACjF,IAAE,CAAA,SAAA,CAAAtI,EAAAA,IAAC,UAAO,SAAO,SAAA,CAAA,EAAS,IAAEsI,GAAe,MAAA,CAAO,CAAA,CAAA,CACrD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAvI,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAoB,uBAAA,EAC5EA,EAAA,IAAA,MAAA,CAAI,UAAU,wCACZ,YAAe,gBAAgB,oBAAoB,IAAI,CAAC4F,EAAS1F,IAC/DH,EAAA,KAAA,MAAA,CAAgB,UAAU,mCACzB,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAA4F,EAAQ,QAAQ,EAC1D5F,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAyB,WAAQ,aAAa,EAC1DA,EAAA,IAAA,OAAA,CAAK,UAAU,yBAA0B,WAAQ,YAAa,CAAA,CAAA,CAHvD,EAAAE,CAIV,CACD,CACH,CAAA,CAAA,EACF,EAEAH,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAA4B,+BAAA,EACpFA,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,YAAe,gBAAgB,2BAA2B,IAAI,CAAC6F,EAAW3F,IACxEH,EAAA,KAAA,MAAA,CAAgB,UAAU,6BACzB,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,oFACb,SAAAA,EAAAA,IAAC,QAAK,UAAU,sCAAuC,SAAQE,EAAA,CAAA,CAAE,CACnE,CAAA,SACC,MACC,CAAA,SAAA,CAAAF,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAA6F,EAAU,UAAU,EAC9D7F,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAyB,WAAU,eAAgB,CAAA,CAAA,CAClE,CAAA,CAAA,CAPQ,EAAAE,CAQV,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGImJ,EAAyB,IAC5BtJ,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAiB,oBAAA,EACrED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,gEAAgE,SAElF,kBAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,kEAAkE,SAEpF,cAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAqB,wBAAA,EACpED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4BAA4B,SAAsB,yBAAA,EAC/DA,EAAA,IAAA,OAAA,CAAK,UAAU,wDAAwD,SAAS,WAAA,CAAA,CAAA,EACnF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,8FAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,0DACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAqB,uBAAA,CAAA,EAC3BA,EAAAA,IAAC,QAAK,SAAoB,sBAAA,CAAA,EAC1BA,EAAAA,IAAC,QAAK,SAAkB,oBAAA,CAAA,CAAA,CAC1B,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4BAA4B,SAA0B,6BAAA,EACnEA,EAAA,IAAA,OAAA,CAAK,UAAU,wDAAwD,SAAS,WAAA,CAAA,CAAA,EACnF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,oFAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,0DACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAA4B,8BAAA,CAAA,EAClCA,EAAAA,IAAC,QAAK,SAAoB,sBAAA,CAAA,EAC1BA,EAAAA,IAAC,QAAK,SAAkB,oBAAA,CAAA,CAAA,CAC1B,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAkB,qBAAA,EACjED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4BAA4B,SAAoB,uBAAA,EAC7DA,EAAA,IAAA,OAAA,CAAK,UAAU,sDAAsD,SAAQ,UAAA,CAAA,CAAA,EAChF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,gFAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,0DACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAA0B,4BAAA,CAAA,EAChCA,EAAAA,IAAC,QAAK,SAAoB,sBAAA,CAAA,EAC1BA,EAAAA,IAAC,QAAK,SAAsB,wBAAA,CAAA,CAAA,CAC9B,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4BAA4B,SAA4B,+BAAA,EACrEA,EAAA,IAAA,OAAA,CAAK,UAAU,0DAA0D,SAAY,cAAA,CAAA,CAAA,EACxF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,oFAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,0DACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAA2B,6BAAA,CAAA,EACjCA,EAAAA,IAAC,QAAK,SAAoB,sBAAA,CAAA,EAC1BA,EAAAA,IAAC,QAAK,SAAsB,wBAAA,CAAA,CAAA,CAC9B,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIsJ,EAAkB,IACrBvJ,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAe,kBAAA,EACnEA,EAAA,IAAC,SAAA,CACC,QAASkJ,EACT,UAAU,gEACX,SAAA,eAAA,CAAA,CAED,EACF,EAEAnJ,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAe,kBAAA,EACvEA,EAAA,IAAA,IAAA,CAAE,UAAU,mCAAoC,WAAU,MAAO,CAAA,CAAA,EACpE,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAkB,qBAAA,EAC1EA,EAAA,IAAA,IAAA,CAAE,UAAU,oCACV,aAAI,IAAIyG,EAAU,IAAImB,GAAKA,EAAE,mBAAmB,cAAc,CAAC,EAAE,KACpE,EACC5H,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAkB,oBAAA,CAAA,CAAA,EACzD,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAU,aAAA,EACnEA,EAAAA,IAAC,IAAE,CAAA,UAAU,qCACV,SAAAyG,EAAU,OAAOmB,GAAKA,EAAE,iBAAmB,WAAW,EAAE,MAC3D,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEA7H,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAa,gBAAA,EAC3DA,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,SAAUyG,EAAA,IAAI,CAACL,EAAUlG,IACxBH,EAAAA,KAAC,MAAgB,CAAA,UAAU,0DACzB,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,4BACX,SAAAoG,EAAS,eAAe,QAAQ,IAAK,GAAG,CAC3C,CAAA,EACArG,EAAAA,KAAC,IAAE,CAAA,UAAU,wBACV,SAAA,CAAAqG,EAAS,mBAAmB,eAAe,iBAChCA,EAAS,iBAAiB,mBAAmB,CAAA,EAC3D,EACCA,EAAS,sBAAsB,OAAS,GACvCpG,EAAAA,IAAC,MAAI,CAAA,UAAU,4BACZ,SAAAoG,EAAS,sBAAsB,MAAM,EAAG,CAAC,EAAE,IAAI,CAACC,EAAckD,IAC5DvJ,EAAA,IAAA,OAAA,CAAwB,UAAU,0DAChC,SAAaqG,EAAA,WAAA,EADLkD,CAEX,CACD,CACH,CAAA,CAAA,EAEJ,QACC,MAAI,CAAA,UAAU,aACb,SAACvJ,EAAA,IAAA,OAAA,CAAK,UAAW,6BACfoG,EAAS,cAAc,UACnB,8BACA,2BACN,GACG,SAAAA,EAAS,cAAc,UAAY,aAAe,YACrD,CAAA,CACF,CAAA,CAAA,CA3BQ,EAAAlG,CA4BV,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGIsJ,EAAe,IAClBzJ,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAmB,sBAAA,EAEtEyI,GAEG1I,EAAA,KAAAU,WAAA,CAAA,SAAA,CAACV,EAAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAkB,qBAAA,EACjED,EAAAA,KAAC,IAAE,CAAA,UAAU,mCAAoC,SAAA,CAAA0I,EAAY,kBAAkB,kBAAkB,GAAA,CAAC,CAAA,CAAA,EACpG,EAEA1I,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAqB,wBAAA,QACnE,IAAE,CAAA,UAAU,oCAAqC,SAAAyI,EAAY,iBAAiB,oBAAqB,CAAA,CAAA,EACtG,EAEA1I,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAoB,uBAAA,QAClE,IAAE,CAAA,UAAU,qCAAsC,SAAAyI,EAAY,eAAe,mBAAoB,CAAA,CAAA,EACpG,EAEA1I,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAgB,mBAAA,EAC/DD,EAAAA,KAAC,IAAE,CAAA,UAAU,qCAAqC,SAAA,CAAA,IAAE0I,EAAY,eAAe,oBAAA,CAAqB,CAAA,CAAA,CACtG,CAAA,CAAA,EACF,EAEA1I,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAiB,oBAAA,EAChED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAA4B,+BAAA,EACpED,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAA0I,EAAY,iBAAiB,2BAA2B,GAAA,CAAC,CAAA,CAAA,EAC1F,EACA1I,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAA4B,+BAAA,EACpED,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAA0I,EAAY,iBAAiB,2BAA2B,GAAA,CAAC,CAAA,CAAA,EAC1F,EACA1I,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAmB,sBAAA,EAC3DD,EAAAA,KAAC,OAAK,CAAA,UAAU,cAAe,SAAA,CAAA0I,EAAY,iBAAiB,mBAAmB,GAAA,CAAC,CAAA,CAAA,CAClF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEA1I,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iCAAiC,SAAqB,wBAAA,EACpED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAoB,uBAAA,QAC3D,OAAK,CAAA,UAAU,cAAe,SAAAyI,EAAY,qBAAqB,8BAA+B,CAAA,CAAA,EACjG,EACA1I,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAmB,sBAAA,QAC1D,OAAK,CAAA,UAAU,cAAe,SAAAyI,EAAY,qBAAqB,0BAA2B,CAAA,CAAA,EAC7F,EACA1I,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAA0B,6BAAA,QACjE,OAAK,CAAA,UAAU,cAAe,SAAAyI,EAAY,qBAAqB,yBAA0B,CAAA,CAAA,CAC5F,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EAEJ,EAGF,OAAIzJ,EAEAgB,MAAC,OAAI,UAAU,wCACb,eAAC,MAAI,CAAA,UAAU,iEAAiE,CAClF,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAe,kBAAA,EAChEA,EAAA,IAAC,SAAA,CACC,QAASiJ,EACT,UAAU,4EACX,SAAA,kBAAA,CAAA,CAED,EACF,EAECX,GAGGvI,EAAA,KAAAU,WAAA,CAAA,SAAA,CAACV,EAAAA,KAAA,MAAA,CAAI,UAAU,sCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,wCAAyC,SAAAsI,EAAc,MAAM,EAC1EtI,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAsB,WAAc,YAAY,EAE7DD,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAW,kCACfsI,EAAc,SAAW,SAAW,8BACpCA,EAAc,SAAW,WAAa,gCACtC,2BACF,GACG,WAAc,OACjB,EACAvI,EAAAA,KAAC,OAAK,CAAA,UAAU,wBACb,SAAA,CAAcuI,EAAA,OAAO,MAAIA,EAAc,SAAA,EAC1C,EACAtI,MAAC,QAAK,UAAU,wBACb,WAAc,SAAS,UAAU,oBACpC,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,2BACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,sBACZ,SAAA,CACC,CAAE,IAAK,WAAY,MAAO,UAAW,EACrC,CAAE,IAAK,cAAe,MAAO,aAAc,EAC3C,CAAE,IAAK,YAAa,MAAO,WAAY,EACvC,CAAE,IAAK,SAAU,MAAO,QAAS,CAAA,EACjC,IAAKU,GACLV,EAAA,IAAC,SAAA,CAEC,QAAS,IAAMb,EAAauB,EAAI,GAAU,EAC1C,UAAW,4CACTxB,IAAcwB,EAAI,IACd,gCACA,4EACN,GAEC,SAAIA,EAAA,KAAA,EARAA,EAAI,GAAA,CAUZ,EACH,CACF,CAAA,EAEAX,EAAAA,KAAC,MAAI,CAAA,UAAU,MACZ,SAAA,CAAAb,IAAc,YAAckK,EAAoB,EAChDlK,IAAc,eAAiBmK,EAAuB,EACtDnK,IAAc,aAAeoK,EAAgB,EAC7CpK,IAAc,UAAYsK,EAAa,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EAEJ,CAEJ,ECreMC,GAAgF,CAAC,CAAE,OAAAhM,KAAa,CACpG,KAAM,CAACiM,EAAeC,CAAgB,EAAIjL,EAAAA,SAAkD,UAAU,EAEhGkL,EAAuB,IAAM,CACjC,OAAQF,EAAe,CACrB,IAAK,WACI,OAAA1J,MAACzB,GAA8B,OAAAd,EAAgB,EACxD,IAAK,gBACI,OAAAuC,MAAC8C,GAA2B,OAAArF,EAAgB,EACrD,IAAK,SACI,OAAAuC,MAACmI,GAAuB,OAAA1K,EAAgB,EACjD,QACS,OAAAuC,MAACzB,GAA8B,OAAAd,EAAgB,CAAA,CAE5D,EAGE,OAAAsC,EAAA,KAAC,MAAI,CAAA,UAAU,0BAEb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,kBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,yCACb,SAAAD,OAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAA4B,+BAAA,EAC5EA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,uEAAA,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,8BACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAAS,IAAM2J,EAAiB,UAAU,EAC1C,UAAW,8DACTD,IAAkB,WACd,mCACA,mCACN,GACD,SAAA,UAAA,CAED,EACA1J,EAAA,IAAC,SAAA,CACC,QAAS,IAAM2J,EAAiB,eAAe,EAC/C,UAAW,8DACTD,IAAkB,gBACd,mCACA,mCACN,GACD,SAAA,eAAA,CAED,EACA1J,EAAA,IAAC,SAAA,CACC,QAAS,IAAM2J,EAAiB,QAAQ,EACxC,UAAW,8DACTD,IAAkB,SACd,mCACA,mCACN,GACD,SAAA,QAAA,CAAA,CAED,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,sCACb,SAAC3J,EAAA,KAAA,MAAA,CAAI,UAAU,8CACZ,SAAA,CAAA2J,IAAkB,YACjB3J,OAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC5E,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,wQAAyQ,CAAA,CAAA,CAChV,CACF,CAAA,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,oCAAoC,SAAiC,oCAAA,EAClFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAG7B,8IAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGD0J,IAAkB,iBAChB3J,OAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC5E,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,+JAAgK,CAAA,CAAA,CACvO,CACF,CAAA,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,oCAAoC,SAAsC,yCAAA,EACvFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAG7B,wJAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGD0J,IAAkB,UAChB3J,OAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC5E,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,wFAAyF,CAAA,CAAA,CAChK,CACF,CAAA,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,oCAAoC,SAA0C,6CAAA,EAC3FA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAG7B,yJAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,EAGCA,EAAA,IAAA,MAAA,CAAI,UAAU,OACZ,aACH,QAGC,MAAI,CAAA,UAAU,yBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,8CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAA6B,gCAAA,EACvEA,EAAA,IAAA,IAAA,CAAE,UAAU,kCAAkC,SAM/C,sWAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,mFACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC9D,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6HAA8H,CAAA,CAAA,CACrM,CACF,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAuB,0BAAA,EACvDA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,+DAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,oFACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC9D,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,yHAA0H,CAAA,CAAA,CACjM,CACF,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAuB,0BAAA,EACvDA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,qEAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,qFACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,QAAQ,YAAY,OAAO,eAC9D,SAACA,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,kNAAmN,CAAA,CAAA,CAC1R,CACF,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAqB,wBAAA,EACrDA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,iEAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ"}