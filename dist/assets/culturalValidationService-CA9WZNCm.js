import{p,q as m,J as g,K as h,M as o,P as v}from"./firebase-DLuFXYhP.js";import{i as u}from"./index-nwrMOwxu.js";class y{async submitValidationRequest(t){try{const e=`validation-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;return await p(m(u,"cultural-validation-requests",e),{...t,id:e,status:"pending",assignedRepresentatives:[],reviewNotes:[],submissionDate:t.submissionDate}),await this.notifyCulturalRepresentatives(t.culturalIdentity,e),e}catch(e){throw console.error("Error submitting validation request:",e),new Error("Failed to submit validation request")}}async getCulturalRepresentatives(t){try{const e=g(h(u,"cultural-representatives"),o("culturalIdentity","==",t),o("activeStatus","==",!0));return(await v(e)).docs.map(r=>r.data())}catch(e){return console.error("Error fetching cultural representatives:",e),[]}}async validateCulturalContent(t){const e=[],s=[];let r=!1;const i=["sacred","ritual","ceremony","traditional medicine","spiritual","ancestral","initiation","burial","marriage customs"],a=`${t.title} ${t.content}`.toLowerCase(),n=i.some(c=>a.includes(c));n&&(r=!0,e.push("Content contains culturally sensitive topics that require community review"),s.push("Consider adding cultural context and sensitivity warnings")),["costume","dress up","exotic","primitive","tribal fashion"].some(c=>a.includes(c))&&(e.push("Content may contain cultural appropriation elements"),s.push("Review language and context to ensure respectful representation"),r=!0),t.author.culturalCredibility==="community_member"?n&&s.push("Consider getting endorsement from cultural representatives"):t.author.culturalCredibility==="cultural_expert"&&n&&s.push("Expert content with sensitive topics should include cultural context");const l=this.analyzeLanguageRespectfulness(a);return l.isRespectful||(e.push(...l.issues),s.push(...l.suggestions),r=!0),{isValid:e.length===0,concerns:e,recommendations:s,requiresReview:r}}analyzeLanguageRespectfulness(t){const e=[],s=[];return[{term:"primitive",suggestion:"traditional"},{term:"savage",suggestion:"warrior"},{term:"exotic",suggestion:"unique"},{term:"weird",suggestion:"different"},{term:"strange",suggestion:"unfamiliar"}].forEach(({term:i,suggestion:a})=>{t.includes(i)&&(e.push(`Use of potentially offensive term: "${i}"`),s.push(`Consider using "${a}" instead of "${i}"`))}),(t.includes("all ")||t.includes(" always ")||t.includes(" never "))&&(e.push("Content may contain cultural generalizations"),s.push("Avoid broad generalizations about cultural groups")),{isRespectful:e.length===0,issues:e,suggestions:s}}async submitToModerationQueue(t,e,s="medium"){try{const r=`moderation-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,i={id:r,contentId:t.id,submittedBy:t.author.userId,culturalContext:t.culture,moderationReason:e,priority:s,culturalRepresentatives:await this.assignCulturalRepresentatives(t.culture),submissionDate:new Date,deadline:new Date(Date.now()+7*24*60*60*1e3),status:"pending"};return await p(m(u,"cultural-moderation-queue",r),i),r}catch(r){throw console.error("Error submitting to moderation queue:",r),new Error("Failed to submit to moderation queue")}}async assignCulturalRepresentatives(t){try{return(await this.getCulturalRepresentatives(t)).sort((r,i)=>i.endorsements-r.endorsements).slice(0,3).map(r=>r.userId)}catch(e){return console.error("Error assigning cultural representatives:",e),[]}}async notifyCulturalRepresentatives(t,e){try{const s=await this.getCulturalRepresentatives(t);console.log(`Notifying ${s.length} representatives for ${t} about request ${e}`)}catch(s){console.error("Error notifying cultural representatives:",s)}}async getValidationStatus(t,e){try{const s=g(h(u,"cultural-validation-requests"),o("userId","==",t),o("culturalIdentity","==",e)),r=await v(s);if(r.empty)return{status:"not_requested"};const a=r.docs.map(n=>n.data()).sort((n,d)=>d.submissionDate.toDate().getTime()-n.submissionDate.toDate().getTime())[0];return{status:a.status,requestId:a.id,verificationDate:a.verificationDate?.toDate(),verifiedBy:a.verifiedBy}}catch(s){return console.error("Error getting validation status:",s),{status:"not_requested"}}}calculateCredibilityScore(t){let e=0;return e+=t.culturalIdentities.length*10,e+=t.verifiedIdentities.length*20,e+=Math.min(t.communityEndorsements*5,50),e+=Math.min(t.contentContributions*2,30),e+=t.culturalKnowledgeScore,Math.min(e,100)}}const C=new y;export{C as c};
//# sourceMappingURL=culturalValidationService-CA9WZNCm.js.map
