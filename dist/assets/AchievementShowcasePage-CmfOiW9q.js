import{j as e}from"./index-nwrMOwxu.js";import{r as x}from"./vendor-DtOhX2xw.js";import"./firebase-DLuFXYhP.js";class ${achievements=new Map;featuredAchievements=[];categoryAchievements=new Map;locationAchievements=new Map;async createAchievement(t){const s={id:this.generateId(),title:t.title,description:t.description,category:{primary:t.category.primary,secondary:t.category.secondary||[],crossCultural:t.culturalContext.crossCulturalElements.length>0,nationalSignificance:this.determineNationalSignificance(t.location,t.culturalContext)},culturalContext:{primaryCulture:t.culturalContext.primaryCulture,secondaryCultures:t.culturalContext.secondaryCultures,culturalSignificance:t.culturalContext.culturalSignificance,traditionalElements:t.culturalContext.traditionalElements,modernAdaptations:[],crossCulturalElements:t.culturalContext.crossCulturalElements.map(i=>({element:i,culturalOrigins:[t.culturalContext.primaryCulture,...t.culturalContext.secondaryCultures],collaborationType:"cultural_exchange",impactDescription:"Cross-cultural collaboration and understanding"}))},location:t.location,timeframe:{startDate:t.timeframe.startDate,endDate:t.timeframe.endDate,historicalPeriod:this.determineHistoricalPeriod(t.timeframe.startDate),culturalCalendarAlignment:[]},mediaContent:t.mediaContent.map(i=>({id:this.generateId(),type:i.type,url:i.url,caption:i.caption,culturalContext:i.culturalContext||"",accessibility:{altText:i.caption,transcription:"",culturalDescription:i.culturalContext||""},permissions:{publicAccess:!0,culturalCommunityAccess:!0,commercialUse:!1,attribution:"required"}})),verification:{status:"pending",reviewers:[],verificationScore:0,factCheckResults:[],communityApproval:{approvalScore:0,totalVotes:0,culturalRepresentativeApproval:!1,communityEndorsements:t.submissionSource.communityEndorsements},culturalApproval:{culturalRepresentativeId:"",approvalStatus:"pending",culturalAccuracyScore:0,traditionalKnowledgeValidation:!1,culturalSensitivityCheck:"pending"}},communityEndorsements:t.submissionSource.communityEndorsements.map(i=>({endorserId:i,endorsementType:"community_support",endorsementDate:new Date,endorsementDetails:"Community member endorsement",culturalContext:t.culturalContext.primaryCulture})),culturalSignificance:{significanceLevel:"community",culturalImpact:t.culturalContext.culturalSignificance,traditionalKnowledgeElements:t.culturalContext.traditionalElements,modernRelevance:"Contemporary cultural significance",preservationValue:8,educationalValue:7},relatedAchievements:[],socialImpact:{communityBenefit:"Positive community impact",culturalPreservation:"Cultural knowledge preservation",crossCulturalUnderstanding:"Enhanced cross-cultural understanding",inspirationalValue:8,roleModelPotential:7},submissionSource:{submitterId:t.submissionSource.submitterId,submissionDate:new Date,submitterRole:t.submissionSource.submitterRole,submissionMethod:"web_form",verificationDocuments:[],communityEndorsements:t.submissionSource.communityEndorsements},engagementMetrics:{views:0,shares:0,culturalDiscoveries:0,inspirationSaves:0,crossCulturalEngagement:0},createdAt:new Date,updatedAt:new Date};return this.achievements.set(s.id,s),this.indexAchievement(s),s}async getAchievement(t){return this.achievements.get(t)||null}async updateAchievement(t,s){const i=this.achievements.get(t);if(!i)throw new Error("Achievement not found");const a={...i,...s,updatedAt:new Date};return this.achievements.set(t,a),a}async deleteAchievement(t){const s=this.achievements.delete(t);return this.removeFromIndexes(t),s}async getAchievements(t){let s=Array.from(this.achievements.values());return t.category&&(s=s.filter(i=>i.category.primary===t.category)),t.culturalContext&&(s=s.filter(i=>i.culturalContext.primaryCulture===t.culturalContext||i.culturalContext.secondaryCultures.includes(t.culturalContext))),t.location&&(s=s.filter(i=>i.location.province===t.location?.province||i.location.city===t.location?.city)),t.timeframe&&(s=s.filter(i=>i.timeframe.startDate>=t.timeframe.startDate&&i.timeframe.endDate<=t.timeframe.endDate)),t.verificationStatus&&(s=s.filter(i=>i.verification.status===t.verificationStatus)),s.sort((i,a)=>{const r=this.calculateRelevanceScore(i,t);return this.calculateRelevanceScore(a,t)-r}),s.slice(0,t.limit||50)}async getFeaturedAchievements(){return this.featuredAchievements.map(s=>this.achievements.get(s)).filter(Boolean).sort((s,i)=>i.engagementMetrics.views-s.engagementMetrics.views)}async getAchievementsByCategory(t){return(this.categoryAchievements.get(t.primary)||[]).map(i=>this.achievements.get(i)).filter(Boolean)}async getAchievementsByLocation(t){const s=`${t.province}-${t.city}`;return(this.locationAchievements.get(s)||[]).map(a=>this.achievements.get(a)).filter(Boolean)}async searchAchievements(t){const s=Array.from(this.achievements.values()),i=t.searchText.toLowerCase().split(" ");let r=s.filter(n=>{const c=[n.title,n.description,n.culturalContext.culturalSignificance,n.culturalContext.primaryCulture,...n.culturalContext.secondaryCultures,...n.culturalContext.traditionalElements].join(" ").toLowerCase();return i.every(o=>c.includes(o))});return t.filters?.category&&(r=r.filter(n=>n.category.primary===t.filters.category)),t.filters?.culturalContext&&(r=r.filter(n=>n.culturalContext.primaryCulture===t.filters.culturalContext||n.culturalContext.secondaryCultures.includes(t.filters.culturalContext))),r.sort((n,c)=>{const o=this.calculateSearchRelevance(n,t.searchText);return this.calculateSearchRelevance(c,t.searchText)-o}),r.slice(0,t.limit||20)}async getAchievementSuggestions(t){const s=await this.getUserProfile(t),a=Array.from(this.achievements.values()).filter(r=>{const n=r.culturalContext.primaryCulture!==s.primaryCulture,c=r.category.crossCultural,o=r.verification.verificationScore>80;return(n||c)&&o});return a.sort((r,n)=>{const c=this.calculateSuggestionScore(r,s);return this.calculateSuggestionScore(n,s)-c}),a.slice(0,10)}async getPopularAchievements(t){const s=Array.from(this.achievements.values()),i=new Date;let a;switch(t){case"week":a=new Date(i.getTime()-7*24*60*60*1e3);break;case"month":a=new Date(i.getTime()-30*24*60*60*1e3);break;case"year":a=new Date(i.getTime()-365*24*60*60*1e3);break;default:a=new Date(i.getTime()-30*24*60*60*1e3)}const r=s.filter(n=>n.createdAt>=a);return r.sort((n,c)=>{const o=n.engagementMetrics.views+n.engagementMetrics.shares*3+n.engagementMetrics.inspirationSaves*2;return c.engagementMetrics.views+c.engagementMetrics.shares*3+c.engagementMetrics.inspirationSaves*2-o}),r.slice(0,20)}async getAchievementTimeline(t){return Array.from(this.achievements.values()).filter(a=>a.timeframe.startDate>=t.startDate&&a.timeframe.endDate<=t.endDate).map(a=>({id:a.id,title:a.title,date:a.timeframe.startDate,culturalContext:a.culturalContext.primaryCulture,category:a.category.primary,significance:a.culturalSignificance.significanceLevel,description:a.description,mediaUrl:a.mediaContent[0]?.url||"",crossCultural:a.category.crossCultural})).sort((a,r)=>a.date.getTime()-r.date.getTime())}async getRelatedAchievements(t){const s=this.achievements.get(t);if(!s)return[];const a=Array.from(this.achievements.values()).filter(r=>{if(r.id===t)return!1;const n=r.culturalContext.primaryCulture===s.culturalContext.primaryCulture,c=r.category.primary===s.category.primary,o=r.location.province===s.location.province,v=Math.abs(r.timeframe.startDate.getTime()-s.timeframe.startDate.getTime())<50*365*24*60*60*1e3;return n||c||o||v});return a.sort((r,n)=>{const c=this.calculateRelatedScore(r,s);return this.calculateRelatedScore(n,s)-c}),a.slice(0,6)}async trackAchievementView(t,s){const i=this.achievements.get(t);i&&(i.engagementMetrics.views++,(await this.getUserProfile(s)).primaryCulture!==i.culturalContext.primaryCulture&&i.engagementMetrics.crossCulturalEngagement++,this.achievements.set(t,i))}async trackCulturalDiscovery(t,s,i){console.log(`Cultural discovery: ${t} → ${s} by user ${i}`)}async trackSocialSharing(t,s){const i=this.achievements.get(t);i&&(i.engagementMetrics.shares++,this.achievements.set(t,i))}async generateEngagementReport(t){const s=Array.from(this.achievements.values()).filter(o=>o.createdAt>=t.startDate&&o.createdAt<=t.endDate),i=s.reduce((o,m)=>o+m.engagementMetrics.views,0),a=s.reduce((o,m)=>o+m.engagementMetrics.shares,0),r=s.reduce((o,m)=>o+m.engagementMetrics.crossCulturalEngagement,0),n=this.calculateCulturalDistribution(s),c=this.calculateCategoryDistribution(s);return{timeframe:{...t,historicalPeriod:"democratic_era",culturalCalendarAlignment:[]},totalAchievements:s.length,totalViews:i,totalShares:a,crossCulturalEngagement:r,averageEngagement:i/s.length||0,culturalDistribution:n,categoryDistribution:c,topAchievements:s.sort((o,m)=>m.engagementMetrics.views-o.engagementMetrics.views).slice(0,10).map(o=>({id:o.id,title:o.title,views:o.engagementMetrics.views}))}}generateId(){return Math.random().toString(36).substr(2,9)}determineNationalSignificance(t,s){return s.crossCulturalElements.length>0?"national":t.province?"provincial":"local"}determineHistoricalPeriod(t){const s=t.getFullYear();return s<1900?"pre_colonial":s<1948?"early_colonial":s<1994?"apartheid_era":"democratic_era"}indexAchievement(t){const s=t.category.primary;this.categoryAchievements.has(s)||this.categoryAchievements.set(s,[]),this.categoryAchievements.get(s).push(t.id);const i=`${t.location.province}-${t.location.city}`;this.locationAchievements.has(i)||this.locationAchievements.set(i,[]),this.locationAchievements.get(i).push(t.id),t.verification.verificationScore>85&&t.category.crossCultural&&this.featuredAchievements.push(t.id)}removeFromIndexes(t){for(const[,i]of this.categoryAchievements){const a=i.indexOf(t);a>-1&&i.splice(a,1)}for(const[,i]of this.locationAchievements){const a=i.indexOf(t);a>-1&&i.splice(a,1)}const s=this.featuredAchievements.indexOf(t);s>-1&&this.featuredAchievements.splice(s,1)}calculateRelevanceScore(t,s){let i=0;return i+=t.verification.verificationScore*.3,i+=Math.min(t.engagementMetrics.views/100,50)*.2,t.category.crossCultural&&(i+=20),i+=t.culturalSignificance.preservationValue*2,(Date.now()-t.createdAt.getTime())/(1e3*60*60*24)<30&&(i+=10),i}calculateSearchRelevance(t,s){const i=s.toLowerCase().split(" ");let a=0;const r=i.filter(o=>t.title.toLowerCase().includes(o)).length;a+=r*10;const n=i.filter(o=>t.description.toLowerCase().includes(o)).length;a+=n*5;const c=i.filter(o=>t.culturalContext.culturalSignificance.toLowerCase().includes(o)||t.culturalContext.primaryCulture.toLowerCase().includes(o)).length;return a+=c*8,a}calculateSuggestionScore(t,s){let i=0;return t.culturalContext.primaryCulture!==s.primaryCulture&&(i+=30),t.category.crossCultural&&(i+=25),i+=t.verification.verificationScore*.2,i+=t.culturalSignificance.educationalValue*3,i+=Math.min(t.engagementMetrics.views/50,20),i}calculateRelatedScore(t,s){let i=0;t.culturalContext.primaryCulture===s.culturalContext.primaryCulture&&(i+=30),t.category.primary===s.category.primary&&(i+=25),t.location.province===s.location.province&&(i+=20);const r=Math.abs(t.timeframe.startDate.getTime()-s.timeframe.startDate.getTime())/(365*24*60*60*1e3);return r<10?i+=15:r<50&&(i+=10),i+=t.verification.verificationScore*.1,i}calculateCulturalDistribution(t){const s={};return t.forEach(i=>{const a=i.culturalContext.primaryCulture;s[a]=(s[a]||0)+1}),s}calculateCategoryDistribution(t){const s={};return t.forEach(i=>{const a=i.category.primary;s[a]=(s[a]||0)+1}),s}async getUserProfile(t){return{primaryCulture:"Zulu",interests:["sports","arts","community_service"],culturalLearningGoals:["cross_cultural_understanding"]}}}const B=new $,_=({userId:h})=>{const[t,s]=x.useState([]),[i,a]=x.useState([]),[r,n]=x.useState(null),[c,o]=x.useState({}),[m,v]=x.useState(""),[S,b]=x.useState(!0),[y,C]=x.useState("gallery"),[N,E]=x.useState([]),[A,T]=x.useState([]),R=[{value:"sports",label:"Sports & Athletics"},{value:"arts",label:"Arts & Culture"},{value:"business",label:"Business & Innovation"},{value:"education",label:"Education & Learning"},{value:"innovation",label:"Innovation & Technology"},{value:"community_service",label:"Community Service"}],L=["Zulu","Xhosa","Afrikaans","English","Sotho","Tswana","Pedi","Venda","Tsonga","Ndebele","Swati"];x.useEffect(()=>{V()},[]),x.useEffect(()=>{m||Object.keys(c).length>0?d():D()},[c,m]);const V=async()=>{try{b(!0);const[l,j]=await Promise.all([B.getFeaturedAchievements(),B.getAchievementSuggestions(h)]);a(l),T(j),await D()}catch(l){console.error("Error loading initial data:",l)}finally{b(!1)}},D=async()=>{try{const l=await B.getAchievements(c);s(l)}catch(l){console.error("Error loading achievements:",l)}},d=async()=>{if(!m.trim()){await D();return}try{const l=await B.searchAchievements({searchText:m,filters:c,limit:20});s(l)}catch(l){console.error("Error searching achievements:",l)}},g=async()=>{try{const l={startDate:new Date(1900,0,1),endDate:new Date},j=await B.getAchievementTimeline(l);E(j)}catch(l){console.error("Error loading timeline:",l)}},f=async l=>{n(l),await B.trackAchievementView(l.id,h);const j=t.map(M=>M.id===l.id?{...M,engagementMetrics:{...M.engagementMetrics,views:M.engagementMetrics.views+1}}:M);s(j)},u=async(l,j)=>{await B.trackSocialSharing(l.id,j),console.log(`Sharing ${l.title} on ${j}`)},p=(l,j)=>{o(M=>({...M,[l]:j}))},w=()=>{o({}),v("")},I=l=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>f(l),children:[l.mediaContent[0]&&e.jsxs("div",{className:"h-48 bg-gray-200 relative",children:[e.jsx("img",{src:l.mediaContent[0].url,alt:l.mediaContent[0].caption,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute top-2 right-2",children:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs text-white ${l.verification.status==="verified"?"bg-green-500":l.verification.status==="pending"?"bg-yellow-500":"bg-gray-500"}`,children:l.verification.status})})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-2",children:l.title}),e.jsx("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:l.description}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[e.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs",children:l.category.primary}),e.jsx("span",{className:"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs",children:l.culturalContext.primaryCulture}),l.category.crossCultural&&e.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded text-xs",children:"Cross-Cultural"})]}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500",children:[e.jsxs("span",{children:[l.location.city,", ",l.location.province]}),e.jsx("span",{children:l.timeframe.startDate.getFullYear()})]}),e.jsxs("div",{className:"flex justify-between items-center mt-3 pt-3 border-t",children:[e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsxs("svg",{className:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:[e.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),e.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]}),l.engagementMetrics.views]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx("svg",{className:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"})}),l.engagementMetrics.shares]})]}),e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("button",{onClick:j=>{j.stopPropagation(),u(l,"facebook")},className:"p-1 text-gray-400 hover:text-blue-600",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z",clipRule:"evenodd"})})}),e.jsx("button",{onClick:j=>{j.stopPropagation(),u(l,"twitter")},className:"p-1 text-gray-400 hover:text-blue-400",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"})})})]})]})]})]},l.id),q=()=>e.jsx("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:e.jsxs("div",{className:"flex flex-wrap gap-4 items-center",children:[e.jsx("div",{className:"flex-1 min-w-64",children:e.jsx("input",{type:"text",placeholder:"Search achievements...",value:m,onChange:l=>v(l.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})}),e.jsxs("select",{value:c.category||"",onChange:l=>p("category",l.target.value||void 0),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Categories"}),R.map(l=>e.jsx("option",{value:l.value,children:l.label},l.value))]}),e.jsxs("select",{value:c.culturalContext||"",onChange:l=>p("culturalContext",l.target.value||void 0),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Cultures"}),L.map(l=>e.jsx("option",{value:l,children:l},l))]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:c.crossCultural||!1,onChange:l=>p("crossCultural",l.target.checked||void 0),className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"Cross-Cultural Only"})]}),(Object.keys(c).length>0||m)&&e.jsx("button",{onClick:w,className:"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600",children:"Clear Filters"})]})}),F=()=>e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-6",children:"Achievement Timeline"}),e.jsx("div",{className:"space-y-6",children:N.map(l=>e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-blue-600 font-semibold",children:l.date.getFullYear()})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:l.title}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:l.description}),e.jsxs("div",{className:"flex items-center space-x-4 mt-2",children:[e.jsx("span",{className:"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs",children:l.culturalContext}),e.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs",children:l.category}),l.crossCultural&&e.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded text-xs",children:"Cross-Cultural"})]})]})]},l.id))})]}),z=()=>e.jsx("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6",children:[{key:"gallery",label:"Gallery",icon:"🖼️"},{key:"featured",label:"Featured",icon:"⭐"},{key:"suggestions",label:"For You",icon:"💡"},{key:"timeline",label:"Timeline",icon:"📅"}].map(l=>e.jsxs("button",{onClick:()=>{C(l.key),l.key==="timeline"&&g()},className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${y===l.key?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[e.jsx("span",{children:l.icon}),e.jsx("span",{children:l.label})]},l.key))});return S?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"South African Achievement Gallery"}),e.jsx("p",{className:"text-gray-600",children:"Discover and celebrate the remarkable achievements of South Africans across all cultures and communities"})]}),z(),y!=="timeline"&&q(),y==="gallery"&&e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(I)}),y==="featured"&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"Featured Achievements"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:i.map(I)})]}),y==="suggestions"&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"Recommended for You"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Discover achievements from different cultures to broaden your perspective and inspire cross-cultural understanding"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:A.map(I)})]}),y==="timeline"&&F(),t.length===0&&y==="gallery"&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No achievements found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria or filters"})]}),r&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:r.title}),e.jsx("button",{onClick:()=>n(null),className:"text-gray-400 hover:text-gray-600",children:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),r.mediaContent[0]&&e.jsx("img",{src:r.mediaContent[0].url,alt:r.mediaContent[0].caption,className:"w-full h-64 object-cover rounded-lg mb-4"}),e.jsx("p",{className:"text-gray-700 mb-6",children:r.description}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Cultural Context"}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:r.culturalContext.culturalSignificance}),e.jsx("h3",{className:"font-semibold mb-2",children:"Location & Time"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[r.location.city,", ",r.location.province,e.jsx("br",{}),r.timeframe.startDate.getFullYear()," - ",r.timeframe.endDate.getFullYear()]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Impact & Significance"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Cultural Impact:"}),e.jsxs("span",{className:"font-medium",children:[r.culturalSignificance.preservationValue,"/10"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Educational Value:"}),e.jsxs("span",{className:"font-medium",children:[r.culturalSignificance.educationalValue,"/10"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Inspirational Value:"}),e.jsxs("span",{className:"font-medium",children:[r.socialImpact.inspirationalValue,"/10"]})]})]})]})]})]})})})]})};class U{userBadges=new Map;badgeDefinitions=new Map;userInteractions=new Map;userServiceActivities=new Map;userBridgeBuilding=new Map;constructor(){this.initializeBadgeDefinitions()}async checkBadgeEligibility(t){const s=[],i=await this.getUserProgress(t);for(const[a,r]of this.badgeDefinitions){const n=this.calculateBadgeProgress(i,r),c=n>=100;s.push({badgeType:r,eligible:c,progress:Math.min(n,100),requirements:r.requirements,nextMilestone:this.getNextMilestone(n,r),estimatedTimeToEarn:this.estimateTimeToEarn(n,r,i)})}return s.sort((a,r)=>r.progress-a.progress)}async awardBadge(t,s){const i=await this.getUserProgress(t),a=this.determineBadgeLevel(i,s),r={id:this.generateId(),userId:t,badgeType:s,level:a,earnedDate:new Date,verificationStatus:{status:"verified",verifiedBy:"system",verificationDate:new Date,communityEndorsements:0},culturalContext:this.extractCulturalContext(i,s),impactMetrics:this.calculateImpactMetrics(i,s),sharingPermissions:{publicProfile:!0,communitySharing:!0,socialMediaSharing:!0,culturalRepresentativeSharing:!0}},n=this.userBadges.get(t)||[];n.push(r),this.userBadges.set(t,n);const c=this.getNextBadgeLevel(a);return{badge:r,celebrationMessage:this.generateCelebrationMessage(r),culturalContext:this.getCulturalContextMessage(r.culturalContext),nextLevelInfo:c,sharingRecommendations:this.generateSharingRecommendations(r)}}async getBadgeProgress(t,s){const i=await this.getUserProgress(t),a=this.calculateBadgeProgress(i,s);return{badgeType:s,currentProgress:a,requirements:s.requirements.map(r=>({requirement:r.requirement,target:r.quantitativeTarget||r.qualitativeTarget,current:this.getCurrentRequirementProgress(i,r),completed:this.isRequirementCompleted(i,r)})),estimatedCompletion:this.estimateTimeToEarn(a,s,i),nextActions:this.getNextActions(i,s)}}async getUserBadges(t){return this.userBadges.get(t)||[]}async recordCrossCulturalInteraction(t){const s=this.userInteractions.get(t.userId)||[];s.push(t),this.userInteractions.set(t.userId,s),await this.checkAndAwardAutomaticBadges(t.userId)}async trackCommunityServiceActivity(t){const s=this.userServiceActivities.get(t.userId)||[];s.push(t),this.userServiceActivities.set(t.userId,s),await this.checkAndAwardAutomaticBadges(t.userId)}async measureBridgeBuildingImpact(t){const s=this.userBridgeBuilding.get(t.userId)||[];s.push(t),this.userBridgeBuilding.set(t.userId,s);const i=s.reduce((r,n)=>r+n.participantsConnected,0);new Set(s.flatMap(r=>r.culturesInvolved)).size,s.filter(r=>r.sustainableOutcome).length;const a={crossCulturalConnections:i,communityServiceHours:0,culturalLearningMoments:s.length,bridgeBuildingInstances:s.length,mentorshipRelationships:s.filter(r=>r.eventType==="connection_facilitation").length};return await this.checkAndAwardAutomaticBadges(t.userId),a}async calculateEngagementScore(t,s){const i=this.userInteractions.get(t)||[],a=this.userServiceActivities.get(t)||[],r=this.userBridgeBuilding.get(t)||[],n=i.filter(b=>b.timestamp>=s.startDate&&b.timestamp<=s.endDate),c=a.filter(b=>b.timestamp>=s.startDate&&b.timestamp<=s.endDate),o=r.filter(b=>b.timestamp>=s.startDate&&b.timestamp<=s.endDate),m=this.calculateCrossCulturalScore(n),v=this.calculateServiceScore(c),S=this.calculateBridgeBuildingScore(o);return{totalScore:m+v+S,crossCulturalEngagement:m,communityService:v,bridgeBuilding:S,culturalDiversity:this.calculateCulturalDiversityScore(n),consistency:this.calculateConsistencyScore(n,c),growth:this.calculateGrowthScore(t,s)}}async generateRecognitionReport(t){const s=await this.getUserBadges(t),i=this.userInteractions.get(t)||[],a=this.userServiceActivities.get(t)||[],r=this.userBridgeBuilding.get(t)||[],n=new Set([...i.flatMap(m=>m.participantCultures),...a.map(m=>m.culturalContext),...r.flatMap(m=>m.culturesInvolved)]).size,c=a.reduce((m,v)=>m+v.hoursContributed,0),o=r.reduce((m,v)=>m+v.participantsConnected,0);return{userId:t,reportDate:new Date,summary:{totalBadges:s.length,badgesByLevel:this.groupBadgesByLevel(s),culturalEngagement:{culturesEngaged:n,crossCulturalInteractions:i.length,averageQualityScore:this.calculateAverageQuality(i)},communityImpact:{serviceHours:c,communitiesServed:new Set(a.map(m=>m.communityBenefited)).size,connectionsFormed:o},achievements:s.map(m=>({badgeType:m.badgeType.specificType,level:m.level.level,earnedDate:m.earnedDate,culturalContext:m.culturalContext.culturesInvolved}))},recommendations:this.generatePersonalizedRecommendations(t,s,i,a)}}async getLeaderboard(t,s){const i=new Set([...this.userInteractions.keys(),...this.userServiceActivities.keys(),...this.userBridgeBuilding.keys()]),a=[];for(const r of i){const n=await this.calculateEngagementScore(r,s),c=await this.getUserBadges(r);let o=0;switch(t){case"cross_cultural_engagement":o=n.crossCulturalEngagement;break;case"community_service":o=n.communityService;break;case"bridge_building":o=n.bridgeBuilding;break;default:o=n.totalScore}a.push({userId:r,score:o,badges:c.length,culturalDiversity:n.culturalDiversity,rank:0})}return a.sort((r,n)=>n.score-r.score),a.forEach((r,n)=>{r.rank=n+1}),a.slice(0,50)}initializeBadgeDefinitions(){this.badgeDefinitions.set("cultural_bridge_builder",{category:"cross_cultural_engagement",specificType:"Cultural Bridge Builder",description:"Connects people from different cultural backgrounds",requirements:[{requirement:"Facilitate meaningful conversations between 5+ different cultures",quantitativeTarget:5,verificationMethod:"automatic"},{requirement:"Maintain cross-cultural relationships for 3+ months",quantitativeTarget:3,verificationMethod:"community_validation"}],culturalScope:{minimumCultures:3,crossCulturalRequired:!0,communityServiceRequired:!1},difficultyLevel:"intermediate"}),this.badgeDefinitions.set("ubuntu_ambassador",{category:"bridge_building",specificType:"Ubuntu Ambassador",description:"Embodies Ubuntu philosophy in cross-cultural interactions",requirements:[{requirement:"Demonstrate Ubuntu principles in 10+ interactions",quantitativeTarget:10,verificationMethod:"community_validation"},{requirement:"Receive community endorsements from 3+ cultures",quantitativeTarget:3,verificationMethod:"expert_review"}],culturalScope:{minimumCultures:3,crossCulturalRequired:!0,communityServiceRequired:!0},difficultyLevel:"advanced"}),this.badgeDefinitions.set("community_service_champion",{category:"community_service",specificType:"Community Service Champion",description:"Dedicated to serving diverse communities",requirements:[{requirement:"Complete 50+ hours of community service",quantitativeTarget:50,verificationMethod:"automatic"},{requirement:"Serve communities from 2+ different cultures",quantitativeTarget:2,verificationMethod:"community_validation"}],culturalScope:{minimumCultures:2,crossCulturalRequired:!1,communityServiceRequired:!0},difficultyLevel:"intermediate"})}async getUserProgress(t){const s=this.userInteractions.get(t)||[],i=this.userServiceActivities.get(t)||[],a=this.userBridgeBuilding.get(t)||[];return{crossCulturalInteractions:s.length,culturesEngaged:new Set(s.flatMap(r=>r.participantCultures)).size,serviceHours:i.reduce((r,n)=>r+n.hoursContributed,0),communitiesServed:new Set(i.map(r=>r.communityBenefited)).size,bridgeBuildingEvents:a.length,connectionsFormed:a.reduce((r,n)=>r+n.participantsConnected,0),averageQualityScore:this.calculateAverageQuality(s),sustainableOutcomes:a.filter(r=>r.sustainableOutcome).length}}calculateBadgeProgress(t,s){let i=0,a=s.requirements.length;for(const r of s.requirements){const n=this.getCurrentRequirementProgress(t,r),c=r.quantitativeTarget||1,o=Math.min(n/c*100,100);i+=o}return i/a}getCurrentRequirementProgress(t,s){const i=s.requirement.toLowerCase();return i.includes("conversation")||i.includes("interaction")?t.crossCulturalInteractions:i.includes("culture")&&i.includes("different")?t.culturesEngaged:i.includes("service")||i.includes("hour")?t.serviceHours:i.includes("community")&&i.includes("serve")?t.communitiesServed:i.includes("connection")||i.includes("bridge")?t.connectionsFormed:i.includes("ubuntu")||i.includes("principle")?t.sustainableOutcomes:0}isRequirementCompleted(t,s){const i=this.getCurrentRequirementProgress(t,s),a=s.quantitativeTarget||1;return i>=a}determineBadgeLevel(t,s){const i=t.crossCulturalInteractions+t.serviceHours+t.bridgeBuildingEvents*5;return i>=200?{level:"platinum",pointsRequired:200,specialPrivileges:["Cultural Ambassador Status"]}:i>=100?{level:"gold",pointsRequired:100,specialPrivileges:["Community Leadership"]}:i>=50?{level:"silver",pointsRequired:50,specialPrivileges:["Mentorship Opportunities"]}:{level:"bronze",pointsRequired:25,specialPrivileges:["Recognition Badge"]}}extractCulturalContext(t,s){return{culturesInvolved:["Zulu","English","Afrikaans"],interactionType:{type:"collaboration",duration:120,qualityScore:8.5,participants:[]},impactLevel:"community",sustainabilityMetrics:{ongoingEngagement:!0,relationshipDevelopment:8,culturalUnderstandingGrowth:9,communityImpact:7},bridgeBuildingScore:85,culturalLearningOutcomes:[]}}calculateImpactMetrics(t,s){return{crossCulturalConnections:t.connectionsFormed,communityServiceHours:t.serviceHours,culturalLearningMoments:t.crossCulturalInteractions,bridgeBuildingInstances:t.bridgeBuildingEvents,mentorshipRelationships:Math.floor(t.bridgeBuildingEvents/2)}}generateCelebrationMessage(t){return{"Cultural Bridge Builder":"Congratulations! You've earned the Cultural Bridge Builder badge for connecting diverse communities through Ubuntu spirit!","Ubuntu Ambassador":`Amazing! You've become an Ubuntu Ambassador, embodying "I am because we are" in all your interactions!`,"Community Service Champion":"Well done! Your dedication to serving diverse communities has earned you the Community Service Champion badge!"}[t.badgeType.specificType]||`Congratulations on earning the ${t.badgeType.specificType} badge!`}getCulturalContextMessage(t){return`This achievement represents meaningful engagement across ${t.culturesInvolved.join(", ")} cultures, embodying the Ubuntu philosophy of interconnectedness.`}generateSharingRecommendations(t){return["Share your achievement with your cultural community","Inspire others by posting about your cross-cultural journey","Mentor someone who is starting their cultural bridge-building journey","Celebrate with the communities you've connected with"]}getNextBadgeLevel(t){const s=["bronze","silver","gold","platinum","cultural_ambassador"],i=s.indexOf(t.level);if(i<s.length-1){const a=s[i+1],r=[25,50,100,200,500][i+1],n=[["Recognition Badge"],["Mentorship Opportunities"],["Community Leadership"],["Cultural Ambassador Status"],["Platform Advisory Role"]][i+1];return{level:a,pointsRequired:r,specialPrivileges:n}}return null}getNextMilestone(t,s){return t>=100?"Badge earned! Ready to claim.":t>=75?"Almost there! Complete one more requirement.":t>=50?"Halfway to earning this badge.":t>=25?"Good progress! Keep engaging across cultures.":"Start by engaging with different cultural communities."}estimateTimeToEarn(t,s,i){const a=100-t,r=s.difficultyLevel;if(a<=0)return"Ready to claim!";const n={beginner:2,intermediate:4,advanced:8,expert:16}[r]||4,c=Math.ceil(a/100*n);return c<=1?"Within a week":c<=4?`${c} weeks`:`${Math.ceil(c/4)} months`}getNextActions(t,s){const i=[];for(const a of s.requirements)if(!this.isRequirementCompleted(t,a)){const r=this.getCurrentRequirementProgress(t,a),c=(a.quantitativeTarget||1)-r;i.push(`${a.requirement} (${c} more needed)`)}return i}async checkAndAwardAutomaticBadges(t){const s=await this.checkBadgeEligibility(t),i=await this.getUserBadges(t),a=new Set(i.map(r=>r.badgeType.specificType));for(const r of s)r.eligible&&!a.has(r.badgeType.specificType)&&await this.awardBadge(t,r.badgeType)}calculateCrossCulturalScore(t){return t.reduce((s,i)=>{const a=i.participantCultures.length*5,r=i.qualityScore*2,n=Math.min(i.duration/30,10);return s+a+r+n},0)}calculateServiceScore(t){return t.reduce((s,i)=>{const a=i.hoursContributed*2,r=i.culturalContext!=="same"?10:0;return s+a+r},0)}calculateBridgeBuildingScore(t){return t.reduce((s,i)=>{const a=i.participantsConnected*5,r=i.culturesInvolved.length*10,n=i.sustainableOutcome?20:0;return s+a+r+n},0)}calculateCulturalDiversityScore(t){const s=new Set(t.flatMap(i=>i.participantCultures));return Math.min(s.size*10,100)}calculateConsistencyScore(t,s){const i=[...t,...s];if(i.length<2)return 0;const r=(Math.max(...i.map(c=>c.timestamp.getTime()))-Math.min(...i.map(c=>c.timestamp.getTime())))/(7*24*60*60*1e3),n=i.length/r;return Math.min(n*20,100)}calculateGrowthScore(t,s){const i=this.userInteractions.get(t)||[],a=i.filter(o=>o.timestamp>=s.startDate),r=i.filter(o=>o.timestamp<s.startDate);if(r.length===0)return 50;const n=this.calculateAverageQuality(a),c=this.calculateAverageQuality(r);return Math.max(0,Math.min(100,50+(n-c)*10))}calculateAverageQuality(t){return t.length===0?0:t.reduce((s,i)=>s+i.qualityScore,0)/t.length}groupBadgesByLevel(t){const s={};return t.forEach(i=>{s[i.level.level]=(s[i.level.level]||0)+1}),s}generatePersonalizedRecommendations(t,s,i,a){const r=[];return new Set(i.flatMap(o=>o.participantCultures)).size<3&&r.push("Try engaging with more diverse cultural communities to broaden your perspective"),a.length<2&&r.push("Consider volunteering in community service to earn service-related badges"),[...i,...a].filter(o=>o.timestamp>new Date(Date.now()-30*24*60*60*1e3)).length<3&&r.push("Increase your engagement frequency to maintain momentum in badge earning"),s.length===0&&r.push("Start with the Cultural Bridge Builder badge by facilitating conversations between different cultures"),r}generateId(){return Math.random().toString(36).substr(2,9)}}const k=new U,H=({userId:h})=>{const[t,s]=x.useState([]),[i,a]=x.useState([]),[r,n]=x.useState(null),[c,o]=x.useState(null),[m,v]=x.useState([]),[S,b]=x.useState(!0),[y,C]=x.useState("badges"),[N,E]=x.useState(null);x.useEffect(()=>{A()},[h]);const A=async()=>{try{b(!0);const[u,p,w,I,q]=await Promise.all([k.getUserBadges(h),k.checkBadgeEligibility(h),k.calculateEngagementScore(h,{startDate:new Date(Date.now()-30*24*60*60*1e3),endDate:new Date}),k.generateRecognitionReport(h),k.getLeaderboard("total",{startDate:new Date(Date.now()-30*24*60*60*1e3),endDate:new Date})]);s(u),a(p),n(w),o(I),v(q)}catch(u){console.error("Error loading user data:",u)}finally{b(!1)}},T=async u=>{try{const p=await k.awardBadge(h,u);E(p),await A()}catch(p){console.error("Error claiming badge:",p)}},R=async()=>{const u={userId:h,interactionType:{type:"conversation",duration:45,qualityScore:8.5,participants:["user1","user2"]},participantCultures:["Zulu","Afrikaans"],duration:45,qualityScore:8.5,culturalLearningOutcomes:[{outcome:"Learned about traditional Zulu greetings",culturalContext:"Zulu",learningType:"knowledge",participants:[h]}],timestamp:new Date,verificationMethod:"automatic"};await k.recordCrossCulturalInteraction(u),await A()},L=async()=>{const u={userId:h,activityType:"volunteering",communityBenefited:"Local Zulu Community",culturalContext:"Zulu",hoursContributed:4,impactDescription:"Helped organize cultural festival",verificationDocuments:[],communityEndorsements:["community-leader-1"],timestamp:new Date};await k.trackCommunityServiceActivity(u),await A()},V=u=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold ${u.level.level==="platinum"?"bg-gray-400":u.level.level==="gold"?"bg-yellow-500":u.level.level==="silver"?"bg-gray-300":"bg-orange-600"}`,children:u.level.level==="cultural_ambassador"?"👑":u.level.level==="platinum"?"💎":u.level.level==="gold"?"🥇":u.level.level==="silver"?"🥈":"🥉"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:u.badgeType.specificType}),e.jsxs("p",{className:"text-sm text-gray-600 capitalize",children:[u.level.level," Level"]})]})]}),e.jsx("span",{className:"text-xs text-gray-500",children:u.earnedDate.toLocaleDateString()})]}),e.jsx("p",{className:"text-sm text-gray-700 mb-4",children:u.badgeType.description}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Cultures Involved:"}),e.jsx("span",{className:"font-medium",children:u.culturalContext.culturesInvolved.join(", ")})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Impact Level:"}),e.jsx("span",{className:"font-medium capitalize",children:u.culturalContext.impactLevel})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Bridge Building Score:"}),e.jsxs("span",{className:"font-medium",children:[u.culturalContext.bridgeBuildingScore,"/100"]})]})]}),u.level.specialPrivileges.length>0&&e.jsxs("div",{className:"mt-4 pt-4 border-t",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Special Privileges:"}),e.jsx("ul",{className:"text-xs text-gray-600 space-y-1",children:u.level.specialPrivileges.map((p,w)=>e.jsxs("li",{children:["• ",p]},w))})]})]},u.id),D=u=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:u.badgeType.specificType}),e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${u.eligible?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:u.eligible?"Ready to Claim!":`${Math.round(u.progress)}% Complete`})]}),e.jsx("p",{className:"text-sm text-gray-700 mb-4",children:u.badgeType.description}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:"Progress"}),e.jsxs("span",{children:[Math.round(u.progress),"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(u.progress,100)}%`}})})]}),e.jsxs("div",{className:"space-y-2 mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Requirements:"}),u.requirements.map((p,w)=>e.jsxs("div",{className:"text-xs text-gray-600",children:["• ",p.requirement]},w))]}),e.jsxs("div",{className:"text-sm text-gray-600 mb-4",children:[e.jsx("strong",{children:"Next Milestone:"})," ",u.nextMilestone]}),e.jsxs("div",{className:"text-sm text-gray-600 mb-4",children:[e.jsx("strong",{children:"Estimated Time:"})," ",u.estimatedTimeToEarn]}),u.eligible&&e.jsx("button",{onClick:()=>T(u.badgeType),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Claim Badge"})]},u.badgeType.specificType),d=()=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-6",children:"Your Engagement Score (Last 30 Days)"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-blue-600",children:r?.totalScore||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Score"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-green-600",children:r?.culturalDiversity||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Cultural Diversity"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-purple-600",children:r?.consistency||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Consistency"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Cross-Cultural Engagement"}),e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:r?.crossCulturalEngagement||0}),e.jsx("div",{className:"text-sm text-blue-700",children:"Points from cultural interactions"})]}),e.jsxs("div",{className:"bg-green-50 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-green-900 mb-2",children:"Community Service"}),e.jsx("div",{className:"text-2xl font-bold text-green-600",children:r?.communityService||0}),e.jsx("div",{className:"text-sm text-green-700",children:"Points from service activities"})]}),e.jsxs("div",{className:"bg-purple-50 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-purple-900 mb-2",children:"Bridge Building"}),e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:r?.bridgeBuilding||0}),e.jsx("div",{className:"text-sm text-purple-700",children:"Points from connecting communities"})]})]})]}),g=()=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-6",children:"Community Leaderboard"}),e.jsx("div",{className:"space-y-4",children:m.slice(0,10).map((u,p)=>e.jsxs("div",{className:`flex items-center justify-between p-3 rounded-lg ${u.userId===h?"bg-blue-50 border border-blue-200":"bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${p===0?"bg-yellow-500":p===1?"bg-gray-400":p===2?"bg-orange-600":"bg-gray-300"}`,children:p+1}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:u.userId===h?"You":`User ${u.userId.slice(-4)}`}),e.jsxs("div",{className:"text-sm text-gray-600",children:[u.badges," badges • ",u.culturalDiversity," cultural diversity"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-bold text-lg",children:u.score}),e.jsx("div",{className:"text-sm text-gray-600",children:"points"})]})]},u.userId))})]}),f=()=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-6",children:"Your Cultural Impact"}),c&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-blue-600",children:c.summary.culturalEngagement.culturesEngaged}),e.jsx("div",{className:"text-sm text-gray-600",children:"Cultures Engaged"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-green-600",children:c.summary.communityImpact.serviceHours}),e.jsx("div",{className:"text-sm text-gray-600",children:"Service Hours"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-purple-600",children:c.summary.communityImpact.connectionsFormed}),e.jsx("div",{className:"text-sm text-gray-600",children:"Connections Formed"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Recent Achievements"}),e.jsx("div",{className:"space-y-2",children:c.summary.achievements.slice(0,5).map((u,p)=>e.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[e.jsx("span",{className:"text-sm",children:u.badgeType}),e.jsx("span",{className:"text-xs text-gray-500",children:u.earnedDate})]},p))})]}),c.recommendations.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Recommendations"}),e.jsx("ul",{className:"space-y-2",children:c.recommendations.map((u,p)=>e.jsxs("li",{className:"text-sm text-gray-700",children:["• ",u]},p))})]})]})]});return S?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Recognition Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Track your cross-cultural engagement and earn recognition for building bridges between communities"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Quick Actions"}),e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsx("button",{onClick:R,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Record Cross-Cultural Interaction"}),e.jsx("button",{onClick:L,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:"Log Community Service"})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6",children:[{key:"badges",label:"My Badges",icon:"🏆"},{key:"progress",label:"Progress",icon:"📈"},{key:"leaderboard",label:"Leaderboard",icon:"🥇"},{key:"impact",label:"Impact",icon:"🌍"}].map(u=>e.jsxs("button",{onClick:()=>C(u.key),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${y===u.key?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[e.jsx("span",{children:u.icon}),e.jsx("span",{children:u.label})]},u.key))}),y==="badges"&&e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-semibold mb-6",children:["Your Badges (",t.length,")"]}),t.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(V)}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No badges yet"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Start engaging with different cultures to earn your first badge!"}),e.jsx("button",{onClick:R,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Record Your First Interaction"})]})]}),y==="progress"&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"Badge Progress"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:i.map(D)})]}),y==="leaderboard"&&g(),y==="impact"&&e.jsxs("div",{className:"space-y-6",children:[d(),f()]}),N&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx("span",{className:"text-2xl",children:"🏆"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Congratulations!"}),e.jsx("h3",{className:"text-lg font-semibold text-blue-600 mb-4",children:N.badge.badgeType.specificType}),e.jsx("p",{className:"text-gray-700 mb-4",children:N.celebrationMessage}),e.jsx("p",{className:"text-sm text-gray-600 mb-6",children:N.culturalContext}),N.nextLevelInfo&&e.jsxs("div",{className:"bg-blue-50 rounded-lg p-4 mb-4",children:[e.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Next Level"}),e.jsxs("p",{className:"text-sm text-blue-700",children:["Earn ",N.nextLevelInfo.pointsRequired," more points to reach ",N.nextLevelInfo.level," level"]})]}),e.jsx("button",{onClick:()=>E(null),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700",children:"Continue"})]})})})]})};class O{curations=new Map;validationQueue=new Map;communityReviews=new Map;expertNetwork=new Map;constructor(){this.initializeExpertNetwork()}async submitCurationRequest(t){const s={id:this.generateId(),culturalRepresentativeId:t.culturalRepresentativeId,achievementId:t.achievementId,curationMetadata:{culturalSignificance:{significanceLevel:"community",culturalImpact:t.proposedSignificance,traditionalKnowledgeElements:t.traditionalElements,modernRelevance:"Contemporary cultural significance",preservationValue:8,educationalValue:7},traditionalElements:t.traditionalElements.map(i=>({element:i,significance:"Traditional cultural practice",authenticity:"verified"})),modernRelevance:{contemporarySignificance:t.proposedSignificance,modernApplications:[],culturalContinuity:"Strong connection to traditional practices",adaptationRespect:!0},culturalProtocols:t.culturalProtocols.map(i=>({protocol:i,description:"Cultural protocol for respectful engagement",importance:"required",culturalContext:"Traditional cultural practice"})),sensitivityLevel:{level:t.sensitivityLevel,restrictions:this.determineSensitivityRestrictions(t.sensitivityLevel),accessRequirements:this.determineAccessRequirements(t.sensitivityLevel)},sharingRestrictions:this.determineSharingRestrictions(t.sensitivityLevel),communityPermissions:{hasPermission:!1,grantedBy:"",grantDate:new Date,conditions:[],revocable:!0}},culturalValidation:{primaryValidator:t.culturalRepresentativeId,secondaryValidators:[],validationCriteria:this.getDefaultValidationCriteria(),expertConsultations:[],communityFeedback:[],validationScore:0,culturalAccuracyRating:0},communityEndorsement:{endorsements:[],votingResults:{totalVotes:0,approvalPercentage:0,culturalCommunityVotes:0,generalCommunityVotes:0,votingPeriod:{startDate:new Date,endDate:new Date(Date.now()+12096e5),historicalPeriod:"democratic_era",culturalCalendarAlignment:[]}},representativeApproval:{approvedBy:"",approvalDate:new Date,approvalNotes:"",conditions:[]}},historicalVerification:{primarySources:[],secondarySources:[],expertVerification:[],traditionalKnowledgeKeepers:[],verificationLevel:"preliminary",accuracyConfidence:0,factCheckingResults:[]},crossCulturalElements:[],publicationStatus:{status:"draft",visibility:"cultural_representatives_only",featuredStatus:!1},impactMetrics:{communityEngagement:0,culturalLearning:0,crossCulturalDiscovery:0,historicalPreservation:0,educationalValue:0}};return this.curations.set(s.id,s),t.communityConsultationRequired&&await this.initiateValidationProcess(s.id),s}async getCuration(t){return this.curations.get(t)||null}async updateCurationMetadata(t,s){const i=this.curations.get(t);if(!i)throw new Error("Curation not found");return i.curationMetadata={...i.curationMetadata,...s},this.curations.set(t,i),i}async initiateValidationProcess(t){const s=this.curations.get(t);if(!s)throw new Error("Curation not found");const a=(await this.identifyRequiredValidators(s)).map(r=>({curationId:t,validatorId:r.id,validatorType:r.type,validationCriteria:this.getValidationCriteriaForType(r.type),expertiseAreas:r.expertiseAreas}));this.validationQueue.set(t,a),await this.notifyValidators(a)}async submitExpertValidation(t,s,i){const a=this.curations.get(t);if(!a)throw new Error("Curation not found");a.culturalValidation.expertConsultations.push(i),await this.updateValidationScore(t),this.curations.set(t,a)}async addHistoricalSource(t,s){const i=this.curations.get(t);if(!i)throw new Error("Curation not found");s.reliability>=8?i.historicalVerification.primarySources.push(s):i.historicalVerification.secondarySources.push(s),await this.updateVerificationLevel(t),this.curations.set(t,i)}async consultTraditionalKnowledgeKeeper(t,s){const i=this.curations.get(t);if(!i)throw new Error("Curation not found");i.historicalVerification.traditionalKnowledgeKeepers.push(s),i.historicalVerification.accuracyConfidence+=20,this.curations.set(t,i)}async submitCommunityReview(t){const s=this.communityReviews.get(t.curationId)||[];s.push(t),this.communityReviews.set(t.curationId,s);const i=this.curations.get(t.curationId);i&&(i.culturalValidation.communityFeedback.push({feedbackId:this.generateId(),providerId:t.reviewerId,feedbackType:t.reviewType,feedback:t.feedback,rating:t.rating,date:new Date}),await this.updateCommunityApproval(t.curationId),this.curations.set(t.curationId,i))}async getCommunityReviews(t){return this.communityReviews.get(t)||[]}async calculateCommunityConsensus(t){const s=this.communityReviews.get(t)||[];if(!this.curations.get(t)||s.length===0)return{consensus:"insufficient_data",confidence:0};const a=s.reduce((o,m)=>o+m.rating,0)/s.length,r=s.filter(o=>this.isCulturalCommunityMember(o.reviewerId,o.culturalContext)),n=r.length>0?r.reduce((o,m)=>o+m.rating,0)/r.length:0;return{consensus:a>=7&&n>=7?"approved":a<=4||n<=4?"rejected":"mixed",confidence:Math.min(s.length*10,100),averageRating:a,culturalConsensus:n,totalReviews:s.length,culturalCommunityReviews:r.length}}async makePublicationDecision(t){const s=this.curations.get(t.curationId);if(!s)throw new Error("Curation not found");const i={status:t.decision==="approve"?"published":t.decision==="reject"?"archived":"cultural_review",publishDate:t.decision==="approve"?new Date:void 0,visibility:t.visibility,featuredStatus:!1};return s.publicationStatus=i,t.decision==="approve"&&await this.updateImpactMetrics(t.curationId),this.curations.set(t.curationId,s),s}async getPublishedCurations(t){const s=Array.from(this.curations.values()).filter(i=>i.publicationStatus.status==="published");return t?.culturalContext?s.filter(i=>i.curationMetadata.culturalSignificance.traditionalKnowledgeElements.some(a=>a.includes(t.culturalContext))):t?.sensitivityLevel?s.filter(i=>i.curationMetadata.sensitivityLevel.level===t.sensitivityLevel):s.sort((i,a)=>a.impactMetrics.communityEngagement-i.impactMetrics.communityEngagement)}async getFeaturedCurations(){return Array.from(this.curations.values()).filter(t=>t.publicationStatus.status==="published"&&t.publicationStatus.featuredStatus)}async generateCurationReport(t){const i=Array.from(this.curations.values()).filter(m=>m.publicationStatus.publishDate&&m.publicationStatus.publishDate>=t.startDate&&m.publicationStatus.publishDate<=t.endDate),a=i.length,r=i.filter(m=>m.publicationStatus.status==="published").length,n=i.reduce((m,v)=>m+v.culturalValidation.validationScore,0)/a||0,c=this.calculateCulturalDistribution(i),o=this.calculateSensitivityDistribution(i);return{timeframe:t,totalCurations:a,publishedCurations:r,publicationRate:r/a*100||0,averageValidationScore:n,culturalDistribution:c,sensitivityDistribution:o,topCurations:i.sort((m,v)=>v.impactMetrics.communityEngagement-m.impactMetrics.communityEngagement).slice(0,10).map(m=>({id:m.id,culturalSignificance:m.curationMetadata.culturalSignificance.culturalImpact,engagement:m.impactMetrics.communityEngagement}))}}async getCurationsByRepresentative(t){return Array.from(this.curations.values()).filter(s=>s.culturalRepresentativeId===t)}async getPendingValidations(t){const s=[];for(const[i,a]of this.validationQueue){const r=a.filter(n=>n.validatorId===t);s.push(...r)}return s}initializeExpertNetwork(){this.expertNetwork.set("expert-1",{id:"expert-1",type:"cultural_expert",expertiseAreas:["Zulu traditions","oral history"],culturalContext:"Zulu",credentials:["PhD Cultural Studies","Community Elder"]}),this.expertNetwork.set("expert-2",{id:"expert-2",type:"historian",expertiseAreas:["South African history","colonial period"],culturalContext:"Academic",credentials:["PhD History","Published researcher"]}),this.expertNetwork.set("keeper-1",{id:"keeper-1",type:"traditional_knowledge_keeper",expertiseAreas:["traditional medicine","ceremonial practices"],culturalContext:"Xhosa",credentials:["Traditional healer","Community recognized"]})}async identifyRequiredValidators(t){const s=[],i=t.curationMetadata.sensitivityLevel.level,a=Array.from(this.expertNetwork.values()).filter(r=>r.type==="cultural_expert");if(a.length>0&&s.push(a[0]),t.historicalVerification.primarySources.length>0){const r=Array.from(this.expertNetwork.values()).filter(n=>n.type==="historian");r.length>0&&s.push(r[0])}if(i==="sacred"||i==="restricted"){const r=Array.from(this.expertNetwork.values()).filter(n=>n.type==="traditional_knowledge_keeper");r.length>0&&s.push(r[0])}return s}getDefaultValidationCriteria(){return[{criterion:"Cultural accuracy",weight:30,passed:!1,notes:""},{criterion:"Historical accuracy",weight:25,passed:!1,notes:""},{criterion:"Cultural sensitivity",weight:25,passed:!1,notes:""},{criterion:"Community appropriateness",weight:20,passed:!1,notes:""}]}getValidationCriteriaForType(t){return{cultural_expert:["Cultural accuracy","Traditional knowledge validation","Community appropriateness"],historian:["Historical accuracy","Source verification","Factual consistency"],traditional_knowledge_keeper:["Sacred knowledge protocols","Cultural sensitivity","Traditional authenticity"],academic:["Academic rigor","Research methodology","Citation accuracy"]}[t]||["General validation"]}async notifyValidators(t){console.log(`Notifying ${t.length} validators for validation requests`)}async updateValidationScore(t){const s=this.curations.get(t);if(!s)return;const i=s.culturalValidation.expertConsultations;if(i.length===0)return;const a=i.reduce((r,n)=>r+n.confidence,0)/i.length;s.culturalValidation.validationScore=a,s.culturalValidation.culturalAccuracyRating=a}async updateVerificationLevel(t){const s=this.curations.get(t);if(!s)return;const i=s.historicalVerification,a=i.primarySources.length,r=i.secondarySources.length,n=i.expertVerification.length,c=i.traditionalKnowledgeKeepers.length;c>0&&a>2&&n>1?i.verificationLevel="culturally_endorsed":a>1&&n>0?i.verificationLevel="expert_verified":(a>0||r>1)&&(i.verificationLevel="validated"),i.accuracyConfidence=Math.min(a*20+r*10+n*15+c*25,100)}async updateCommunityApproval(t){const s=this.communityReviews.get(t)||[],i=this.curations.get(t);if(!i)return;const a=s.length,r=s.filter(o=>o.rating>=7).length,n=a>0?r/a*100:0,c=s.filter(o=>this.isCulturalCommunityMember(o.reviewerId,o.culturalContext)).length;i.communityEndorsement.votingResults={...i.communityEndorsement.votingResults,totalVotes:a,approvalPercentage:n,culturalCommunityVotes:c,generalCommunityVotes:a-c}}async updateImpactMetrics(t){const s=this.curations.get(t);s&&(s.impactMetrics={communityEngagement:Math.floor(Math.random()*100)+50,culturalLearning:Math.floor(Math.random()*100)+40,crossCulturalDiscovery:Math.floor(Math.random()*100)+30,historicalPreservation:s.historicalVerification.accuracyConfidence,educationalValue:s.curationMetadata.culturalSignificance.educationalValue*10})}determineSensitivityRestrictions(t){return{public:[],community_only:["Requires community membership"],restricted:["Requires cultural representative approval","Limited sharing"],sacred:["Sacred content - restricted access","No commercial use","Cultural protocols required"]}[t]||[]}determineAccessRequirements(t){return{public:[],community_only:["Community membership verification"],restricted:["Cultural representative endorsement","Purpose statement"],sacred:["Traditional knowledge keeper approval","Cultural protocol training","Community elder endorsement"]}[t]||[]}determineSharingRestrictions(t){const s=[{restrictionType:"attribution_required",description:"Attribution to cultural community required",enforcement:"automatic"}];return(t==="sacred"||t==="restricted")&&s.push({restrictionType:"no_commercial_use",description:"Commercial use prohibited",enforcement:"community_moderated"}),t==="sacred"&&s.push({restrictionType:"sacred_content",description:"Sacred cultural content - special protocols apply",enforcement:"expert_reviewed"}),s}isCulturalCommunityMember(t,s){return Math.random()>.5}calculateCulturalDistribution(t){const s={};return t.forEach(i=>{i.curationMetadata.culturalSignificance.traditionalKnowledgeElements.forEach(a=>{const r="Zulu";s[r]=(s[r]||0)+1})}),s}calculateSensitivityDistribution(t){const s={};return t.forEach(i=>{const a=i.curationMetadata.sensitivityLevel.level;s[a]=(s[a]||0)+1}),s}generateId(){return Math.random().toString(36).substr(2,9)}}const P=new O,K=({userId:h,userRole:t})=>{const[s,i]=x.useState([]),[a,r]=x.useState([]),[n,c]=x.useState(null),[o,m]=x.useState(!0),[v,S]=x.useState("my_curations"),[b,y]=x.useState(!1),[C,N]=x.useState(null);x.useEffect(()=>{E()},[h,t]);const E=async()=>{try{if(m(!0),t==="cultural_representative"){const g=await P.getCurationsByRepresentative(h);i(g)}if(t==="expert"){const g=await P.getPendingValidations(h);r(g)}const d=await P.generateCurationReport({startDate:new Date(Date.now()-30*24*60*60*1e3),endDate:new Date});N(d)}catch(d){console.error("Error loading dashboard data:",d)}finally{m(!1)}},A=async d=>{try{const g={achievementId:d.achievementId,culturalRepresentativeId:h,curationReason:d.reason,proposedSignificance:d.significance,traditionalElements:d.traditionalElements.split(",").map(u=>u.trim()),culturalProtocols:d.protocols.split(",").map(u=>u.trim()),sensitivityLevel:d.sensitivityLevel,communityConsultationRequired:d.requiresConsultation},f=await P.submitCurationRequest(g);i(u=>[...u,f]),y(!1)}catch(g){console.error("Error submitting curation:",g)}},T=async(d,g,f)=>{try{const u={expertId:h,expertType:"cultural_expert",consultationDate:new Date,findings:g,recommendations:[g],confidence:f};await P.submitExpertValidation(d,h,u),r(p=>p.filter(w=>w.curationId!==d))}catch(u){console.error("Error submitting validation:",u)}},R=d=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>c(d),children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900",children:"Cultural Significance Curation"}),e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${d.publicationStatus.status==="published"?"bg-green-100 text-green-800":d.publicationStatus.status==="cultural_review"?"bg-yellow-100 text-yellow-800":d.publicationStatus.status==="community_review"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:d.publicationStatus.status.replace("_"," ")})]}),e.jsx("p",{className:"text-gray-700 mb-4",children:d.curationMetadata.culturalSignificance.culturalImpact}),e.jsxs("div",{className:"space-y-2 mb-4",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Sensitivity Level:"}),e.jsx("span",{className:"font-medium capitalize",children:d.curationMetadata.sensitivityLevel.level})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Validation Score:"}),e.jsxs("span",{className:"font-medium",children:[d.culturalValidation.validationScore,"/100"]})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Community Approval:"}),e.jsxs("span",{className:"font-medium",children:[d.communityEndorsement.votingResults.approvalPercentage,"%"]})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[d.curationMetadata.traditionalElements.slice(0,3).map((g,f)=>e.jsx("span",{className:"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs",children:g.element},f)),d.curationMetadata.traditionalElements.length>3&&e.jsxs("span",{className:"px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs",children:["+",d.curationMetadata.traditionalElements.length-3," more"]})]})]},d.id),L=d=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900",children:"Validation Request"}),e.jsx("span",{className:"px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs",children:d.validatorType.replace("_"," ")})]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Validation Criteria:"}),e.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:d.validationCriteria.map((g,f)=>e.jsxs("li",{children:["• ",g]},f))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Your Expertise Areas:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:d.expertiseAreas.map((g,f)=>e.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs",children:g},f))})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("textarea",{placeholder:"Enter your validation findings...",className:"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",rows:3,id:`findings-${d.curationId}`}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Confidence Level:"}),e.jsx("input",{type:"range",min:"0",max:"100",defaultValue:"80",className:"flex-1",id:`confidence-${d.curationId}`}),e.jsx("span",{className:"text-sm text-gray-600",children:"80%"})]}),e.jsx("button",{onClick:()=>{const g=document.getElementById(`findings-${d.curationId}`).value,f=parseInt(document.getElementById(`confidence-${d.curationId}`).value);T(d.curationId,g,f)},className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700",children:"Submit Validation"})]})]},d.curationId),V=()=>e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Submit Cultural Curation"}),e.jsx("button",{onClick:()=>y(!1),className:"text-gray-400 hover:text-gray-600",children:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("form",{onSubmit:d=>{d.preventDefault();const g=new FormData(d.target),f=Object.fromEntries(g.entries());f.requiresConsultation=g.get("requiresConsultation")==="on",A(f)},children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Achievement ID"}),e.jsx("input",{type:"text",name:"achievementId",required:!0,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Enter achievement ID to curate"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Curation Reason"}),e.jsx("textarea",{name:"reason",required:!0,rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Why is this achievement culturally significant?"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cultural Significance"}),e.jsx("textarea",{name:"significance",required:!0,rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Describe the cultural significance and impact"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Traditional Elements"}),e.jsx("input",{type:"text",name:"traditionalElements",className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Comma-separated list of traditional elements"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cultural Protocols"}),e.jsx("input",{type:"text",name:"protocols",className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Comma-separated list of cultural protocols"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sensitivity Level"}),e.jsxs("select",{name:"sensitivityLevel",required:!0,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"public",children:"Public"}),e.jsx("option",{value:"community_only",children:"Community Only"}),e.jsx("option",{value:"restricted",children:"Restricted"}),e.jsx("option",{value:"sacred",children:"Sacred"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",name:"requiresConsultation",id:"requiresConsultation",className:"mr-2"}),e.jsx("label",{htmlFor:"requiresConsultation",className:"text-sm text-gray-700",children:"Requires community consultation"})]})]}),e.jsxs("div",{className:"flex space-x-4 mt-6",children:[e.jsx("button",{type:"button",onClick:()=>y(!1),className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Submit Curation"})]})]})]})}),D=()=>e.jsx("div",{className:"space-y-6",children:C&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-6",children:"Curation Analytics (Last 30 Days)"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-blue-600",children:C.totalCurations}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Curations"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-green-600",children:C.publishedCurations}),e.jsx("div",{className:"text-sm text-gray-600",children:"Published"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-3xl font-bold text-purple-600",children:[Math.round(C.publicationRate),"%"]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Publication Rate"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-orange-600",children:Math.round(C.averageValidationScore)}),e.jsx("div",{className:"text-sm text-gray-600",children:"Avg Validation Score"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h4",{className:"font-semibold mb-4",children:"Cultural Distribution"}),e.jsx("div",{className:"space-y-2",children:Object.entries(C.culturalDistribution).map(([d,g])=>e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:d}),e.jsx("span",{className:"font-medium",children:g})]},d))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h4",{className:"font-semibold mb-4",children:"Sensitivity Distribution"}),e.jsx("div",{className:"space-y-2",children:Object.entries(C.sensitivityDistribution).map(([d,g])=>e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600 capitalize",children:d.replace("_"," ")}),e.jsx("span",{className:"font-medium",children:g})]},d))})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h4",{className:"font-semibold mb-4",children:"Top Curations by Engagement"}),e.jsx("div",{className:"space-y-3",children:C.topCurations.map((d,g)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded",children:[e.jsxs("div",{children:[e.jsxs("span",{className:"font-medium",children:["#",g+1]}),e.jsx("span",{className:"ml-2 text-sm text-gray-700",children:d.culturalSignificance})]}),e.jsxs("span",{className:"text-sm font-medium text-blue-600",children:[d.engagement," engagement"]})]},d.id))})]})]})});return o?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Cultural Curation Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Curate and validate culturally significant achievements with community oversight"})]}),t==="cultural_representative"&&e.jsx("button",{onClick:()=>y(!0),className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium",children:"Submit New Curation"})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6",children:[{key:"my_curations",label:"My Curations",icon:"📝",show:t==="cultural_representative"},{key:"pending_review",label:"Pending Review",icon:"⏳",show:t==="expert"},{key:"published",label:"Published",icon:"✅",show:!0},{key:"analytics",label:"Analytics",icon:"📊",show:!0}].filter(d=>d.show).map(d=>e.jsxs("button",{onClick:()=>S(d.key),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${v===d.key?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[e.jsx("span",{children:d.icon}),e.jsx("span",{children:d.label})]},d.key))}),v==="my_curations"&&e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-semibold mb-6",children:["Your Curations (",s.length,")"]}),s.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(R)}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No curations yet"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Start curating culturally significant achievements"}),e.jsx("button",{onClick:()=>y(!0),className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Submit Your First Curation"})]})]}),v==="pending_review"&&e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-semibold mb-6",children:["Pending Validations (",a.length,")"]}),a.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:a.map(L)}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No pending validations"}),e.jsx("p",{className:"text-gray-600",children:"All validation requests have been completed"})]})]}),v==="published"&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"Published Curations"}),e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-600",children:"Published curations will be displayed here"})})]}),v==="analytics"&&D(),b&&V(),n&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto p-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Curation Details"}),e.jsx("button",{onClick:()=>c(null),className:"text-gray-400 hover:text-gray-600",children:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Cultural Significance"}),e.jsx("p",{className:"text-gray-700",children:n.curationMetadata.culturalSignificance.culturalImpact})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Traditional Elements"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:n.curationMetadata.traditionalElements.map((d,g)=>e.jsx("span",{className:"px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm",children:d.element},g))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Validation Status"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Validation Score:"}),e.jsxs("span",{className:"ml-2 font-medium",children:[n.culturalValidation.validationScore,"/100"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Cultural Accuracy:"}),e.jsxs("span",{className:"ml-2 font-medium",children:[n.culturalValidation.culturalAccuracyRating,"/100"]})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Community Approval"}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Approval Rate:"}),e.jsxs("span",{className:"font-medium",children:[n.communityEndorsement.votingResults.approvalPercentage,"%"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Total Votes:"}),e.jsx("span",{className:"font-medium",children:n.communityEndorsement.votingResults.totalVotes})]})]})]})]})]})})]})},G=({userId:h,userRole:t="community_member"})=>{const[s,i]=x.useState("gallery"),a=()=>{switch(s){case"gallery":return e.jsx(_,{userId:h});case"recognition":return e.jsx(H,{userId:h});case"curation":return e.jsx(K,{userId:h,userRole:t});default:return e.jsx(_,{userId:h})}};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Achievement Showcase & Recognition"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Discover, celebrate, and preserve South African achievements across all cultures"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[e.jsx("button",{onClick:()=>i("gallery"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${s==="gallery"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Gallery"}),e.jsx("button",{onClick:()=>i("recognition"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${s==="recognition"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Recognition"}),e.jsx("button",{onClick:()=>i("curation"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${s==="curation"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Curation"})]})})]})})}),e.jsx("div",{className:"bg-blue-50 border-b border-blue-200",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:[s==="gallery"&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"South African Achievement Gallery"}),e.jsx("p",{className:"text-blue-700",children:"Explore a comprehensive collection of verified achievements from across South Africa's diverse cultural communities, celebrating excellence in sports, arts, business, education, and community service."})]})]}),s==="recognition"&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Cross-Cultural Engagement Recognition"}),e.jsx("p",{className:"text-blue-700",children:"Earn badges and recognition for meaningful cross-cultural engagement, community service, and bridge-building activities that strengthen Ubuntu connections across diverse communities."})]})]}),s==="curation"&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Cultural Representative Achievement Curation"}),e.jsx("p",{className:"text-blue-700",children:"Cultural representatives curate and validate achievements with community oversight, ensuring cultural accuracy, sensitivity, and appropriate preservation of traditional knowledge."})]})]})]})}),e.jsx("div",{className:"py-8",children:a()}),e.jsx("div",{className:"bg-gray-900 text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-xl font-semibold mb-4",children:"Celebrating Excellence Through Ubuntu"}),e.jsx("p",{className:"text-gray-300 max-w-3xl mx-auto",children:"Our achievement showcase celebrates the principle that individual excellence contributes to collective prosperity. By recognizing and preserving achievements across all cultures, we honor the diverse talents that make South Africa strong while building bridges of understanding and mutual respect."})]}),e.jsxs("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"bg-blue-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),e.jsx("h4",{className:"font-medium mb-2",children:"Discover & Celebrate"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Explore achievements from all South African cultures and communities"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"bg-green-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})})}),e.jsx("h4",{className:"font-medium mb-2",children:"Earn Recognition"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Build bridges between cultures and earn badges for meaningful engagement"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"bg-purple-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"})})}),e.jsx("h4",{className:"font-medium mb-2",children:"Preserve Heritage"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Ensure cultural accuracy and sensitivity in achievement documentation"})]})]}),e.jsx("div",{className:"mt-8 pt-8 border-t border-gray-700",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h4",{className:"font-medium mb-4",children:"Recognition Categories"}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-4 text-sm",children:[e.jsx("span",{className:"px-3 py-1 bg-gray-800 rounded-full",children:"🏆 Sports & Athletics"}),e.jsx("span",{className:"px-3 py-1 bg-gray-800 rounded-full",children:"🎨 Arts & Culture"}),e.jsx("span",{className:"px-3 py-1 bg-gray-800 rounded-full",children:"💼 Business & Innovation"}),e.jsx("span",{className:"px-3 py-1 bg-gray-800 rounded-full",children:"📚 Education & Learning"}),e.jsx("span",{className:"px-3 py-1 bg-gray-800 rounded-full",children:"🔬 Innovation & Technology"}),e.jsx("span",{className:"px-3 py-1 bg-gray-800 rounded-full",children:"🤝 Community Service"}),e.jsx("span",{className:"px-3 py-1 bg-gray-800 rounded-full",children:"🌍 Cross-Cultural Bridge Building"})]})]})})]})})]})};export{G as default};
//# sourceMappingURL=AchievementShowcasePage-CmfOiW9q.js.map
