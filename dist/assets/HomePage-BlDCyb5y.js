import{u as m,g as j,a as g,j as e,B as s,C as l,b as r,d as t,e as a,f as i}from"./index-nwrMOwxu.js";import"./vendor-DtOhX2xw.js";import"./firebase-DLuFXYhP.js";const N=()=>{const{t:n}=m(),c=j(),{user:d,logout:o,userDisplayName:x}=g(),u=async()=>{try{await o()}catch(h){console.error("Logout failed:",h)}};return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-cultural-50 via-white to-ubuntu-50",children:[e.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-cultural-500 to-ubuntu-500 rounded-full"}),e.jsx("h1",{className:"text-xl font-bold text-cultural-gradient",children:n("app.name")})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-sm text-gray-600",children:["Welcome, ",x]}),e.jsx(s,{variant:"outline",size:"sm",onClick:u,children:n("auth.logout")})]})]})})}),e.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Welcome to Ubuntu Connect"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:n("app.tagline")})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsxs(l,{variant:"cultural",hover:!0,children:[e.jsxs(r,{children:[e.jsx(t,{children:"Cultural Heritage"}),e.jsx(a,{children:"Document and preserve South African cultural heritage for future generations"})]}),e.jsx(i,{children:e.jsx(s,{variant:"outline",fullWidth:!0,onClick:()=>c("/cultural-knowledge"),children:"Explore Heritage"})})]}),e.jsxs(l,{variant:"cultural",hover:!0,children:[e.jsxs(r,{children:[e.jsx(t,{children:"Knowledge Sharing"}),e.jsx(a,{children:"Share skills and learn from mentors across cultural boundaries"})]}),e.jsx(i,{children:e.jsx(s,{variant:"outline",fullWidth:!0,onClick:()=>c("/cultural-knowledge"),children:"Share & Learn"})})]}),e.jsxs(l,{variant:"cultural",hover:!0,children:[e.jsxs(r,{children:[e.jsx(t,{children:"Content Discovery"}),e.jsx(a,{children:"Discover personalized cultural content and trending topics"})]}),e.jsx(i,{children:e.jsx(s,{variant:"outline",fullWidth:!0,onClick:()=>c("/cultural-knowledge"),children:"Discover Content"})})]})]}),e.jsxs(l,{variant:"elevated",children:[e.jsxs(r,{children:[e.jsx(t,{children:"Your Profile"}),e.jsx(a,{children:"Complete your profile to get the most out of Ubuntu Connect"})]}),e.jsx(i,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Email Verification"}),e.jsx("span",{className:`text-sm px-2 py-1 rounded-full ${d?.emailVerified?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:d?.emailVerified?"Verified":"Pending"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Cultural Profile"}),e.jsx(s,{variant:"outline",size:"sm",children:"Complete Profile"})]})]})})]})]})})]})};export{N as default};
//# sourceMappingURL=HomePage-BlDCyb5y.js.map
