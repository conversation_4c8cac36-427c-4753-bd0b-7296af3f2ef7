if(!self.define){let e,s={};const n=(n,i)=>(n=new URL(n+".js",i).href,s[n]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()})).then((()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e})));self.define=(i,l)=>{const r=e||("document"in self?document.currentScript.src:"")||location.href;if(s[r])return;let o={};const t=e=>n(e,r),u={module:{uri:r},exports:o,require:t};s[r]=Promise.all(i.map((e=>u[e]||t(e)))).then((e=>(l(...e),o)))}}define(["./workbox-239d0d27"],(function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"assets/AchievementShowcasePage-CmfOiW9q.js",revision:null},{url:"assets/AuthPage-Chhb6i1V.js",revision:null},{url:"assets/CrossCulturalCollaborationPage-CeaGM6MI.js",revision:null},{url:"assets/CulturalKnowledgePage-BnYvObkg.js",revision:null},{url:"assets/CulturalOnboardingPage-Cu7AFwSZ.js",revision:null},{url:"assets/culturalValidationService-CA9WZNCm.js",revision:null},{url:"assets/firebase-DLuFXYhP.js",revision:null},{url:"assets/HomePage-BlDCyb5y.js",revision:null},{url:"assets/index-DW0Ai7MP.css",revision:null},{url:"assets/index-nwrMOwxu.js",revision:null},{url:"assets/KnowledgeExchangePage-C23voltd.js",revision:null},{url:"assets/ui-ktUZWAxd.js",revision:null},{url:"assets/vendor-DtOhX2xw.js",revision:null},{url:"index.html",revision:"93e15d5ea9f00baecb95a69e872d878b"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"manifest.webmanifest",revision:"2a66951ad2c66b0c2be392d52c7ebbf4"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/fonts\.googleapis\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-cache",plugins:[new e.ExpirationPlugin({maxEntries:10,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/firestore\.googleapis\.com\/.*/i,new e.NetworkFirst({cacheName:"firestore-cache",networkTimeoutSeconds:3,plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:300})]}),"GET")}));
//# sourceMappingURL=sw.js.map
